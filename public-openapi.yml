openapi: "3.0.1"
x-amazon-apigateway-importexport-version: "1.0"
info:
  title: "${http_api_name}"
paths:
  /api/v1.0/health-check:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/health-check"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/brokerages:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerages"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerages"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/brokerages/{id}:
    put:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerages/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/brokerages/find:
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerages/find"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/brokerage_employees/find:
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerage_employees/find"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/revert_to_bc:
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}/revert_to_bc"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/revert_to_do:
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}/revert_to_do"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/revert/{state}:
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}/revert/{state}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/brokerage_employees:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerage_employees"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerages"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/brokerage_employees/{id}:
    put:
      responses:
        default:
          description: "Default response for GET /api/v3.0/brokerage_employees/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/organizations/{id}/org-metric-configs:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/organizations/{id}/org-metric-configs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/assign_clearing_user:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{id}/assign_clearing_user"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/users/notifications:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{id}/users/notifications"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/users/notifications"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/report-id:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/report-id"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/public-info:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/public-info"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/organization:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v1.0/organization"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/organization"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/timezones:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/timezones"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/dashboards/{name}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/dashboards/{name}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/classifiers-metadata:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/classifiers-metadata"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/customizable-classifiers:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/customizable-classifiers"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/customizable-classifiers"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/customizable-classifiers-v2:
      get:
        responses:
          default:
            description: "Default response for GET /api/v3.0/customizable-classifiers-v2"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      post:
        responses:
          default:
            description: "Default response for POST /api/v3.0/customizable-classifiers-v2"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/customizable-classifiers-v2/{id}:
      patch:
        responses:
          default:
            description: "Default response for PATCH /api/v3.0/customizable-classifiers-v2/{id}"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      delete:
        responses:
          default:
            description: "Default response for DELETE /api/v3.0/customizable-classifiers-v2/{id}"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/customizable-classifiers-v2/{classifier_id}/activate-version/{version_id}:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/customizable-classifiers-v2/{classifier_id}/activate-version/{version_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/customizable-classifiers-v2/{classifier_id}/versions/{version_id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/customizable-classifiers-v2/{classifier_id}/versions/{version_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/report_processing_dependencies:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/report_processing_dependencies"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/customizable-classifiers/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/customizable-classifiers/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/customizable-classifiers/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/customizable-classifiers/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/customizable-classifiers/{classifier_id}/test-runs:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/customizable-classifiers/{classifier_id}/test-runs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/customizable-classifiers/{classifier_id}/test-runs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/file-stats:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/file-stats"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/custom-file-types/aggregated:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/custom-file-types/aggregated"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/file-types/aggregated:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/file-types/aggregated"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/feature-flags/{feature}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/feature-flags/{feature}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/customizable-classifiers/{classifier_id}/test-runs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/test-runs/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/test-runs/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/test-runs/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/classification-tasks/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/classification-tasks/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/classification-tasks/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/resolutions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/resolutions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/resolutions/identifiers:
    get:
      responses:
        default:
          description: "Default response for POST /api/v3.0/resolutions/identifiers"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/feedback/{id}/{pageNumber}/{type}:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/feedback/{id}/{pageNumber}/{type}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/uploaded-forms/{id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v1.0/uploaded-forms/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v2.0/summary_preferences:
    get:
      responses:
        default:
          description: "Default response for GET /api/v2.0/summary_preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/business-rules-data:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/business-rules-data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/recommendation-invocations:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/recommendation-invocations"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/validate-requested-recommendations:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/validate-requested-recommendations"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/recommendations/sync_submission_data:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/recommendations/sync_submission_data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/recommendation-aggregation:
    get:
      responses:
        default:
          description: "Default response for POST /api/v3.0/recommendation-aggregation"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/organizations/support-email:
    get:
      responses:
        default:
          description: "Default response for POST /api/v3.0/organizations/support-email"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/rush-evidence:
    get:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports/{id}/rush-evidence"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/metric_templates/{id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/metric_templates/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/metric_templates/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/hub_templates/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/hub_templates/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/hub_templates/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/support:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/support"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /users/support/check-activity:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/support/check-activity"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/support/current:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/support/current"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/support/{user_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/support/{user_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/users/support/{user_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{userId}/bookmarks:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{userId}/bookmark"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{userId}/bookmarks/{submissionId}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/users/{userId}/bookmark/{submissionId}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/read:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/read"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{id}/read"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{userId}/hub_templates:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{userId}/hub_templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{userId}/hub_templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/metric_preferences:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}/metric_preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports/{id}/metric_preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/pds-debugger-data/files:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{report_id}/pds-debugger-data/files"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{user_id}/open-reports:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{user_id}/open-reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{user_id}/delete-open-reports:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{user_id}/delete-open-reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{user_id}/open-reports/{report_id}:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{user_id}/open-reports/{report_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/users/{user_id}/open-reports/{report_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/summary_preferences:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/reports/{id}/summary_preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}/summary_preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v2.0/reports/{id}/summary_preferences:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v2.0/reports/{id}/summary_preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v2.0/reports/{id}/summary_preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/get_metrics_v2:
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}/get_metrics_v2"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}/evidence-feedback:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/reports/{id}/evidence-feedback"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}/news:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/{id}/news"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/extract:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/extract"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/merge:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports/merge"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/extract:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/{id}/extract"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/exports:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/{id}/exports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/process:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/reports/{id}/process"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/cancel_process:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/reports/{id}/cancel_process"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/reprocess:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/reports/{id}/reprocess"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}/service:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/reports/{id}/service"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{report_id}/fields/{field_id}/history:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/{report_id}/fields/{field_id}/history"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}/fields:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v1.0/reports/{id}/fields"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/{id}/fields"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}/feedback:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/reports/{id}/feedback"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}/stage:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/{id}/stage"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}/coverage:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v1.0/reports/{id}/coverage"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v1.0/reports/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v2.0/reports/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v2.0/reports/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/reports/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/reports/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{id}/external:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/reports/{id}/external"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{id}/external"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/status/external:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports/{report_id}/status/external"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/files/{external_id}/external:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/reports/{report_id}/files/{external_id}/external"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/reports/{report_id}/files/{external_id}/external"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/notes/external:
    post:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/reports/{report_id}/notes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/notes/{note_id}/external:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/reports/{report_id}/notes/{note_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/clear:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/clear"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/merge-correspondence:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/merge-correspondence"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/upload-osha-violation:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/upload-osha-violation"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/workers-comp-experience/{submissionId}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/workers-comp-experience/{submissionId}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/workers-comp-experience:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/workers-comp-experience"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submission_level_extracted_data:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submission_level_extracted_data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/users:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/users"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{submission_id}/users"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/client-submission-ids:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/client-submission-ids"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{submission_id}/client-submission-ids"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/client-submission-ids/bulk:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/client-submission-ids/bulk"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/verify:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/verify"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/download-files:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/download-files"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/download_loaded_data:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/download_loaded_data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/shareholders:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/shareholders"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/description-of-operations:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/description-of-operations"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/files-data:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/files-data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/identifier-suggestions:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/identifier-suggestions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/requested-coverages/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/requested-coverages/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/coverages/groups:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/coverages/groups"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/requested-coverages:
    post:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{id}/requested-coverages"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/requested-coverages/{coverage_id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{id}/requested-coverages/{coverage_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{id}/requested-coverages/{coverage_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"

  /api/v3.0/submissions/{submission_id}/business_resolution_data:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/business_resolution_data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/business_resolution_data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/user-submission-stages/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/user-submission-stages/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/metric_preferences/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/metric_preferences/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submission-permissions/{id}/close-referral:
    post:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submission-permissions/{id}/close-referral"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"        
  /api/v3.0/dossiers/{id}/stories:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/dossiers/{id}/stories"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/losses:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/losses"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/losses"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/loss_lob_inference_requirements:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/loss_lob_inference_requirements"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/losses/submission/{submission_id}/file_statuses:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/losses/submission/{submission_id}/file_statuses"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/losses/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/losses/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/losses/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/losses/summary:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/losses/summary"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/losses/policy:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submissionId}/policies"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/losses/policies:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/losses/policies"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/audit_questions:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/audit_questions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/audit_questions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/history:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/history"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/coverage-history:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/coverage-history"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/reassign-first-party-fact-subtype:
    post:
      responses:
        default:
          description: "Default response for POST /submissions/{id}/reassign-first-party-fact-subtype"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/first-party-suggestions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/first-party-suggestions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/first-party-suggestions/v2:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/first-party-suggestions/v2"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submissionId}/business-field-values:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submissionId}/business-field-values"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submissionId}/business-field-values/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{submissionId}/business-field-values/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submissionId}/business-fields/{id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{submissionId}/business-fields/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{submissionId}/business-fields/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submissionId}/quotes:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submissionId}/quotes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submissionId}/quotes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/quotes/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/quotes/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/quotes/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/business-fields:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/business-fields"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/business-fields"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/file-types:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/file-types"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/data-onboarding/units:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/data-onboarding/units"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/finish-external-clearing:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/finish-external-clearing"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/files:
    get:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/files"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/file_by_parent/{file_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/file_by_parent/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/files/{file_id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{id}/files/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/files/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{id}/files/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/files_clearing:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/files_clearing"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/entity_mapping:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/entity_mapping"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/entity_mapping"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/entity_mapping"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/business_confirmation:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/business_confirmation"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/data_onboarding:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/data_onboarding"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/data_onboarding"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/data_onboarding"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/files/{file_id}/onboarded_data:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/files/{file_id}/onboarded_data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/files/{file_id}/onboarded_data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/files/{file_id}/download:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/files/{file_id}/download"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/files/{file_id}/url:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/files/{file_id}/url"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"        
  /api/v3.0/submissions/{submission_id}/suggestions/groups:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/suggestions/groups"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/complete:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/complete"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/files:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/files"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/enhanced-files:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/enhanced-files"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/files/{file_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/files/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/files/{file_id}/split_pdf:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3:0/files/{file_id}/split_pdf"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/files/external:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/files/external"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/files/upload_url:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/files/upload_url"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/files/reprocess/{file_id}:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/files/reprocess/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/files/download_file:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{report_id}/files/download_file"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/files/download-url:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{report_id}/files/download-url"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"        
  /api/v3.0/reports/{report_id}/external/files:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{report_id}/external/files"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/processed_file/{file_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/processed_file/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/processed_file/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/processed_file/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/processed_file/{id}/resolution_data_row:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/processed_file/{id}/resolution_data_row"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/additional-file:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/additional-file"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/urls:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/urls"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/spreadsheets:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/spreadsheets"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/spreadsheets"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/spreadsheets/{id}:
    patch:
      responses:
        default:
          description: "Default response for POST /api/v3.0/spreadsheets/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/notebook-threads/{id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/notebook-threads/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/notebook/messages/{message_id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{id}/notebook/messages/{message_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{id}/notebook/messages/{message_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/notebook/threads/{thread_id}/messages:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/notebook/threads/{thread_id}/messages"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/notebook/threads:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/notebook/threads"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/notebook/threads"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/businesses/{businessId}:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{id}/businesses/{businessId}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{id}/businesses/{businessId}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{id}/businesses/{businessId}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/businesses:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/businesses"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/businesses/{id}/at_location:
    get:
      responses:
        default:
          description: "Default response for GET /submissions/{submission_id}/businesses/{id}/at_location"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/businesses/full:
    get:
      responses:
        default:
          description: "Default response for GET /submissions/{submission_id}/businesses/full"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"        
  /api/v3.0/submissions/{submission_id}/businesses/{id}/prometrix_risks:
    get:
      responses:
        default:
          description: "Default response for GET /submissions/{submission_id}/businesses/{id}/prometrix_risks"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/snapshot:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/snapshot"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/invoke_loss_run_processing:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/invoke_loss_run_processing"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/process_loss_runs:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/process_loss_runs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/pds-check:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/pds-check"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/permissions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/permissions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/permissions/{id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/permissions/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/permissions/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/organizations/{organizationId}/policies/{policyId}:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/organizations/{organizationId}/policies/{policyId}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/organizations/{organizationId}/policies/{policyId}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submissionId}/policies:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submissionId}/policies"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{user_id}/profile:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/users/{user_id}/profile"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v2.0/user_actions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v2.0/user_actions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/knock-token:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/knock-token"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/current-user:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/current-user"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/login:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/login"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}/subscriptions:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/{id}/subscriptions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}/feedback/insights:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/{id}/feedback/insights"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v1.0/users/{id}/feedback/insights"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}/metric_templates:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/{id}/metric_templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v1.0/users/{id}/metric_templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{user_id}/tenants/feedback/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v1.0/users/{user_id}/tenants/feedback/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}/tenants-feedback:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/{id}/tenants-feedback"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/users/{id}/tenants-feedback"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}/client-applications:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/{id}/client-applications"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/organization/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/organization/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}/submissions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/users/{id}/submissions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/users/{id}/reports:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/users/{id}/reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/users/{id}/reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/coverages:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/coverages"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/emails/send:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/emails/send"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/bulk_decline:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/bulk_decline"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/send_declined_email:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/send_declined_email"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/send_email:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/send_email"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/email_dynamic_data:
    get:
      responses:
        default:
          description: "Default response for POST /api/v3.0/send_email"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/emails-unauthorized/send:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/emails-unauthorized/send"
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports/{report_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v1.0/reports/{report_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/submissions/{submission_id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v1.0/submissions/{submission_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v1.0/reports:
    post:
      responses:
        default:
          description: "Default response for POST /api/v1.0/reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/subscriptions:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{id}/subscriptions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/feedback/insights:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{id}/feedback/insights"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/users/{id}/feedback/insights"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/metric_templates:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{id}/metric_templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/users/{id}/metric_templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v2.0/users/{user_id}/tenants/feedback/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v2.0/users/{user_id}/tenants/feedback/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/tenants-feedback:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{id}/tenants-feedback"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{id}/tenants-feedback"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/stuck-submission-feedback:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/stuck-submission-feedback"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/client-applications:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{id}/client-applications"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/organization/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/organization/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/uploads:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{id}/uploads"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/submissions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{id}/submissions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/reports:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{id}/reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/users/{id}/reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/{id}/email-templates:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/{id}/email-templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/users/signup:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/users/signup"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/coverages:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/coverages"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{report_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/{submission_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/execution-events:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/execution-events"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/external:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports/external"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/bulk-get:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports/bulk-get"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/lobs:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/lobs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/lobs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/lobs/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/lobs/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/lobs/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/lob-types:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/lob-types"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/settings/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/settings/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/temp-migrate-agents-without-agency:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/temp-migrate-agents-without-agency"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/agents:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/agents"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/agents"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/agents/{id}:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/agents/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/organizations/{id}/settings:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/organizations/{id}/settings"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/organizations/{id}/settings"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/organizations:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/organizations"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/organizations/{organization_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/organizations/{organization_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/organizations/{organization_id}/sic_codes:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/organizations/{organization_id}/sic_codes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/link-reports:
    post:
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/bundle-reports:
    post:
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/unbundle-reports:
    post:
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/search:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/search"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/webhooks/sensible/acords:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/webhooks/sensible/acords"
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/identifiers:
    put:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/identifiers"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"        
  /api/v3.0/submissions/{submission_id}/identifier/delete:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/identifier/delete"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"            
  /api/v3.0/webhooks/sensible/lossruns:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/webhooks/sensible/lossruns"
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/webhooks/sendgrid/event:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/webhooks/sendgrid/event"
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/email-templates:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/email-templates"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/email-templates/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/email-templates/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/email-templates/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/email-templates/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/ask-questions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/ask-questions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/subtypes-benchmark-data:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0//subtypes-benchmark-data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0//subtypes-benchmark-data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0//subtypes-benchmark-data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0//subtypes-benchmark-data"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/emails:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{report_id}/emails"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/reports/{report_id}/emails"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/reports/{report_id}/shadow:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/reports/{report_id}/shadow"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/admin/organizations:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/admin/organizations"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/admin/users:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/admin/users"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/admin/users"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/admin/users"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/user-groups:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/user-groups"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/user-groups"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/user-groups/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/user-groups/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/user-groups/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/user-groups/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{id}/notes:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{id}/notes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{id}/notes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/notes/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/submissions/notes/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/notes/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/user-groups/{id}/users/{user_id}:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/user-groups/{id}/users/{user_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/user-groups/{id}/users/{user_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions-queue:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions-queue"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions-queue/count:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions-queue/count"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions-naics-queue:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions-naics-queue"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/workers-comp-state-rating-information/class-codes:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/workers-comp-state-rating-information/class-codes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/ifta-data/{file_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/ifta-data/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/ifta-entity:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/ifta-entity/"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/ifta-entity/{id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/ifta-entity/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/ifta-entity/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/aggregated-ifta-data/{submission_id}:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/aggregated-ifta-data/{submission_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/aggregated-ifta-data/{submission_id}/quarters:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/aggregated-ifta-data/{submission_id}/quarters"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/submission_priority:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/submission_priority"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submissions/{submission_id}/submission_priority"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/statistics/recommendations_breakdown:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/statistics/recommendations_breakdown"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/client_stage_config:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/client_stage_config"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/workers-comp-state-rating-information/{submission_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/workers-comp-state-rating-information/{submission_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/report_id:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/report_id"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/stuck-details:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/stuck-details"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/stuck-details"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/experiments:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/experiments"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/experiments/fix_em_experiments:
    post:
      responses:
        default:
          description: "Default response for GET /api/v3.0/experiments"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/experiments/{exp_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/experiments/{exp_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/experiments/{exp_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/experiments/{exp_id}/runs:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/experiments/{exp_id}/runs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/experiments/{exp_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submissions/{submission_id}/experiments/{exp_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/broker_groups:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/broker_groups"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/experiments/{exp_id}/force:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submissions/{submission_id}/experiments/{exp_id}/force"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submissions/{submission_id}/experiments/{exp_id}/samples/{sample_id}:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submissions/{submission_id}/experiments/{exp_id}/force"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submission-relations:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submission-relations"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submission-relations"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submission-relations/replace:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/submission-relations/replace"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/submission-relations/{id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/submission-relations/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/submission-relations/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/submission-relations/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/routing_rules:
    get:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/routing_rules"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    post:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/routing_rules"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"              
  /api/v3.0/routing_rules/{id}:
    patch:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/routing_rules/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/routing_rules/{id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"        
  /api/v3.0/fact-subtypes:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/fact-subtypes"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/metadata/{submission_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/metadata/{submission_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/rater/execute:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/rater/execute"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/task-definitions/{task_code}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/task-definitions/{task_code}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/tasks:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/tasks"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/tasks/{task_id}:
    patch:
      responses:
        default:
          description: "Default response for PATCH /api/v3.0/tasks/{"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/tasks/{task_id}/executions:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/tasks/{task_id}/executions"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/preferences:
    put:
      responses:
        default:
          description: "Default response for PUT /api/v3.0/preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/preferences"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/fact-subtype-selection-benchmark:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/fact-subtype-selection-benchmark"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/emails/classifications:
      post:
        responses:
          default:
            description: "Default response for POST /api/v3.0/emails/classifications"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/emails/classifications/{id}:
      patch:
        responses:
          default:
            description: "Default response for PATCH /api/v3.0/emails/classifications/{id}"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      delete:
        responses:
          default:
            description: "Default response for DELETE /api/v3.0/emails/classifications/{id}"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/emails/classification_labels:
      get:
        responses:
          default:
            description: "Default response for GET /api/v3.0/emails/classification_labels"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      post:
        responses:
          default:
            description: "Default response for POST /api/v3.0/emails/classification_labels"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      patch:
        responses:
          default:
            description: "Default response for PATCH /api/v3.0/emails/classification_labels"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      delete:
        responses:
          default:
            description: "Default response for DELETE /api/v3.0/emails/classification_labels"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/email_classifiers:
      get:
        responses:
          default:
            description: "Default response for GET /api/v3.0/email_classifiers"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      post:
        responses:
          default:
            description: "Default response for POST /api/v3.0/email_classifiers"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/brokerages/offices:
      get:
        responses:
          default:
            description: "Default response for GET /api/v3.0/brokerages/offices"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"        
  /api/v3.0/email_classifiers/{id}:
      get:
        responses:
          default:
            description: "Default response for GET /api/v3.0/email_classifiers/{id}"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      patch:
        responses:
          default:
            description: "Default response for PATCH /api/v3.0/email_classifiers/{id}"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
      delete:
        responses:
          default:
            description: "Default response for DELETE /api/v3.0/email_classifiers/{id}"
        security:
          - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
          $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/insights-document-types/{organization_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/insights-document-types/{organization_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/emails/{id}/classification_labels:
    post:
        responses:
            default:
            description: "Default response for POST /api/v3.0/emails/{id}/classification_labels"
        security:
            - custom-lambda-authorizer: [ ]
        x-amazon-apigateway-integration:
            $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/custom-file-types/{organization_id}:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/custom-file-types/{organization_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/custom-file-type:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0//custom-file-type"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/document-ingestion-queue:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/document-ingestion-queue"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/document-ingestion-queue/assignments:
    post:
      responses:
        default:
          description: "Default response for POST /api/v3.0/document-ingestion-queue/assignments"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/document-ingestion-queue/assignments/{file_id}:
    delete:
      responses:
        default:
          description: "Default response for DELETE /api/v3.0/document-ingestion-queue/assignments/{file_id}"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/taxonomy-mappings:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/taxonomy-mappings"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"
  /api/v3.0/integrations/logs:
    get:
      responses:
        default:
          description: "Default response for GET /api/v3.0/integrations/logs"
      security:
        - custom-lambda-authorizer: [ ]
      x-amazon-apigateway-integration:
        $ref: "#/components/x-amazon-apigateway-integrations/alb-integration"

components:
  securitySchemes:
    ${custom_lambda_authorizer}
  x-amazon-apigateway-integrations:
    ${alb_integration}

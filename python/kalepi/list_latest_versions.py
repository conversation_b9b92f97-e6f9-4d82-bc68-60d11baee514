import json
import os
import re

import requests
from requests.auth import AuthBase


def get_latest_versions(base_url: str, index: str, auth: AuthBase | None = None) -> dict[str, dict[str, str]]:
    url = f"{base_url}/{index}"
    headers = {"Accept": "text/html"}
    response = requests.get(url, auth=auth, headers=headers)
    response.raise_for_status()
    html = response.text
    versions = re.findall(
        rf"href=[\"\']http[s|]:\/\/(?:(?:\w+\.)+(?:\w+))\/(\w+\/\w+)\/([\w\-]+)\/([\d\.]+)[\"\']", html
    )

    return {
        package: version for package, version in {x[1].lower(): x[2] for x in versions if x[0].lower() == index}.items()
    }


if __name__ == "__main__":
    from python.kalepi.kalepi_auth import repository_data_from_env

    print(json.dumps(get_latest_versions(*repository_data_from_env()), indent=4 if os.isatty(1) else None))

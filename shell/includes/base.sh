set -euo pipefail

export INCLUDES_DIR=$(realpath "$(dirname "${BASH_SOURCE[0]}")")
export SHELL_DIR=$(realpath "$(dirname "${BASH_SOURCE[0]}")/../")
export REPO_ROOT_DIR=$(realpath "$(dirname "${BASH_SOURCE[0]}")/../../")
export BIN_DIR=$(realpath "${REPO_ROOT_DIR}/bin/")
export PYTHON_DIR=$(realpath "${REPO_ROOT_DIR}/python/")

_NC='\033[0m' # Text Reset

# Regular             Bold                  Underline             High Intensity        BoldHigh Intens       Background           High Intensity Backgrounds
Bla='\033[0;30m';     BBla='\033[1;30m';    UBla='\033[4;30m';    IBla='\033[0;90m';    BIBla='\033[1;90m';   On_Bla='\033[40m';    On_IBla='\033[0;100m';
Red='\033[0;31m';     BRed='\033[1;31m';    URed='\033[4;31m';    IRed='\033[0;91m';    BIRed='\033[1;91m';   On_Red='\033[41m';    On_IRed='\033[0;101m';
Gre='\033[0;32m';     BGre='\033[1;32m';    UGre='\033[4;32m';    IGre='\033[0;92m';    BIGre='\033[1;92m';   On_Gre='\033[42m';    On_IGre='\033[0;102m';
Yel='\033[0;33m';     BYel='\033[1;33m';    UYel='\033[4;33m';    IYel='\033[0;93m';    BIYel='\033[1;93m';   On_Yel='\033[43m';    On_IYel='\033[0;103m';
Blu='\033[0;34m';     BBlu='\033[1;34m';    UBlu='\033[4;34m';    IBlu='\033[0;94m';    BIBlu='\033[1;94m';   On_Blu='\033[44m';    On_IBlu='\033[0;104m';
Pur='\033[0;35m';     BPur='\033[1;35m';    UPur='\033[4;35m';    IPur='\033[0;95m';    BIPur='\033[1;95m';   On_Pur='\033[45m';    On_IPur='\033[0;105m';
Cya='\033[0;36m';     BCya='\033[1;36m';    UCya='\033[4;36m';    ICya='\033[0;96m';    BICya='\033[1;96m';   On_Cya='\033[46m';    On_ICya='\033[0;106m';
Whi='\033[0;37m';     BWhi='\033[1;37m';    UWhi='\033[4;37m';    IWhi='\033[0;97m';    BIWhi='\033[1;97m';   On_Whi='\033[47m';    On_IWhi='\033[0;107m';

_BASEC=$IWhi

_txtwrap() {
  local text="$*"
  local wrapped_text

  wrapped_text="${text//${_NC}/${_NC}${_BASEC}}"

  echo -en "${_BASEC}${wrapped_text}${_NC}"
}

pecho() {
    echo -e $(_txtwrap "$*")
}

pprintf() {
    echo -en $(_txtwrap "$*")
}


function _date() {
  DATE=date
  if command -v gdate &>/dev/null; then
    DATE=gdate
  fi
  $DATE "+%Y-%m-%d %T.%3N"
}

function _logprint() {
  echo -en "${2}[${1}]${_NC} ${BWhi}$(_date)${_NC}${2}S${_NC}${BWhi}:${_NC} ${_BASEC}"
  pprintf "${@:3}"
  echo -e "${_NC}"
}

function info() {
  _logprint INFO "$BBlu" "$@"
}

function debug() {
  _logprint DEBUG "$BPur" "$@"
}

function error() {
  _logprint ERROR "$BRed" "$@"
}

function warn() {
  _logprint WARN "$BYel" "$@"
}

function fatal() {
  step "Script execution stopped due to a fatal error"
  _logprint FATAL "$BRed" "${@:2}"
  exit $1
}

function step() {
  echo -en "${IWhi}-----${_NC} ${BIWhi}"
  echo -n "$@"
  echo -e "${_NC} ${IWhi}-----${_NC}"
}

is_dirty() {
  pushd "${REPO_ROOT_DIR}" &>/dev/null
  if [[ -n $(git status -s) ]]; then
    popd &>/dev/null
    return 0
  fi
  popd &>/dev/null
  return 1
}

utils_version() {
  local version
  pushd "${REPO_ROOT_DIR}" &>/dev/null
  version=$(git rev-parse --short HEAD)
  if [[ -n $(git status -s) ]]; then
    version="${version}-dirty"
  fi
  tag=$(git describe --tags --exact-match 2>/dev/null || true)
  if [[ -n "${tag}" ]]; then
    version="v${tag} (${version})"
  else
    commit_date=$(git show -s --format=%cd --date=format:%y.%m.%d HEAD)
    commit_id=$(printf "%03d" $(git rev-list --count HEAD))
    version="v${commit_date}-${commit_id} (${version})"
  fi
  echo "${version}"
  popd &>/dev/null
}

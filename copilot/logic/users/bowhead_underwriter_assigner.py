from flask import current_app
from infrastructure_common.logging import get_logger
from sqlalchemy import desc, func
from sqlalchemy.orm import joinedload, load_only
from static_common.enums.organization import OrganizationGroups
from static_common.enums.underwriters import SubmissionUserSource
from static_common.models.submission_events import UnderwriterChange
import redis_lock

from copilot.logic.users.underwriter_assigner import (
    BaseUnderwriterAssigner,
    UnderwriterAssignerConfig,
)
from copilot.models import BrokerageEmployee, Submission, User, db
from copilot.models.bowhead_uw_mappings import BowheadUWMapping
from copilot.models.emails import Email
from copilot.models.reports import SubmissionUser
from copilot.models.types import PermissionType
from copilot.models.uw_assignment_logs import UwAssignmentLog

logger = get_logger()


class BowheadUnderwriterAssigner(BaseUnderwriterAssigner):
    _default_config = UnderwriterAssignerConfig(delete_other_assigned=False, already_assigned_error=False)

    ORG_GROUP_TO_USER_GROUP = {
        OrganizationGroups.BOWHEAD_PRIMARY.value: "Primary Casualty",
        OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value: "Environmental",
        OrganizationGroups.BOWHEAD_XS.value: "Excess Casualty",
    }

    SOURCES_PRIORITY = {
        SubmissionUserSource.SYNC: 4,
        SubmissionUserSource.EMAIL: 3,
        SubmissionUserSource.MANUAL: 2,
        SubmissionUserSource.RECOMMENDATIONS: 2,
        SubmissionUserSource.AUTO: 1,
    }

    DEFAULT_PRIORITY = 0

    ROUND_ROBIN_UW_EMAILS = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]
    ENVIRONMENTAL_INBOX = "<EMAIL>"

    DEFAULT_PRIMARY_UW_ID = 7880

    def auto_assign_underwriters(
        self, submission: Submission, config: UnderwriterAssignerConfig | None = None
    ) -> list[SubmissionUser]:
        config = UnderwriterAssignerConfig.determine_config(config, self._default_config)
        log = logger.bind(submission_id=submission.id, organization_group=submission.organization_group)
        uws = set()
        org_group = submission.organization_group

        if not org_group:
            return []

        if org_group == OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value:
            # For Environmental, we check if there's a direct sender. If not, we do a round-robin assignment
            # if UW does not exist.
            uw = self._get_uw_from_direct_sender(submission, log)
            if uw:
                log.info("Found UW using direct sender", uw=uw)
                uws.add(uw.id)
                return self.assign_underwriters(list(uws), submission, config=config, source=SubmissionUserSource.AUTO)
            if submission.assigned_underwriters:
                log.info("UW already exists, skipping round robin")
                return []
            with redis_lock.Lock(current_app.locks_client, name="bowhead_env_round_robin", expire=5, auto_renewal=True):
                round_robin_uw = self._get_next_round_robin_uw_id(submission, log)
                if round_robin_uw:
                    log.info("Found round robin UW", uw=round_robin_uw)
                    uws.add(round_robin_uw)

        else:
            # For Primary and XS, use mapping in the file.
            # For <NAME_EMAIL> as a fallback.
            mappings = self._get_broker_mapping_by_employee(org_group, submission.broker, log)
            if mappings:
                log.info("Bowhead: resolving UW through broker mapping")
            if not mappings:
                mappings = self._get_broker_mapping_by_employee(org_group, submission.brokerage_contact, log)
                if mappings:
                    log.info("Bowhead: resolving UW through cc mapping")

            if not mappings and org_group == OrganizationGroups.BOWHEAD_PRIMARY.value:
                log.info("Bowhead: resolving UW through default primary")
                uws.add(self.DEFAULT_PRIMARY_UW_ID)
            if mappings:
                uws.update({mapping.user_id for mapping in mappings})

        if not uws:
            return []
        return self.assign_underwriters(list(uws), submission, config=config, source=SubmissionUserSource.AUTO)

    def _get_uw_from_direct_sender(self, submission: Submission, log) -> User:
        correspondence_id = submission.report.correspondence_id
        all_emails = (
            db.session.query(Email)
            .options(load_only(Email.email_to, Email.email_from))
            .filter(Email.correspondence_id == correspondence_id)
            .all()
        )
        for email in all_emails:
            if not email.email_from or not email.email_to:
                continue
            uw_sender = next((uw for uw in self.ROUND_ROBIN_UW_EMAILS if uw in email.email_from.lower()), None)
            if uw_sender and self.ENVIRONMENTAL_INBOX in email.email_to.lower():
                log.info("Found UW using direct sender", uw=uw_sender)
                return db.session.query(User).filter(func.lower(User.email) == uw_sender).first()

    def _get_broker_mapping_by_employee(
        self, org_group: str, employee: BrokerageEmployee | None, log
    ) -> list[BowheadUWMapping]:
        if not employee:
            return []

        log.info("Looking for broker mapping based on id", employee_id=employee.id)
        q = db.session.query(BowheadUWMapping).filter(BowheadUWMapping.broker_id == employee.id)
        q = q.filter(BowheadUWMapping.org_group == org_group)
        broker_mappings_by_broker_id = q.all()
        if broker_mappings_by_broker_id:
            return broker_mappings_by_broker_id

        if employee.all_emails:
            log.info("Looking for broker mapping based on email", emails=employee.all_emails)
            q = db.session.query(BowheadUWMapping).filter(BowheadUWMapping.org_group == org_group)
            q = q.filter(BowheadUWMapping.broker_email.in_(employee.all_emails))
            broker_mappings_by_email = q.all()
            if broker_mappings_by_email:
                return broker_mappings_by_email

        log.info("No broker mapping found by employee")
        return []

    def _get_next_round_robin_uw_id(self, submission: Submission, log) -> int | None:
        query = db.session.query(UwAssignmentLog).filter(UwAssignmentLog.organization_id == submission.organization_id)
        query = query.filter(UwAssignmentLog.organization_org_group == submission.organization_group)
        last_assignment = query.order_by(desc(UwAssignmentLog.sequence_number)).first()

        if not last_assignment:
            uw_email = self.ROUND_ROBIN_UW_EMAILS[0]
        else:
            if last_assignment.assigned_underwriter_email in self.ROUND_ROBIN_UW_EMAILS:
                last_uw_index = self.ROUND_ROBIN_UW_EMAILS.index(last_assignment.assigned_underwriter_email)
            else:
                log.error("UW not found in rr, defaulting to first", uw=last_assignment.assigned_underwriter_email)
                last_uw_index = 0
            uw_email = self.ROUND_ROBIN_UW_EMAILS[(last_uw_index + 1) % len(self.ROUND_ROBIN_UW_EMAILS)]

        new_log = UwAssignmentLog(
            report_id=submission.report_id,
            assigned_underwriter_email=uw_email,
            organization_id=submission.organization_id,
            organization_org_group=submission.organization_group,
        )
        db.session.add(new_log)
        db.session.commit()
        return db.session.query(User.id).filter(func.lower(User.email) == uw_email).scalar()

    def assign_underwriters(
        self,
        requested_user_ids: list[int],
        submission: Submission,
        sharing_permission: PermissionType | None = None,
        config: UnderwriterAssignerConfig | None = None,
        source: SubmissionUserSource | None = SubmissionUserSource.AUTO,
        change_log: list[UnderwriterChange] | None = None,
    ) -> list[SubmissionUser]:
        config = UnderwriterAssignerConfig.determine_config(config, self._default_config)

        change_log = change_log or []
        log = logger.bind(submission_id=submission.id, requested_user_ids=requested_user_ids, source=source)

        current_priority = self.DEFAULT_PRIORITY
        if source not in self.SOURCES_PRIORITY:
            log.error("Submission user source is unknown, defaulting to lowest priority")
        else:
            current_priority = self.SOURCES_PRIORITY[source]

        # Check that user group matches the org group
        user_ids_to_assign = set(requested_user_ids)
        if submission.organization_group is not None:
            expected_user_group = self.ORG_GROUP_TO_USER_GROUP[submission.organization_group]
            users_q = db.session.query(User).filter(User.id.in_(requested_user_ids)).options(joinedload(User.groups))
            users = users_q.all()
            for user in users:
                if not any(group.name == expected_user_group for group in user.groups):
                    log.info("User group mismatch", user_id=user.id, expected_user_group=expected_user_group)
                    user_ids_to_assign.remove(user.id)
        user_ids_to_assign = list(user_ids_to_assign)
        if not user_ids_to_assign:
            log.warning("No valid users to assign")
            return []

        uws_to_delete = []
        for existing_source, priority in self.SOURCES_PRIORITY.items():
            for su in submission.assigned_underwriters:
                if su.source != existing_source or priority == current_priority or su.user_id in user_ids_to_assign:
                    continue

                if priority > current_priority:
                    log.warning("Higher priority UW already assigned, skipping", existing_source=existing_source)
                    return []
                else:
                    uws_to_delete.append(su)

        if uws_to_delete:
            change_log.extend(self.remove_uws(uws_to_delete, submission))

        return super().assign_underwriters(
            user_ids_to_assign, submission, sharing_permission, config, source, change_log
        )

from collections import defaultdict

from common.clients.zippopotam_client import ZippopotamClient
from common.logic.entity_resolution.entity import get_physical_premises
from infrastructure_common.logging import get_logger
from static_common.enums.brokerage_employee_roles import BrokerageEmployeeRoles
from static_common.enums.enum import StrEnum
from static_common.enums.organization import ExistingOrganizations
import flask

from copilot.constants import ALLY_AUTO_BROKERAGE_NAME
from copilot.logic.agencies.base_agent_assigner import BaseAgentAssigner
from copilot.logic.dao.brokerage_employee_dao import BrokerageEmployeeDAO
from copilot.models import Submission, db
from copilot.models.brokerages_v2 import Brokerage, BrokerageEmployee
from copilot.models.types import BrokerageEmployeeSource

logger = get_logger()

# Ally auto brokers
ADAM_HUCKINGS = "<EMAIL>"
ADAM_TATE = "<EMAIL>"
ANDREW_FILIPS = "<EMAIL>"
BENJAMIN_TORRANCE = "<EMAIL>"
BRENNAN_OVERWAY = "<EMAIL>"
BRIAN_JUBA = "<EMAIL>"
BRIAN_SPITZMUELLER = "<EMAIL>"
BRYAN_ZIOLKOWSKI = "<EMAIL>"
DAVID_LOPES = "<EMAIL>"
DOUG_DARLING = "<EMAIL>"
GARY_FOLTZ = "<EMAIL>"
GUY_JONES = "<EMAIL>"
JANIS_WILKS = "<EMAIL>"
JILLIAN_OSTROWSKI = "<EMAIL>"
KAREY_GALLAGHER = "<EMAIL>"
KENT_HARVEY = "<EMAIL>"
LINDSEY_GREENFELDER = "<EMAIL>"
MASON_MILLER = "<EMAIL>"
NANCY_ORENSTEIN = "<EMAIL>"
TANNER_MAYBERRY = "<EMAIL>"
THOMAS_BACIGALUPO = "<EMAIL>"
WALTER_MORRISETTE = "<EMAIL>"
ZACHARY_NOLEN = "<EMAIL>"


class USStates(StrEnum):
    AK = "AK"
    AL = "AL"
    AR = "AR"
    AZ = "AZ"
    CA = "CA"
    CO = "CO"
    CT = "CT"
    DE = "DE"
    FL = "FL"
    GA = "GA"
    HI = "HI"
    IA = "IA"
    ID = "ID"
    IL = "IL"
    IN = "IN"
    KS = "KS"
    KY = "KY"
    LA = "LA"
    MA = "MA"
    MD = "MD"
    ME = "ME"
    MI = "MI"
    MN = "MN"
    MO = "MO"
    MS = "MS"
    MT = "MT"
    NC = "NC"
    ND = "ND"
    NE = "NE"
    NH = "NH"
    NJ = "NJ"
    NM = "NM"
    NV = "NV"
    NY = "NY"
    OH = "OH"
    OK = "OK"
    OR = "OR"
    PA = "PA"
    RI = "RI"
    SC = "SC"
    SD = "SD"
    TN = "TN"
    TX = "TX"
    UT = "UT"
    VA = "VA"
    VT = "VT"
    WA = "WA"
    WI = "WI"
    WV = "WV"
    WY = "WY"


class USCounties(StrEnum):
    ACCOMACK = "ACCOMACK"
    ADAMS = "ADAMS"
    ALAMEDA = "ALAMEDA"
    ALBEMARLE = "ALBEMARLE"
    ALEXANDER = "ALEXANDER"
    ALEXANDRIA_CITY = "ALEXANDRIA CITY"
    ALLEGHENY = "ALLEGHENY"
    AMADOR = "AMADOR"
    AMELIA = "AMELIA"
    AMHERST = "AMHERST"
    ANDERSON = "ANDERSON"
    ANDREWS = "ANDREWS"
    ANGELINA = "ANGELINA"
    ANTELOPE = "ANTELOPE"
    APPOMATTOX = "APPOMATTOX"
    ARANSAS = "ARANSAS"
    ARCHER = "ARCHER"
    ARLINGTON = "ARLINGTON"
    ARMSTRONG = "ARMSTRONG"
    ATASCOSA = "ATASCOSA"
    AUGUSTA = "AUGUSTA"
    AUSTIN = "AUSTIN"
    BANDERA = "BANDERA"
    BARBOUR = "BARBOUR"
    BARNES = "BARNES"
    BASTROP = "BASTROP"
    BAYLOR = "BAYLOR"
    BEADLE = "BEADLE"
    BEAVER = "BEAVER"
    BEDFORD = "BEDFORD"
    BEE = "BEE"
    BELL = "BELL"
    BENNETT = "BENNETT"
    BERKELEY = "BERKELEY"
    BERKS = "BERKS"
    BEXAR = "BEXAR"
    BLAIR = "BLAIR"
    BOND = "BOND"
    BON_HOMME = "BON HOMME"
    BOONE = "BOONE"
    BOSQUE = "BOSQUE"
    BOTETOURT = "BOTETOURT"
    BOTTINEAU = "BOTTINEAU"
    BOWIE = "BOWIE"
    BOWMAN = "BOWMAN"
    BRADFORD = "BRADFORD"
    BRAXTON = "BRAXTON"
    BRAZORIA = "BRAZORIA"
    BRAZOS = "BRAZOS"
    BREWSTER = "BREWSTER"
    BRISTOL = "BRISTOL"
    BROOKE = "BROOKE"
    BROOKINGS = "BROOKINGS"
    BROWN = "BROWN"
    BRULE = "BRULE"
    BRUNSWICK = "BRUNSWICK"
    BUCHANAN = "BUCHANAN"
    BUCKINGHAM = "BUCKINGHAM"
    BUCKS = "BUCKS"
    BUENA_VISTA_CITY = "BUENA VISTA CITY"
    BUFFALO = "BUFFALO"
    BUREAU = "BUREAU"
    BURLESON = "BURLESON"
    BURLIEGH = "BURLIEGH"
    BURNET = "BURNET"
    BURT = "BURT"
    BUTLER = "BUTLER"
    BUTTE = "BUTTE"
    CABELL = "CABELL"
    CALAVERAS = "CALAVERAS"
    CALDWELL = "CALDWELL"
    CALHOUN = "CALHOUN"
    CALLAHAN = "CALLAHAN"
    CAMBRIA = "CAMBRIA"
    CAMERON = "CAMERON"
    CAMP = "CAMP"
    CAMPBELL = "CAMPBELL"
    CARBON = "CARBON"
    CARROLL = "CARROLL"
    CASS = "CASS"
    CAVALIER = "CAVALIER"
    CEDAR = "CEDAR"
    CENTRE = "CENTRE"
    CHAMBERS = "CHAMBERS"
    CHAMPAIGN = "CHAMPAIGN"
    CHARLES_MIX = "CHARLES MIX"
    CHARLOTTE = "CHARLOTTE"
    CHARLOTTESVILLE_CITY = "CHARLOTTESVILLE CITY"
    CHASE = "CHASE"
    CHEROKEE = "CHEROKEE"
    CHERRY = "CHERRY"
    CHESAPEAKE_CITY = "CHESAPEAKE CITY"
    CHESTER = "CHESTER"
    CHESTERFIELD = "CHESTERFIELD"
    CHEYENNE = "CHEYENNE"
    CHILDRESS = "CHILDRESS"
    CHRISTIAN = "CHRISTIAN"
    CLARION = "CLARION"
    CLARK = "CLARK"
    CLAY = "CLAY"
    CLEARFIELD = "CLEARFIELD"
    CLINTON = "CLINTON"
    CODINGTON = "CODINGTON"
    COKE = "COKE"
    COLEMAN = "COLEMAN"
    COLES = "COLES"
    COLFAX = "COLFAX"
    COLLIN = "COLLIN"
    COLONIAL_HEIGHTS_CITY = "COLONIAL HEIGHTS CITY"
    COLORADO = "COLORADO"
    COLUMBIA = "COLUMBIA"
    COLUSA = "COLUSA"
    COMAL = "COMAL"
    COMANCHE = "COMANCHE"
    CONCHO = "CONCHO"
    CONTRA_COSTA = "CONTRA_COSTA"
    COOK = "COOK"
    COOKE = "COOKE"
    CORYELL = "CORYELL"
    COTTLE = "COTTLE"
    COVINGTON_CITY = "COVINGTON CITY"
    CRAIG = "CRAIG"
    CRANE = "CRANE"
    CRAWFORD = "CRAWFORD"
    CULPEPER = "CULPEPER"
    CUMBERLAND = "CUMBERLAND"
    CUMING = "CUMING"
    CUSTER = "CUSTER"
    DALLAM = "DALLAM"
    DALLAS = "DALLAS"
    DANVILLE_CITY = "DANVILLE CITY"
    DAUPHIN = "DAUPHIN"
    DAVISON = "DAVISON"
    DAWES = "DAWES"
    DAWSON = "DAWSON"
    DAY = "DAY"
    DEAF_SMITH = "DEAF SMITH"
    DEKALB = "DEKALB"
    DELAWARE = "DELAWARE"
    DELTA = "DELTA"
    DEL_NORTE = "DEL_NORTE"
    DENTON = "DENTON"
    DEUEL = "DEUEL"
    DEWITT = "DEWITT"
    DE_KALB = "DE KALB"
    DE_WITT = "DE WITT"
    DICKENSON = "DICKENSON"
    DICKEY = "DICKEY"
    DIMMIT = "DIMMIT"
    DISTRICT_OF_COLUMBIA = "DISTRICT OF COLUMBIA"
    DODDRIDGE = "DODDRIDGE"
    DODGE = "DODGE"
    DONLEY = "DONLEY"
    DOUGLAS = "DOUGLAS"
    DUNN = "DUNN"
    DUPAGE = "DUPAGE"
    DUVAL = "DUVAL"
    DU_PAGE = "DU PAGE"
    EASTLAND = "EASTLAND"
    ECTOR = "ECTOR"
    EDDY = "EDDY"
    EDGAR = "EDGAR"
    EDMUNDS = "EDMUNDS"
    EDWARDS = "EDWARDS"
    EFFINGHAM = "EFFINGHAM"
    ELK = "ELK"
    ELLIS = "ELLIS"
    EL_DORADO = "EL_DORADO"
    EL_PASO = "EL PASO"
    EMPORIA_CITY = "EMPORIA CITY"
    ERATH = "ERATH"
    ERIE = "ERIE"
    ESSEX = "ESSEX"
    FAIRFAX = "FAIRFAX"
    FALLS = "FALLS"
    FALLS_CHURCH_CITY = "FALLS CHURCH CITY"
    FALL_RIVER = "FALL RIVER"
    FANNIN = "FANNIN"
    FAUQUIER = "FAUQUIER"
    FAYETTE = "FAYETTE"
    FILLMORE = "FILLMORE"
    FLOYD = "FLOYD"
    FLUVANNA = "FLUVANNA"
    FORD = "FORD"
    FORT_BEND = "FORT BEND"
    FOSTER = "FOSTER"
    FRANKLIN = "FRANKLIN"
    FREDERICK = "FREDERICK"
    FREDERICKSBURG_CITY = "FREDERICKSBURG CITY"
    FREESTONE = "FREESTONE"
    FRESNO = "FRESNO"
    FRIO = "FRIO"
    FULTON = "FULTON"
    FURNAS = "FURNAS"
    GAGE = "GAGE"
    GALAX_CITY = "GALAX CITY"
    GALVESTON = "GALVESTON"
    GILES = "GILES"
    GILLESPIE = "GILLESPIE"
    GLENN = "GLENN"
    GLOUCESTER = "GLOUCESTER"
    GOLIAD = "GOLIAD"
    GONZALES = "GONZALES"
    GOOCHLAND = "GOOCHLAND"
    GRAND_FORKS = "GRAND FORKS"
    GRANT = "GRANT"
    GRAY = "GRAY"
    GRAYSON = "GRAYSON"
    GREENBRIER = "GREENBRIER"
    GREENE = "GREENE"
    GREGG = "GREGG"
    GRIGGS = "GRIGGS"
    GRIMES = "GRIMES"
    GRUNDY = "GRUNDY"
    GUADALUPE = "GUADALUPE"
    HAAKON = "HAAKON"
    HALE = "HALE"
    HALIFAX = "HALIFAX"
    HALL = "HALL"
    HAMILTON = "HAMILTON"
    HAMPSHIRE = "HAMPSHIRE"
    HAMPTON_CITY = "HAMPTON CITY"
    HANCOCK = "HANCOCK"
    HAND = "HAND"
    HANOVER = "HANOVER"
    HANSFORD = "HANSFORD"
    HARDIN = "HARDIN"
    HARDY = "HARDY"
    HARLAN = "HARLAN"
    HARRIS = "HARRIS"
    HARRISON = "HARRISON"
    HARRISONBURG_CITY = "HARRISONBURG CITY"
    HASKELL = "HASKELL"
    HAYS = "HAYS"
    HENDERSON = "HENDERSON"
    HENRICO = "HENRICO"
    HENRY = "HENRY"
    HETTINGER = "HETTINGER"
    HIDAL = "HIDAL"
    HIDALGO = "HIDALGO"
    HILL = "HILL"
    HOCKLEY = "HOCKLEY"
    HOLT = "HOLT"
    HOOD = "HOOD"
    HOPEWELL_CITY = "HOPEWELL CITY"
    HOPKINS = "HOPKINS"
    HOUSTON = "HOUSTON"
    HOWARD = "HOWARD"
    HUGHES = "HUGHES"
    HUMBOLDT = "HUMBOLDT"
    HUNT = "HUNT"
    HUNTINGDON = "HUNTINGDON"
    HUTCHINSON = "HUTCHINSON"
    IMPERIAL = "IMPERIAL"
    INDIANA = "INDIANA"
    INYO = "INYO"
    IROQUOIS = "IROQUOIS"
    ISLE_OF_WIGHT = "ISLE OF WIGHT"
    JACK = "JACK"
    JACKSON = "JACKSON"
    JAMES_CITY = "JAMES CITY"
    JASPER = "JASPER"
    JEFFERSON = "JEFFERSON"
    JERAULD = "JERAULD"
    JERSEY = "JERSEY"
    JIM_HOGG = "JIM HOGG"
    JIM_WELLS = "JIM WELLS"
    JOHNSON = "JOHNSON"
    JONES = "JONES"
    JO_DAVIESS = "JO DAVIESS"
    JUNIATA = "JUNIATA"
    KANAWHA = "KANAWHA"
    KANE = "KANE"
    KANKAKEE = "KANKAKEE"
    KARNES = "KARNES"
    KAUFMAN = "KAUFMAN"
    KEITH = "KEITH"
    KENDALL = "KENDALL"
    KENT = "KENT"
    KERN = "KERN"
    KERR = "KERR"
    KIMBALL = "KIMBALL"
    KIMBLE = "KIMBLE"
    KINGS = "KINGS"
    KINGSBURY = "KINGSBURY"
    KING_AND_QUEEN = "KING AND QUEEN"
    KING_GEORGE = "KING GEORGE"
    KING_WILLIAM = "KING WILLIAM"
    KLEBERG = "KLEBERG"
    KNOX = "KNOX"
    LACKAWANNA = "LACKAWANNA"
    LAKE = "LAKE"
    LAMAR = "LAMAR"
    LAMB = "LAMB"
    LAMPASAS = "LAMPASAS"
    LANCASTER = "LANCASTER"
    LASSEN = "LASSEN"
    LAVACA = "LAVACA"
    LAWRENCE = "LAWRENCE"
    LA_SALLE = "LA SALLE"
    LEBANON = "LEBANON"
    LEE = "LEE"
    LEHIGH = "LEHIGH"
    LEON = "LEON"
    LEWIS = "LEWIS"
    LEXINGTON_CITY = "LEXINGTON CITY"
    LIBERTY = "LIBERTY"
    LIMESTONE = "LIMESTONE"
    LINCOLN = "LINCOLN"
    LIVE_OAK = "LIVE OAK"
    LIVINGSTON = "LIVINGSTON"
    LLANO = "LLANO"
    LOGAN = "LOGAN"
    LOS_ANGELES = "LOS_ANGELES"
    LOUDOUN = "LOUDOUN"
    LOUISA = "LOUISA"
    LUBBOCK = "LUBBOCK"
    LUZERNE = "LUZERNE"
    LYCOMING = "LYCOMING"
    LYMAN = "LYMAN"
    LYNCHBURG_CITY = "LYNCHBURG CITY"
    MACON = "MACON"
    MACOUPIN = "MACOUPIN"
    MADERA = "MADERA"
    MADISON = "MADISON"
    MANASSAS_CITY = "MANASSAS CITY"
    MANASSAS_PARK_CITY = "MANASSAS PARK CITY"
    MARIN = "MARIN"
    MARION = "MARION"
    MARSHALL = "MARSHALL"
    MARTIN = "MARTIN"
    MASON = "MASON"
    MASSAC = "MASSAC"
    MATAGORDA = "MATAGORDA"
    MATHEWS = "MATHEWS"
    MAVERICK = "MAVERICK"
    MCCOOK = "MCCOOK"
    MCCULLOCH = "MCCULLOCH"
    MCDONOUGH = "MCDONOUGH"
    MCDOWELL = "MCDOWELL"
    MCHENRY = "MCHENRY"
    MCINTOSH = "MCINTOSH"
    MCKEAN = "MCKEAN"
    MCKENZIE = "MCKENZIE"
    MCLEAN = "MCLEAN"
    MCLENNAN = "MCLENNAN"
    MEADE = "MEADE"
    MECKLENBURG = "MECKLENBURG"
    MEDINA = "MEDINA"
    MENARD = "MENARD"
    MENDOCINO = "MENDOCINO"
    MERCED = "MERCED"
    MERCER = "MERCER"
    MERRICK = "MERRICK"
    MIDDLESEX = "MIDDLESEX"
    MIDLAND = "MIDLAND"
    MIFFLIN = "MIFFLIN"
    MILAM = "MILAM"
    MILLS = "MILLS"
    MINERAL = "MINERAL"
    MINGO = "MINGO"
    MINNEHAHA = "MINNEHAHA"
    MINNIEHAHA = "MINNIEHAHA"
    MODOC = "MODOC"
    MONONGALIA = "MONONGALIA"
    MONROE = "MONROE"
    MONTAGUE = "MONTAGUE"
    MONTEREY = "MONTEREY"
    MONTGOMERY = "MONTGOMERY"
    MONTOUR = "MONTOUR"
    MOODY = "MOODY"
    MOORE = "MOORE"
    MORGAN = "MORGAN"
    MORRILL = "MORRILL"
    MORTON = "MORTON"
    MOULTRIE = "MOULTRIE"
    MOUNTRAIL = "MOUNTRAIL"
    Marion = "Marion"
    NACOGDOCHES = "NACOGDOCHES"
    NAPA = "NAPA"
    NAVARRO = "NAVARRO"
    NELSON = "NELSON"
    NEMAHA = "NEMAHA"
    NEVADA = "NEVADA"
    NEWPORT_NEWS_CITY = "NEWPORT NEWS CITY"
    NICHOLAS = "NICHOLAS"
    NOLAN = "NOLAN"
    NORFOLK_CITY = "NORFOLK CITY"
    NORTHAMPTON = "NORTHAMPTON"
    NORTHUMBERLAND = "NORTHUMBERLAND"
    NORTON_CITY = "NORTON CITY"
    NOTTOWAY = "NOTTOWAY"
    NUCKOLLS = "NUCKOLLS"
    NUECES = "NUECES"
    OCHILTREE = "OCHILTREE"
    OGLE = "OGLE"
    OHIO = "OHIO"
    ORANGE = "ORANGE"
    OTOE = "OTOE"
    PAGE = "PAGE"
    PALO_PINTO = "PALO PINTO"
    PANOLA = "PANOLA"
    PARKER = "PARKER"
    PATRICK = "PATRICK"
    PECOS = "PECOS"
    PEMBINA = "PEMBINA"
    PENDLETON = "PENDLETON"
    PENNINGTON = "PENNINGTON"
    PEORIA = "PEORIA"
    PERRY = "PERRY"
    PETERSBURG_CITY = "PETERSBURG CITY"
    PHELPS = "PHELPS"
    PHILADELPHIA = "PHILADELPHIA"
    PIATT = "PIATT"
    PIERCE = "PIERCE"
    PIKE = "PIKE"
    PITTSYLVANIA = "PITTSYLVANIA"
    PLACER = "PLACER"
    PLATTE = "PLATTE"
    PLEASANTS = "PLEASANTS"
    POCAHONTAS = "POCAHONTAS"
    POLK = "POLK"
    POQUOSON_CITY = "POQUOSON CITY"
    PORTSMOUTH_CITY = "PORTSMOUTH CITY"
    POTTER = "POTTER"
    POWHATAN = "POWHATAN"
    PRESIDIO = "PRESIDIO"
    PRESTON = "PRESTON"
    PRINCE_EDWARD = "PRINCE EDWARD"
    PRINCE_GEORGE = "PRINCE GEORGE"
    PRINCE_WILLIAM = "PRINCE WILLIAM"
    PROSPER = "PROSPER"
    PULASKI = "PULASKI"
    PUTNAM = "PUTNAM"
    RADFORD = "RADFORD"
    RALEIGH = "RALEIGH"
    RAMSEY = "RAMSEY"
    RANDALL = "RANDALL"
    RANDOLPH = "RANDOLPH"
    RANSOM = "RANSOM"
    REAGAN = "REAGAN"
    RED_RIVER = "RED RIVER"
    RED_WILLOW = "RED WILLOW"
    REEVES = "REEVES"
    REFUGIO = "REFUGIO"
    RICHARDSON = "RICHARDSON"
    RICHLAND = "RICHLAND"
    RICHMOND_CITY = "RICHMOND CITY"
    RITCHIE = "RITCHIE"
    RIVERSIDE = "RIVERSIDE"
    ROANE = "ROANE"
    ROANOKE = "ROANOKE"
    ROANOKE_CITY = "ROANOKE CITY"
    ROBERTS = "ROBERTS"
    ROBERTSON = "ROBERTSON"
    ROCKBRIDGE = "ROCKBRIDGE"
    ROCKINGHAM = "ROCKINGHAM"
    ROCKWALL = "ROCKWALL"
    ROCKWELL = "ROCKWELL"
    ROCK_ISLAND = "ROCK ISLAND"
    ROLETTE = "ROLETTE"
    RUNNELS = "RUNNELS"
    RUSK = "RUSK"
    RUSSELL = "RUSSELL"
    SACRAMENTO = "SACRAMENTO"
    SAINT_CLAIR = "SAINT CLAIR"
    SALEM = "SALEM"
    SALINE = "SALINE"
    SANGAMON = "SANGAMON"
    SANTA_BARBARA = "SANTA_BARBARA"
    SANTA_CLARA = "SANTA_CLARA"
    SANTA_CRUZ = "SANTA_CRUZ"
    SAN_AUGUSTINE = "SAN AUGUSTINE"
    SAN_BENITO = "SAN_BENITO"
    SAN_BERNARDINO = "SAN_BERNARDINO"
    SAN_DIEGO = "SAN_DIEGO"
    SAN_FRANCISCO = "SAN_FRANCISCO"
    SAN_JOAQUIN = "SAN_JOAQUIN"
    SAN_LUIS_OBISPO = "SAN_LUIS_OBISPO"
    SAN_MATEO = "SAN_MATEO"
    SAN_PATRICIO = "SAN PATRICIO"
    SAN_SABA = "SAN SABA"
    SARGENT = "SARGENT"
    SARPY = "SARPY"
    SAUNDERS = "SAUNDERS"
    SCHUYLKILL = "SCHUYLKILL"
    SCOTT = "SCOTT"
    SCOTTS_BLUFF = "SCOTTS BLUFF"
    SCURRY = "SCURRY"
    SEWARD = "SEWARD"
    SHASTA = "SHASTA"
    SHELBY = "SHELBY"
    SHENANDOAH = "SHENANDOAH"
    SHERMAN = "SHERMAN"
    SISKIYOU = "SISKIYOU"
    SMITH = "SMITH"
    SMYTH = "SMYTH"
    SNYDER = "SNYDER"
    SOLANO = "SOLANO"
    SOMERSET = "SOMERSET"
    SOMERVELL = "SOMERVELL"
    SONOMA = "SONOMA"
    SPINK = "SPINK"
    SPOTSYLVANIA = "SPOTSYLVANIA"
    STAFFORD = "STAFFORD"
    STANISLAUS = "STANISLAUS"
    STANLEY = "STANLEY"
    STARK = "STARK"
    STARR = "STARR"
    STATE = "STATE"
    STAUNTON_CITY = "STAUNTON CITY"
    STEELE = "STEELE"
    STEPHENS = "STEPHENS"
    STEPHENSON = "STEPHENSON"
    STUTSMAN = "STUTSMAN"
    ST_CLAIR = "ST CLAIR"
    SUFFOLK_CITY = "SUFFOLK CITY"
    SULLY = "SULLY"
    SUSQUEHANNA = "SUSQUEHANNA"
    SUSSEX = "SUSSEX"
    SUTTER = "SUTTER"
    TARRANT = "TARRANT"
    TAYLOR = "TAYLOR"
    TAZEWELL = "TAZEWELL"
    TEHAMA = "TEHAMA"
    TERRY = "TERRY"
    THAYER = "THAYER"
    THURSTON = "THURSTON"
    TIOGA = "TIOGA"
    TITUS = "TITUS"
    TOM_GREEN = "TOM GREEN"
    TRAILL = "TRAILL"
    TRAVIS = "TRAVIS"
    TRIPP = "TRIPP"
    TUCKER = "TUCKER"
    TULARE = "TULARE"
    TUOLUMNE = "TUOLUMNE"
    TURNER = "TURNER"
    TYLER = "TYLER"
    UNION = "UNION"
    UPSHUR = "UPSHUR"
    USA = "USA"
    UVALDE = "UVALDE"
    VAL_VERDE = "VAL VERDE"
    VAN_ZANDT = "VAN ZANDT"
    VENANGO = "VENANGO"
    VENTURA = "VENTURA"
    VERMILION = "VERMILION"
    VICTORIA = "VICTORIA"
    VIRGINIA_BEACH_CITY = "VIRGINIA BEACH CITY"
    WABASH = "WABASH"
    WALKER = "WALKER"
    WALLER = "WALLER"
    WALSH = "WALSH"
    WALWORTH = "WALWORTH"
    WARD = "WARD"
    WARREN = "WARREN"
    WASHINGTON = "WASHINGTON"
    WAYNE = "WAYNE"
    WAYNESBORO_CITY = "WAYNESBORO CITY"
    WEBB = "WEBB"
    WELLS = "WELLS"
    WESTMORELAND = "WESTMORELAND"
    WETZEL = "WETZEL"
    WHARTON = "WHARTON"
    WHEELER = "WHEELER"
    WHITE = "WHITE"
    WHITESIDE = "WHITESIDE"
    WICHITA = "WICHITA"
    WILBARGER = "WILBARGER"
    WILL = "WILL"
    WILLACY = "WILLACY"
    WILLIAMS = "WILLIAMS"
    WILLIAMSON = "WILLIAMSON"
    WILSON = "WILSON"
    WINCHESTER_CITY = "WINCHESTER CITY"
    WINNEBAGO = "WINNEBAGO"
    WISE = "WISE"
    WOOD = "WOOD"
    WOODFORD = "WOODFORD"
    WYOMING = "WYOMING"
    WYTHE = "WYTHE"
    YANKTON = "YANKTON"
    YOAKUM = "YOAKUM"
    YOLO = "YOLO"
    YORK = "YORK"
    YOUNG = "YOUNG"


ally_auto_brokers_mapper = {
    USStates.AK: defaultdict(lambda: GUY_JONES),
    USStates.AL: defaultdict(lambda: JANIS_WILKS),
    USStates.AR: defaultdict(lambda: KENT_HARVEY),
    USStates.AZ: defaultdict(lambda: TANNER_MAYBERRY),
    USStates.CA: defaultdict(
        lambda: GUY_JONES,
        {
            USCounties.ALAMEDA: GUY_JONES,
            USCounties.AMADOR: GUY_JONES,
            USCounties.BUTTE: GUY_JONES,
            USCounties.CALAVERAS: GUY_JONES,
            USCounties.COLUSA: GUY_JONES,
            USCounties.CONTRA_COSTA: GUY_JONES,
            USCounties.DEL_NORTE: GUY_JONES,
            USCounties.EL_DORADO: GUY_JONES,
            USCounties.FRESNO: GUY_JONES,
            USCounties.GLENN: GUY_JONES,
            USCounties.HUMBOLDT: GUY_JONES,
            USCounties.IMPERIAL: TANNER_MAYBERRY,
            USCounties.INYO: GUY_JONES,
            USCounties.KERN: MASON_MILLER,
            USCounties.KINGS: GUY_JONES,
            USCounties.LAKE: GUY_JONES,
            USCounties.LASSEN: GUY_JONES,
            USCounties.LOS_ANGELES: MASON_MILLER,
            USCounties.MADERA: GUY_JONES,
            USCounties.MARIN: GUY_JONES,
            USCounties.MENDOCINO: GUY_JONES,
            USCounties.MERCED: GUY_JONES,
            USCounties.MODOC: GUY_JONES,
            USCounties.MONTEREY: GUY_JONES,
            USCounties.NAPA: GUY_JONES,
            USCounties.NEVADA: GUY_JONES,
            USCounties.ORANGE: TANNER_MAYBERRY,
            USCounties.PLACER: GUY_JONES,
            USCounties.RIVERSIDE: TANNER_MAYBERRY,
            USCounties.SACRAMENTO: GUY_JONES,
            USCounties.SANTA_BARBARA: MASON_MILLER,
            USCounties.SANTA_CLARA: GUY_JONES,
            USCounties.SANTA_CRUZ: GUY_JONES,
            USCounties.SAN_BENITO: GUY_JONES,
            USCounties.SAN_BERNARDINO: MASON_MILLER,
            USCounties.SAN_DIEGO: TANNER_MAYBERRY,
            USCounties.SAN_FRANCISCO: GUY_JONES,
            USCounties.SAN_JOAQUIN: GUY_JONES,
            USCounties.SAN_LUIS_OBISPO: MASON_MILLER,
            USCounties.SAN_MATEO: GUY_JONES,
            USCounties.SHASTA: GUY_JONES,
            USCounties.SISKIYOU: GUY_JONES,
            USCounties.SOLANO: GUY_JONES,
            USCounties.SONOMA: GUY_JONES,
            USCounties.STANISLAUS: GUY_JONES,
            USCounties.SUTTER: GUY_JONES,
            USCounties.TEHAMA: GUY_JONES,
            USCounties.TULARE: GUY_JONES,
            USCounties.TUOLUMNE: GUY_JONES,
            USCounties.VENTURA: MASON_MILLER,
            USCounties.YOLO: GUY_JONES,
        },
    ),
    USStates.CO: defaultdict(lambda: KAREY_GALLAGHER),
    USStates.CT: defaultdict(lambda: ADAM_HUCKINGS),
    USStates.DE: defaultdict(lambda: JILLIAN_OSTROWSKI),
    USStates.FL: defaultdict(lambda: BRYAN_ZIOLKOWSKI),
    USStates.GA: defaultdict(lambda: KENT_HARVEY),
    USStates.HI: defaultdict(lambda: TANNER_MAYBERRY),
    USStates.IA: defaultdict(lambda: BRIAN_SPITZMUELLER),
    USStates.ID: defaultdict(lambda: GUY_JONES),
    USStates.IL: defaultdict(
        lambda: ZACHARY_NOLEN,
        {
            USCounties.ADAMS: ADAM_TATE,
            USCounties.ALEXANDER: ANDREW_FILIPS,
            USCounties.BOND: ADAM_TATE,
            USCounties.BOONE: ZACHARY_NOLEN,
            USCounties.BUREAU: ANDREW_FILIPS,
            USCounties.CALHOUN: ADAM_TATE,
            USCounties.CARROLL: ANDREW_FILIPS,
            USCounties.CASS: ANDREW_FILIPS,
            USCounties.CHAMPAIGN: ANDREW_FILIPS,
            USCounties.CHRISTIAN: ANDREW_FILIPS,
            USCounties.CLARK: ANDREW_FILIPS,
            USCounties.CLAY: ANDREW_FILIPS,
            USCounties.CLINTON: ADAM_TATE,
            USCounties.COLES: ANDREW_FILIPS,
            USCounties.COOK: ZACHARY_NOLEN,
            USCounties.CRAWFORD: ANDREW_FILIPS,
            USCounties.DEKALB: ZACHARY_NOLEN,
            USCounties.DEWITT: ANDREW_FILIPS,
            USCounties.DE_KALB: ZACHARY_NOLEN,
            USCounties.DOUGLAS: ANDREW_FILIPS,
            USCounties.DUPAGE: ZACHARY_NOLEN,
            USCounties.DU_PAGE: ZACHARY_NOLEN,
            USCounties.EDGAR: ANDREW_FILIPS,
            USCounties.EDWARDS: ANDREW_FILIPS,
            USCounties.EFFINGHAM: ANDREW_FILIPS,
            USCounties.FAYETTE: ADAM_TATE,
            USCounties.FORD: ANDREW_FILIPS,
            USCounties.FRANKLIN: ADAM_TATE,
            USCounties.FULTON: ANDREW_FILIPS,
            USCounties.GREENE: ADAM_TATE,
            USCounties.GRUNDY: ANDREW_FILIPS,
            USCounties.HAMILTON: ANDREW_FILIPS,
            USCounties.HANCOCK: ANDREW_FILIPS,
            USCounties.HENRY: ANDREW_FILIPS,
            USCounties.IROQUOIS: ANDREW_FILIPS,
            USCounties.JACKSON: ADAM_TATE,
            USCounties.JASPER: ANDREW_FILIPS,
            USCounties.JEFFERSON: ADAM_TATE,
            USCounties.JERSEY: ADAM_TATE,
            USCounties.JOHNSON: ANDREW_FILIPS,
            USCounties.JO_DAVIESS: ANDREW_FILIPS,
            USCounties.KANE: ZACHARY_NOLEN,
            USCounties.KANKAKEE: ANDREW_FILIPS,
            USCounties.KENDALL: ANDREW_FILIPS,
            USCounties.KNOX: ANDREW_FILIPS,
            USCounties.LAKE: ZACHARY_NOLEN,
            USCounties.LAWRENCE: ANDREW_FILIPS,
            USCounties.LA_SALLE: ANDREW_FILIPS,
            USCounties.LEE: ANDREW_FILIPS,
            USCounties.LIVINGSTON: ANDREW_FILIPS,
            USCounties.LOGAN: ANDREW_FILIPS,
            USCounties.MACON: ANDREW_FILIPS,
            USCounties.MACOUPIN: ADAM_TATE,
            USCounties.MADISON: ADAM_TATE,
            USCounties.MARION: ADAM_TATE,
            USCounties.MARSHALL: ANDREW_FILIPS,
            USCounties.MASON: ANDREW_FILIPS,
            USCounties.MASSAC: ANDREW_FILIPS,
            USCounties.MCDONOUGH: ANDREW_FILIPS,
            USCounties.MCHENRY: ZACHARY_NOLEN,
            USCounties.MCLEAN: ANDREW_FILIPS,
            USCounties.MENARD: ANDREW_FILIPS,
            USCounties.MERCER: ANDREW_FILIPS,
            USCounties.MONROE: ADAM_TATE,
            USCounties.MONTGOMERY: ADAM_TATE,
            USCounties.MORGAN: ANDREW_FILIPS,
            USCounties.MOULTRIE: ANDREW_FILIPS,
            USCounties.OGLE: ANDREW_FILIPS,
            USCounties.PEORIA: ANDREW_FILIPS,
            USCounties.PERRY: ADAM_TATE,
            USCounties.PIATT: ANDREW_FILIPS,
            USCounties.PIKE: ADAM_TATE,
            USCounties.PULASKI: ANDREW_FILIPS,
            USCounties.PUTNAM: ANDREW_FILIPS,
            USCounties.RANDOLPH: ADAM_TATE,
            USCounties.RICHLAND: ANDREW_FILIPS,
            USCounties.ROCK_ISLAND: ANDREW_FILIPS,
            USCounties.SAINT_CLAIR: ADAM_TATE,
            USCounties.SALINE: ANDREW_FILIPS,
            USCounties.SANGAMON: ANDREW_FILIPS,
            USCounties.SCOTT: ADAM_TATE,
            USCounties.SHELBY: ANDREW_FILIPS,
            USCounties.STEPHENSON: ANDREW_FILIPS,
            USCounties.ST_CLAIR: ADAM_TATE,
            USCounties.TAZEWELL: ANDREW_FILIPS,
            USCounties.UNION: ANDREW_FILIPS,
            USCounties.VERMILION: ANDREW_FILIPS,
            USCounties.WABASH: ANDREW_FILIPS,
            USCounties.WARREN: ANDREW_FILIPS,
            USCounties.WASHINGTON: ADAM_TATE,
            USCounties.WAYNE: ANDREW_FILIPS,
            USCounties.WHITE: ANDREW_FILIPS,
            USCounties.WHITESIDE: ANDREW_FILIPS,
            USCounties.WILL: ANDREW_FILIPS,
            USCounties.WINNEBAGO: ZACHARY_NOLEN,
            USCounties.WOODFORD: ANDREW_FILIPS,
        },
    ),
    USStates.IN: defaultdict(lambda: ANDREW_FILIPS),
    USStates.KS: defaultdict(lambda: ADAM_TATE),
    USStates.KY: defaultdict(lambda: JANIS_WILKS),
    USStates.LA: defaultdict(lambda: KAREY_GALLAGHER),
    USStates.MA: defaultdict(lambda: BENJAMIN_TORRANCE),
    USStates.MD: defaultdict(lambda: JILLIAN_OSTROWSKI),
    USStates.ME: defaultdict(lambda: BENJAMIN_TORRANCE),
    USStates.MI: defaultdict(lambda: LINDSEY_GREENFELDER),
    USStates.MN: defaultdict(lambda: BRIAN_SPITZMUELLER),
    USStates.MO: defaultdict(lambda: ADAM_TATE),
    USStates.MS: defaultdict(lambda: KENT_HARVEY),
    USStates.MT: defaultdict(lambda: GUY_JONES),
    USStates.NC: defaultdict(lambda: BRENNAN_OVERWAY),
    USStates.ND: defaultdict(
        lambda: ZACHARY_NOLEN,
        {
            USCounties.ADAMS: TANNER_MAYBERRY,
            USCounties.BARNES: ZACHARY_NOLEN,
            USCounties.BOTTINEAU: ZACHARY_NOLEN,
            USCounties.BOWMAN: TANNER_MAYBERRY,
            USCounties.BURLIEGH: TANNER_MAYBERRY,
            USCounties.CASS: ZACHARY_NOLEN,
            USCounties.CAVALIER: ZACHARY_NOLEN,
            USCounties.DICKEY: ZACHARY_NOLEN,
            USCounties.DUNN: TANNER_MAYBERRY,
            USCounties.EDDY: ZACHARY_NOLEN,
            USCounties.FOSTER: ZACHARY_NOLEN,
            USCounties.GRAND_FORKS: ZACHARY_NOLEN,
            USCounties.GRIGGS: ZACHARY_NOLEN,
            USCounties.HETTINGER: TANNER_MAYBERRY,
            USCounties.LOGAN: ZACHARY_NOLEN,
            USCounties.MCINTOSH: ZACHARY_NOLEN,
            USCounties.MCKENZIE: TANNER_MAYBERRY,
            USCounties.MERCER: TANNER_MAYBERRY,
            USCounties.MORTON: TANNER_MAYBERRY,
            USCounties.MOUNTRAIL: TANNER_MAYBERRY,
            USCounties.PEMBINA: ZACHARY_NOLEN,
            USCounties.PIERCE: ZACHARY_NOLEN,
            USCounties.RAMSEY: ZACHARY_NOLEN,
            USCounties.RANSOM: ZACHARY_NOLEN,
            USCounties.RICHLAND: ZACHARY_NOLEN,
            USCounties.ROLETTE: ZACHARY_NOLEN,
            USCounties.SARGENT: ZACHARY_NOLEN,
            USCounties.STARK: TANNER_MAYBERRY,
            USCounties.STEELE: ZACHARY_NOLEN,
            USCounties.STUTSMAN: ZACHARY_NOLEN,
            USCounties.TRAILL: ZACHARY_NOLEN,
            USCounties.WALSH: ZACHARY_NOLEN,
            USCounties.WARD: TANNER_MAYBERRY,
            USCounties.WELLS: ZACHARY_NOLEN,
            USCounties.WILLIAMS: TANNER_MAYBERRY,
        },
    ),
    USStates.NE: defaultdict(
        lambda: LINDSEY_GREENFELDER,
        {
            USCounties.ADAMS: LINDSEY_GREENFELDER,
            USCounties.ANTELOPE: LINDSEY_GREENFELDER,
            USCounties.BOONE: LINDSEY_GREENFELDER,
            USCounties.BROWN: LINDSEY_GREENFELDER,
            USCounties.BUFFALO: LINDSEY_GREENFELDER,
            USCounties.BURT: LINDSEY_GREENFELDER,
            USCounties.BUTLER: LINDSEY_GREENFELDER,
            USCounties.CASS: LINDSEY_GREENFELDER,
            USCounties.CEDAR: LINDSEY_GREENFELDER,
            USCounties.CHASE: LINDSEY_GREENFELDER,
            USCounties.CHERRY: LINDSEY_GREENFELDER,
            USCounties.CHEYENNE: KAREY_GALLAGHER,
            USCounties.CLAY: LINDSEY_GREENFELDER,
            USCounties.COLFAX: LINDSEY_GREENFELDER,
            USCounties.CUMING: LINDSEY_GREENFELDER,
            USCounties.CUSTER: LINDSEY_GREENFELDER,
            USCounties.DAWES: KAREY_GALLAGHER,
            USCounties.DAWSON: LINDSEY_GREENFELDER,
            USCounties.DODGE: LINDSEY_GREENFELDER,
            USCounties.DOUGLAS: LINDSEY_GREENFELDER,
            USCounties.FILLMORE: LINDSEY_GREENFELDER,
            USCounties.FURNAS: LINDSEY_GREENFELDER,
            USCounties.GAGE: LINDSEY_GREENFELDER,
            USCounties.HALL: LINDSEY_GREENFELDER,
            USCounties.HAMILTON: LINDSEY_GREENFELDER,
            USCounties.HARLAN: LINDSEY_GREENFELDER,
            USCounties.HOLT: LINDSEY_GREENFELDER,
            USCounties.HOWARD: LINDSEY_GREENFELDER,
            USCounties.JEFFERSON: LINDSEY_GREENFELDER,
            USCounties.JOHNSON: LINDSEY_GREENFELDER,
            USCounties.KEITH: LINDSEY_GREENFELDER,
            USCounties.KIMBALL: KAREY_GALLAGHER,
            USCounties.KNOX: LINDSEY_GREENFELDER,
            USCounties.LANCASTER: LINDSEY_GREENFELDER,
            USCounties.LINCOLN: LINDSEY_GREENFELDER,
            USCounties.MADISON: LINDSEY_GREENFELDER,
            USCounties.MERRICK: LINDSEY_GREENFELDER,
            USCounties.MORRILL: KAREY_GALLAGHER,
            USCounties.NEMAHA: LINDSEY_GREENFELDER,
            USCounties.NUCKOLLS: LINDSEY_GREENFELDER,
            USCounties.OTOE: LINDSEY_GREENFELDER,
            USCounties.PHELPS: LINDSEY_GREENFELDER,
            USCounties.PIERCE: LINDSEY_GREENFELDER,
            USCounties.PLATTE: LINDSEY_GREENFELDER,
            USCounties.POLK: LINDSEY_GREENFELDER,
            USCounties.RED_WILLOW: LINDSEY_GREENFELDER,
            USCounties.RICHARDSON: LINDSEY_GREENFELDER,
            USCounties.SALINE: LINDSEY_GREENFELDER,
            USCounties.SARPY: LINDSEY_GREENFELDER,
            USCounties.SAUNDERS: LINDSEY_GREENFELDER,
            USCounties.SCOTTS_BLUFF: KAREY_GALLAGHER,
            USCounties.SEWARD: LINDSEY_GREENFELDER,
            USCounties.SHERMAN: LINDSEY_GREENFELDER,
            USCounties.THAYER: LINDSEY_GREENFELDER,
            USCounties.THURSTON: LINDSEY_GREENFELDER,
            USCounties.WASHINGTON: LINDSEY_GREENFELDER,
            USCounties.WAYNE: LINDSEY_GREENFELDER,
            USCounties.YORK: LINDSEY_GREENFELDER,
        },
    ),
    USStates.NH: defaultdict(lambda: BENJAMIN_TORRANCE),
    USStates.NJ: defaultdict(lambda: THOMAS_BACIGALUPO),
    USStates.NM: defaultdict(lambda: TANNER_MAYBERRY),
    USStates.NV: defaultdict(lambda: MASON_MILLER),
    USStates.NY: defaultdict(lambda: DAVID_LOPES),
    USStates.OH: defaultdict(lambda: BRIAN_JUBA),
    USStates.OK: defaultdict(lambda: NANCY_ORENSTEIN),
    USStates.OR: defaultdict(lambda: GUY_JONES),
    USStates.PA: defaultdict(
        lambda: ADAM_HUCKINGS,
        {
            USCounties.ADAMS: ADAM_HUCKINGS,
            USCounties.ALLEGHENY: BRIAN_JUBA,
            USCounties.ARMSTRONG: BRIAN_JUBA,
            USCounties.BEAVER: BRIAN_JUBA,
            USCounties.BEDFORD: ADAM_HUCKINGS,
            USCounties.BERKS: JILLIAN_OSTROWSKI,
            USCounties.BLAIR: ADAM_HUCKINGS,
            USCounties.BRADFORD: ADAM_HUCKINGS,
            USCounties.BUCKS: JILLIAN_OSTROWSKI,
            USCounties.BUTLER: BRIAN_JUBA,
            USCounties.CAMBRIA: ADAM_HUCKINGS,
            USCounties.CARBON: JILLIAN_OSTROWSKI,
            USCounties.CENTRE: ADAM_HUCKINGS,
            USCounties.CHESTER: JILLIAN_OSTROWSKI,
            USCounties.CLARION: ADAM_HUCKINGS,
            USCounties.CLEARFIELD: ADAM_HUCKINGS,
            USCounties.CLINTON: ADAM_HUCKINGS,
            USCounties.COLUMBIA: ADAM_HUCKINGS,
            USCounties.CRAWFORD: ADAM_HUCKINGS,
            USCounties.CUMBERLAND: ADAM_HUCKINGS,
            USCounties.DAUPHIN: ADAM_HUCKINGS,
            USCounties.DELAWARE: JILLIAN_OSTROWSKI,
            USCounties.ELK: ADAM_HUCKINGS,
            USCounties.ERIE: BRIAN_JUBA,
            USCounties.FAYETTE: ADAM_HUCKINGS,
            USCounties.FRANKLIN: ADAM_HUCKINGS,
            USCounties.FREDERICK: JILLIAN_OSTROWSKI,
            USCounties.FULTON: ADAM_HUCKINGS,
            USCounties.GREENE: ADAM_HUCKINGS,
            USCounties.HUNTINGDON: ADAM_HUCKINGS,
            USCounties.INDIANA: BRIAN_JUBA,
            USCounties.JEFFERSON: ADAM_HUCKINGS,
            USCounties.JUNIATA: ADAM_HUCKINGS,
            USCounties.LACKAWANNA: ADAM_HUCKINGS,
            USCounties.LANCASTER: JILLIAN_OSTROWSKI,
            USCounties.LAWRENCE: BRIAN_JUBA,
            USCounties.LEBANON: JILLIAN_OSTROWSKI,
            USCounties.LEHIGH: JILLIAN_OSTROWSKI,
            USCounties.LUZERNE: ADAM_HUCKINGS,
            USCounties.LYCOMING: ADAM_HUCKINGS,
            USCounties.MCKEAN: ADAM_HUCKINGS,
            USCounties.MERCER: ADAM_HUCKINGS,
            USCounties.MIFFLIN: ADAM_HUCKINGS,
            USCounties.MONROE: JILLIAN_OSTROWSKI,
            USCounties.MONTGOMERY: JILLIAN_OSTROWSKI,
            USCounties.MONTOUR: ADAM_HUCKINGS,
            USCounties.NORTHAMPTON: JILLIAN_OSTROWSKI,
            USCounties.NORTHUMBERLAND: ADAM_HUCKINGS,
            USCounties.PERRY: ADAM_HUCKINGS,
            USCounties.PHILADELPHIA: JILLIAN_OSTROWSKI,
            USCounties.PIKE: JILLIAN_OSTROWSKI,
            USCounties.POTTER: ADAM_HUCKINGS,
            USCounties.SCHUYLKILL: JILLIAN_OSTROWSKI,
            USCounties.SNYDER: ADAM_HUCKINGS,
            USCounties.SOMERSET: ADAM_HUCKINGS,
            USCounties.SUSQUEHANNA: ADAM_HUCKINGS,
            USCounties.TIOGA: ADAM_HUCKINGS,
            USCounties.UNION: ADAM_HUCKINGS,
            USCounties.VENANGO: ADAM_HUCKINGS,
            USCounties.WARREN: ADAM_HUCKINGS,
            USCounties.WASHINGTON: BRIAN_JUBA,
            USCounties.WAYNE: JILLIAN_OSTROWSKI,
            USCounties.WESTMORELAND: BRIAN_JUBA,
            USCounties.WYOMING: ADAM_HUCKINGS,
            USCounties.YORK: JILLIAN_OSTROWSKI,
        },
    ),
    USStates.RI: defaultdict(lambda: ADAM_HUCKINGS),
    USStates.SC: defaultdict(lambda: BRENNAN_OVERWAY),
    USStates.SD: defaultdict(
        lambda: ZACHARY_NOLEN,
        {
            USCounties.BEADLE: ZACHARY_NOLEN,
            USCounties.BON_HOMME: ZACHARY_NOLEN,
            USCounties.BROOKINGS: ZACHARY_NOLEN,
            USCounties.BROWN: ZACHARY_NOLEN,
            USCounties.BRULE: ZACHARY_NOLEN,
            USCounties.CAMPBELL: ZACHARY_NOLEN,
            USCounties.CHARLES_MIX: ZACHARY_NOLEN,
            USCounties.CLAY: ZACHARY_NOLEN,
            USCounties.CODINGTON: ZACHARY_NOLEN,
            USCounties.DAVISON: ZACHARY_NOLEN,
            USCounties.DAY: ZACHARY_NOLEN,
            USCounties.DEUEL: ZACHARY_NOLEN,
            USCounties.EDMUNDS: ZACHARY_NOLEN,
            USCounties.GRANT: ZACHARY_NOLEN,
            USCounties.HAND: ZACHARY_NOLEN,
            USCounties.HUGHES: ZACHARY_NOLEN,
            USCounties.JERAULD: ZACHARY_NOLEN,
            USCounties.KINGSBURY: ZACHARY_NOLEN,
            USCounties.LAKE: ZACHARY_NOLEN,
            USCounties.LINCOLN: ZACHARY_NOLEN,
            USCounties.LYMAN: ZACHARY_NOLEN,
            USCounties.MCCOOK: ZACHARY_NOLEN,
            USCounties.MINNEHAHA: ZACHARY_NOLEN,
            USCounties.MINNIEHAHA: ZACHARY_NOLEN,
            USCounties.MOODY: ZACHARY_NOLEN,
            USCounties.ROBERTS: ZACHARY_NOLEN,
            USCounties.SPINK: ZACHARY_NOLEN,
            USCounties.STANLEY: ZACHARY_NOLEN,
            USCounties.SULLY: ZACHARY_NOLEN,
            USCounties.TRIPP: ZACHARY_NOLEN,
            USCounties.TURNER: ZACHARY_NOLEN,
            USCounties.UNION: ZACHARY_NOLEN,
            USCounties.WALWORTH: ZACHARY_NOLEN,
            USCounties.YANKTON: ZACHARY_NOLEN,
            USCounties.BENNETT: TANNER_MAYBERRY,
            USCounties.BUTTE: TANNER_MAYBERRY,
            USCounties.CUSTER: TANNER_MAYBERRY,
            USCounties.FALL_RIVER: TANNER_MAYBERRY,
            USCounties.HAAKON: TANNER_MAYBERRY,
            USCounties.LAWRENCE: TANNER_MAYBERRY,
            USCounties.MEADE: TANNER_MAYBERRY,
            USCounties.PENNINGTON: TANNER_MAYBERRY,
        },
    ),
    USStates.TN: defaultdict(lambda: JANIS_WILKS),
    USStates.TX: defaultdict(
        lambda: NANCY_ORENSTEIN,
        {
            USCounties.ANDERSON: NANCY_ORENSTEIN,
            USCounties.ANDREWS: NANCY_ORENSTEIN,
            USCounties.ARCHER: NANCY_ORENSTEIN,
            USCounties.BAYLOR: NANCY_ORENSTEIN,
            USCounties.BOWIE: NANCY_ORENSTEIN,
            USCounties.BREWSTER: NANCY_ORENSTEIN,
            USCounties.CALLAHAN: NANCY_ORENSTEIN,
            USCounties.CAMP: NANCY_ORENSTEIN,
            USCounties.CASS: NANCY_ORENSTEIN,
            USCounties.CHEROKEE: NANCY_ORENSTEIN,
            USCounties.CHILDRESS: NANCY_ORENSTEIN,
            USCounties.CLAY: NANCY_ORENSTEIN,
            USCounties.COLLIN: NANCY_ORENSTEIN,
            USCounties.COOKE: NANCY_ORENSTEIN,
            USCounties.COTTLE: NANCY_ORENSTEIN,
            USCounties.CRANE: NANCY_ORENSTEIN,
            USCounties.DALLAM: NANCY_ORENSTEIN,
            USCounties.DALLAS: NANCY_ORENSTEIN,
            USCounties.DAWSON: NANCY_ORENSTEIN,
            USCounties.DEAF_SMITH: NANCY_ORENSTEIN,
            USCounties.DELTA: NANCY_ORENSTEIN,
            USCounties.DENTON: NANCY_ORENSTEIN,
            USCounties.DONLEY: NANCY_ORENSTEIN,
            USCounties.EASTLAND: NANCY_ORENSTEIN,
            USCounties.ECTOR: NANCY_ORENSTEIN,
            USCounties.ELLIS: NANCY_ORENSTEIN,
            USCounties.EL_PASO: NANCY_ORENSTEIN,
            USCounties.ERATH: NANCY_ORENSTEIN,
            USCounties.FANNIN: NANCY_ORENSTEIN,
            USCounties.FLOYD: NANCY_ORENSTEIN,
            USCounties.FRANKLIN: NANCY_ORENSTEIN,
            USCounties.GRAY: NANCY_ORENSTEIN,
            USCounties.GRAYSON: NANCY_ORENSTEIN,
            USCounties.GREGG: NANCY_ORENSTEIN,
            USCounties.HALE: NANCY_ORENSTEIN,
            USCounties.HANSFORD: NANCY_ORENSTEIN,
            USCounties.HARRISON: NANCY_ORENSTEIN,
            USCounties.HASKELL: NANCY_ORENSTEIN,
            USCounties.HENDERSON: NANCY_ORENSTEIN,
            USCounties.HILL: NANCY_ORENSTEIN,
            USCounties.HOCKLEY: NANCY_ORENSTEIN,
            USCounties.HOOD: NANCY_ORENSTEIN,
            USCounties.HOPKINS: NANCY_ORENSTEIN,
            USCounties.HOWARD: NANCY_ORENSTEIN,
            USCounties.HUNT: NANCY_ORENSTEIN,
            USCounties.HUTCHINSON: NANCY_ORENSTEIN,
            USCounties.JACK: NANCY_ORENSTEIN,
            USCounties.JOHNSON: NANCY_ORENSTEIN,
            USCounties.JONES: NANCY_ORENSTEIN,
            USCounties.KAUFMAN: NANCY_ORENSTEIN,
            USCounties.KENT: NANCY_ORENSTEIN,
            USCounties.LAMAR: NANCY_ORENSTEIN,
            USCounties.LAMB: NANCY_ORENSTEIN,
            USCounties.LUBBOCK: NANCY_ORENSTEIN,
            USCounties.MARION: NANCY_ORENSTEIN,
            USCounties.MARTIN: NANCY_ORENSTEIN,
            USCounties.MIDLAND: NANCY_ORENSTEIN,
            USCounties.MONTAGUE: NANCY_ORENSTEIN,
            USCounties.MOORE: NANCY_ORENSTEIN,
            USCounties.NACOGDOCHES: NANCY_ORENSTEIN,
            USCounties.NAVARRO: NANCY_ORENSTEIN,
            USCounties.NOLAN: NANCY_ORENSTEIN,
            USCounties.OCHILTREE: NANCY_ORENSTEIN,
            USCounties.PALO_PINTO: NANCY_ORENSTEIN,
            USCounties.PANOLA: NANCY_ORENSTEIN,
            USCounties.PARKER: NANCY_ORENSTEIN,
            USCounties.PECOS: NANCY_ORENSTEIN,
            USCounties.POTTER: NANCY_ORENSTEIN,
            USCounties.PRESIDIO: NANCY_ORENSTEIN,
            USCounties.PROSPER: NANCY_ORENSTEIN,
            USCounties.RANDALL: NANCY_ORENSTEIN,
            USCounties.RED_RIVER: NANCY_ORENSTEIN,
            USCounties.REEVES: NANCY_ORENSTEIN,
            USCounties.ROCKWALL: NANCY_ORENSTEIN,
            USCounties.ROCKWELL: NANCY_ORENSTEIN,
            USCounties.RUSK: NANCY_ORENSTEIN,
            USCounties.SAN_AUGUSTINE: NANCY_ORENSTEIN,
            USCounties.SCURRY: NANCY_ORENSTEIN,
            USCounties.SHELBY: NANCY_ORENSTEIN,
            USCounties.SHERMAN: NANCY_ORENSTEIN,
            USCounties.SMITH: NANCY_ORENSTEIN,
            USCounties.SOMERVELL: NANCY_ORENSTEIN,
            USCounties.STEPHENS: NANCY_ORENSTEIN,
            USCounties.TARRANT: NANCY_ORENSTEIN,
            USCounties.TAYLOR: NANCY_ORENSTEIN,
            USCounties.TERRY: NANCY_ORENSTEIN,
            USCounties.TITUS: NANCY_ORENSTEIN,
            USCounties.UPSHUR: NANCY_ORENSTEIN,
            USCounties.USA: NANCY_ORENSTEIN,
            USCounties.VAN_ZANDT: NANCY_ORENSTEIN,
            USCounties.WARD: NANCY_ORENSTEIN,
            USCounties.WHEELER: NANCY_ORENSTEIN,
            USCounties.WICHITA: NANCY_ORENSTEIN,
            USCounties.WILBARGER: NANCY_ORENSTEIN,
            USCounties.WISE: NANCY_ORENSTEIN,
            USCounties.WOOD: NANCY_ORENSTEIN,
            USCounties.YOAKUM: NANCY_ORENSTEIN,
            USCounties.YOUNG: NANCY_ORENSTEIN,
            USCounties.ANGELINA: KAREY_GALLAGHER,
            USCounties.ARANSAS: KAREY_GALLAGHER,
            USCounties.ATASCOSA: KAREY_GALLAGHER,
            USCounties.AUSTIN: KAREY_GALLAGHER,
            USCounties.BANDERA: KAREY_GALLAGHER,
            USCounties.BASTROP: KAREY_GALLAGHER,
            USCounties.BEE: KAREY_GALLAGHER,
            USCounties.BELL: KAREY_GALLAGHER,
            USCounties.BEXAR: KAREY_GALLAGHER,
            USCounties.BOSQUE: KAREY_GALLAGHER,
            USCounties.BRAZORIA: KAREY_GALLAGHER,
            USCounties.BRAZOS: KAREY_GALLAGHER,
            USCounties.BROWN: KAREY_GALLAGHER,
            USCounties.BURLESON: KAREY_GALLAGHER,
            USCounties.BURNET: KAREY_GALLAGHER,
            USCounties.CALDWELL: KAREY_GALLAGHER,
            USCounties.CALHOUN: KAREY_GALLAGHER,
            USCounties.CAMERON: KAREY_GALLAGHER,
            USCounties.CHAMBERS: KAREY_GALLAGHER,
            USCounties.COKE: KAREY_GALLAGHER,
            USCounties.COLEMAN: KAREY_GALLAGHER,
            USCounties.COLORADO: KAREY_GALLAGHER,
            USCounties.COMAL: KAREY_GALLAGHER,
            USCounties.COMANCHE: KAREY_GALLAGHER,
            USCounties.CONCHO: KAREY_GALLAGHER,
            USCounties.CORYELL: KAREY_GALLAGHER,
            USCounties.DE_WITT: KAREY_GALLAGHER,
            USCounties.DIMMIT: KAREY_GALLAGHER,
            USCounties.DISTRICT_OF_COLUMBIA: KAREY_GALLAGHER,
            USCounties.DUVAL: KAREY_GALLAGHER,
            USCounties.FALLS: KAREY_GALLAGHER,
            USCounties.FAYETTE: KAREY_GALLAGHER,
            USCounties.FORT_BEND: KAREY_GALLAGHER,
            USCounties.FREESTONE: KAREY_GALLAGHER,
            USCounties.FRIO: KAREY_GALLAGHER,
            USCounties.GALVESTON: KAREY_GALLAGHER,
            USCounties.GILLESPIE: KAREY_GALLAGHER,
            USCounties.GOLIAD: KAREY_GALLAGHER,
            USCounties.GONZALES: KAREY_GALLAGHER,
            USCounties.GRIMES: KAREY_GALLAGHER,
            USCounties.GUADALUPE: KAREY_GALLAGHER,
            USCounties.HAMILTON: KAREY_GALLAGHER,
            USCounties.HARDIN: KAREY_GALLAGHER,
            USCounties.HARRIS: KAREY_GALLAGHER,
            USCounties.HAYS: KAREY_GALLAGHER,
            USCounties.HIDAL: KAREY_GALLAGHER,
            USCounties.HIDALGO: KAREY_GALLAGHER,
            USCounties.HOUSTON: KAREY_GALLAGHER,
            USCounties.JACKSON: KAREY_GALLAGHER,
            USCounties.JASPER: KAREY_GALLAGHER,
            USCounties.JEFFERSON: KAREY_GALLAGHER,
            USCounties.JIM_HOGG: KAREY_GALLAGHER,
            USCounties.JIM_WELLS: KAREY_GALLAGHER,
            USCounties.KARNES: KAREY_GALLAGHER,
            USCounties.KENDALL: KAREY_GALLAGHER,
            USCounties.KERR: KAREY_GALLAGHER,
            USCounties.KIMBLE: KAREY_GALLAGHER,
            USCounties.KLEBERG: KAREY_GALLAGHER,
            USCounties.LAMPASAS: KAREY_GALLAGHER,
            USCounties.LAVACA: KAREY_GALLAGHER,
            USCounties.LEE: KAREY_GALLAGHER,
            USCounties.LEON: KAREY_GALLAGHER,
            USCounties.LIBERTY: KAREY_GALLAGHER,
            USCounties.LIMESTONE: KAREY_GALLAGHER,
            USCounties.LIVE_OAK: KAREY_GALLAGHER,
            USCounties.LLANO: KAREY_GALLAGHER,
            USCounties.MADISON: KAREY_GALLAGHER,
            USCounties.MATAGORDA: KAREY_GALLAGHER,
            USCounties.MAVERICK: KAREY_GALLAGHER,
            USCounties.MCCULLOCH: KAREY_GALLAGHER,
            USCounties.MCLENNAN: KAREY_GALLAGHER,
            USCounties.MEDINA: KAREY_GALLAGHER,
            USCounties.MILAM: KAREY_GALLAGHER,
            USCounties.MILLS: KAREY_GALLAGHER,
            USCounties.MONTGOMERY: KAREY_GALLAGHER,
            USCounties.NUECES: KAREY_GALLAGHER,
            USCounties.ORANGE: KAREY_GALLAGHER,
            USCounties.POLK: KAREY_GALLAGHER,
            USCounties.REAGAN: KAREY_GALLAGHER,
            USCounties.REFUGIO: KAREY_GALLAGHER,
            USCounties.ROBERTSON: KAREY_GALLAGHER,
            USCounties.RUNNELS: KAREY_GALLAGHER,
            USCounties.SAN_PATRICIO: KAREY_GALLAGHER,
            USCounties.SAN_SABA: KAREY_GALLAGHER,
            USCounties.STARR: KAREY_GALLAGHER,
            USCounties.TOM_GREEN: KAREY_GALLAGHER,
            USCounties.TRAVIS: KAREY_GALLAGHER,
            USCounties.TYLER: KAREY_GALLAGHER,
            USCounties.UVALDE: KAREY_GALLAGHER,
            USCounties.VAL_VERDE: KAREY_GALLAGHER,
            USCounties.VICTORIA: KAREY_GALLAGHER,
            USCounties.WALKER: KAREY_GALLAGHER,
            USCounties.WALLER: KAREY_GALLAGHER,
            USCounties.WASHINGTON: KAREY_GALLAGHER,
            USCounties.WEBB: KAREY_GALLAGHER,
            USCounties.WHARTON: KAREY_GALLAGHER,
            USCounties.WILLACY: KAREY_GALLAGHER,
            USCounties.WILLIAMSON: KAREY_GALLAGHER,
            USCounties.WILSON: KAREY_GALLAGHER,
        },
    ),
    USStates.UT: defaultdict(lambda: MASON_MILLER),
    USStates.VA: defaultdict(
        lambda: BRENNAN_OVERWAY,
        {
            USCounties.ACCOMACK: BRENNAN_OVERWAY,
            USCounties.ALBEMARLE: JILLIAN_OSTROWSKI,
            USCounties.ALEXANDRIA_CITY: JILLIAN_OSTROWSKI,
            USCounties.AMELIA: BRENNAN_OVERWAY,
            USCounties.AMHERST: BRENNAN_OVERWAY,
            USCounties.APPOMATTOX: BRENNAN_OVERWAY,
            USCounties.ARLINGTON: BRENNAN_OVERWAY,
            USCounties.AUGUSTA: JILLIAN_OSTROWSKI,
            USCounties.BEDFORD: BRENNAN_OVERWAY,
            USCounties.BOTETOURT: BRENNAN_OVERWAY,
            USCounties.BRISTOL: BRENNAN_OVERWAY,
            USCounties.BRUNSWICK: BRENNAN_OVERWAY,
            USCounties.BUCHANAN: BRENNAN_OVERWAY,
            USCounties.BUCKINGHAM: BRENNAN_OVERWAY,
            USCounties.BUENA_VISTA_CITY: BRENNAN_OVERWAY,
            USCounties.CAMPBELL: BRENNAN_OVERWAY,
            USCounties.CARROLL: BRENNAN_OVERWAY,
            USCounties.CHARLOTTE: BRENNAN_OVERWAY,
            USCounties.CHARLOTTESVILLE_CITY: BRENNAN_OVERWAY,
            USCounties.CHESAPEAKE_CITY: BRENNAN_OVERWAY,
            USCounties.CHESTERFIELD: BRENNAN_OVERWAY,
            USCounties.COLONIAL_HEIGHTS_CITY: BRENNAN_OVERWAY,
            USCounties.COVINGTON_CITY: BRENNAN_OVERWAY,
            USCounties.CRAIG: BRENNAN_OVERWAY,
            USCounties.CULPEPER: JILLIAN_OSTROWSKI,
            USCounties.DANVILLE_CITY: BRENNAN_OVERWAY,
            USCounties.DICKENSON: BRENNAN_OVERWAY,
            USCounties.EMPORIA_CITY: BRENNAN_OVERWAY,
            USCounties.ESSEX: BRENNAN_OVERWAY,
            USCounties.FAIRFAX: JILLIAN_OSTROWSKI,
            USCounties.FALLS_CHURCH_CITY: JILLIAN_OSTROWSKI,
            USCounties.FAUQUIER: JILLIAN_OSTROWSKI,
            USCounties.FLOYD: BRENNAN_OVERWAY,
            USCounties.FLUVANNA: BRENNAN_OVERWAY,
            USCounties.FRANKLIN: BRENNAN_OVERWAY,
            USCounties.FREDERICK: JILLIAN_OSTROWSKI,
            USCounties.FREDERICKSBURG_CITY: JILLIAN_OSTROWSKI,
            USCounties.GALAX_CITY: BRENNAN_OVERWAY,
            USCounties.GILES: BRENNAN_OVERWAY,
            USCounties.GLOUCESTER: BRENNAN_OVERWAY,
            USCounties.GOOCHLAND: BRENNAN_OVERWAY,
            USCounties.GRAYSON: BRENNAN_OVERWAY,
            USCounties.GREENE: JILLIAN_OSTROWSKI,
            USCounties.HALIFAX: BRENNAN_OVERWAY,
            USCounties.HAMPTON_CITY: BRENNAN_OVERWAY,
            USCounties.HANOVER: BRENNAN_OVERWAY,
            USCounties.HARRISONBURG_CITY: JILLIAN_OSTROWSKI,
            USCounties.HENRICO: BRENNAN_OVERWAY,
            USCounties.HENRY: BRENNAN_OVERWAY,
            USCounties.HOPEWELL_CITY: BRENNAN_OVERWAY,
            USCounties.ISLE_OF_WIGHT: BRENNAN_OVERWAY,
            USCounties.JAMES_CITY: BRENNAN_OVERWAY,
            USCounties.KING_AND_QUEEN: BRENNAN_OVERWAY,
            USCounties.KING_GEORGE: JILLIAN_OSTROWSKI,
            USCounties.KING_WILLIAM: BRENNAN_OVERWAY,
            USCounties.LANCASTER: BRENNAN_OVERWAY,
            USCounties.LEE: BRENNAN_OVERWAY,
            USCounties.LEXINGTON_CITY: BRENNAN_OVERWAY,
            USCounties.LOUDOUN: JILLIAN_OSTROWSKI,
            USCounties.LOUISA: JILLIAN_OSTROWSKI,
            USCounties.LYNCHBURG_CITY: BRENNAN_OVERWAY,
            USCounties.MADISON: JILLIAN_OSTROWSKI,
            USCounties.MANASSAS_CITY: JILLIAN_OSTROWSKI,
            USCounties.MANASSAS_PARK_CITY: JILLIAN_OSTROWSKI,
            USCounties.MATHEWS: BRENNAN_OVERWAY,
            USCounties.MECKLENBURG: BRENNAN_OVERWAY,
            USCounties.MIDDLESEX: BRENNAN_OVERWAY,
            USCounties.MONTGOMERY: BRENNAN_OVERWAY,
            USCounties.NELSON: BRENNAN_OVERWAY,
            USCounties.NEWPORT_NEWS_CITY: BRENNAN_OVERWAY,
            USCounties.NORFOLK_CITY: BRENNAN_OVERWAY,
            USCounties.NORTHAMPTON: BRENNAN_OVERWAY,
            USCounties.NORTHUMBERLAND: BRENNAN_OVERWAY,
            USCounties.NORTON_CITY: BRENNAN_OVERWAY,
            USCounties.NOTTOWAY: BRENNAN_OVERWAY,
            USCounties.ORANGE: JILLIAN_OSTROWSKI,
            USCounties.PAGE: JILLIAN_OSTROWSKI,
            USCounties.PATRICK: BRENNAN_OVERWAY,
            USCounties.PETERSBURG_CITY: BRENNAN_OVERWAY,
            USCounties.PITTSYLVANIA: BRENNAN_OVERWAY,
            USCounties.POQUOSON_CITY: BRENNAN_OVERWAY,
            USCounties.PORTSMOUTH_CITY: BRENNAN_OVERWAY,
            USCounties.POWHATAN: BRENNAN_OVERWAY,
            USCounties.PRINCE_EDWARD: BRENNAN_OVERWAY,
            USCounties.PRINCE_GEORGE: BRENNAN_OVERWAY,
            USCounties.PRINCE_WILLIAM: JILLIAN_OSTROWSKI,
            USCounties.PULASKI: BRENNAN_OVERWAY,
            USCounties.RADFORD: BRENNAN_OVERWAY,
            USCounties.RICHMOND_CITY: BRENNAN_OVERWAY,
            USCounties.ROANOKE: BRENNAN_OVERWAY,
            USCounties.ROANOKE_CITY: BRENNAN_OVERWAY,
            USCounties.ROCKBRIDGE: BRENNAN_OVERWAY,
            USCounties.ROCKINGHAM: JILLIAN_OSTROWSKI,
            USCounties.RUSSELL: BRENNAN_OVERWAY,
            USCounties.SALEM: BRENNAN_OVERWAY,
            USCounties.SCOTT: BRENNAN_OVERWAY,
            USCounties.SHENANDOAH: JILLIAN_OSTROWSKI,
            USCounties.SMYTH: BRENNAN_OVERWAY,
            USCounties.SPOTSYLVANIA: JILLIAN_OSTROWSKI,
            USCounties.STAFFORD: JILLIAN_OSTROWSKI,
            USCounties.STAUNTON_CITY: JILLIAN_OSTROWSKI,
            USCounties.SUFFOLK_CITY: BRENNAN_OVERWAY,
            USCounties.SUSSEX: BRENNAN_OVERWAY,
            USCounties.TAZEWELL: BRENNAN_OVERWAY,
            USCounties.VIRGINIA_BEACH_CITY: BRENNAN_OVERWAY,
            USCounties.WARREN: JILLIAN_OSTROWSKI,
            USCounties.WASHINGTON: BRENNAN_OVERWAY,
            USCounties.WAYNESBORO_CITY: JILLIAN_OSTROWSKI,
            USCounties.WESTMORELAND: JILLIAN_OSTROWSKI,
            USCounties.WINCHESTER_CITY: JILLIAN_OSTROWSKI,
            USCounties.WISE: BRENNAN_OVERWAY,
            USCounties.WYTHE: BRENNAN_OVERWAY,
            USCounties.YORK: BRENNAN_OVERWAY,
        },
    ),
    USStates.VT: defaultdict(lambda: ADAM_HUCKINGS),
    USStates.WA: defaultdict(lambda: GUY_JONES),
    USStates.WI: defaultdict(lambda: BRIAN_SPITZMUELLER),
    USStates.WV: defaultdict(
        lambda: ADAM_HUCKINGS,
        {
            USCounties.BARBOUR: ADAM_HUCKINGS,
            USCounties.BERKELEY: ADAM_HUCKINGS,
            USCounties.BOONE: JANIS_WILKS,
            USCounties.BRAXTON: ADAM_HUCKINGS,
            USCounties.BROOKE: ADAM_HUCKINGS,
            USCounties.CABELL: JANIS_WILKS,
            USCounties.CALHOUN: JANIS_WILKS,
            USCounties.CLAY: JANIS_WILKS,
            USCounties.DODDRIDGE: ADAM_HUCKINGS,
            USCounties.FAYETTE: JANIS_WILKS,
            USCounties.GRANT: ADAM_HUCKINGS,
            USCounties.GREENBRIER: JANIS_WILKS,
            USCounties.HAMPSHIRE: ADAM_HUCKINGS,
            USCounties.HANCOCK: ADAM_HUCKINGS,
            USCounties.HARDY: ADAM_HUCKINGS,
            USCounties.HARRISON: ADAM_HUCKINGS,
            USCounties.JACKSON: JANIS_WILKS,
            USCounties.JEFFERSON: ADAM_HUCKINGS,
            USCounties.KANAWHA: JANIS_WILKS,
            USCounties.LEWIS: ADAM_HUCKINGS,
            USCounties.LINCOLN: JANIS_WILKS,
            USCounties.LOGAN: JANIS_WILKS,
            USCounties.MARION: ADAM_HUCKINGS,
            USCounties.MARSHALL: ADAM_HUCKINGS,
            USCounties.MASON: JANIS_WILKS,
            USCounties.MCDOWELL: JANIS_WILKS,
            USCounties.MERCER: JANIS_WILKS,
            USCounties.MINERAL: ADAM_HUCKINGS,
            USCounties.MINGO: JANIS_WILKS,
            USCounties.MONONGALIA: ADAM_HUCKINGS,
            USCounties.MONROE: JANIS_WILKS,
            USCounties.NICHOLAS: JANIS_WILKS,
            USCounties.OHIO: ADAM_HUCKINGS,
            USCounties.PENDLETON: ADAM_HUCKINGS,
            USCounties.PLEASANTS: ADAM_HUCKINGS,
            USCounties.POCAHONTAS: JANIS_WILKS,
            USCounties.PRESTON: ADAM_HUCKINGS,
            USCounties.PUTNAM: JANIS_WILKS,
            USCounties.RALEIGH: JANIS_WILKS,
            USCounties.RANDOLPH: ADAM_HUCKINGS,
            USCounties.RITCHIE: ADAM_HUCKINGS,
            USCounties.ROANE: JANIS_WILKS,
            USCounties.TAYLOR: ADAM_HUCKINGS,
            USCounties.TUCKER: ADAM_HUCKINGS,
            USCounties.TYLER: ADAM_HUCKINGS,
            USCounties.UPSHUR: ADAM_HUCKINGS,
            USCounties.WAYNE: JANIS_WILKS,
            USCounties.WETZEL: ADAM_HUCKINGS,
            USCounties.WOOD: JANIS_WILKS,
            USCounties.WYOMING: JANIS_WILKS,
        },
    ),
    USStates.WY: defaultdict(lambda: MASON_MILLER),
}


class AllyAutoAgentAssigner(BaseAgentAssigner):
    ally_auto_org_id = ExistingOrganizations.Paragon.value

    def __init__(self):
        super().__init__(None)
        self._zippopotam_client = ZippopotamClient()

    def assign_agent(self, submission: Submission) -> tuple[BrokerageEmployee | None, BrokerageEmployee | None]:
        log = logger.bind(submission_id=submission.id)
        if not submission.is_ally_auto:
            log.error("Submission is not Ally Auto. Wrong agent assigner.")
            return None, None

        corr_cont = None
        broker = None
        if not (fni := next((sb for sb in submission.businesses if sb.is_fni), None)):
            log.info("No FNI business found. Skipping agent assignment.")
            return None, None
        ers_entity = flask.current_app.ers_client_v3.get_entity(entity_id=str(fni.business_id))
        premises = get_physical_premises(ers_entity)
        # if premises.premises and premises.is_physical_address:
        # Lifting is_physical_address check for now, because it is a lot not true for Paragon (?)
        if not premises.premises:
            log.info("Not found premises for FNI. Skipping agent assignment.")
            return None, None
        county = premises.premises.county
        state = premises.premises.state
        zip_code = premises.premises.postal_code

        if zip_code and not state:
            (
                possible_city,
                possible_state,
            ) = self._zippopotam_client.try_get_city_and_state_abbreviation_from_zipcode(zip_code.strip())
            log.info(f"Fallback state search: got state from zip code {possible_city=} {possible_state=}")
            state = state or possible_state
        if not state:
            log.info("No state found for FNI premises. Skipping agent assignment.")
            return None, None

        state_str = state.upper()
        county_str = county.upper() if county else "UNKNOWN"
        if county_str.endswith(" COUNTY"):
            county_str = county_str[:-7]

        state_brokers_mapper = ally_auto_brokers_mapper.get(state_str)
        agent_email = state_brokers_mapper[county_str]

        try:
            if not submission.brokerage_id:
                brokerage = Brokerage.query.filter_by(name=ALLY_AUTO_BROKERAGE_NAME).first()
                submission.set_brokerage(brokerage, BrokerageEmployeeSource.AUTO)
            if submission.brokerage_id:
                broker = BrokerageEmployeeDAO.get_broker_by_email_and_brokerage(
                    agent_email,
                    submission.brokerage_id,
                    organization_ids=[self.ally_auto_org_id],
                    roles=[BrokerageEmployeeRoles.AGENT],
                )
            if not broker:
                log.info("No broker found for Ally Auto.")
                return None, None
            if broker.email:
                BrokerageEmployeeDAO.enrich_broker_with_roles(broker, [BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT])
                submission.set_brokerage_contact(broker)
                corr_cont = broker
            submission.set_broker(broker, source=BrokerageEmployeeSource.AUTO)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error("Ally auto broker v2 error", exc_info=e)
        return corr_cont, broker

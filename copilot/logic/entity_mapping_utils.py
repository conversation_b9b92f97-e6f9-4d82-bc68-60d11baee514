from uuid import UUID

from common.logic.identification.fact_value_resolution import (
    resolve_fact_subtype_value_types,
)
from facts_client_v2.model.fact_subtype import FactSubtype
from infrastructure_common.logging import get_logger
from static_common.enums.coverage_names import CoverageName
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.file_type import FileType
from static_common.enums.probability import ProbabilityInterpretation
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
    SuggestedField,
)

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.models import File, Organization, Submission, db
from copilot.models.reports import SubmissionCoverage

logger = get_logger()

_PRACTICE_REQUIRED_FACT_SUBTYPES = {
    FactSubtypeID.YEARS_IN_BUSINESS,
    FactSubtypeID.TOTAL_SALES,
    FactSubtypeID.PAYROLL,
    FactSubtypeID.PROJECT_SUBCONTRACTORS_COST,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_PUBLIC,
    FactSubtypeID.PROJECT_NEW_CONSTRUCTION,
    FactSubtypeID.PROJECT_REMODELING_OR_REPAIR,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_EXTERIOR_WORK,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_INTERIOR_WORK,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_GC,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS,
    FactSubtypeID.PROJECT_SUBCONTRACTORS_USED,
    FactSubtypeID.PROJECT_USE_OF_EIFS,
    FactSubtypeID.PROJECT_SCAFFOLDING,
    FactSubtypeID.PROJECT_MOLD_REMOVAL,
    FactSubtypeID.PROJECT_ROOF_WORK,
    FactSubtypeID.PROJECT_CRANE_WORK,
    FactSubtypeID.PROJECT_DEMOLITION_WORK,
    FactSubtypeID.PROJECT_DRILLING_WORK,
    FactSubtypeID.PROJECT_GAS_LINE_WORK,
    FactSubtypeID.PROJECT_SOLAR_WORK,
    FactSubtypeID.PROJECT_BLASTING_WORK,
    FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL,
    FactSubtypeID.PROJECT_EXCAVATION_WORK,
    FactSubtypeID.PROJECT_BELOW_GRADE,
    FactSubtypeID.PROJECT_DEPTH_OF_WORK,
    FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT,
    FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES,
}

_PROJECT_REQUIRED_FACT_SUBTYPES = {
    FactSubtypeID.PROJECT_SUBCONTRACTORS_USED,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS,
    FactSubtypeID.PROJECT_SUBCONTRACTORS_COST,
    FactSubtypeID.PROJECT_ESTIMATED_CONSTRUCTION_COST,
    FactSubtypeID.PROJECT_START_DATE,
    FactSubtypeID.PROJECT_END_DATE,
    FactSubtypeID.PROJECT_USE_OF_EIFS,
    FactSubtypeID.PROJECT_SCAFFOLDING,
    FactSubtypeID.PROJECT_MOLD_REMOVAL,
    FactSubtypeID.PROJECT_ROOF_WORK,
    FactSubtypeID.PROJECT_CRANE_WORK,
    FactSubtypeID.PROJECT_DEMOLITION_WORK,
    FactSubtypeID.PROJECT_DRILLING_WORK,
    FactSubtypeID.PROJECT_GAS_LINE_WORK,
    FactSubtypeID.PROJECT_SOLAR_WORK,
    FactSubtypeID.PROJECT_BLASTING_WORK,
    FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL,
    FactSubtypeID.PROJECT_EXCAVATION_WORK,
    FactSubtypeID.PROJECT_BELOW_GRADE,
    FactSubtypeID.PROJECT_DEPTH_OF_WORK,
    FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT,
    FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES,
    FactSubtypeID.PROJECT_SAFETY_PROGRAM,
    FactSubtypeID.PROJECT_QUALITY_CONTROL_PROGRAM,
    FactSubtypeID.PROJECT_SITE_INSPECTION_PROGRAM,
}

_CONSTRUCTION_SPECIFIC_FACT_SUBTYPES = [
    FactSubtypeID.PROJECT_START_DATE,
    FactSubtypeID.PROJECT_END_DATE,
    FactSubtypeID.PROJECT_SUBCONTRACTORS_COST,
    FactSubtypeID.PROJECT_SUBCONTRACTORS_USED,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_GC,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS,
    FactSubtypeID.PROJECT_NEW_CONSTRUCTION,
    FactSubtypeID.PROJECT_REMODELING_OR_REPAIR,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_EXTERIOR_WORK,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_INTERIOR_WORK,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL,
    FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_PUBLIC,
    FactSubtypeID.PROJECT_AIRPORT_WORK,
    FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL,
    FactSubtypeID.PROJECT_BLASTING_WORK,
    FactSubtypeID.PROJECT_BOILER_WORK,
    FactSubtypeID.PROJECT_CRANE_WORK,
    FactSubtypeID.PROJECT_DEMOLITION_WORK,
    FactSubtypeID.PROJECT_DRILLING_WORK,
    FactSubtypeID.PROJECT_USE_OF_EIFS,
    FactSubtypeID.PROJECT_EXCAVATION_WORK,
    FactSubtypeID.PROJECT_GAS_LINE_WORK,
    FactSubtypeID.PROJECT_MOLD_REMOVAL,
    FactSubtypeID.PROJECT_ROOF_WORK,
    FactSubtypeID.PROJECT_SCAFFOLDING,
    FactSubtypeID.PROJECT_SOLAR_WORK,
    FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT,
    FactSubtypeID.PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES,
    FactSubtypeID.PROJECT_BELOW_GRADE,
    FactSubtypeID.PROJECT_DEPTH_OF_WORK,
    FactSubtypeID.PROJECT_SAFETY_PROGRAM,
    FactSubtypeID.PROJECT_TORCH_DOWN,
    FactSubtypeID.PROJECT_HOT_AIR_WELDING,
    FactSubtypeID.PROJECT_HOT_TAR,
    FactSubtypeID.PROJECT_PILE_DRIVING_WORK,
    FactSubtypeID.PROJECT_GAS_LINE_WORK,
    FactSubtypeID.PROJECT_BOILER_WORK,
    FactSubtypeID.PROJECT_DRILLING_WORK,
    FactSubtypeID.SWIMMING_POOL_CONSTRUCTION,
]

_WORKERS_COMP_REQUIRED_SUBTYPES = {
    FactSubtypeID.YEAR_FOUNDED,
    FactSubtypeID.EMPLOYEE_COUNT,
    FactSubtypeID.FT_EMPLOYEES_COUNT,
    FactSubtypeID.PT_EMPLOYEES_COUNT,
    FactSubtypeID.ANNUAL_TURNOVER_PERCENT,
    FactSubtypeID.UNIONIZED_EMPLOYMENT_PERCENTAGE,
    FactSubtypeID.PAID_SICK_LEAVE_PROVIDED,
    FactSubtypeID.PAID_VACATIONS_PROVIDED,
    FactSubtypeID.PRE_HIRE_DRUG_TESTING,
    FactSubtypeID.POST_ACCIDENT_DRUG_TESTING,
}

_WORKERS_COMP_MANUFATURING_SUBTYPES = {
    FactSubtypeID.NUMBER_OF_SHIFTS,
    FactSubtypeID.HAS_MONTHLY_SAFETY_MEETINGS,
    FactSubtypeID.HAS_SAFETY_MANUAL,
    FactSubtypeID.HAS_SAFETY_POLICY,
}

_MANAGEMENT_LIAIBILITY_REQUIRED_SUBTYPES = [
    FactSubtypeID.YEAR_FOUNDED,
    FactSubtypeID.YEARS_IN_BUSINESS,
    FactSubtypeID.WEBSITE,
    FactSubtypeID.BANKRUPTCY_RESTRUCTURING_LIQUIDATION_EXPECTED,
    FactSubtypeID.BANKRUPTCY_RESTRUCTURING_LIQUIDATION_PAST_24_MONTHS,
    FactSubtypeID.EXPECTED_MERGER_OR_ACQUISITION,
    FactSubtypeID.MERGER_OR_ACQUISITION_ASSET_SALES_PAST_24_MONTHS,
    FactSubtypeID.OFFICERS_CHANGES_EXPECTED,
    FactSubtypeID.OFFICERS_CHANGES_PAST_24_MONTHS,
    FactSubtypeID.EXPECTED_ENTITIES_CLOSING_LAYOFFS,
    FactSubtypeID.CLOSING_LAYOFFS_PAST_24_MONTHS,
    FactSubtypeID.PLAN_BENEFIT_CHANGES_EXPECTED,
    FactSubtypeID.PLAN_BENEFIT_CHANGES_PAST_24_MONTHS,
    FactSubtypeID.PLAN_CHANGES_TERMINATIONS_EXPECTED,
    FactSubtypeID.PLAN_CHANGES_TERMINATIONS_PAST_24_MONTHS,
    FactSubtypeID.PUBLIC_OR_PRIVATE_OFFERING_EXPECTED,
    FactSubtypeID.PUBLIC_OR_PRIVATE_OFFERING_PAST_24_MONTHS,
    FactSubtypeID.EMPLOYEE_COUNT,
    FactSubtypeID.US_EMPLOYEES_COUNT,
    FactSubtypeID.FOREIGN_EMPLOYEES_COUNT,
    FactSubtypeID.FT_EMPLOYEES_COUNT,
    FactSubtypeID.FT_US_EMPLOYEES_COUNT,
    FactSubtypeID.FT_NON_US_EMPLOYEES_COUNT,
    FactSubtypeID.PT_EMPLOYEES_COUNT,
    FactSubtypeID.PT_US_EMPLOYEES_COUNT,
    FactSubtypeID.PT_NON_US_EMPLOYEES_COUNT,
    FactSubtypeID.INDEPENDENT_CONTRACTOR_COUNT,
    FactSubtypeID.INDEPENDENT_CONTRACTOR_US_COUNT,
    FactSubtypeID.INDEPENDENT_CONTRACTOR_NON_US_COUNT,
    FactSubtypeID.LEASED_EMPLOYEES_COUNT,
    FactSubtypeID.SEASONAL_EMPLOYEES_COUNT,
    FactSubtypeID.VOLUNTEERS_EMPLOYEES_COUNT,
    FactSubtypeID.CA_EMPLOYEES_COUNT,
    FactSubtypeID.FT_CA_EMPLOYEES_COUNT,
    FactSubtypeID.PT_CA_EMPLOYEES_COUNT,
    FactSubtypeID.NY_EMPLOYEES_COUNT,
    FactSubtypeID.FT_NY_EMPLOYEES_COUNT,
    FactSubtypeID.PT_NY_EMPLOYEES_COUNT,
    FactSubtypeID.FL_EMPLOYEES_COUNT,
    FactSubtypeID.FT_FL_EMPLOYEES_COUNT,
    FactSubtypeID.PT_FL_EMPLOYEES_COUNT,
    FactSubtypeID.TX_EMPLOYEES_COUNT,
    FactSubtypeID.FT_TX_EMPLOYEES_COUNT,
    FactSubtypeID.PT_TX_EMPLOYEES_COUNT,
    FactSubtypeID.LAYOFFS_EMPLOYEES_COUNT,
    FactSubtypeID.VOL_TERMINATED_EMPLOYEES_COUNT,
    FactSubtypeID.INVOL_TERMINATED_EMPLOYEES_COUNT,
    FactSubtypeID.ANNUAL_TURNOVER_PERCENT,
    FactSubtypeID.PLAN_ERISA_NON_COMPLIANT,
]

K2_FACTS_TO_POPULATE = {
    FactSubtypeID.GARAGE_OTHER_OPERATIONS,
    FactSubtypeID.GARAGE_FUEL_CONVERSION,
    FactSubtypeID.GARAGE_PERFORMANCE_ENHANCEMENTS,
    FactSubtypeID.GARAGE_LOAN_OR_RENT,
    FactSubtypeID.GARAGE_PAWNING_TITLE_LOANS,
    FactSubtypeID.GARAGE_DISMANTLE_OR_SALVAGE,
    FactSubtypeID.GARAGE_CAR_CRUSHER_OR_SALVAGE,
    FactSubtypeID.GARAGE_BUY_HERE_PAY_HERE,
    FactSubtypeID.GARAGE_AIRPORT_SEAPORT_RAILROAD,
    FactSubtypeID.GARAGE_BREATHALYZER_IGNITION_INTERLOCK,
    FactSubtypeID.GARAGE_MANUFACTURE_AUTO_PARTS,
    FactSubtypeID.GARAGE_STRUCTURALLY_ALTER_VEHICLES,
    FactSubtypeID.GARAGE_TWO_OR_MORE_VEHICLE_TRANSPORT,
    FactSubtypeID.GARAGE_DRIVERS_UNDER_20_OVER_70,
}


CNA_FACTS_TO_POPULATE = {
    FactSubtypeID.ADULT_DAY_CARE,
    FactSubtypeID.CONCIERGE_MEDICINE,
    FactSubtypeID.DETOX_WITH_ANESTHESIA,
    FactSubtypeID.ERECTILE_DYSFUNCTION_CLINIC,
    FactSubtypeID.INSTITUTIONAL_REVIEW_BOARD,
    FactSubtypeID.INTRAOPERATIVE_NEUROMONITORING,
    FactSubtypeID.OIL_RIG_MEDICAL,
    FactSubtypeID.OFF_LABEL_DRUGS,
    FactSubtypeID.LETHAL_INJECTION_COMPOUNDS,
    FactSubtypeID.PRENATAL_ULTRASOUND,
    FactSubtypeID.INTERVENTIONAL_PAIN_MANAGEMENT,
    FactSubtypeID.NEUROSURGERY_SERVICE,
    FactSubtypeID.ORTHOPEDIC_SURGERY_SERVICE,
    FactSubtypeID.CORD_BLOOD_BANK,
    FactSubtypeID.CORONER_SERVICE,
    FactSubtypeID.CORRECTIONAL_FACILITY_MEDICINE,
    FactSubtypeID.PERCENT_CORRECTIONAL_FACILITY_MEDICINE,
    FactSubtypeID.ALTERNATIVE_MEDICINE,
    FactSubtypeID.DURABLE_MEDICAL_EQUIPMENT,
    FactSubtypeID.DURABLE_MEDICAL_EQUIPMENT_SALES,
    FactSubtypeID.DURABLE_MEDICAL_EQUIPMENT_PERCENTAGE,
    FactSubtypeID.FRAUD_CONVICTION,
    FactSubtypeID.ALTERNATIVE_MEDICINE_PERCENTAGE,
    FactSubtypeID.HAS_PATIENTS_ON_VENTILATORS,
    FactSubtypeID.PERCENT_PATIENTS_ON_VENTILATORS,
    FactSubtypeID.MEDICALLY_FRAGILE_INFANTS,
    FactSubtypeID.OCULAR_LABORATORY,
    FactSubtypeID.MEDICAL_MANAGEMENT_COMPANY,
    FactSubtypeID.TOTAL_NUMBER_OF_PROCEDURES,
}


def add_suggested_fields(
    onboarded_file: OnboardedFile, fact_subtypes: list[FactSubtype], submission_id: UUID
) -> OnboardedFile:
    submission = Submission.query.get_or_404(submission_id, description="The submission with specified ID wasn't found")
    submission_coverages: list[SubmissionCoverage] = (
        SubmissionCoverage.query.join(SubmissionCoverage.coverage)
        .filter(SubmissionCoverage.submission_id == submission_id)
        .all()
    )
    submission_coverages_names = {coverage.coverage.name for coverage in submission_coverages}
    is_workers_comp_coverage = CoverageName.WorkersComp.value in submission_coverages_names
    if is_workers_comp_coverage:
        onboarded_file, _ = _add_missing_fact_subtypes(
            onboarded_file,
            _WORKERS_COMP_REQUIRED_SUBTYPES,
            fact_subtypes,
            SubmissionEntityType.PRIMARY_INSURED,
        )

    management_liability_coverages = {
        CoverageName.FiduciaryLiability.value,
        CoverageName.Crime.value,
        CoverageName.EmploymentPracticesLiability.value,
        CoverageName.ManagementLiability.value,
    }
    is_management_liability = bool(submission_coverages_names.intersection(management_liability_coverages))

    if FeatureFlagsClient.is_feature_enabled(FeatureType.ADD_ML_FACTSUBTYPES_TO_EM) and (
        is_management_liability or Organization.is_nationwide_ml_for_id(submission.organization_id)
    ):
        onboarded_file, _ = _add_missing_fact_subtypes(
            onboarded_file,
            set(_MANAGEMENT_LIAIBILITY_REQUIRED_SUBTYPES),
            fact_subtypes,
            SubmissionEntityType.PRIMARY_INSURED,
        )
        return sort_fields(onboarded_file, _MANAGEMENT_LIAIBILITY_REQUIRED_SUBTYPES)

    is_manufacturing = submission.primary_naics_code and (
        submission.primary_naics_code.startswith("NAICS_31") or submission.primary_naics_code.startswith("NAICS_33")
    )

    if is_workers_comp_coverage and is_manufacturing:
        onboarded_file, _ = _add_missing_fact_subtypes(
            onboarded_file,
            _WORKERS_COMP_MANUFATURING_SUBTYPES,
            fact_subtypes,
            SubmissionEntityType.PRIMARY_INSURED,
        )

    is_construction = submission.primary_naics_code and submission.primary_naics_code.startswith("NAICS_23")

    onboarded_file, _ = _add_missing_fact_subtypes(
        onboarded_file,
        _PROJECT_REQUIRED_FACT_SUBTYPES,
        fact_subtypes,
        SubmissionEntityType.PROJECT,
    )
    onboarded_file, updated_gc = _add_missing_fact_subtypes(
        onboarded_file,
        _PRACTICE_REQUIRED_FACT_SUBTYPES,
        fact_subtypes,
        SubmissionEntityType.GENERAL_CONTRACTOR,
    )
    if updated_gc:
        return sort_fields(onboarded_file, _CONSTRUCTION_SPECIFIC_FACT_SUBTYPES)
    fact_subtype_ids = {field.fact_subtype_id for field in onboarded_file.fields if field.fact_subtype_id}
    if is_construction or any(
        fact_subtype_id in _CONSTRUCTION_SPECIFIC_FACT_SUBTYPES for fact_subtype_id in fact_subtype_ids
    ):
        onboarded_file, updated_fni = _add_missing_fact_subtypes(
            onboarded_file, _PRACTICE_REQUIRED_FACT_SUBTYPES, fact_subtypes, SubmissionEntityType.PRIMARY_INSURED
        )
        if updated_fni:
            return sort_fields(onboarded_file, _CONSTRUCTION_SPECIFIC_FACT_SUBTYPES)
        onboarded_file, _ = _add_missing_fact_subtypes(
            onboarded_file, _PRACTICE_REQUIRED_FACT_SUBTYPES, fact_subtypes, SubmissionEntityType.BUSINESS
        )
    if Organization.is_k2_for_id(submission.organization_id):
        _populate_with_unknown_aegis_supplemental_facts(fact_subtype_ids, fact_subtypes, onboarded_file, submission_id)
    if Organization.is_cna_for_id(submission.organization_id):
        onboarded_file, _ = _add_missing_fact_subtypes(
            onboarded_file,
            CNA_FACTS_TO_POPULATE,
            fact_subtypes,
            SubmissionEntityType.PRIMARY_INSURED,
        )

    return sort_fields(onboarded_file, _CONSTRUCTION_SPECIFIC_FACT_SUBTYPES)


def _populate_with_unknown_aegis_supplemental_facts(
    fact_subtype_ids: set[FactSubtypeID],
    fact_subtypes: list[FactSubtype],
    onboarded_file: OnboardedFile,
    submission_id: UUID,
) -> None:
    missing_fact_subtype_ids = K2_FACTS_TO_POPULATE - fact_subtype_ids
    missing_facts = [fact for fact in fact_subtypes if fact.id in missing_fact_subtype_ids]
    supplemental_ids = (
        db.session.query(File.id)
        .filter(File.submission_id == submission_id)
        .filter(File.file_type == FileType.SUPPLEMENTAL_FORM)
        .all()
    )
    if not supplemental_ids:
        logger.warning(
            "No supplemental form found for K2 submission, skipping adding of fields",
            submission_id=submission_id,
        )
        return
    for supplemental_id in supplemental_ids:
        try:
            file_idx = onboarded_file.files.index(supplemental_id[0])
        except ValueError:
            continue
        break
    else:
        logger.warning(
            "No supplemental form found in the onboarded file, skipping adding of fields",
            submission_id=submission_id,
        )
        return
    entity_idx = onboarded_file.fni_idx
    if entity_idx is None:
        entity_idx = len(onboarded_file.entities)
        onboarded_file.entities.append(
            SubmissionEntity(
                type=SubmissionEntityType.BUSINESS,
                entity_named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                id="dummy-entity",
            )
        )
    for fact in missing_facts:
        onboarded_file.fields.append(
            ResolvedDataField(
                name=fact.display_name,
                fact_subtype_id=FactSubtypeID.try_parse_str(fact.id),
                value_type=FieldType.TEXT,
                values=[
                    ResolvedDataValue(
                        entity_idx=entity_idx, value=ProbabilityInterpretation.UNKNOWN.value, file_idx=file_idx
                    )
                ],
            )
        )


def sort_fields(onboarded_file: OnboardedFile, order: list[FactSubtypeID]) -> OnboardedFile:
    max_value = len(order) + 1
    onboarded_file.fields = sorted(
        onboarded_file.fields,
        key=lambda x: (order.index(x.fact_subtype_id) if x.fact_subtype_id in order else max_value),
    )
    onboarded_file.additional_data.suggested_fields = sorted(
        onboarded_file.additional_data.suggested_fields,
        key=lambda x: (order.index(x.fact_subtype_id) if x.fact_subtype_id in order else max_value),
    )
    return onboarded_file


def _add_missing_fact_subtypes(
    onboarded_file: OnboardedFile,
    required_subtypes: set[FactSubtypeID],
    all_fact_subtypes: list[FactSubtype],
    entity_type: SubmissionEntityType,
) -> tuple[OnboardedFile, bool]:
    updated = False
    fact_subtype_ids = {field.fact_subtype_id for field in onboarded_file.fields if field.fact_subtype_id}
    suggested_fact_subtypes = {
        suggestion.fact_subtype_id for suggestion in onboarded_file.additional_data.suggested_fields
    }

    entity_idx = next((i for i, v in enumerate(onboarded_file.entities) if v.type == entity_type), None)
    if entity_type == SubmissionEntityType.PRIMARY_INSURED:
        entity_idx = next((i for i, v in enumerate(onboarded_file.entities) if v.is_fni), None)
    elif entity_type == SubmissionEntityType.PROJECT:
        entity_idx = next((i for i, v in enumerate(onboarded_file.entities) if v.is_project), None)
    elif entity_type == SubmissionEntityType.GENERAL_CONTRACTOR:
        entity_idx = next((i for i, v in enumerate(onboarded_file.entities) if v.is_gc), None)

    if entity_idx is not None:
        updated = True
        missing_fact_subtype_ids = required_subtypes - fact_subtype_ids - suggested_fact_subtypes
        missing_fact_subtypes = [
            fact_subtype for fact_subtype in all_fact_subtypes if fact_subtype.id in missing_fact_subtype_ids
        ]
        for missing_fact_subtype in missing_fact_subtypes:
            onboarded_file.additional_data.suggested_fields.append(
                SuggestedField(
                    name=missing_fact_subtype.display_name,
                    fact_subtype_id=FactSubtypeID(missing_fact_subtype.id),
                )
            )
        return onboarded_file, updated
    return onboarded_file, updated


def _create_empty_fact_subtype_list(
    missing_fact_subtypes: list[FactSubtype], entity_idx: int
) -> list[ResolvedDataField]:
    data_fields = []
    for missing_fact_subtype in missing_fact_subtypes:
        _, value_type, unit = resolve_fact_subtype_value_types("", missing_fact_subtype)
        data_fields.append(
            ResolvedDataField(
                fact_subtype_id=FactSubtypeID(missing_fact_subtype.id),
                name=missing_fact_subtype.display_name,
                value_type=value_type,
                unit=unit,
                values=[ResolvedDataValue(entity_idx=entity_idx, value=None, file_idx=None, observed_name=None)],
            )
        )
    return data_fields

from typing import Any
import json

from sqlalchemy.orm.attributes import flag_modified
from static_common.enums.fact_subtype import FactSubtypeID

from copilot.logic.dao.processed_file_dao import ProcessedFileDAO
from copilot.models import db
from copilot.models.experiments import Experiment, ExperimentRun, ExperimentSample
from copilot.models.files import ProcessedFile, SubmissionFilesData

EXPERIMENT_NAME_TO_FACT_SUBTYPE_ID = {
    "payroll": FactSubtypeID.PAYROLL,
    "years_in_business": FactSubtypeID.YEARS_IN_BUSINESS,
    "total_sales": FactSubtypeID.TOTAL_SALES,
}


def recompute_em_experiments():
    em_experiments = list(EXPERIMENT_NAME_TO_FACT_SUBTYPE_ID.keys())

    experiment_runs: list[ExperimentRun] = (
        db.session.query(ExperimentRun)
        .join(ExperimentRun.experiment)
        .filter(Experiment.name.in_(em_experiments))
        .filter(
            ExperimentRun.id.in_(
                db.session.query(ExperimentSample.experiment_run_id)
                .filter(ExperimentSample.is_match.is_(False))
                .filter(ExperimentSample.user_input["value"].astext == "")  # noqa
                .filter(ExperimentSample.user_input["fix_executed"].is_(None))
                .subquery()
            )
        )
        .all()
    )

    for experiment_run in experiment_runs:
        if len(experiment_run.samples) == 0:
            continue
        processed_files = [
            pf
            for pf in ProcessedFileDAO.get_processed_files_for_submission(
                experiment_run.submission_id, ["processed_data", "business_resolution_data"]
            )
            if pf.processed_data and pf.business_resolution_data
        ]

        row_after_pds = (
            db.session.query(SubmissionFilesData.entity_mapped_data)
            .filter(SubmissionFilesData.submission_id == experiment_run.submission_id)
            .first()
        )
        data_after_pds: dict[str, Any] = row_after_pds[0] if row_after_pds else {}

        samples: list[ExperimentSample] = experiment_run.samples
        for sample in samples:
            sample_entity_id = __find_entity_id_for_sample(sample, processed_files)

            # These are all values presented to the user for the entities that ended up having same entity_id
            onboarded_file_entity_ids = __get_fact_values_for_same_entity_id(sample_entity_id, processed_files)

            all_values_after_pds = __get_values_for_onboarded_file_entity_ids(
                onboarded_file_entity_ids,
                EXPERIMENT_NAME_TO_FACT_SUBTYPE_ID[experiment_run.experiment.name],
                data_after_pds,
            )

            __fix_em_sample(all_values_after_pds, sample)


def __fix_em_sample(raw_all_values_after_pds: list[Any], sample: ExperimentSample):
    all_values_after_pds = __normalize_values(raw_all_values_after_pds)
    post_entity_merge_result = None
    should_be_match = False
    if len(all_values_after_pds) == 1:
        post_entity_merge_result = "NO_OTHER_VALUES"
    elif len(all_values_after_pds) > 1:
        normalized_computed = __normalize_number_or_timeseries(sample.computed_value["value"])
        matching_values = 0
        for value in all_values_after_pds:
            if value in normalized_computed:
                matching_values += 1
        if matching_values == 1:
            post_entity_merge_result = "OTHER_DIFFERENT_VALUE_PRESENT"
        elif matching_values > 1:
            post_entity_merge_result = "OTHER_SAME_VALUE_PRESENT"
            should_be_match = True
        else:
            post_entity_merge_result = "VALUE_NOT_FOUND"
    sample.user_input["fix_executed"] = True
    sample.user_input["fix_old_match"] = False
    sample.user_input["fix_found_values"] = all_values_after_pds
    sample.user_input["fix_result"] = post_entity_merge_result
    flag_modified(sample, "user_input")
    sample.is_match = should_be_match
    db.session.commit()


def __find_entity_id_for_sample(sample: ExperimentSample, processed_files: list[ProcessedFile]):
    for processed_file in processed_files:
        entities = processed_file.processed_data.get("entities", [])
        for idx, entity in enumerate(entities):
            if entity.get("id") == sample.value_id:
                rd_data = processed_file.business_resolution_data.get("resolution_data", [])
                for rd_item in rd_data:
                    if rd_item.get("entity_idx") == idx:
                        return rd_item.get("entity_id")
    return None


def __get_fact_values_for_same_entity_id(entity_id: str, processed_files: list[ProcessedFile]):
    onboarded_file_entity_ids = []
    for processed_file in processed_files:
        if not processed_file.business_resolution_data:
            continue
        rd_data = processed_file.business_resolution_data.get("resolution_data", [])
        entity_idxes = []
        for idx, rd_item in enumerate(rd_data):
            if rd_item.get("entity_id") == entity_id:
                entity_idxes.append(idx)

        entities = processed_file.processed_data.get("entities", [])
        for idx in entity_idxes:
            if len(entities) > idx:
                onboarded_file_entity_ids.append(entities[idx].get("id"))

    return onboarded_file_entity_ids


def __get_values_for_onboarded_file_entity_ids(
    onboarded_file_entity_ids: list[str], fact_subtype_id: str, data: dict[str, Any]
):
    fact_values = []
    valid_entity_idxes = []

    if not data:
        return []

    entities = data.get("entities", [])
    for idx, entity in enumerate(entities):
        if entity.get("id") in onboarded_file_entity_ids:
            valid_entity_idxes.append(idx)

    fields = data.get("fields", [])
    for field in fields:
        if field.get("fact_subtype_id") == fact_subtype_id:
            values = field.get("values", [])
            for value in values:
                if value.get("entity_idx", -1) in valid_entity_idxes:
                    fact_values.append(value.get("value", -1))

    return fact_values


def __normalize_number_or_timeseries(value: float | str | int) -> list[float]:
    normalized_as_number = __normalize_number(value)
    if normalized_as_number is not None:
        return [normalized_as_number]
    try:
        ts_value = json.loads(value)
        return ts_value.get("values", [])
    except (ValueError, TypeError):
        return []


def __normalize_number(number: float | str | int) -> float | None:
    try:
        return float(str(number).replace(",", "")) if number is not None and number else 0.0
    except ValueError:
        return None


def __normalize_values(list_of_numbers: list[float | str | int]) -> list[float | None]:
    result = []
    for value in list_of_numbers:
        result.extend(__normalize_number_or_timeseries(value))
    return result

from typing import Any
from urllib import parse
import copy
import json

from copilot.models.reports import (
    RecommendationSubmissionNoteRequest,
    Submission,
    SubmissionNote,
)
from copilot.utils import generate_report_url

TEXT_NODE: dict[str, Any] = {
    "detail": 0,
    "format": 0,
    "mode": "normal",
    "style": "",
    "text": "",  # Insert text here
    "type": "extended-text",
    "version": 1,
}

UW_MENTION_NODE: dict[str, Any] = {
    "detail": 1,
    "format": 0,
    "mode": "segmented",
    "style": "background-color: rgba(24, 119, 232, 0.2)",
    "text": "",  # @user_name (email)
    "type": "mention",
    "version": 1,
    "user_id": 0,  # Insert user_id here
    "email": "",  # Insert email here,
    "user_name": "",  # Insert name here,
}

HYPERLINK_NODE: dict[str, Any] = {
    "children": [
        {
            "detail": 0,
            "format": 0,
            "mode": "normal",
            "style": "",
            "text": "",  # Insert text here
            "type": "extended-text",
            "version": 1,
        }
    ],
    "direction": "ltr",
    "format": "",
    "indent": 0,
    "type": "link",
    "version": 1,
    "rel": "noopener",
    "target": None,
    "title": None,
    "url": "",  # Insert URL here,
}

LINE_BREAK_NODE: dict[str, Any] = {"type": "linebreak", "version": 1}

NOTES_ROOT: dict[str, Any] = {
    "root": {
        "children": [
            {
                "children": [
                    # paragraph nodes here
                ],
                "direction": "ltr",
                "format": "",
                "indent": 0,
                "type": "custom-paragraph",
                "version": 1,
            }
        ],
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "root",
        "version": 1,
    }
}


def generate_note_for_recommendation(request: RecommendationSubmissionNoteRequest) -> SubmissionNote:
    text = request.text

    is_json = '"root"' in text and '"children"' in text
    if not is_json:
        text = __create_note_text(
            [
                __text_node(request.text),
            ]
        )

    return SubmissionNote(
        submission_id=request.submission_id,
        author_id=None,
        is_generated_note=True,
        is_editable=request.is_editable,
        rule_id=request.rule_id,
        text=text,
        hashed_text=hash(request.text),
    )


def generate_note_for_submission_lost(submission_id: str, submission_change: Submission) -> SubmissionNote:
    return SubmissionNote(
        submission_id=submission_id,
        author_id=None,
        is_generated_note=True,
        text=__create_note_text(
            [
                __text_node("Submission marked as Lost, reason:"),
                __new_line(),
                __text_node(", ".join(submission_change.lost_reasons or ["not specified"])),
            ]
        ),
    )


def generate_note_for_submission_declined(
    submission_id: str, submission_change: Submission, author_id: str
) -> SubmissionNote:
    return SubmissionNote(
        submission_id=submission_id,
        author_id=author_id,
        is_generated_note=True,
        text=__create_note_text(
            [
                __text_node("Submission Declined"),
                __new_line(),
                __text_node(f"Reason: {submission_change.reason_for_declining or 'not specified'}"),
            ]
        ),
    )


def generate_note_for_pdf_export_download(submission: Submission, download_url: str) -> SubmissionNote:
    uw_mentions = []
    for au in submission.assigned_underwriters:
        if uw_mentions:
            uw_mentions.append(__text_node(", "))
        uw_mentions.append(__uw_mention_node(au.user_id, au.user.email, au.user.name))

    return SubmissionNote(
        submission_id=submission.id,
        author_id=None,
        is_generated_note=True,
        text=__create_note_text(
            [
                *uw_mentions,
                __text_node(" The "),
                __hyperlink_node("PDF Export", download_url),
                __text_node(" is ready for download."),
            ]
        ),
    )


def generate_note_for_account_review_document(submission: Submission, url: str) -> SubmissionNote:
    uw_mentions = []

    for su in submission.assigned_underwriters:
        if uw_mentions:
            uw_mentions.append(__text_node(", "))
        uw_mentions.append(__uw_mention_node(su.user_id, su.user.email, su.user.name))

    if url.startswith("s3://"):
        parts = url.removeprefix("s3://").split("/", 1)
        key = parts[1]
        extension = key.rsplit(".", 1)[1]
        filename = submission.name.replace(",", "") + "." + extension
        report_url = generate_report_url(submission.report_id)
        report_url = f"{report_url}/document-download?"
        key = parse.quote(key, safe="")
        filename = parse.quote(filename, safe="")
        url = f"{report_url}s3_key={key}&name={filename}"

    return SubmissionNote(
        submission_id=submission.id,
        author_id=None,
        is_generated_note=True,
        text=__create_note_text(
            [
                *uw_mentions,
                __text_node(" The "),
                __hyperlink_node("Review File", url),
                __text_node(" is ready for download."),
            ]
        ),
    )


def __create_note_text(nodes: list[dict[str, Any]]) -> str:
    note = copy.deepcopy(NOTES_ROOT)
    note["root"]["children"][0]["children"] = nodes
    return json.dumps(note)


def __new_line():
    return copy.deepcopy(LINE_BREAK_NODE)


def __text_node(text: str):
    node = copy.deepcopy(TEXT_NODE)
    node["text"] = text
    return node


def __uw_mention_node(user_id: int, email: str, name: str):
    node = copy.deepcopy(UW_MENTION_NODE)
    node["user_id"] = user_id
    node["email"] = email
    node["user_name"] = name
    node["text"] = f"@{name} ({email})"
    return node


def __hyperlink_node(text: str, url: str):
    node = copy.deepcopy(HYPERLINK_NODE)
    node["url"] = url
    node["children"][0]["text"] = text
    return node


def create_text_note(text: str) -> str:
    new_line_splits = text.split("\n")
    children = []
    for i in range(len(new_line_splits)):
        line = new_line_splits[i]
        if line:
            children.append(__text_node(line))
        if i < len(new_line_splits) - 1:
            children.append(__new_line())
    return __create_note_text(children)

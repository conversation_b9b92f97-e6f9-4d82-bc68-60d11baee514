from collections import defaultdict
from collections.abc import Sequence
from typing import Any
from uuid import UUID
import dataclasses
import os

from common.clients.slack_alerter.slack_alert_handler import AlertHandlerPayload
from common.clients.slack_alerter.slack_alert_type import SlackAlertType
from entity_resolution_service_client_v3 import Ad<PERSON><PERSON><PERSON>ult
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import contains_eager, load_only
from sqlalchemy.orm.exc import StaleDataError
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.origin import Origin
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.enums.task_definition_codes import TaskDefinitionCodes
from static_common.enums.underwriters import SubmissionUserSource
from static_common.models.business_resolution_data import ExternalIdentifier
from static_common.models.coverages import CoverageDetails
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataValue,
    SubmissionEntity,
)
from static_common.models.submission_level_data import SourceDetails
from static_common.models.types import StrUUID
from static_common.schemas.coverages import CoverageDetailsSchema
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
from static_common.taxonomies.industry_classification import NAICSMappingType
from structlog.stdlib import BoundLogger

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.constants import NAME_MATCH_THRESHOLD
from copilot.logic.acords import consolidate_acord_data
from copilot.logic.agencies.agent_assigner import auto_assign_agent
from copilot.logic.agencies.base_agent_assigner import AgentInfo
from copilot.logic.coverages.coverages import assign_coverages
from copilot.logic.dao.processed_file_dao import ProcessedFileDAO
from copilot.logic.dao.report_dao import ReportDAO
from copilot.logic.onboarded_files_transformation import (
    calculate_entity_id,
    get_or_create_entity_field_by_name,
    get_or_create_value_for_entity,
    load_processed_data,
)
from copilot.logic.org_groups.org_groups import auto_assign_org_group
from copilot.logic.pds.data_consolidation_process import (
    maybe_finish_consolidation_process,
)
from copilot.logic.pds.submission_fields_consolidation.description_and_naics_consolidator import (
    DescriptionAndNaicsConsolidator,
)
from copilot.logic.pds.submission_fields_consolidation.effective_date_consolidator import (
    EffectiveDateConsolidator,
)
from copilot.logic.pds.submission_fields_consolidation.submission_fields_consolidator import (
    ApiValuesOnlyConsolidator,
    FilterOutFileTypesConsolidator,
    SubmissionFieldsConsolidationContext,
    SubmissionFieldsConsolidator,
    TakeAllValuesConsolidator,
    TakeFirstValueConsolidator,
)
from copilot.logic.taxonomy import update_submission_naics_if_needed
from copilot.logic.users.underwriter_assigner import UnderwriterAssignerConfig
from copilot.logic.users.users import assign_underwriters, auto_assign_underwriters
from copilot.models import Organization, Submission, User, db
from copilot.models.files import File, OnboardedFileEntityInformation, ProcessedFile
from copilot.models.reports import Coverage
from copilot.models.submission_consolidation_process import (
    SubmissionConsolidationProcess,
)
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.types import (
    BrokerageEmployeeSource,
    CoverageType,
    PermissionType,
    SubmissionConsolidationStatus,
    SubmissionCoverageSource,
)
from copilot.v3.utils.submission import handle_updating_naics_code

logger = get_logger()


def get_consolidation_lock_name(submission_id: UUID) -> str:
    return f"consolidate_data_{submission_id}"


def consolidate_acords_and_em_files(
    submission: Submission, consolidation_process: SubmissionConsolidationProcess
) -> OnboardedFileEntityInformation | None:
    log = logger.bind(submission_id=submission.id)

    em_files_for_data_consolidation = [
        f
        for f in submission.files
        if f.classification in ClassificationDocumentType.data_consolidation_classifications()
        and f.processing_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION
        and f.file_type != FileType.ACORD_FORM
    ]
    exists_consolidated_em_files = any(
        f
        for f in submission.files
        if f.classification in ClassificationDocumentType.data_consolidation_classifications()
        and f.processing_state in FileProcessingState.already_consolidated_states()
        and f.file_type != FileType.ACORD_FORM
    )
    is_acord_data_consolidated, named_insureds = consolidate_acord_data(
        submission=submission,
        always_return_ni=bool(em_files_for_data_consolidation),
        ers_client=current_app.ers_client_v3,
    )
    if acord_fni := _get_fni_from_named_insureds(submission, named_insureds):
        _update_submission_fni_state(submission, acord_fni)
    if not em_files_for_data_consolidation:
        consolidation_process.is_em_and_acord_files_consolidated = (
            is_acord_data_consolidated or exists_consolidated_em_files
        )
        return acord_fni

    fields_to_load = ["processed_data", "file_id"]
    em_files_data: list[ProcessedFile] = (
        db.session.query(ProcessedFile)
        .options(load_only(*fields_to_load))
        .filter(ProcessedFile.file_id.in_([f.id for f in em_files_for_data_consolidation]))
        .all()
    )
    fni = _consolidate_em_files_data(em_files_data, acord_fni, named_insureds, submission, log)
    for f in em_files_for_data_consolidation:
        f.processing_state = FileProcessingState.DATA_CONSOLIDATED
    try:
        db.session.commit()
    except StaleDataError as e:
        logger.warning("Got stale data error, one or more files were removed from the submission", error=str(e))
        db.session.rollback()
        consolidation_process.is_em_and_acord_files_consolidated = False
        return None
    consolidation_process.is_em_and_acord_files_consolidated = True
    return fni


def _get_fni_from_named_insureds(
    submission: Submission, named_insureds: list[OnboardedFileEntityInformation]
) -> OnboardedFileEntityInformation | None:
    if not named_insureds:
        return None
    classification_map = {f.id: f.classification for f in submission.files}
    fnis = [ei for ei in named_insureds if ei.is_fni]
    if not fnis:
        return None
    fnis.sort(key=lambda ei: ClassificationDocumentType.get_consolidation_priority(classification_map[ei.file_id]))
    return fnis[0]


def _update_submission_fni_state(submission: Submission, fni: OnboardedFileEntityInformation) -> None:
    if not fni.address:
        return
    try:
        address = fni.address.replace("\n", " ")
        fni_address_result: AddressResult = current_app.ers_client_v3.get_address(address)
        if not fni_address_result or not fni_address_result.premises or not fni_address_result.premises.state:
            logger.warning(
                "FNI premises state not found",
                submission_id=submission.id,
                fni_address=address,
                fni_address_result=fni_address_result,
            )
            return
        submission.fni_state = fni_address_result.premises.state
    except Exception as e:
        logger.warning(
            "Failed to get FNI premises state", submission_id=submission.id, fni_address=fni.address, error=str(e)
        )


def _consolidate_em_files_data(
    files: list[ProcessedFile],
    fni: OnboardedFileEntityInformation | None,
    named_insureds: list[OnboardedFileEntityInformation] | None,
    submission: Submission,
    log: BoundLogger,
) -> OnboardedFileEntityInformation | None:
    loaded_files = {f.file_id: load_processed_data(f) for f in files}
    processed_files = {f.file_id: f for f in files}
    should_extract_fni_from_em_files = False
    # As we set oni only in ACORD 125, there shouldn't be any duplicates
    onis = [ei for ei in named_insureds if ei.entity.is_oni and ei.address]
    if not fni:
        log.info("No FNI found in ACORDS")
        should_extract_fni_from_em_files = True
    elif not fni.name:
        log.info("ACORD FNI name is empty, not populating address value")
        should_extract_fni_from_em_files = True
    files_to_consolidate = loaded_files
    if should_extract_fni_from_em_files:
        fni, files_to_consolidate = _extract_fni_from_em_files(loaded_files)
        if fni and fni.address:
            _update_submission_fni_state(submission, fni)
        if not files_to_consolidate:
            log.info("All EM files have FNI")
            return fni
    if not fni:
        log.info("No FNI found in EM files")
        return fni
    for file_id in files_to_consolidate:
        processed_data: OnboardedFile = loaded_files[file_id]
        if processed_data.is_empty:
            continue
        fni_idx, has_fni = _get_fni_entity_index(processed_data)
        if fni_idx is None and has_fni:
            continue
        if fni_idx is None and has_fni is False:
            fni_idx = len(processed_data.entities)
            processed_data.entities.append(
                SubmissionEntity(
                    type=SubmissionEntityType.BUSINESS,
                    entity_named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                    id=fni.entity_id,
                )
            )

            # Assign the extracted data to the FNI
            for f in processed_data.fields:
                for val in f.values:
                    if val.entity_idx is None:
                        val.entity_idx = fni_idx

        name_value, address_value = _get_fni_name_and_address(processed_data, fni_idx)

        if not has_fni or not name_value.value:
            logger.info(
                "FNI not found in EM file or missing data, using one from ACORD",
                file_id=file_id,
                has_fni=has_fni,
                name=name_value.value,
                address=address_value.value,
                fni_data_to_be_use=fni,
            )
            name_value.value = fni.name
            name_value.external_file_id = fni.file_id
            address_value.value = fni.address
            address_value.external_file_id = fni.file_id
            _add_external_identifiers(processed_data, fni_idx, fni.identifiers, fni.file_id)
            processed_data.entities[fni_idx].id = calculate_entity_id(
                name=name_value.value, address=address_value.value
            )
            processed_file = processed_files[file_id]
            processed_file.processed_data = LeanOnboardedFileSchema().dump(processed_data)
            db.session.add(processed_file)
            continue
        existing_identifiers = processed_data.get_external_identifiers().get(fni_idx, [])
        potential_names = [fni.name] + [oni.name for oni in onis if oni.name]
        if len(potential_names) < len(onis) + 1:
            logger.warning(
                "There is one or more ONIs without name",
                submission_id=submission.id,
            )
        if not (
            name_score_result := current_app.ers_client_v3.get_entity_names_score(
                name=name_value.value, potential_names=potential_names, enforce_flexible_in_name_value=True
            )
        ):
            continue
        above_threshold = [r.name.value for r in name_score_result if r.score > NAME_MATCH_THRESHOLD]
        if len(above_threshold) > 1:
            logger.info(
                "More than 1 name above threshold",
                file_id=file_id,
                name=name_value.value,
                above_threshold=above_threshold,
            )
            if fni.name in above_threshold:
                if _has_conflicting_unique_identifiers(existing_identifiers, fni.identifiers):
                    logger.warning(
                        "Conflicting identifiers found",
                        file_id=file_id,
                        existing_identifiers=existing_identifiers,
                        new_identifiers=fni.identifiers,
                    )
                    continue
                logger.info("FNI name is above threshold, updating name", file_id=file_id)
                name_value.value = fni.name
                name_value.external_file_id = fni.file_id
                if not address_value.value:
                    address_value.value = fni.address
                    address_value.external_file_id = fni.file_id
                processed_data.entities[fni_idx].id = calculate_entity_id(
                    name=name_value.value, address=address_value.value
                )
            else:
                logger.warning(
                    "FNI name not above threshold, but multiple ONIs are",
                    file_id=file_id,
                    name=name_value.value,
                    above_threshold=above_threshold,
                )
                continue
        elif len(above_threshold) == 1:
            logger.info(
                "Name above threshold",
                file_id=file_id,
                name=name_value.value,
                above_threshold=above_threshold[0],
            )
            if above_threshold[0] == fni.name:
                if _has_conflicting_unique_identifiers(existing_identifiers, fni.identifiers):
                    logger.warning(
                        "Conflicting identifiers found",
                        file_id=file_id,
                        existing_identifiers=existing_identifiers,
                        new_identifiers=fni.identifiers,
                    )
                    continue
                _add_external_identifiers(processed_data, fni_idx, fni.identifiers, fni.file_id)
                if address_value.value:
                    continue
                logger.info("FNI name is above threshold, updating address", file_id=file_id)
                address_value.value = fni.address
                address_value.external_file_id = fni.file_id
                processed_data.entities[fni_idx].id = calculate_entity_id(
                    name=name_value.value, address=address_value.value
                )
            else:
                logger.warning(
                    "FNI name not above threshold, but ONI is",
                    file_id=file_id,
                    name=name_value.value,
                    above_threshold=above_threshold,
                )
                oni = next(oni for oni in onis if oni.name == above_threshold[0])
                if _has_conflicting_unique_identifiers(existing_identifiers, oni.identifiers):
                    logger.warning(
                        "Conflicting identifiers found",
                        file_id=file_id,
                        existing_identifiers=existing_identifiers,
                        new_identifiers=oni.identifiers,
                    )
                    continue
                _add_external_identifiers(processed_data, fni_idx, oni.identifiers, oni.file_id)
                if not address_value.value and oni.address:
                    address_value.value = oni.address
                    address_value.external_file_id = oni.file_id
                processed_data.entities[fni_idx].type = SubmissionEntityType.BUSINESS
                processed_data.entities[fni_idx].entity_named_insured = (
                    SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED
                )
                processed_data.entities[fni_idx].id = calculate_entity_id(
                    name=name_value.value, address=address_value.value
                )
        else:
            logger.info(
                "FNI name is different than ACORD name, not populating address value",
                file_id=file_id,
                em_name=name_value.value,
                acord_name=fni.name,
                reason=name_score_result[0].reason,
                score=name_score_result[0].score,
            )
        processed_file = processed_files[file_id]
        processed_file.processed_data = LeanOnboardedFileSchema().dump(processed_data)
        db.session.add(processed_file)
        return fni


def _get_fni_entity_index(processed_data: OnboardedFile) -> tuple[int | None, bool]:
    fni_idxs = [idx for idx, entity in enumerate(processed_data.entities) if entity.is_fni]
    has_fni = False
    fni_idx = None
    if len(fni_idxs) > 1:
        # Based on the current implementation both Email and Supplemental forms has exactly 1 FNI,
        # so this shouldn't be possible
        logger.error("More than 1 FNI found", file_id=processed_data.files[0], fni_idxs=fni_idxs)
        has_fni = True
        return None, has_fni
    if len(fni_idxs) == 1:
        fni_idx = fni_idxs[0]
        has_fni = True
    return fni_idx, has_fni


def _extract_fni_from_em_files(
    files: dict[UUID, OnboardedFile],
) -> tuple[OnboardedFileEntityInformation | None, list[UUID]]:
    files_without_fni = []
    fnis = []
    for file_id, processed_data in files.items():
        fni_idx, has_fni = _get_fni_entity_index(processed_data)
        if not has_fni:
            files_without_fni.append(file_id)
            continue
        name_value, address_value = _get_fni_name_and_address(processed_data, fni_idx)
        external_identifiers = processed_data.get_external_identifiers()
        if name_value.value and address_value.value:
            fni_entity = processed_data.entities[fni_idx]
            fnis.append(
                OnboardedFileEntityInformation(
                    entity_id=fni_entity.id,
                    entity_type=fni_entity.type,
                    entity=fni_entity,
                    name=name_value.value,
                    address=address_value.value,
                    file_id=file_id,
                    identifiers=external_identifiers.get(fni_idx, []),
                )
            )
            continue
        files_without_fni.append(file_id)
    fni = None
    if len(fnis) == 1:
        fni = fnis[0]
    elif len(fnis) > 1:
        # Not sure what is the scenario here so we just log and will revisit based on the logs.
        # I expect that this will be extremely rare eitherway and when it happens most probably
        # we don't have anything to do
        logger.info("More than 1 FNI found in EM files", fni_names=[f.name for f in fnis])
    return fni, files_without_fni


def _get_fni_name_and_address(
    processed_data: OnboardedFile, fni_idx: int
) -> tuple[ResolvedDataValue, ResolvedDataValue]:
    name_field = get_or_create_entity_field_by_name(processed_data, EntityFieldID.NAME.value, FieldType.TEXT)
    name_value = get_or_create_value_for_entity(name_field, fni_idx, 0)
    address_field = get_or_create_entity_field_by_name(processed_data, EntityFieldID.ADDRESS.value, FieldType.TEXT)
    address_value = get_or_create_value_for_entity(address_field, fni_idx, 0)
    return name_value, address_value


def _add_external_identifiers(
    processed_data: OnboardedFile, fni_idx: int, identifiers: list[ExternalIdentifier], file_id: UUID
) -> None:
    for identifier in identifiers:
        identifier_field = get_or_create_entity_field_by_name(processed_data, identifier.type.value, FieldType.TEXT)
        identifier_resolved_value = get_or_create_value_for_entity(identifier_field, fni_idx, 0)
        identifier_resolved_value.value = identifier.value
        identifier_resolved_value.external_file_id = file_id
    return None


def _has_conflicting_unique_identifiers(
    list_of_identifiers: list[ExternalIdentifier], other: list[ExternalIdentifier]
) -> bool:
    if not list_of_identifiers or not other:
        return False
    unique_identifiers = {
        i.type: i.value for i in list_of_identifiers if i.type in ExternalIdentifierType.unique_external_identifiers()
    }
    other_unique_identifiers = {
        i.type: i.value for i in other if i.type in ExternalIdentifierType.unique_external_identifiers()
    }
    for identifier, value in unique_identifiers.items():
        if identifier in other_unique_identifiers and other_unique_identifiers[identifier] != value:
            return True
    return False


def finish_naics_and_description_consolidation(
    submission: Submission,
    consolidation_process: SubmissionConsolidationProcess,
    selected_naics_id: str | None,
    selected_naics_value: str | None,
    selected_description_id: str | None,
    selected_description_value: str | None,
) -> None:
    consolidation_process.is_description_and_naics_consolidated = True

    extracted_submission_level_data = {}
    if selected_naics_value:
        extracted_submission_level_data[EntityInformation.NAICS_CODES] = selected_naics_value
    if selected_description_value:
        extracted_submission_level_data[EntityInformation.DESCRIPTION] = selected_description_value

    submission_level_data_rows = (
        SubmissionLevelExtractedData.query.filter(
            SubmissionLevelExtractedData.submission_id == submission.id
            and SubmissionLevelExtractedData.field in [EntityInformation.DESCRIPTION, EntityInformation.NAICS_CODES]
        )
        .with_for_update()
        .all()
    )

    for submission_level_data in submission_level_data_rows:
        if str(submission_level_data.id) in [selected_naics_id, selected_description_id]:
            submission_level_data.is_selected = True
            submission_level_data.selected_by_user = current_user.email
        else:
            submission_level_data.is_selected = False
            submission_level_data.selected_by_user = None

    _process_submission_fields(submission, extracted_submission_level_data)
    _process_naics_data(submission, extracted_submission_level_data, consolidation_process)
    _start_naics_mappings_generation(submission, selected_naics_id, consolidation_process)


def _can_skip_consolidation_submission_level_data(submission: Submission) -> bool:
    if any(f for f in submission.files if f.processing_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION):
        return False
    previous_consolidation_process = SubmissionConsolidationProcess.query.filter(
        SubmissionConsolidationProcess.submission_id == submission.id,
        SubmissionConsolidationProcess.status.in_(
            [SubmissionConsolidationStatus.COMPLETED, SubmissionConsolidationStatus.NOT_READY]
        ),
    ).first()
    return previous_consolidation_process is not None


def consolidate_submission_level_data(
    submission: Submission, consolidation_process: SubmissionConsolidationProcess
) -> None:
    # set the flags to True, if there is no NAICS we won't start the process to generate this data
    # this is later set to False when we start async NAICS generation
    consolidation_process.is_description_and_naics_consolidated = True
    consolidation_process.is_gl_code_generated = True
    consolidation_process.is_sic_code_generated = True

    if _can_skip_consolidation_submission_level_data(submission):
        return
    consolidation_context = SubmissionFieldsConsolidationContext(
        submission=submission,
        extracted_data_items={},
        extracted_data_collections=defaultdict(list),
        consolidation_process=consolidation_process,
    )
    default_consolidator: SubmissionFieldsConsolidator = TakeFirstValueConsolidator(consolidation_context)
    consolidators = defaultdict(lambda: default_consolidator)
    if submission.is_boss:
        consolidators[EntityInformation.UNDERWRITER_USER_IDS] = ApiValuesOnlyConsolidator(consolidation_context)
        consolidators[EntityInformation.UNDERWRITER_EMAILS] = ApiValuesOnlyConsolidator(consolidation_context)
    consolidators[EntityInformation.COVERAGES] = FilterOutFileTypesConsolidator(
        consolidation_context,
        {
            FileType.EMAIL,
            FileType.COVER_SHEET,
        },
    )
    consolidators[EntityInformation.COVERAGES_DETAILS] = TakeAllValuesConsolidator(
        consolidation_context, reverse_order=True
    )
    consolidators[EntityInformation.POLICY_EFFECTIVE_START_DATE] = EffectiveDateConsolidator(consolidation_context)

    submission_level_data = (
        SubmissionLevelExtractedData.query.outerjoin(File)
        .options(contains_eager(SubmissionLevelExtractedData.file))
        .filter(SubmissionLevelExtractedData.submission_id == submission.id)
        .all()
    )
    field_submission_level_data = defaultdict(list)
    for field_data in submission_level_data:
        field_submission_level_data[field_data.field].append(field_data)

    fields_for_task_based_consolidation = [EntityInformation.NAICS_CODES, EntityInformation.DESCRIPTION]

    for field, submission_data in field_submission_level_data.items():
        if field not in fields_for_task_based_consolidation:
            consolidators[field].resolve_conflicts(submission_data)
            consolidators[field].consolidate(submission_data)

    if EntityInformation.NAICS_CODES in field_submission_level_data:
        consolidator = DescriptionAndNaicsConsolidator(consolidation_context, sources_to_skip=[SourceDetails.WEBSITE])
        submission_data = field_submission_level_data[EntityInformation.NAICS_CODES]
        submission_data.extend(field_submission_level_data.get(EntityInformation.DESCRIPTION, []))
        consolidator.resolve_conflicts(submission_data)
        consolidator.consolidate(submission_data)

    extracted_data = {key: value[0] for key, value in consolidation_context.extracted_data_items.items()}
    if not extracted_data and not consolidation_context.extracted_data_collections:
        return

    _process_submission_fields(submission, extracted_data)
    _process_naics_data(submission, extracted_data, consolidation_context.consolidation_process)

    if FeatureFlagsClient.is_feature_enabled(FeatureType.USE_BROKER_RESOLVER):
        incoming_broker_id = extracted_data.get(EntityInformation.BROKER_ID)
        incoming_correspondence_contact_id = extracted_data.get(EntityInformation.CORRESPONDENCE_CONTACT_ID)
        incoming_brokerage_id = extracted_data.get(EntityInformation.BROKERAGE_ID)

        # not technically correct. This needs to be inferred from the source of the data
        source = BrokerageEmployeeSource.API if submission.is_boss else BrokerageEmployeeSource.EMAIL

        _process_broker_and_brokerage_information(
            incoming_broker_id,
            incoming_correspondence_contact_id,
            incoming_brokerage_id,
            submission,
            source,
        )
    elif EntityInformation.BROKER_EMAIL in extracted_data or (not submission.brokerage_id and submission.is_ally_auto):
        _process_broker_information(
            broker_email=extracted_data.get(EntityInformation.BROKER_EMAIL),
            broker_name=extracted_data.get(EntityInformation.BROKER_NAME),
            brokerage_name=extracted_data.get(EntityInformation.BROKERAGE_NAME),
            submission=submission,
        )

    if submission.origin not in [Origin.SYNC, Origin.API]:
        for value, submission_level_data_item in consolidation_context.extracted_data_collections[
            EntityInformation.COVERAGES
        ]:
            # TODO(ENG-23355): this should no longer be needed, for now we first set coverages and then add details
            _process_coverages(
                submission,
                submission_level_data_item.file_id,
                value,
                submission_level_data_item.file.file_type,
            )

        # reversing here is important as the highest priority data is in the front
        for value, submission_level_data_item in consolidation_context.extracted_data_collections[
            EntityInformation.COVERAGES_DETAILS
        ]:
            process_coverages_details(
                submission,
                submission_level_data_item.file_id,
                value,
                submission_level_data_item.file.file_type,
            )

    # TODO(ENG-23355): it is important that this is done after the coverages are processed
    # we currently process the excess limit and attachment point into multiple structures:
    # EXCESS_LIMIT and EXCESS_ATTACHMENT from ACORDS and they are only used for liability excess coverage
    # COVERAGE_DETAILS from emails and they are used for all applicable coverages
    # the ordering ensures that ACORD data overwrites the email one if it's present
    # once ENG-23355 is done we can get rid of this
    if any(key in extracted_data for key in [EntityInformation.EXCESS_LIMIT, EntityInformation.EXCESS_ATTACHMENT]):
        _process_excess_data(
            extracted_data.get(EntityInformation.EXCESS_LIMIT),
            extracted_data.get(EntityInformation.EXCESS_ATTACHMENT),
            submission,
        )

    if EntityInformation.UNDERWRITER_USER_IDS in extracted_data and submission.origin != Origin.SYNC:
        _process_uw_information(
            uw_user_ids=extracted_data[EntityInformation.UNDERWRITER_USER_IDS], submission=submission
        )

    uw_config = UnderwriterAssignerConfig(
        raise_exceptions=False, should_share=True, already_assigned_error=False, delete_other_assigned=False
    )
    auto_assign_underwriters(submission, uw_config, skip_if_assigned=True, processing_email=True)
    auto_assign_org_group(submission)
    return


def _process_submission_fields(submission: Submission, submission_level_data: dict) -> None:
    mapping = {
        EntityInformation.DESCRIPTION: "generated_description_of_operations",
        EntityInformation.PROJECT_DESCRIPTION: "email_project_description",
        EntityInformation.TARGET_PREMIUM: "target_premium",
        EntityInformation.EXPIRING_PREMIUM: "expired_premium",
        EntityInformation.POLICY_EFFECTIVE_START_DATE: "proposed_effective_date",
        EntityInformation.POLICY_END_DATE: "policy_expiration_date",
        EntityInformation.IS_RENEWAL: "is_renewal",
    }
    try:
        for field, property_name in mapping.items():
            if field in submission_level_data:
                description_of_operations_before = getattr(submission, property_name)
                if (
                    field == EntityInformation.DESCRIPTION
                    and description_of_operations_before != submission_level_data[field]
                ):
                    current_app.slack_alerter.send_alert(
                        alert_type=SlackAlertType.DescriptionOfOperationsUpdated,
                        payload=AlertHandlerPayload(
                            organization_id=submission.organization_id,
                            submission_id=str(submission.id),
                            report_id=str(submission.report_id),
                            description_of_operations_before_update=description_of_operations_before,
                            description_of_operations_after_update=submission_level_data[field],
                        ),
                    )
                setattr(submission, property_name, submission_level_data[field])
        db.session.commit()
    except:
        db.session.rollback()
        logger.exception("Failed to update submission with submission level data")


def _process_naics_data(
    submission: Submission, submission_level_data: dict, consolidation_process: SubmissionConsolidationProcess
) -> None:
    if not consolidation_process.is_description_and_naics_consolidated:
        return
    if submission.is_naics_verified and ReportDAO.get_parent_processing_dependency(submission.report_id) is not None:
        return
    naics_verified_before_update = submission.is_naics_verified
    primary_naics_code_before_update = submission.primary_naics_code
    sic_code_before_update = submission.sic_code

    if naics_code := submission_level_data.get(EntityInformation.NAICS_CODES):
        update_submission_naics_if_needed(submission, naics_code)
        submission.is_naics_verified = True

    if os.environ.get("KALEPA_ENV", None) == "stage" and submission.is_boss and submission.primary_naics_code is None:
        import random
        import re

        from static_common.taxonomies.industry_classification import NaicsCode

        codes: list[NaicsCode] = [n for n in NaicsCode if re.match(r"NAICS_[0-9]{6}", n)]  # type: ignore
        random_naics: NaicsCode = random.choice(codes)
        update_submission_naics_if_needed(submission, random_naics.value)
        submission.is_naics_verified = True

    if not submission.primary_naics_code and (
        submission.is_boss
        or (
            Organization.is_triage_processing_enabled_for_id(submission.organization_id)
            and not submission.is_processing_enabled
        )
    ):
        submission.manual_naics_assignment_required = True

    db.session.commit()

    if submission.is_naics_verified:
        handle_updating_naics_code(
            submission,
            naics_verified_before_update,
            primary_naics_code_before_update,
            sic_code_before_update,
        )


def _start_naics_mappings_generation(
    submission: Submission,
    naics_id: StrUUID | None,
    consolidation_process: SubmissionConsolidationProcess,
) -> None:
    if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
        mapping_types = [NAICSMappingType.ISO_GL_CODE]

        if Organization.is_cna_for_id(submission.organization_id):
            mapping_types.append(NAICSMappingType.SIC_5_CODE)
        else:
            mapping_types.append(NAICSMappingType.SIC_CODE)

        for mt in mapping_types:
            current_app.workflows_client.invoke_execute_task(
                task_code=TaskDefinitionCodes.EXTRACT_NAICS_MAPPING,
                organization_id=submission.organization_id,
                submission_id=submission.id,
                context={
                    "consolidation_process_id": str(consolidation_process.id),
                    "naics_code": submission.primary_naics_code,
                    "description": (
                        submission.generated_description_of_operations or submission.description_of_operations
                    ),
                    "naics_id": naics_id,
                    "mapping_type": mt.value,
                },
            )
    else:
        consolidation_process.is_gl_code_generated = True
        consolidation_process.is_sic_code_generated = True
        maybe_finish_consolidation_process(submission, consolidation_process)


def _process_excess_data(limit: float | None, attachment_point: float | None, submission: Submission) -> None:
    excess_coverage = None
    try:
        for submission_coverage in submission.coverages:
            if (
                submission_coverage.coverage.name == Coverage.ExistingNames.Liability
                and submission_coverage.coverage_type == CoverageType.EXCESS
            ):
                excess_coverage = submission_coverage
                break

        if not excess_coverage:
            logger.warning(
                "No excess coverage found when assigning limit and attachment point", submission_id=submission.id
            )
            return

        if not FeatureFlagsClient.is_feature_enabled(
            FeatureType.ENABLE_LIMIT_AND_ATTACHMENT_FROM_EMAILS, submission.user.email
        ):
            logger.info(
                "Assigning limit and attachment point are disabled",
                limit=limit,
                attachment_point=attachment_point,
                submission_id=submission.id,
            )
            return

        if limit:
            excess_coverage.limit = limit

        if attachment_point:
            excess_coverage.attachment_point = attachment_point

        db.session.add(excess_coverage)
        db.session.commit()
    except:
        db.session.rollback()
        logger.exception("Failed to update submission with limit and attachment point")


def _process_broker_and_brokerage_information(
    broker_id: StrUUID | None,
    correspondence_contact_id: StrUUID | None,
    brokerage_id: StrUUID | None,
    submission: Submission,
    source: BrokerageEmployeeSource = BrokerageEmployeeSource.EMAIL,
):
    try:
        if brokerage_id:
            submission.set_brokerage_id(brokerage_id, source)
        if broker_id:
            submission.set_broker_id(broker_id, source)
        if correspondence_contact_id:
            submission.set_brokerage_contact_id(correspondence_contact_id, source)
        db.session.add(submission)
        db.session.commit()
    except:
        db.session.rollback()
        logger.exception("Failed to update submission with broker and brokerage details")


def _process_broker_information(
    broker_email: str | None, broker_name: str | None, brokerage_name: str | None, submission: Submission
) -> None:
    if not broker_name:
        logger.warning("Missing optional data - broker name", submission_id=submission.id)

    agent_info = AgentInfo(agent_name=broker_name, agent_email=broker_email, agency_name=brokerage_name)
    auto_assign_agent(submission, agent_info)


def _process_coverages(
    submission: Submission,
    file_id: UUID,
    coverages: Sequence[tuple[str, str]],
    file_type: FileType,
) -> None:
    for coverage_info in coverages:
        try:
            coverage_name, coverage_type = coverage_info

            if file_type == FileType.EMAIL:
                source = SubmissionCoverageSource.EMAIL
            else:
                source = SubmissionCoverageSource.FILE

            assign_coverages(
                submission_id=submission.id,
                requested_coverage_name=coverage_name,
                requested_coverage_type=coverage_type,
                allow_no_coverages=True,
                source=source,
                source_details=f"Created from processing results for {file_type} file {file_id!s}",
            )
            db.session.commit()
        except:
            db.session.rollback()
            logger.exception("Failed to update submission with coverage details")


def process_coverages_details(submission: Submission, file_id: UUID, coverages: str, file_type: FileType) -> None:
    coverages: list[CoverageDetails] = CoverageDetailsSchema().loads(coverages, many=True)
    for coverage_info in coverages:
        try:
            if file_type == FileType.EMAIL:
                source = SubmissionCoverageSource.EMAIL
            else:
                source = SubmissionCoverageSource.FILE

            submission_coverages = assign_coverages(
                submission_id=submission.id,
                requested_coverage_name=coverage_info.coverage_name.value,
                requested_coverage_type=(
                    CoverageType.try_parse_str(coverage_info.coverage_type.value)
                    if coverage_info.coverage_type
                    else None
                ),
                allow_no_coverages=True,
                source=source,
                source_details=f"Created from processing results for {file_type} file {file_id!s}",
            )
            for sc in submission_coverages or []:
                sc.self_insurance_retention = coverage_info.self_insurance_retention or sc.self_insurance_retention
                sc.attachment_point = coverage_info.excess_attachment_point or sc.attachment_point
                sc.limit = coverage_info.limit or sc.limit
            db.session.commit()
        except:
            db.session.rollback()
            logger.exception("Failed to update submission with coverage details")


def _process_uw_information(uw_user_ids: list[int], submission: Submission) -> None:
    for user_id in uw_user_ids:
        uw = User.query.get(user_id)
        if uw is None:
            logger.warning("Underwriter with given id does not exist", user_id=user_id)
            continue
        sharing_permission = PermissionType.OWNER
        if uw.is_read_only_account:
            sharing_permission = PermissionType.VIEWER
        try:
            assign_underwriters(
                submission,
                [user_id],
                sharing_permission=sharing_permission,
                config=UnderwriterAssignerConfig(
                    already_assigned_error=False,
                ),
                source=SubmissionUserSource.EMAIL,
            )
        except:
            logger.exception("Failed to update submission with underwriter details")


@dataclasses.dataclass(frozen=True)
class CFSFieldKey:
    fact_subtype_id: FactSubtypeID
    interval: str
    entity_id: str | None


class FinancialStatementConsolidator:
    def __init__(self, submission: Submission, consolidation_process: SubmissionConsolidationProcess) -> None:
        self._submission = submission
        self._consolidation_process = consolidation_process
        self._submission_files: list[File] = []
        self._submission_files_map: dict[UUID, File] = {}
        self._log = logger.bind(submission_id=submission.id)
        self._processed_data_map: dict[UUID, OnboardedFile] = {}
        self._consolidated_files: set[UUID] = set()

    @staticmethod
    def _normalise_entity_name(name: str) -> str:
        return name.strip().lower().replace(".", "").replace(",", "")

    def _get_entity_name_maps(self, onboarded_file: OnboardedFile) -> tuple[dict[int, str], dict[str, str]]:
        entity_id_to_idx_map: dict[int, str] = {}
        entity_id_to_name_map: dict[str, str] = {}
        name_field = next((f for f in onboarded_file.entity_information if f.name == EntityFieldID.NAME), None)
        if name_field:
            entity_id_to_idx_map = {v.entity_idx: self._normalise_entity_name(v.value) for v in name_field.values}
            entity_id_to_name_map = {self._normalise_entity_name(v.value): v.value for v in name_field.values}
        return entity_id_to_idx_map, entity_id_to_name_map

    def _init_vars(self) -> None:
        self._submission_files = self._submission.files
        self._submission_files_map = {f.id: f for f in self._submission_files}
        self._cfs_file_ids = {
            f.id
            for f in self._submission.files
            if f.classification in ClassificationDocumentType.files_with_names_only_extracted()
            and f.processing_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION
        }

    def _update_processing_state(self) -> bool:
        self._log.info("Updating processing states")
        files_to_update = self._consolidated_files | self._cfs_file_ids
        result = (
            db.session.query(File)
            .where(File.id.in_(files_to_update))
            .update({File.processing_state: FileProcessingState.DATA_CONSOLIDATED}, synchronize_session=False)
        )
        db.session.commit()
        return bool(result)

    def _extract_entities(self) -> tuple[dict, dict[UUID, dict[int, bool]]]:
        processed_files = ProcessedFileDAO.get_processed_files_for_submission(
            submission_id=self._submission.id, additional_fields=["processed_data", "file_id"]
        )
        entity_info_map = {}
        entity_named_insured_map = {}
        for pf in processed_files:
            if not pf.processed_data:
                continue
            processed_data_obj = load_processed_data(pf)
            self._processed_data_map[pf.file_id] = processed_data_obj
            file_map = defaultdict(dict)
            entities_map = {idx: e for idx, e in enumerate(processed_data_obj.entities)}
            for entity_info in processed_data_obj.entity_information:
                for value in entity_info.values:
                    if value.entity_idx is None:
                        self._log.warning(
                            "Entity value has no entity index",
                            file_id=pf.file_id,
                            field=entity_info.name,
                            value=value.value,
                        )
                        continue
                    if entities_map[value.entity_idx].type not in SubmissionEntityType.resolvable_entities():
                        continue
                    file_map[value.entity_idx][entity_info.name] = value.value
            entity_info_map[pf.file_id] = file_map
            entity_named_insured_map[pf.file_id] = {idx: e.is_fni for idx, e in enumerate(processed_data_obj.entities)}
        return entity_info_map, entity_named_insured_map

    @staticmethod
    def _add_value(field_name: str, value: Any, entity_idx: int, processed_data: OnboardedFile, file_id: UUID) -> None:
        field = get_or_create_entity_field_by_name(processed_data, field_name, FieldType.TEXT)
        resolved_value = get_or_create_value_for_entity(field, entity_idx, 0)
        resolved_value.value = value
        resolved_value.external_file_id = file_id

    def _consolidate_entities(self, fni: OnboardedFileEntityInformation | None) -> None:
        if not self._cfs_file_ids:
            self._log.info("No consolidated financial statements to consolidate entities")
            return
        entity_info_map, entity_named_insured_map = self._extract_entities()
        entities_to_choose_from: dict[str, tuple] = {}
        fni_info_from_files = self._get_best_fni_form_files(entity_info_map, entity_named_insured_map)

        for file_id, entity_map in entity_info_map.items():
            for entity_idx, entity_info in entity_map.items():
                # We want to have at least both name and address to consider the entity
                if not entity_info.get(EntityFieldID.NAME.value) or not entity_info.get(EntityFieldID.ADDRESS.value):
                    continue
                entry = (file_id, entity_idx, entity_named_insured_map[file_id][entity_idx])
                # If there is already entry for the entity name, we want to keep the FNI
                if (
                    existing_entry := entities_to_choose_from.get(entity_info[EntityFieldID.NAME.value])
                ) and existing_entry[2]:
                    entry = existing_entry
                entities_to_choose_from[entity_info[EntityFieldID.NAME.value]] = entry
        potential_names = list(entities_to_choose_from.keys())
        if not potential_names:
            self._log.info("No entities to consolidate to, skipping")
            return
        updated_files = set()
        for file_id in self._cfs_file_ids:
            if file_id not in entity_info_map:
                self._log.info(
                    "No entities found for file, won't consolidate entities",
                    file_id=file_id,
                    submission_id=self._submission.id,
                )
                continue
            file_map = entity_info_map[file_id]
            for entity_idx, entity_info in file_map.items():
                if entity_info.get(EntityFieldID.ADDRESS.value):
                    continue
                entity_name = entity_info.get(EntityFieldID.NAME.value)
                is_fni = entity_named_insured_map[file_id][entity_idx]
                if not entity_name and is_fni and fni:  # no name lets get it from FNI
                    for resolved_data_field in fni.entity_information:
                        if resolved_data_field.values and resolved_data_field.values[0]:
                            self._add_value(
                                resolved_data_field.name,
                                resolved_data_field.values[0].value,
                                entity_idx,
                                self._processed_data_map[file_id],
                                file_id,
                            )
                    _add_external_identifiers(
                        self._processed_data_map[file_id], entity_idx, fni.identifiers, fni.file_id
                    )
                    updated_files.add(file_id)
                    continue
                if not entity_name and is_fni and fni_info_from_files:
                    for field_name, field_value in fni_info_from_files.items():
                        self._add_value(field_name, field_value, entity_idx, self._processed_data_map[file_id], file_id)
                    updated_files.add(file_id)
                    continue
                if not entity_name:
                    self._log.warn(
                        "No name found for entity, skipping",
                        file_id=file_id,
                        entity_idx=entity_idx,
                        entity_info=entity_info,
                        is_fni=is_fni,
                        fni=fni,
                        fni_info_from_files=fni_info_from_files,
                    )
                    continue
                if not (
                    name_score_result := current_app.ers_client_v3.get_entity_names_score(
                        name=entity_name, potential_names=potential_names, enforce_flexible_in_name_value=True
                    )
                ):
                    continue
                above_threshold = [r.name.value for r in name_score_result if r.score > NAME_MATCH_THRESHOLD]
                if len(above_threshold) > 1:
                    self._log.info(
                        "Found multiple names above threshold",
                        file_id=file_id,
                        name=entity_name,
                        above_threshold=above_threshold,
                    )
                    # If there are multiple names above threshold,
                    # we filter those that are FNIs while keeping them ordered on score
                    if above_threshold_named_insureds := [
                        name for name in above_threshold if entities_to_choose_from[name][2]
                    ]:
                        above_threshold = above_threshold_named_insureds
                if not above_threshold:
                    self._log.info("No matching names found", file_id=file_id, name=entity_name)
                    continue
                matched_entity_file_id, matched_entity_entity_idx, is_fni = entities_to_choose_from.get(
                    above_threshold[0]
                )
                if not is_fni:
                    self._log.info(
                        "Matched to not FNI entity", file_id=file_id, name=entity_name, matched_name=above_threshold[0]
                    )
                matched_entity = entity_info_map[matched_entity_file_id][matched_entity_entity_idx]
                for field_name, field_value in matched_entity.items():
                    if field_name == EntityFieldID.NAME.value:
                        continue
                    self._add_value(field_name, field_value, entity_idx, self._processed_data_map[file_id], file_id)
                    updated_files.add(file_id)
        processed_files = db.session.query(ProcessedFile).filter(ProcessedFile.file_id.in_(updated_files)).all()
        for pf in processed_files:
            processed_data = self._processed_data_map[pf.file_id]
            pf.processed_data = LeanOnboardedFileSchema().dump(processed_data)
            db.session.add(pf)
            self._consolidated_files.add(pf.file_id)
        db.session.commit()

    def _get_best_fni_form_files(
        self, entity_info_map: dict, entity_named_insured_map: dict[UUID, dict[int, bool]]
    ) -> dict:
        fni_info_from_files = {}
        try:
            for file_id, mapping in entity_named_insured_map.items():
                for entity_idx, is_fni in mapping.items():
                    if not is_fni:
                        continue
                    if not entity_info_map[file_id][entity_idx].get(EntityFieldID.NAME.value):
                        continue  # useless
                    if not fni_info_from_files:
                        fni_info_from_files = entity_info_map[file_id][entity_idx]
                        continue
                    more_field = len(fni_info_from_files) < len(entity_info_map[file_id][entity_idx])
                    has_address_when_current_not = entity_info_map[file_id][entity_idx].get(
                        EntityFieldID.ADDRESS.value
                    ) and not fni_info_from_files.get(EntityFieldID.ADDRESS.value)
                    if more_field or has_address_when_current_not:
                        fni_info_from_files = entity_info_map[file_id][entity_idx]
        except Exception as e:
            self._log.error(
                "Failed to get best FNI form files. Error but not critical. WIll use (if any) fein found so far",
                error=str(e),
                fni_found_so_far=fni_info_from_files,
            )
        return fni_info_from_files

    def consolidate(self, fni: OnboardedFileEntityInformation | None) -> None:
        self._log.info("Consolidating financial statements", fni=fni)
        self._init_vars()
        try:
            self._log.info("Consolidating entities")
            self._consolidate_entities(fni)
        except Exception as e:
            db.session.rollback()
            self._log.error("Failed to consolidate entities", error=str(e))
        self._consolidation_process.is_financial_statements_consolidated = self._update_processing_state()

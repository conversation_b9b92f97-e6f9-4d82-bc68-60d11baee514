from uuid import UUID

from copilot.models import SubmissionActionType, SubmissionParentType
from copilot.models.submission_history import track_submission_history_event


def after_commit_track_submission_history(
    submission_id: UUID | str,
    action_type: SubmissionActionType,
    additional_data: dict,
    parent_id: UUID | str | None,
    parent_type: SubmissionParentType | None,
    session,
    logger,
) -> None:
    track_submission_history_event(
        submission_id,
        action_type,
        additional_data,
        db_session=session,
        parent_id=parent_id,
        parent_type=parent_type,
    )

from marshmallow import EXCLUDE
from marshmallow_enum import En<PERSON><PERSON><PERSON>
from marshmallow_sqlalchemy import auto_field
from static_common.enums.submission import (
    SubmissionRelationSource,
    SubmissionRelationType,
)

from copilot.models.submission_relations import SubmissionRelation
from copilot.schemas.base import BaseSchema


class SubmissionRelationSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionRelation
        unknown = EXCLUDE

    id = auto_field(required=False)
    created_at = auto_field(required=False)
    updated_at = auto_field(required=False)
    from_submission_id = auto_field()
    to_submission_id = auto_field()
    type = EnumField(SubmissionRelationType, by_value=True, required=True, allow_none=False)
    reason = auto_field()
    confidence = auto_field()
    is_resolved = auto_field()
    is_light = auto_field()
    is_active = auto_field()
    source = EnumField(SubmissionRelationSource, by_value=True, required=False, allow_none=True)

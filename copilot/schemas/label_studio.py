from marshmallow import EXCLUDE, Schema, fields, post_load
from marshmallow_enum import <PERSON>um<PERSON>ield
from marshmallow_sqlalchemy import auto_field
from static_common.enums.file_processing_state import FileProcessingState

from copilot.models.label_studio import (
    FileLabelStudio,
    FilePageDetailsRequest,
    FilePageProcessedRequest,
    FileSendToLabelStudioRequest,
)
from copilot.schemas.base import BaseSchema


class FileLabelStudioSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = FileLabelStudio
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    file_id = auto_field()
    total_pages = auto_field()
    page_num = auto_field()
    status = EnumField(FileProcessingState, by_value=True)
    image_s3_key = auto_field()
    task_id = auto_field(required=False, allow_none=True)
    processed_data = auto_field(required=False, allow_none=True)


class FilePageDetailsRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    page_num = fields.Integer()
    image_s3_key = fields.String()
    task_id = fields.Integer()

    # noinspection PyUnusedLocal
    @post_load
    def make_object(self, data, **kwargs) -> FilePageDetailsRequest:
        return FilePageDetailsRequest(**data)


class FileSendToLabelStudioRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    file_id = fields.UUID()
    total_pages = fields.Integer()
    pages = fields.Nested(FilePageDetailsRequestSchema, many=True)

    # noinspection PyUnusedLocal
    @post_load
    def make_object(self, data, **kwargs) -> FileSendToLabelStudioRequest:
        return FileSendToLabelStudioRequest(**data)


class FilePageProcessedRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    file_id = fields.UUID()
    page_num = fields.Integer()
    processed_data = fields.List(fields.Dict)

    # noinspection PyUnusedLocal
    @post_load
    def make_object(self, data, **kwargs) -> FilePageProcessedRequest:
        return FilePageProcessedRequest(**data)

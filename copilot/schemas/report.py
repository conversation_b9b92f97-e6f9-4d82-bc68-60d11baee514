from dataclasses import dataclass
from dataclasses import field as _field
from datetime import datetime
from typing import Any
from uuid import UUID
import re

from dataclasses_json import dataclass_json
from entity_resolution_service_client_v3 import ExternalIdentifierRequest
from flask_login import current_user
from infrastructure_common.logging import get_logger
from marshmallow import (
    EXCLUDE,
    Schema,
    ValidationError,
    fields,
    post_dump,
    post_load,
    pre_load,
    validates_schema,
)
from marshmallow_enum import Enum<PERSON>ield
from marshmallow_sqlalchemy import auto_field
from static_common.enums.additional_identifier import AdditionalIdentifierType
from static_common.enums.contractor import ContractorSubmissionType
from static_common.enums.coverage import CoverageGroup
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.insurance import ProjectInsuranceType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.recommendation import RecommendationActionEnum
from static_common.enums.underwriters import SubmissionUserSource
from static_common.schemas.first_party import FirstP<PERSON>yFieldsSchema

from copilot.logic.dao.report_dao import ReportDAO
from copilot.models.reports import (
    AdditionalIdentifier,
    ClientSubmissionStageConfig,
    Coverage,
    CreateOrReplaceNoteRequest,
    CreateReportRequest,
    ExternalCustomStatus,
    ExternalCustomStatusPart,
    ExternalReportsResponse,
    NAICSCode,
    RecommendationSubmissionNoteRequest,
    ReportSummaryPreference,
    ReportV2,
    SaveSubmissionNoteOptions,
    Submission,
    SubmissionBusiness,
    SubmissionClearingIssue,
    SubmissionClearingSubStatus,
    SubmissionClientId,
    SubmissionCoverage,
    SubmissionDeductible,
    SubmissionIdentifier,
    SubmissionNote,
    SubmissionPremises,
    SubmissionPriority,
    SubmissionRecommendationResult,
    SubmissionUser,
    SubmissionUserRequest,
    Subscription,
    SummaryPreference,
    UserSubmissionNotification,
)
from copilot.models.types import (
    PermissionType,
    ReportStatus,
    SubmissionCoverageType,
    SubmissionPremisesType,
    SubmissionStage,
)
from copilot.models.user import UserGroup
from copilot.schemas.base import BaseSchema
from copilot.schemas.brokerages import BrokerageEmployeeSchema, BrokerageSchema
from copilot.schemas.emails import EmailSchema
from copilot.schemas.ers import (
    ERSEntitySchema,
    ERSExternalIdentifierSchema,
    ERSRelationSchema,
)
from copilot.schemas.files import FileSchema
from copilot.schemas.permissions import ReportPermissionSchema
from copilot.schemas.requested_properties import RequestedPropertiesSchema
from copilot.schemas.stuck_details import StuckDetailsSchema
from copilot.schemas.underlying_policy import UnderlyingPolicySchema
from copilot.schemas.workers_comp_experience import (
    WorkersCompExperienceSchema,
    WorkersCompStateRatingInfoSchema,
)
from copilot.v1.schemas import UserGroupSchema, UserSchema

logger = get_logger()


class SummaryPreferenceSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SummaryPreference

    id = auto_field()
    display_name = auto_field()
    group_display_name = auto_field()
    is_default = auto_field()
    icon_name = auto_field()
    metric_type = auto_field()

    # noinspection PyUnusedLocal
    @post_dump(pass_many=True)
    def wrap_with_envelope(self, data, many, **kwargs):
        if many:
            data = {"summary_preferences": data}
        return data


class ReportSummaryPreferenceSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ReportSummaryPreference

    summary_preference_id = auto_field()

    ENVELOPE_KEY = "report_summary_preferences"

    # noinspection PyUnusedLocal
    @post_dump(pass_many=True)
    def wrap_with_envelope(self, data, many, **kwargs):
        if many:
            data = {self.ENVELOPE_KEY: data}
        return data

    # noinspection PyUnusedLocal
    @pre_load(pass_many=True)
    def unwrap_envelope(self, data, many, **kwargs):
        if many:
            return data[self.ENVELOPE_KEY]
        return data


class ExternalIdentifierRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    type = EnumField(ExternalIdentifierType)
    value = fields.String()

    # noinspection PyUnusedLocal
    @post_load()
    def make_object(self, data, **kwargs):
        return ExternalIdentifierRequest(**data)


class ResolutionResultSchema(BaseSchema):
    business_id = fields.Str()
    premises_id = fields.Str()
    name = fields.Str()
    address = fields.Str()
    latitude = fields.Float()
    longitude = fields.Float()
    industries = fields.List(fields.Str())
    is_closed = fields.Bool()
    closed_at = fields.DateTime()
    aliases = fields.List(fields.Str())
    other_addresses = fields.List(fields.Str())
    identifiers = fields.Nested(ERSExternalIdentifierSchema, many=True, allow_none=True)
    relations_to = fields.Nested(ERSRelationSchema, many=True, allow_none=True)
    relations_from = fields.Nested(ERSRelationSchema, many=True, allow_none=True)


class SubmissionBusinessSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionBusiness
        unknown = EXCLUDE

    id = auto_field(allow_none=True)
    created_at = auto_field()
    updated_at = auto_field(required=False, allow_none=True)
    requested_name = auto_field(required=False, allow_none=True)
    requested_address = auto_field(required=False)
    requested_phone_number = auto_field(required=False, allow_none=True)
    requested_industries = fields.List(fields.Str(), required=False, allow_none=True)
    requested_identifiers = fields.List(
        fields.Nested(ExternalIdentifierRequestSchema), allow_none=True, required=False, load_only=True
    )
    business_id = fields.UUID(required=False, allow_none=True)
    is_user_confirmed = auto_field(required=False)
    aliases = fields.List(fields.Str(), required=False, allow_none=True)
    selected_alias = fields.Str(required=False, allow_none=True)
    submission_id = fields.UUID(allow_none=True)
    resolution_results = fields.List(fields.Nested(ResolutionResultSchema), dump_only=True)
    entity_data = fields.Nested(ERSEntitySchema, allow_none=True, dump_only=True)
    resolution_confidence = fields.Str(required=False, dump_only=True)
    force_automatch = fields.Boolean(missing=False)
    expand_address_range = fields.Boolean(missing=False)
    entity_role = auto_field(required=False, allow_none=True)
    named_insured = auto_field(required=False, allow_none=True)
    project_insurance_type = auto_field(required=False, allow_none=True)
    description_of_operations = auto_field(required=False, allow_none=True)
    ers_snapshot_id = auto_field(required=False, allow_none=True)
    hide_property_facts = auto_field(required=False, allow_none=True)
    hide = auto_field(required=False, allow_none=True)
    additional_info = auto_field(required=False, allow_none=True)
    file_id_sources = auto_field(required=False, allow_none=True)

    @pre_load
    def remove_forbidden_characters(self, data, **kwargs):
        keys = ["requested_name", "requested_address", "requested_phone_number", "selected_alias"]
        for key in keys:
            if not data.get(key):
                continue
            if "\x00" in data[key]:
                data[key] = data[key].replace("\x00", "")
        return data

    @post_dump
    def cleanup_fields(self, data, **kwargs):
        if not data.get("submission_id"):
            for field in data.get("fields", []):
                keys = list(field.keys())
                for key in keys:
                    if key not in ["orig_value", "value", "remote_id", "parent_id"]:
                        del field[key]
        return data


class CoverageSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = Coverage

    id = auto_field()
    name = auto_field(required=False)
    display_name = auto_field(required=False)
    is_disabled = auto_field()
    coverage_types = auto_field()
    logical_group = fields.Enum(enum=CoverageGroup, required=False)
    groups = fields.Dict(dump_only=True)

    @post_dump(pass_many=True)
    def wrap_with_envelope(self, data, many, **kwargs):
        if many:
            data = {"coverages": data}
        return data


class SubmissionCoverageSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionCoverage
        unknown = EXCLUDE

    id = auto_field()
    estimated_premium = auto_field()
    is_quoted = fields.Boolean(load_default=False)
    quoted_premium = auto_field()
    bound_premium = auto_field()
    total_premium = auto_field()
    rating_premium = auto_field()
    total_premium_or_bound_premium = fields.Float(dump_only=True, required=False, nullable=True)
    submission_id = fields.UUID()
    limit = auto_field()
    attachment_point = auto_field()
    self_insurance_retention = auto_field()
    coverage_id = auto_field(load_only=True, required=False)
    coverage = fields.Nested(CoverageSchema, required=False)
    coverage_type = auto_field()
    period = auto_field()
    other_terms = auto_field()
    additional_data = auto_field()
    stage = auto_field()

    @pre_load
    def replace_null_additional_data_with_default(self, data, **kwargs):
        if data.get("additional_data") is None:
            data["additional_data"] = {}
        return data


class SubmissionDeductibleSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionDeductible
        unknown = EXCLUDE

    id = auto_field()
    coverage_id = auto_field(load_only=True, required=False)
    submission_id = fields.UUID()
    policy_limit = auto_field()
    policy_level = auto_field()
    policy_level_type = auto_field()
    minimum = auto_field()
    comment = auto_field()
    coverage = fields.Nested(CoverageSchema, required=False)
    coverage_type = auto_field()


class SubmissionHistoryElementSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    report_id = fields.UUID(dump_only=True)
    report_name = fields.String(dump_only=True)
    effective_date = fields.DateTime(dump_only=True)


class LossSummarySchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    year = fields.String()
    count = fields.Integer()
    sum_of_total_net_incurred = fields.Float()


class NAICSCodeSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = NAICSCode
        unknown = EXCLUDE

    id = auto_field()
    title = auto_field()
    description = auto_field()


class SubmissionClearingSubStatusSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionClearingSubStatus
        unknown = EXCLUDE

    id = auto_field()
    status = auto_field()
    sub_status = auto_field()
    submission_clearing_issue_id = auto_field()


class SubmissionClearingIssueSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionClearingIssue
        unknown = EXCLUDE

    id = auto_field()
    submission_id = auto_field(required=False)
    suspected_report_id = auto_field(required=False)
    is_resolved = auto_field(required=False)
    is_light = auto_field(required=False)

    clearing_sub_statuses = fields.Nested(SubmissionClearingSubStatusSchema, many=True, required=False, allow_none=True)
    reason = auto_field(required=False)
    suspected_report_name = fields.String(required=False)
    report_broker_id = fields.String(required=False, allow_none=True)
    report_broker_name = fields.String(required=False, allow_none=True)
    report_broker_email = fields.String(required=False, allow_none=True)
    report_brokerage_name = fields.String(required=False, allow_none=True)
    report_client_stage_name = fields.String(required=False, allow_none=True)
    report_created_date = fields.DateTime(required=False)
    report_effective_date = fields.DateTime(required=False)
    report_stage = fields.Enum(enum=SubmissionStage, dump_only=True)
    report_is_auto_processed = fields.Boolean(required=False, dump_only=True)
    report_coverages = fields.List(fields.Nested(SubmissionCoverageSchema), required=False, dump_only=True)
    report_origin = fields.String(required=False, allow_none=True, dump_only=True)
    report_has_email = fields.Boolean(required=False, dump_only=True)
    report_is_deleted = fields.Boolean(required=False, dump_only=True)


class SubmissionClientIdSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionClientId
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    client_submission_id = auto_field()
    submission_id = fields.UUID()
    source = auto_field(required=False)


class SubmissionPrioritySchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionPriority
        unknown = EXCLUDE

    id = auto_field()
    submission_id = fields.UUID(allow_none=True)
    escalated_at = auto_field()
    removed_from_auto_assign = auto_field()
    overwritten_priority = auto_field()


class ClientSubmissionStageConfigSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ClientSubmissionStageConfig
        unknown = EXCLUDE

    id = auto_field()
    organization_id = auto_field()
    client_stage = auto_field()
    tags = auto_field()
    tag_labels = auto_field()
    copilot_stage = auto_field()
    with_comment = auto_field()
    not_selectable = auto_field()


class SubmissionIdentifierSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionIdentifier
        unknown = EXCLUDE

    id = auto_field()
    submission_id = fields.UUID(allow_none=True)
    identifier = auto_field()
    identifier_type = auto_field()


class SubmissionPremisesSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionPremises
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field()
    named_insured = auto_field()
    address = auto_field()
    submission_id = fields.UUID()
    premises_id = fields.UUID(allow_none=True)
    submission_premises_type = EnumField(SubmissionPremisesType, by_value=True)
    additional_data = fields.Dict(allow_none=True)
    organization_id = auto_field()

    @post_dump(pass_many=True)
    def wrap_with_envelope(self, data, many, **kwargs):
        if many:
            data = {"premises_data": data}
        return data


class SetSubmissionIdentifiersRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    quote_number = fields.String(required=False, allow_none=True)
    policy_number = fields.String(required=False, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        result = []
        if "quote_number" in data:
            result.append(
                AdditionalIdentifier(type=AdditionalIdentifierType.QUOTE_NUMBER, identifier=data["quote_number"])
            )
        if "policy_number" in data:
            result.append(
                AdditionalIdentifier(type=AdditionalIdentifierType.POLICY_NUMBER, identifier=data["policy_number"])
            )
        return result


class DeleteSubmissionIdentifierRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    identifier_type = fields.String(required=True)
    value = fields.String(required=True)

    @post_load
    def make_object(self, data, **kwargs):
        return AdditionalIdentifier(type=data["identifier_type"], identifier=data["value"])


class SubmissionRecommendationResultSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionRecommendationResult
        unknown = EXCLUDE

    action = EnumField(RecommendationActionEnum, allow_none=True)
    priority = auto_field(required=False)
    is_refer = auto_field(required=False)
    score = auto_field(required=False)
    score_ml = auto_field(required=False)
    pm_rules_modifier = auto_field(required=False)


class SubmissionSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = Submission
        unknown = EXCLUDE

    id = auto_field()
    account_name = auto_field()
    created_at = auto_field()
    updated_at = auto_field()
    name = auto_field(required=False)
    owner_id = auto_field(required=False)
    stage = auto_field(required=False)
    proposed_effective_date = auto_field(required=False)
    policy_expiration_date = auto_field(required=False)
    due_date = auto_field(required=False)
    received_date = auto_field(required=False)
    files = fields.List(fields.Nested(FileSchema, exclude=["presigned_url"]))
    stage_details = auto_field(required=False)
    frozen_as_of = fields.DateTime(required=False, allow_none=True, dump_only=True)
    history = fields.Nested(SubmissionHistoryElementSchema, many=True, dump_only=True)
    coverages = fields.Nested(SubmissionCoverageSchema, many=True)
    underlying_policies = fields.Nested(UnderlyingPolicySchema, many=True)
    deductibles = fields.Nested(SubmissionDeductibleSchema, many=True)
    businesses = fields.Nested(SubmissionBusinessSchema, many=True)
    recommendation_v2_action = EnumField(RecommendationActionEnum, allow_none=True)
    recommendation_v2_priority = auto_field(required=False)
    recommendation_v2_is_refer = auto_field(required=False)
    recommendation_v2_score = auto_field(required=False)
    recommendation_result = fields.Nested(SubmissionRecommendationResultSchema, required=False, allow_none=True)
    description_of_operations = auto_field(required=False)
    generated_description_of_operations = auto_field(required=False)
    email_description = auto_field(required=False)
    email_project_description = auto_field(required=False)
    is_renewal = auto_field(required=False)
    expected_premium = fields.Float(dump_only=True)
    target_premium = fields.Float(required=False, allow_none=True)
    expired_premium = fields.Float(required=False, allow_none=True)
    sales = fields.Float(required=False, allow_none=True)
    renewal_creation_date = auto_field(dump_only=True)
    report_ids = fields.List(fields.UUID)
    grouped_first_party_fields = fields.Nested(FirstPartyFieldsSchema, required=False, allow_none=True)
    clearing_issues = fields.List(fields.Nested(SubmissionClearingIssueSchema))
    primary_naics_code = auto_field(required=False)
    iso_gl_code = auto_field(required=False)
    sic_code = auto_field(required=False)
    icc_code = auto_field(required=False)
    lob_id = auto_field(required=False)
    light_cleared = auto_field(required=False)
    line_of_business = fields.Str(required=False)
    broker_id = auto_field(required=False)
    broker = fields.Nested(BrokerageEmployeeSchema, required=False, allow_none=True)
    brokerage_id = auto_field(required=False)
    brokerage = fields.Nested(BrokerageSchema, required=False, allow_none=True)
    brokerage_contact_id = auto_field(required=False)
    brokerage_contact = fields.Nested(BrokerageEmployeeSchema, required=False, allow_none=True)
    declined_user_id = auto_field(required=False)
    declined_date = auto_field(required=False)
    reason_for_declining = auto_field(required=False)
    parent_id = auto_field(required=False)
    mode = auto_field(required=False)
    coverage_type = auto_field(required=False)
    has_unresolved_clearing_issues = fields.Boolean(required=False)
    is_naics_verified = auto_field(required=False)
    is_verified = auto_field(required=False)
    is_auto_verified = auto_field(required=False)
    is_manual_verified = auto_field(required=False)
    verified_at = auto_field("_verified_at", required=False)
    is_verification_required = auto_field(required=False)
    is_metrics_set_manually = auto_field(required=False)
    processing_state = auto_field("_processing_state", required=False)
    assigned_underwriters = fields.Method("get_assigned_underwriters", dump_only=True)
    clearing_assignee_id = auto_field(required=False)
    client_submission_ids = fields.Nested(
        SubmissionClientIdSchema(only=["client_submission_id"]),
        many=True,
        required=False,
    )
    is_bookmarked = fields.Method("get_is_bookmarked", dump_only=True)
    lost_reasons = fields.List(fields.Str(), required=False, allow_none=True)
    notes = auto_field(required=False)
    send_decline_email = fields.Boolean(allow_none=True)
    decline_email_tracking_id = fields.UUID(allow_none=True)
    decline_email_delivered = fields.Boolean(allow_none=True)
    active_email_template_id = fields.String(allow_none=True)
    decline_email_recipient_address = fields.String(allow_none=True)
    decline_email_cc = auto_field(required=False)
    decline_custom_template = auto_field(required=False)
    decline_custom_subject = auto_field(required=False)
    decline_attachments = auto_field(required=False)
    account_id = auto_field(required=False, allow_none=True)
    is_auto_processed = fields.Boolean(allow_none=True)
    is_processing = fields.Boolean(allow_none=True)
    is_waiting_for_auto_verify = fields.Boolean(allow_none=True)
    origin = EnumField(Origin, by_value=True, allow_none=True)
    stuck_reason = fields.String(attribute="_stuck_reason", allow_none=True)
    is_stuck_engineering = auto_field("_is_stuck_engineering", required=False, allow_none=True)
    pds_special_note = auto_field(required=False, allow_none=True)
    is_for_audit = auto_field(required=False, allow_none=True)
    audited_at = auto_field(required=False, allow_none=True)
    audited_by = auto_field(required=False, allow_none=True)
    is_verified_shell = auto_field(required=False, allow_none=True)
    priority = fields.Nested(
        SubmissionPrioritySchema(only=["escalated_at", "removed_from_auto_assign"]),
        many=False,
        required=False,
    )
    manual_naics_assignment_required = auto_field(required=False, allow_none=True)
    was_read = fields.Method("get_was_read", dump_only=True)
    missing_data_status = auto_field(required=False, allow_none=True)
    missing_documents = auto_field(required=False, allow_none=True)
    report_is_deleted = fields.Boolean(allow_none=True, required=False, dump_only=True)
    sent_rule_email = auto_field(required=False, allow_none=True)
    stuck_details = fields.List(fields.Nested(StuckDetailsSchema), required=False, allow_none=True, many=True)
    quoted_date = auto_field(required=False)
    bound_date = auto_field(required=False)
    has_unique_submission_client_id = fields.Boolean(required=False, allow_none=True, dump_only=True)
    has_been_synced = fields.Boolean(required=False, allow_none=True, dump_only=True)
    brokerage_contact_or_broker_name = fields.String(required=False, allow_none=True)
    brokerage_contact_or_broker_email = fields.String(required=False, allow_none=True)
    is_escalated = auto_field(required=False)
    contractor_submission_type = EnumField(ContractorSubmissionType, by_value=True, allow_none=True, required=False)
    project_insurance_type = EnumField(ProjectInsuranceType, by_value=True, allow_none=True, required=False)
    incumbent_carrier = auto_field(required=False, allow_none=True)
    fni_state = auto_field(required=False, allow_none=True)
    workers_comp_experience = fields.Nested(
        WorkersCompExperienceSchema(deduplicate=True), required=False, allow_none=True, many=True
    )
    workers_comp_rating_info = fields.Nested(
        WorkersCompStateRatingInfoSchema, required=False, allow_none=True, many=True
    )
    is_enhanced_shell = auto_field(required=False, allow_none=True)
    fni_fein = auto_field(required=False, allow_none=True)
    adjusted_tiv = auto_field(required=False, allow_none=True)
    organization_group = fields.String(required=False, allow_none=True)
    is_brokerage_set_by_machine_user = fields.Boolean(required=False, allow_none=True)
    is_broker_agent_set_by_machine_user = fields.Boolean(required=False, allow_none=True)
    is_broker_correspondence_contact_set_by_machine_user = fields.Boolean(required=False, allow_none=True)
    broker_group = fields.String(required=False, allow_none=True, dump_only=True)
    client_stage_id = auto_field(required=False, allow_none=True)
    client_stage = fields.Nested(
        ClientSubmissionStageConfigSchema, required=False, allow_none=True, dump_only=True, many=False
    )
    identifiers = fields.Nested(SubmissionIdentifierSchema, many=True, required=False, allow_none=True)
    primary_state = auto_field(required=False, allow_none=True)
    client_stage_comment = auto_field(required=False, allow_none=True)
    tiv = auto_field(required=False, allow_none=True)
    client_clearing_status = auto_field(required=False, allow_none=True)
    clearing_status = auto_field(required=False, allow_none=True)
    cleared_at = fields.DateTime(required=False, dump_only=True, allow_none=True)
    brokerage_office = auto_field(required=False, allow_none=True)
    sub_producer_name = auto_field(required=False, allow_none=True)
    sub_producer_email = auto_field(required=False, allow_none=True)
    prevent_clearing_updates = auto_field(required=False, allow_none=True)
    client_recommendations_score = auto_field(required=False, allow_none=True)
    facts_config = auto_field(required=False, allow_none=True)
    clearing_sub_statuses = fields.Nested(SubmissionClearingSubStatusSchema, many=True, required=False, allow_none=True)
    policy_status = auto_field(required=False, allow_none=True)
    agent_code = fields.String(required=False, allow_none=True)
    agency_code = fields.String(required=False, allow_none=True)
    is_stub = fields.Boolean(required=False, allow_none=True)

    @post_dump(pass_original=True)
    def modify_account_id(self, data: dict, submission: Any, **kwargs) -> dict:
        if not submission.use_internal_account_id:
            return data
        has_shared_fni = ReportDAO.has_shared_fni(submission.report)
        if has_shared_fni:
            data["account_id"] = f"business-intersection-with:{submission.report.id}"
        return data

    @post_dump(pass_original=True)
    def dont_return_hidden_businesses(self, data: dict, submission: Any, **kwargs) -> dict:
        if not data.get("businesses") or not submission.businesses:
            return data
        data["businesses"] = [business for business in data["businesses"] if not business.get("hide")]
        return data

    @post_dump(pass_original=True)
    def dont_return_internal_or_deleted_files(self, data: dict, submission: Any, **kwargs) -> dict:
        if (
            (hasattr(current_user, "is_kalepa") and current_user.is_kalepa_internal)
            or "files" not in data
            or (hasattr(current_user, "email") and current_user.email == "<EMAIL>")
        ):
            return data

        data["files"] = [file for file in data["files"] if not file["is_internal"]]
        data["files"] = [file for file in data["files"] if not file.get("is_deleted")]
        return data

    @post_dump(pass_original=True)
    def hide_s3_key_unless_requested(self, data: dict, submission: Any, **kwargs) -> dict:
        if "files" in data and current_user.is_internal_machine_user and self.context.get("include_s3_key") is True:
            return data
        for file in data.get("files", []):
            if "s3_key" in file:
                del file["s3_key"]
        return data

    def get_is_bookmarked(self, submission: Submission) -> bool:
        return any([bookmark for bookmark in submission.bookmarks if bookmark.user_id == current_user.id])

    def get_was_read(self, submission: Submission) -> bool:
        return any(read for read in submission.read_by_users if read.user_id == current_user.id)

    def get_assigned_underwriters(self, submission: Submission) -> list[UserSchema]:
        list = []
        assigned_uw_schema = AssignedUnderwriterSchema()
        for su in submission.assigned_underwriters:
            submission_user = assigned_uw_schema.dump(su.user)
            submission_user["source"] = su.source
            list.append(submission_user)
        return list

    # noinspection PyUnusedLocal
    @post_load
    def set_default_name(self, submission: Submission, **kwargs) -> Submission:
        if not submission.name:
            if submission.files:
                submission.name = submission.files[0].name
            elif submission.businesses:
                name = None
                for submission_business in submission.businesses:
                    if submission_business.requested_name:
                        name = submission_business.requested_name
                        break
                if not name:
                    submission.name = f"Real Estate: {submission.businesses[0].requested_address}"
            else:
                submission.name = "My Submission"
        return submission

    @validates_schema
    def validates_m2m_update(self, data, **kwargs) -> None:
        ALLOWED_UPDATES = {"stage", "stage_details", "coverages", "client_stage_id", "client_stage_comment"}

        if not current_user.is_internal_machine_user and current_user.is_machine_user:
            if data.get("stage") and not SubmissionStage.is_user_independent(data.get("stage")):
                raise ValidationError(f"Submission cannot be updated to stage {data['stage']}")

            disallowed_properties = [key for key in data.keys() if key not in ALLOWED_UPDATES]
            if disallowed_properties:
                raise ValidationError([f"{property} cannot be updated" for property in disallowed_properties])


class SubmissionSnapshotSchema(SubmissionSchema):
    @validates_schema
    def validates_m2m_update(self, data, **kwargs):
        """
        The snapshot can be assumed to be valid.
        """
        pass


class ReportRedirectSchema(BaseSchema):
    redirect_url = fields.Str()


class ReportCorrespondenceSchema(BaseSchema):
    emails = fields.Nested(EmailSchema, many=True)


class ReportV2Schema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ReportV2
        unknown = EXCLUDE

    id = auto_field(dump_only=True)
    owner_id = fields.Int()
    name = fields.Str(required=False)
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    submissions = fields.List(fields.Nested(SubmissionSchema))
    is_deleted = fields.Bool()
    is_archived = auto_field()
    is_rush = auto_field()
    organization_permission_level = EnumField(PermissionType, by_value=False, allow_none=True)
    permissions = fields.Method("include_permissions", dump_only=True)
    current_user_permission_type = fields.Str(required=False, dump_only=True, allow_none=True)
    url = fields.Method("get_report_url", dump_only=True)
    links = fields.Method("get_links", dump_only=True)
    bundled_reports = fields.Method("get_bundle", dump_only=True)
    zendesk_ticket_id = fields.Str(required=False, dump_only=True)
    full_pds = fields.Bool(required=False, dump_only=True)
    email_body = fields.Str(required=False, allow_none=True)
    email_subject = fields.Str(required=False, allow_none=True)
    email_message_id = fields.Str(required=False, allow_none=True)
    email_references = fields.Str(required=False, allow_none=True)
    tier = auto_field(required=False, allow_none=True)
    correspondence_id = auto_field(required=False, allow_none=True)
    organization_id = auto_field(required=False, allow_none=False)
    last_assigned_at = fields.DateTime(required=False, dump_only=True, allow_none=True)
    org_group = auto_field(required=False, allow_none=True)
    shadow_type = fields.Str(required=False, allow_none=True, dump_only=True)
    routing_tags = fields.List(fields.Str(), required=False, allow_none=True)
    is_user_waiting_for_shadow = auto_field(required=False, allow_none=True)
    correspondence = fields.Nested(ReportCorrespondenceSchema, required=False, allow_none=True)
    is_backfill = fields.Boolean(required=False, allow_none=True, dump_only=True)

    def get_bundle(self, report: ReportV2):
        bundled_reports = set(report.report_bundle.reports) if report.report_bundle else set()
        bundled_reports.discard(report)

        if not current_user.is_trusted:
            bundled_reports = [
                r
                for r in bundled_reports
                if r.submission and (r.submission.is_verified or r.submission.is_verification_required is not True)
            ]

        bundles = [
            ReportV2Schema(
                only=(
                    "id",
                    "name",
                    "current_user_permission_type",
                    "created_at",
                    "is_archived",
                    "submissions.coverages",
                    "submissions.assigned_underwriters",
                    "submissions.stage",
                    "submissions.proposed_effective_date",
                    "submissions.is_renewal",
                    "submissions.account_id",
                )
            ).dump(bundled_report)
            for bundled_report in bundled_reports
            if not bundled_report.is_deleted
        ]

        return bundles

    def get_links(self, obj: ReportV2):
        return [
            ReportV2Schema(
                only=(
                    "id",
                    "name",
                    "current_user_permission_type",
                    "created_at",
                    "is_archived",
                )
            ).dump(link.report_2)
            for link in obj.links
            if not link.report_2.is_deleted
        ]

    def include_permissions(self, obj: ReportV2) -> list[ReportPermissionSchema]:
        non_org_level_permissions = filter(lambda x: not x.grantee or x.grantee.is_enabled, obj.report_permissions)
        return ReportPermissionSchema().dump(non_org_level_permissions, many=True)

    def get_report_url(self, obj: ReportV2) -> str:
        if current_user.idp and obj.url:
            return f"{obj.url}?idp={current_user.idp}"
        return obj.url

    # noinspection PyUnusedLocal
    @post_load
    def set_default_name(self, report: ReportV2, **kwargs):
        if not report.name:
            report.name = report.submission.name if report.submission else "My Report"
        return report

    @post_dump
    def cleanup_fields(self, data, **kwargs):
        fields_to_exclude_if_none = ["primary_naics_code"]
        for submission in data.get("submissions", []):
            for field in fields_to_exclude_if_none:
                if field in submission and submission.get(field) is None:
                    del submission[field]
        if data.get("organization_id", -1) == ExistingOrganizations.Paragon.value:
            self._filter_paragon_data(data)
        return data

    def _filter_paragon_data(self, data: dict) -> None:
        for submission in data.get("submissions", []):
            for csi in submission.get("client_submission_ids", []):
                client_id = csi.get("client_submission_id")
                if not client_id:
                    continue

                # Regular expression to find all numbers in json-string. For Paragon it means all captured
                # control numbers.
                pattern = r"(?<=\s)\d+(?=\})"
                control_numbers = re.findall(pattern, client_id)
                if control_numbers:
                    csi["client_submission_id"] = ", ".join(control_numbers)


class ExternalReportsResponseSchema(Schema):
    class Meta:
        unknown = EXCLUDE
        model = ExternalReportsResponse

    name = fields.String(allow_none=True)
    id = fields.UUID(allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return ExternalReportsResponse(**data)


DEFAULT_ONLY_USER_REPORTS = [
    "id",
    "routing_tags",
    "name",
    "created_at",
    "current_user_permission_type",
    "zendesk_ticket_id",
    "full_pds",
    "bundled_reports",
    "permissions",
    "is_rush",
    "org_group",
    "submissions.id",
    "submissions.account_id",
    "submissions.brokerage_office",
    "submissions.stage",
    "submissions.stage_details",
    "submissions.proposed_effective_date",
    "submissions.policy_expiration_date",
    "submissions.due_date",
    "submissions.received_date",
    "submissions.is_renewal",
    "submissions.renewal_creation_date",
    "submissions.declined_user_id",
    "submissions.declined_date",
    "submissions.reason_for_declining",
    "submissions.is_naics_verified",
    "submissions.is_verified",
    "submissions.is_verification_required",
    "submissions.broker",
    "submissions.brokerage",
    "submissions.coverages",
    "submissions.is_bookmarked",
    "submissions.was_read",
    "submissions.lost_reasons",
    "submissions.identifiers",
    "submissions.recommendation_v2_action",
    "submissions.recommendation_v2_priority",
    "submissions.recommendation_v2_is_refer",
    "submissions.recommendation_v2_score",
    "submissions.processing_state",
    "submissions.primary_naics_code",
    "submissions.assigned_underwriters",
    "submissions.clearing_assignee_id",
    "submissions.target_premium",
    "submissions.expired_premium",
    "submissions.sales",
    "submissions.is_verified_shell",
    "submissions.is_enhanced_shell",
    "submissions.adjusted_tiv",
    "submissions.client_stage_id",
    "submissions.client_stage",
    "submissions.tiv",
    "submissions.client_clearing_status",
    "submissions.clearing_status",
    "submissions.sic_code",
    "submissions.clearing_sub_statuses",
]

DEFAULT_ONLY_USER_INBOX_REPORTS = [
    *DEFAULT_ONLY_USER_REPORTS,
    "correspondence.emails.id",
    "correspondence.emails.created_at",
    "correspondence.emails.type",
    "correspondence.emails.email_subject",
    "correspondence.emails.attachments",
    "correspondence.emails.email_attachments_count",
    "correspondence.emails.normalized_subject",
    "correspondence.emails.email_from",
    "correspondence.emails.email_sent_at",
    "correspondence.emails.classifications",
]


class ReportsEnvelopeSchemaBase(Schema):
    page = fields.Integer(dump_only=True)
    total_pages = fields.Integer(dump_only=True)
    total_reports = fields.Integer(dump_only=True)
    has_next = fields.Boolean(dump_only=True)
    total_with_the_account_id = fields.Integer(dump_only=True, required=False)


class InboxReportsEnvelopeSchema(ReportsEnvelopeSchemaBase):
    reports = fields.Nested(ReportV2Schema(only=DEFAULT_ONLY_USER_INBOX_REPORTS, many=True))


class ReportsEnvelopeSchema(ReportsEnvelopeSchemaBase):
    reports = fields.Nested(ReportV2Schema(only=DEFAULT_ONLY_USER_REPORTS, many=True))


class WCReportsEnvelopeSchema(ReportsEnvelopeSchemaBase):
    reports_only = [
        *DEFAULT_ONLY_USER_REPORTS,
        "submissions.fni_state",
        "submissions.workers_comp_experience.experience_modification",
        "submissions.workers_comp_rating_info.class_code",
        "submissions.workers_comp_rating_info.payroll",
    ]
    reports = fields.Nested(ReportV2Schema(only=reports_only, many=True))


class AffectedReportsEnvelopeSchema(Schema):
    reports = fields.Nested(
        ReportV2Schema(
            only=[
                "id",
                "name",
                "created_at",
                "submissions.id",
                "submissions.stage",
                "submissions.coverages",
                "submissions.assigned_underwriters.id",
                "submissions.assigned_underwriters.name",
                "submissions.client_submission_ids.client_submission_id",
                "submissions.is_verified",
                "submissions.client_stage",
            ]
        ),
        many=True,
    )
    page = fields.Integer(dump_only=True)
    total_pages = fields.Integer(dump_only=True)
    total_reports = fields.Integer(dump_only=True)
    has_next = fields.Boolean(dump_only=True)
    total_with_the_account_id = fields.Integer(dump_only=True, required=False)


class PermissionsEnvelopeSchema(Schema):
    permissions = fields.Nested(ReportPermissionSchema, many=True)


class SubscriptionSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = Subscription

    id = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)
    report_id = auto_field(dump_only=True)
    user_id = auto_field(required=False)
    is_muted = auto_field()


class AdditionalIdentifierSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    type = EnumField(AdditionalIdentifierType, by_value=True)
    identifier = fields.String()

    @post_load
    def make_instance(self, data, **kwargs):
        return AdditionalIdentifier(**data)


class CreateReportRequestSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    original_report_id = fields.UUID(required=False, allow_none=True)
    target_stage = EnumField(SubmissionStage, by_value=True, allow_none=True, required=False)
    user_email = fields.Str(required=False, allow_none=True)
    external_id = fields.Str(required=False, allow_none=True)
    is_renewal = fields.Bool(required=False, allow_none=True)
    proposed_effective_date = fields.Date(required=False, allow_none=True)
    policy_expiration_date = fields.Date(required=False, allow_none=True)
    coverage_type = EnumField(SubmissionCoverageType, by_value=True, allow_none=True)
    broker = fields.Str(required=False, allow_none=True)
    broker_email = fields.Email(required=False, allow_none=True)
    brokerage = fields.Str(required=False, allow_none=True)
    brokerage_office = fields.Str(required=False, allow_none=True)
    correspondence_contact_name = fields.Str(required=False, allow_none=True)
    correspondence_contact_email = fields.Email(required=False, allow_none=True)
    sub_producer_name = fields.Str(required=False, allow_none=True)
    sub_producer_email = fields.Str(required=False, allow_none=True)
    submission_status = fields.Str(required=False, allow_none=True)
    s3_key = fields.Str(required=False, allow_none=True)
    s3_bucket = fields.Str(required=False, allow_none=True)
    businesses = fields.Nested(RequestedPropertiesSchema, required=False, many=True)
    submission_link = fields.Str(required=False, allow_none=True)
    name = fields.Str(required=False, allow_none=True)
    status = EnumField(ReportStatus, by_value=True, allow_none=True)
    additional_data = fields.Raw(required=False, allow_none=True)
    pds = fields.Bool(required=False, allow_none=True, missing=False)
    origin = EnumField(Origin, by_value=True, allow_none=True)
    email_body = fields.Str(required=False, allow_none=True)
    email_subject = fields.Str(required=False, allow_none=True)
    email_message_id = fields.Str(required=False, allow_none=True)
    email_references = fields.Str(required=False, allow_none=True)
    correspondence_id = fields.Str(required=False, allow_none=True)
    client_clearing_status = fields.Str(required=False, allow_none=True)
    additional_identifiers = fields.Nested(AdditionalIdentifierSchema, required=False, many=True)
    is_pre_renewal = fields.Bool(required=False, allow_none=True)
    is_stub = fields.Bool(required=False, allow_none=True)
    org_group = fields.Str(required=False, allow_none=True)

    @pre_load
    def replace_blank_strings_with_none(self, data, **kwargs):
        # NW likes to send empty strings instead of nulls or omitting the fields
        for key in data:
            if isinstance(data[key], str) and not data[key]:
                data[key] = None
        return data

    # noinspection PyUnusedLocal
    @post_load()
    def make_create_report_request(self, data, **kwargs) -> CreateReportRequest:
        return CreateReportRequest(**data)


@dataclass
class ReportExtractResponse:
    id: str
    name: str
    stage: str
    risk_score: float | None = None
    url: str | None = None
    naics_code: str | None = None


class SubmissionUserSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionUser
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    user_id = auto_field()
    submission_id = fields.UUID()
    source = auto_field()


class SubmissionUserRequestSchema(SubmissionUserSchema):
    submission_id = fields.UUID(required=True)
    user_id = fields.Integer(required=True)
    should_share = fields.Boolean(required=False, allow_none=True)
    ignore_frozen = fields.Bool(required=False, missing=False)

    @post_load
    def make_instance(self, data, **kwargs) -> SubmissionUserRequest:
        return SubmissionUserRequest(**data)


@dataclass
class AssignedUnderwriter:
    id: int
    name: str
    email: str
    source: SubmissionUserSource | None = None
    groups: list[UserGroup] | None = None


class AssignedUnderwriterSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    id = fields.Int()
    name = fields.String(allow_none=True)
    email = fields.String(allow_none=True)
    redirect_user_id = fields.Int(allow_none=True, required=False)
    source = EnumField(SubmissionUserSource, by_value=True, allow_none=True)
    groups = fields.List(fields.Nested(UserGroupSchema(only=("id", "name"))))

    @post_load
    def make_instance(self, data, **kwargs) -> AssignedUnderwriter:
        return AssignedUnderwriter(**data)


@dataclass
class SubmissionUserUpdate:
    user_id: int


class SubmissionUserUpdateSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    user_id = fields.Int()

    @post_load
    def make_instance(self, data, **kwargs) -> SubmissionUserUpdate:
        return SubmissionUserUpdate(**data)


@dataclass
class SubmissionBulkDeclinePatch:
    ids: list[UUID]
    reason: str | None = None
    send_decline_email: bool = False
    client_stage_id: int | None = None
    clearing_status: str | None = None
    prevent_clearing_updates: bool | None = None


class SubmissionBulkDeclinePatchSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    ids = fields.List(fields.UUID)
    reason = fields.String(allow_none=True)
    client_stage_id = fields.Int(allow_none=True)
    send_decline_email = fields.Bool(missing=False)
    clearing_status = fields.String(allow_none=True)
    prevent_clearing_updates = fields.Bool(allow_none=True)

    @post_load
    def make_instance(self, data, **kwargs) -> SubmissionBulkDeclinePatch:
        return SubmissionBulkDeclinePatch(**data)


@dataclass
class SubmissionStageUpdateRequest:
    stage: SubmissionStage
    client_stage_id: int | None = None
    send_decline_email: bool = False
    reason_for_declining: str | None = None
    declined_date: datetime | None = None
    clearing_status: str | None = None
    prevent_clearing_updates: bool | None = None


class ExternalCustomStatusPartSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    name = fields.String(allow_none=False)
    value = fields.String(allow_none=False)

    @post_load
    def make_instance(self, data, **kwargs) -> ExternalCustomStatusPart:
        return ExternalCustomStatusPart(**data)


class ExternalCustomStatusSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    custom_status = fields.List(fields.Nested(ExternalCustomStatusPartSchema), required=True)

    @post_load
    def make_instance(self, data, **kwargs) -> ExternalCustomStatus:
        return ExternalCustomStatus(**data)


class SubmissionStageUpdateRequestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    stage = EnumField(SubmissionStage, by_value=True, required=True)
    client_stage_id = fields.Int(allow_none=True)
    reason_for_declining = fields.String(allow_none=True)
    send_decline_email = fields.Bool(missing=False)
    declined_date = fields.DateTime(allow_none=True)
    clearing_status = fields.String(allow_none=True)
    prevent_clearing_updates = fields.Bool(allow_none=True)

    @post_load
    def make_instance(self, data, **kwargs) -> SubmissionStageUpdateRequest:
        return SubmissionStageUpdateRequest(**data)


@dataclass_json
@dataclass
class AttachmentRequest:
    name: str
    # base64 encoded file
    content: str


@dataclass_json
@dataclass
class SendSubmissionEmailRequest:
    submission_id: UUID
    user_id: int
    preview_only: bool = False
    template_id: str | None = None
    custom_template: str | None = None
    custom_subject: str | None = None
    cc_emails: list[str] | None = None
    # Portfolio Management rule name
    rule_name: str | None = None
    attachments: list[AttachmentRequest] | None = None


@dataclass
class ReportPatchStatusDetails:
    update_date: datetime | None = None
    reason: str | None = None


@dataclass
class ReportPatchCoverage:
    coverage_type: str
    quoted_premium: float | None = None
    bound_premium: float | None = None
    estimated_premium: float | None = None
    total_premium: float | None = None


@dataclass
class ReportPatchRequest:
    name: str | None = None
    assigned_user_email: str | None = None
    external_id: str | None = None
    is_renewal: bool | None = None
    is_rush: bool | None = None
    status: ReportStatus | None = None
    status_details: ReportPatchStatusDetails | None = None
    proposed_effective_date: datetime | None = None
    policy_expiration_date: datetime | None = None
    received_date: datetime | None = None
    coverages: list[ReportPatchCoverage] | None = None
    businesses: list[dict] = _field(default_factory=list)
    email_body: str | None = None
    email_subject: str | None = None
    email_message_id: str | None = None
    email_references: str | None = None
    broker: str | None = None
    broker_email: str | None = None
    brokerage: str | None = None
    brokerage_office: str | None = None
    correspondence_contact_name: str | None = None
    correspondence_contact_email: str | None = None
    sub_producer_name: str | None = None
    sub_producer_email: str | None = None
    additional_identifiers: list[AdditionalIdentifier] | None = None
    org_group: str | None = None
    primary_naics_code: str | None = None
    iso_gl_code: str | None = None
    client_clearing_status: str | None = None
    sic_code: str | None = None
    additional_data: dict | None = None


class ReportPatchStatusDetailsSchema(Schema):
    update_date = fields.Date(required=False, allow_none=True)
    reason = fields.Str(required=False, allow_none=True)

    @post_load
    def make_instance(self, data, **kwargs):
        return ReportPatchStatusDetails(**data)


class ReportPatchCoverageSchema(Schema):
    coverage_type = fields.Str(required=True)
    quoted_premium = fields.Number(required=False, allow_none=True)
    bound_premium = fields.Number(required=False, allow_none=True)
    estimated_premium = fields.Number(required=False, allow_none=True)
    total_premium = fields.Number(required=False, allow_none=True)

    @post_load
    def make_instance(self, data, **kwargs):
        return ReportPatchCoverage(**data)


class ReportPatchSchema(Schema):
    class Meta(Schema.Meta):
        unknown = EXCLUDE

    name = fields.Str(required=False, allow_none=True)
    assigned_user_email = fields.Email(required=False, allow_none=True)
    external_id = fields.Str(required=False, allow_none=True)
    is_renewal = fields.Bool(required=False, allow_none=True)
    is_rush = fields.Bool(required=False, allow_none=True)
    status = EnumField(ReportStatus, by_value=True, allow_none=True, required=False)
    status_details = fields.Nested(ReportPatchStatusDetailsSchema, required=False, allow_none=True, many=False)
    proposed_effective_date = fields.Date(required=False, allow_none=True)
    policy_expiration_date = fields.Date(required=False, allow_none=True)
    received_date = fields.Date(required=False, allow_none=True)
    coverages = fields.Nested(ReportPatchCoverageSchema, required=False, allow_none=True, many=True)
    businesses = fields.Nested(RequestedPropertiesSchema, required=False, many=True)
    email_body = fields.Str(required=False, allow_none=True)
    email_subject = fields.Str(required=False, allow_none=True)
    email_message_id = fields.Str(required=False, allow_none=True)
    email_references = fields.Str(required=False, allow_none=True)
    broker = fields.Str(required=False, allow_none=True)
    broker_email = fields.Email(required=False, allow_none=True)
    brokerage = fields.Str(required=False, allow_none=True)
    brokerage_office = fields.Str(required=False, allow_none=True)
    correspondence_contact_name = fields.Str(required=False, allow_none=True)
    correspondence_contact_email = fields.Email(required=False, allow_none=True)
    sub_producer_name = fields.Str(required=False, allow_none=True)
    sub_producer_email = fields.Str(required=False, allow_none=True)
    additional_identifiers = fields.Nested(AdditionalIdentifierSchema, required=False, many=True)
    org_group = fields.Str(required=False, allow_none=True)
    primary_naics_code = fields.Str(required=False, allow_none=True)
    iso_gl_code = fields.Str(required=False, allow_none=True)
    client_clearing_status = fields.Str(required=False, allow_none=True)
    sic_code = fields.Str(required=False, allow_none=True)
    additional_data = fields.Dict(required=False, allow_none=True)

    @pre_load
    def replace_blank_strings_with_none(self, data, **kwargs):
        # NW likes to send empty strings instead of nulls or omitting the fields
        for key in data:
            if isinstance(data[key], str) and not data[key]:
                data[key] = None
        return data

    @post_load
    def make_instance(self, data, **kwargs):
        return ReportPatchRequest(**data)


class RecommendationAggregationSchema(Schema):
    recommendation = fields.Str(required=False, allow_none=True)
    min_value = fields.Float(required=False, allow_none=True)
    max_value = fields.Float(required=False, allow_none=True)


class ReportsQueueEnvelopeSchema(Schema):
    reports = fields.Nested(
        ReportV2Schema(
            only=[
                "id",
                "name",
                "created_at",
                "full_pds",
                "tier",
                "organization_id",
                "submissions.id",
                "submissions.stage",
                "submissions.is_naics_verified",
                "submissions.is_verified",
                "submissions.is_verification_required",
                "submissions.iso_gl_code",
                "submissions.processing_state",
                "submissions.primary_naics_code",
                "submissions.stuck_reason",
                "submissions.priority",
                "submissions.is_stuck_engineering",
                "submissions.missing_data_status",
                "submissions.is_escalated",
                "last_assigned_at",
            ]
        ),
        many=True,
    )
    page = fields.Integer(dump_only=True)
    total_pages = fields.Integer(dump_only=True)
    total_reports = fields.Integer(dump_only=True)
    has_next = fields.Boolean(dump_only=True)


class SubmissionNoteSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = SubmissionNote
        unknown = EXCLUDE

    id = fields.UUID(dump_only=True)
    submission_id = fields.UUID(dump_only=True)
    author_id = fields.Integer(dump_only=True)
    author_name = fields.String(dump_only=True)
    last_edit_by_id = fields.Integer(dump_only=True)
    last_editor_name = fields.String(dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    last_edit_at = fields.DateTime(dump_only=True, attribute="updated_at")
    text = fields.Str(required=True)
    referred_to_user_ids = fields.List(fields.Integer(), allow_none=True)
    referrals_closed_to_user_ids = fields.List(fields.Integer(), allow_none=True)
    is_generated_note = fields.Boolean(dump_only=True)
    is_editable = fields.Boolean(dump_only=True)
    rule_id = fields.UUID(required=False, allow_none=True)
    is_note = fields.Boolean(required=False, allow_none=True)
    html_content = fields.String(required=False, allow_none=True)


class RecommendationSubmissionNoteRequestSchema(Schema):
    class Meta(BaseSchema.Meta):
        model = RecommendationSubmissionNoteRequest
        unknown = EXCLUDE

    submission_id = fields.UUID(required=True, allow_none=False)
    text = fields.Str(required=True, allow_none=False)
    rule_id = fields.UUID(required=True, allow_none=False)
    is_editable = fields.Bool(required=False, allow_none=True)
    is_delete_request = fields.Bool(required=False, allow_none=True)

    @post_load
    def make_instance(self, data, **kwargs) -> RecommendationSubmissionNoteRequest:
        return RecommendationSubmissionNoteRequest(**data)


class SaveSubmissionNoteOptionsSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        unknown = EXCLUDE

    note_html = fields.Str(required=True)
    users_to_notify = fields.List(fields.Integer, allow_none=False, default=list)
    users_to_refer = fields.List(fields.Integer, allow_none=True)

    @post_load
    def make_instance(self, data, **kwargs) -> SaveSubmissionNoteOptions:
        return SaveSubmissionNoteOptions(**data)


class UserSubmissionNotificationSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = UserSubmissionNotification
        unknown = EXCLUDE

    user_id = auto_field()
    submission_id = auto_field()
    seen_emails = auto_field()
    seen_notes = auto_field()


class CreateOrReplaceNoteRequestSchema(BaseSchema):
    class Meta:
        unknown = EXCLUDE

    notes = fields.String(required=True, allow_none=False)
    smart_matching = fields.Boolean(required=False, allow_none=False, missing=False)

    @post_load
    def make_instance(self, data, **kwargs) -> CreateOrReplaceNoteRequest:
        return CreateOrReplaceNoteRequest(**data)


class SubmissionIdentifiersSuggestionResponseSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    created_at = fields.DateTime(required=True, allow_none=False)
    account_name = fields.String(required=True, allow_none=False)
    client_id = fields.String(required=True, allow_none=False)
    quote_numbers = fields.List(fields.String(), required=True, allow_none=False)
    policy_numbers = fields.List(fields.String(), required=True, allow_none=False)
    confidence = fields.Float(required=True, allow_none=False)
    is_assigned = fields.Boolean(required=True, allow_none=False)

    @post_dump(pass_many=True)
    def wrap_with_envelope(self, data, many, **kwargs):
        if many:
            data = {"suggestions": data}
        return data

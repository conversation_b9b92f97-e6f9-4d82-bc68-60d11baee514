from typing import Any
import datetime
import re

from dateutil.parser import parse
from infrastructure_common.logging import get_logger
from marshmallow import (
    EXCLUDE,
    Schema,
    ValidationError,
    fields,
    post_load,
    pre_load,
    validates,
    validates_schema,
)
from static_common.schemas.sensible import SensiblePolygonSchema
from structlog.stdlib import Bo<PERSON><PERSON>ogger

from copilot.constants import ACORD_ADDRESS_FILTERS, ACORD_GENERAL_FILTERS
from copilot.logic.loss_runs.validity import sensible_claim_data_is_loadable
from copilot.models.sensible_claim import (
    Acord101Form,
    Acord125Form,
    Acord126AdditionalInterest,
    Acord126ApplicationInformation,
    Acord126AthleticTeamsSponsored,
    Acord126ClaimsMade,
    Acord126Contractor,
    Acord126Coverages,
    Acord126EmployeeBenefitsLiability,
    Acord126EquipmentLoaned,
    Acord126Form,
    Acord126GeneralInformation,
    Acord126LeasedEmployees,
    Acord126Product,
    Acord126ProductsCompletedOperation,
    Acord126Signature,
    Acord130AgencyInformation,
    Acord130ApplicantInformation,
    Acord130BillingAuditInformation,
    Acord130Form,
    Acord130GeneralInformation,
    Acord130Location,
    Acord130NatureOfBusiness,
    Acord130PolicyInformation,
    Acord130PremiumInformation,
    Acord130PriorCarrierInformation,
    Acord130RatingInformation,
    Acord130RatingInformationRow,
    Acord130SubmissionStatus,
    Acord131AdditionalExposure,
    Acord131Form,
    Acord131PolicyInformation,
    Acord131PremisesInformation,
    Acord139Form,
    Acord139LossInfoSection,
    Acord139PropertyEntry,
    Acord139TotalSection,
    Acord140BuildingInformation,
    Acord140Form,
    Acord140PremisesInformationTable,
    Acord160AgencyInformation,
    Acord160ApartmentsAndCondominiums,
    Acord160AthleticSponsorship,
    Acord160BuildingDetails,
    Acord160BusinessOperation,
    Acord160CoverageDetails,
    Acord160CrimeInformation,
    Acord160EquipmentRental,
    Acord160FireProtection,
    Acord160Form,
    Acord160GeneralInformation,
    Acord160LeasedEmployeeInfo,
    Acord160PremisesDetails,
    Acord160PremisesGeneralInformation,
    Acord160PremisesInformation,
    Acord160PremiumInformation,
    Acord160SurroundingExposures,
    Acord211Form,
    Acord823Form,
    Acord829Endorsements,
    Acord829Form,
    AcordAgencyInformation,
    AcordAgencyInformationType,
    AcordApplicantInformationType,
    AcordGeneralInformationType,
    AcordHazard,
    AcordLinesOfBusinessType,
    AcordNatureOfBusinessType,
    AcordPolicyInformationType,
    AcordPremisesInfoType,
    Applied98Form,
    Applied125Form,
    Applied126Form,
    Applied130Form,
    AthleticTeamsSponsored,
    ExtractionError,
    ExtractionErrorType,
    OFSCHHAZForm,
    SensibleBoolValue,
    SensibleClaim,
    SensibleConfiguration,
    SensibleConfigurationVersion,
    SensibleDateValue,
    SensibleDocumentType,
    SensibleFloatValue,
    SensibleIntegerValue,
    SensiblePolicy,
    SensibleStringValue,
)

logger = get_logger()


class ExtractionErrorSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    path = fields.String(required=True, allow_none=False)
    error_type = fields.Enum(ExtractionErrorType, required=True, allow_none=False, by_value=True)

    @post_load
    def make_object(self, data, **kwargs) -> ExtractionError:  # type: ignore
        return ExtractionError(**data)


class SensibleHazardsPremiumBasisSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    _premium_basis_mappings = {
        "S": "Gross Sales",
        "P": "Payroll",
        "A": "Area",
        "C": "Total Cost",
        "M": "Admissions",
        "U": "Unit",
        "T": "Other",
    }

    value = fields.String()
    type = fields.String(allow_none=True)
    source = fields.String(allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @pre_load
    def transform_value(self, data, **kwargs) -> Any:  # type: ignore
        val = data.get("value", None)
        if val and isinstance(val, str) and val.upper() in self._premium_basis_mappings:
            data["value"] = self._premium_basis_mappings[val.upper()]
        return data

    @post_load
    def make_object(self, data, **kwargs) -> SensibleStringValue:  # type: ignore
        return SensibleStringValue(**data)


class SensibleBoolValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    sensible_bool_truthy = {"x", "X"}.union(fields.Boolean.truthy)

    value = fields.Boolean(truthy=sensible_bool_truthy)
    type = fields.String(allow_none=True)
    extraction_error = fields.Enum(ExtractionErrorType, allow_none=True)

    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @pre_load
    def mark_invalid_boolean(self, data, **kwargs) -> Any:  # type: ignore
        val = data.get("value", None)
        if val not in self.sensible_bool_truthy.union(fields.Boolean.falsy):
            data["extraction_error"] = ExtractionErrorType.INVALID_TYPE_ENCOUNTERED
            data["value"] = False
        return data

    @post_load
    def make_object(self, data, **kwargs) -> SensibleBoolValue | None:  # type: ignore
        if "extraction_error" in data:
            return None
        return SensibleBoolValue(**data)


class SensibleStringAsBoolValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    value = fields.String()
    type = fields.String(allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @post_load
    def make_object(self, data, **kwargs) -> SensibleBoolValue:  # type: ignore
        val = data.get("value", "").lower()
        data["value"] = val in ["y", "yes", "x"]
        return SensibleBoolValue(**data)


class SensibleStringValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    value = fields.String()
    type = fields.String(allow_none=True)
    source = fields.String(allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @post_load
    def make_object(self, data, **kwargs) -> SensibleStringValue:  # type: ignore
        return SensibleStringValue(**data)


class SensibleEntityPartValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    value = fields.String()
    type = fields.String(allow_none=True)
    source = fields.String(allow_none=True)
    extraction_error = fields.Enum(ExtractionErrorType, allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @pre_load
    def mark_invalid_address_part(self, data, **kwargs) -> Any:  # type: ignore
        # values from the acord
        invalid_values = [
            "NAICS",
            "FEIN OR",
            "SEC #",
            "GL CODE",
            "CITY LIMITS INTEREST",
            "FULL TIME EMPL",
            "LOC #",
            "OUTSIDE TENANT",
            "PART TIME EMPL",
        ]
        val = data.get("value", None)
        if not val:
            data["extraction_error"] = ExtractionErrorType.EMPTY_VALUE_EXTRACTED
            data["value"] = ""
        else:
            cleaned_val = re.sub(r"[^a-zA-Z0-9]", " ", val)
            words = cleaned_val.lower().split()

            if any(word in invalid_values for word in words):
                data["extraction_error"] = ExtractionErrorType.INVALID_VALUE_EXTRACTED
                data["value"] = ""
            else:
                filters = set(ACORD_ADDRESS_FILTERS + ACORD_GENERAL_FILTERS)
                if set(words) & set(filters):
                    data["extraction_error"] = ExtractionErrorType.INVALID_VALUE_EXTRACTED
                    data["value"] = ""

        return data

    @post_load
    def make_object(self, data, **kwargs) -> SensibleStringValue:  # type: ignore
        return SensibleStringValue(**{k: v for k, v in data.items() if k != "extraction_error"})


class SensibleFloatValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    value = fields.Float()
    type = fields.String(allow_none=True)
    source = fields.String(allow_none=True)
    unit = fields.String(allow_none=True)
    extraction_error = fields.Enum(ExtractionErrorType, allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @pre_load
    def mark_invalid_number(self, data, **kwargs) -> Any:  # type: ignore
        val = data.get("value", None)
        if val is not None and isinstance(val, str) and not val.isnumeric():
            data["extraction_error"] = ExtractionErrorType.INVALID_TYPE_ENCOUNTERED
            data["value"] = 0
        return data

    @post_load
    def make_object(self, data, **kwargs) -> SensibleFloatValue | None:  # type: ignore
        if "extraction_error" in data:
            return None
        return SensibleFloatValue(**data)


class SensibleIntegerValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    value = fields.Integer()
    type = fields.String(allow_none=True)
    source = fields.String(allow_none=True)
    unit = fields.String(allow_none=True)
    extraction_error = fields.Enum(ExtractionErrorType, allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @pre_load
    def mark_invalid_number(self, data, **kwargs) -> Any:  # type: ignore
        val = data.get("value", None)
        if val is not None and isinstance(val, str) and not val.isnumeric():
            data["extraction_error"] = ExtractionErrorType.INVALID_TYPE_ENCOUNTERED
            data["value"] = 0
        return data

    @post_load
    def make_object(self, data, **kwargs) -> SensibleIntegerValue | None:  # type: ignore
        if "extraction_error" in data:
            return None
        return SensibleIntegerValue(**data)


class SensibleAcordDateValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    # unfortunately this is needed because value extraction for acords seems somewhat unreliable and
    # we have to be more lenient with checks and validations. Also the SensibleDateValueSchema is used
    # in loss runs extractions which seem to be more reliable and by making it more lenient, the loss run
    # functionality will suffer some consequences.
    value = fields.DateTime(allow_none=True)
    type = fields.String(allow_none=True)
    source = fields.String(allow_none=True)
    extraction_error = fields.Enum(ExtractionErrorType, allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @pre_load
    def mark_invalid_number(self, data, **kwargs) -> Any:  # type: ignore
        val = data["value"]
        try:
            parsed = parse(val, fuzzy=False)
            parsed = parsed.replace(tzinfo=datetime.UTC)
            if parsed < datetime.datetime(1859, 1, 1, 0, 0, tzinfo=datetime.UTC) or parsed > datetime.datetime(
                2050, 1, 1, 0, 0, tzinfo=datetime.UTC
            ):
                logger.warning("Invalid date received from sensible", date=data["value"])
                data["extraction_error"] = ExtractionErrorType.INVALID_VALUE_EXTRACTED
                data["value"] = None
            else:
                data["value"] = parsed.isoformat()
        except Exception:
            logger.warning("Failed to parse sensible date value", exc_info=True)
            data["extraction_error"] = ExtractionErrorType.INVALID_TYPE_ENCOUNTERED
            data["value"] = None
        return data

    @post_load
    def make_object(self, data, **kwargs) -> SensibleDateValue | None:  # type: ignore
        if "extraction_error" in data:
            return None
        return SensibleDateValue(**data)


class SensibleDateValueSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    value = fields.DateTime()
    type = fields.String(allow_none=True)
    source = fields.String(allow_none=True)
    lines = fields.List(fields.Nested(SensiblePolygonSchema), allow_none=True, missing=[])

    @post_load
    def make_object(self, data, **kwargs) -> SensibleDateValue:  # type: ignore
        return SensibleDateValue(**data)

    @validates("value")
    def validate_date(self, data) -> None:  # type: ignore
        if data < datetime.datetime(1900, 1, 1, 0, 0, tzinfo=datetime.UTC) or data > datetime.datetime(
            2050, 1, 1, 0, 0, tzinfo=datetime.UTC
        ):
            raise ValidationError("Invalid date parsed from loss run")


class SensibleClaimSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    claim_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    carrier = fields.Nested(SensibleStringValueSchema, allow_none=True)
    date_of_loss = fields.Nested(SensibleDateValueSchema, allow_none=True)
    loss_reported_date = fields.Nested(SensibleDateValueSchema, allow_none=True)
    line_of_business = fields.Nested(SensibleStringValueSchema, allow_none=True)
    named_insured = fields.Nested(SensibleStringValueSchema, allow_none=True)
    claimant_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    policy_effective_date = fields.Nested(SensibleDateValueSchema, allow_none=True)
    policy_expiration_date = fields.Nested(SensibleDateValueSchema, allow_none=True)
    loss_location = fields.Nested(SensibleStringValueSchema, allow_none=True)
    loss_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    loss_type_raw = fields.Nested(SensibleStringValueSchema, allow_none=True)
    loss_type = fields.Nested(SensibleStringValueSchema, allow_none=True)
    total_amount_incurred = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_paid_medical = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_paid_loss = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_amount_paid = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_amount_paid_expense = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_amount_reserved = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_amount_reserved_loss = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_amount_reserved_medical = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_amount_reserved_expense = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_amount_recovered = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    claim_status = fields.Nested(SensibleStringValueSchema, allow_none=True)
    policy_id = fields.Nested(SensibleStringValueSchema, allow_none=True)
    policy_number = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @validates_schema
    def has_valid_payload(self, data, **kwargs) -> None:  # type: ignore
        logger: BoundLogger | None = self.context.get("logger", None) if hasattr(self, "context") else None
        error_message: str | None = sensible_claim_data_is_loadable(data, logger)
        if error_message is not None:
            raise ValidationError(error_message)

    @post_load
    def make_object(self, data, **kwargs) -> SensibleClaim:  # type: ignore
        return SensibleClaim(**data)


class SensiblePolicySchema(Schema):
    class Meta:
        unknown = EXCLUDE

    number = fields.Nested(SensibleStringValueSchema, required=True)
    effective_date = fields.Nested(SensibleDateValueSchema, allow_none=True)
    expiration_date = fields.Nested(SensibleDateValueSchema, allow_none=True)
    line_of_business = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> SensiblePolicy:  # type: ignore
        return SensiblePolicy(**data)


# These match coverage names in the DB and are used to assign submission coverages
class AcordLinesOfBusinessTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    boilerMachinery = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    boilerMachinery_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    businessAuto = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    businessAuto_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    businessOwners = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    businessOwners_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    generalLiability = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    generalLiability_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    commercialInlandMarine = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    commercialInlandMarine_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    property = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    property_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    crime = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    crime_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    cyberPrivacy = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    cyberPrivacy_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    fiduciaryLiability = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    fiduciaryLiability_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    garageDealers = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    garageDealers_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    liquorLiability = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    liquorLiability_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    motorCarriers = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    motorCarriers_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    truckers = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    truckers_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    umbrella = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    umbrella_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    yacht = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    yacht_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    accountsReceivable = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    accountsReceivable_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    glassSign = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    glassSign_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    equipmentFloater = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    equipmentFloater_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    installationBuildersRisk = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    installationBuildersRisk_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    electronicDataProc = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    electronicDataProc_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    vehicleSchedule = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    vehicleSchedule_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    workersCompensation = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    workersCompensation_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    dealers = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    dealers_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    openCargo = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    openCargo_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    transportation = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    transportation_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> AcordLinesOfBusinessType:  # type: ignore
        return AcordLinesOfBusinessType(**data)


class AcordApplicantInformationTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    applicant_name_and_address = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    applicant_address = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    applicant_info_gl_code_1 = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicant_sic = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicant_naics = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicant_fein = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicant_website_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicant_desc_of_ops = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> AcordApplicantInformationType:  # type: ignore
        return AcordApplicantInformationType(**data)


class AcordPremisesInfoTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    street = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    city = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    state = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    county = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    zip = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    city_limits_inside = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    city_limits_outside = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    interest_owner = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    interest_tenant = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    fulltime_employees = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    parttime_employees = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    annual_revenues = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    occupied_area = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    open_to_public_area = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    building_area = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    area_leased_to_others = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    operations_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    extraction_error = fields.Enum(ExtractionErrorType, allow_none=True)
    full_address = fields.Nested(SensibleEntityPartValueSchema, allow_none=True, required=False)

    @pre_load
    def mark_invalid_address(self, data, **kwargs) -> Any:  # type: ignore
        street = data.get("street")
        city = data.get("city")
        state = data.get("state")
        zip_address = data.get("zip")
        full_address = data.get("full_address")
        address_fields = [street, city, state, zip_address]

        if full_address:
            return data

        if any(address_fields) and (not street or not any([city, state, zip_address])):
            data["extraction_error"] = ExtractionErrorType.ADDRESS_CANNOT_BE_CONSTRUCTED
            return data

        if any(address_fields) and not all(address_fields):
            data["extraction_error"] = ExtractionErrorType.PARTIAL_ADDRESS_CONSTRUCTED
        return data

    @post_load
    def make_object(self, data, **kwargs) -> AcordPremisesInfoType:  # type: ignore
        return AcordPremisesInfoType(**{k: v for k, v in data.items() if k != "extraction_error"})


class AcordNatureOfBusinessTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    apartments = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    contractor = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    manufacturing = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    restaurant = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    service = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    condominiums = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    institutional = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    office = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    retail = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    wholesale = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    custom_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    custom = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    start_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    description_of_primary_operations = fields.Nested(SensibleStringValueSchema, allow_none=True)
    retail_or_service_percentage_sales = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    installation_service_repair_work_percentage = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    off_premises_work = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    operations_description_of_other_named_insureds = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> AcordNatureOfBusinessType:  # type: ignore
        return AcordNatureOfBusinessType(**data)


class AcordAgencyInformationTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    agency = fields.Nested(SensibleStringValueSchema, allow_none=True)
    date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    carrier = fields.Nested(SensibleStringValueSchema, allow_none=True)
    naic_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    policy_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    quote = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    bound = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    change = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    cancel = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    issue_policy = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    program_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    sub_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    contact_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    phone = fields.Nested(SensibleStringValueSchema, allow_none=True)
    fax = fields.Nested(SensibleStringValueSchema, allow_none=True)
    company_policy_or_program_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    transaction_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    agency_customer_id = fields.Nested(SensibleStringValueSchema, allow_none=True)
    email = fields.Nested(SensibleStringValueSchema, allow_none=True)
    underwriter = fields.Nested(SensibleStringValueSchema, allow_none=True)
    underwriter_office = fields.Nested(SensibleStringValueSchema, allow_none=True)
    renew = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return AcordAgencyInformationType(**data)


class AcordPolicyInformationTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    proposed_effective_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    proposed_expiration_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    billing_plan_direct = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    billing_plan_agency = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    payment_plan = fields.Nested(SensibleStringValueSchema, allow_none=True)
    method_of_payment = fields.Nested(SensibleStringValueSchema, allow_none=True)
    audit = fields.Nested(SensibleStringAsBoolValueSchema, allow_none=True)
    deposit = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    minimum_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    policy_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return AcordPolicyInformationType(**data)


class AcordGeneralInformationTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    is_applicant_subsidiary = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    subsidiary_parent_company_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    subsidiary_percentage_owned = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    does_applicant_have_subsidiaries = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    applicants_subsidiary_company_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicants_subsidiary_percentage_owned = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    is_formal_safety_plan_in_operation = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    safety_program_manual = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    safety_plan_monthly_meetings = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    safety_plan_safety_position = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    safety_plan_osha = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_exposure_flammables = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    flammables_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_policy_or_coverage_declined = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_declined_non_payment = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_declined_agent = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_declined_non_renewal = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_declined_underwriting = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_declined_condition_corrected = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_past_losses_relating_to_sexual_abuse = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_past_losses_relating_to_sexual_abuse_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_applicant_been_indicted = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_applicant_been_indicted_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_uncorrected_fire_or_safety_violations = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    has_applicant_had_forclosure = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    has_applicant_had_judgement_or_lien = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    is_business_placed_in_trust = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    name_of_trust = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_foreign_operations = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_foreign_operations_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    past_conviction_arson = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    past_conviction_arson_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    catastrophe_exposure = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    catastrophe_exposure_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    has_other_insurance = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    has_other_insurance_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    does_applicant_have_other_business_ventures = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    does_applicant_have_other_business_ventures_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    does_applicant_own_lease_operate_any_drones = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    does_applicant_own_lease_operate_any_drones_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    does_applicant_hire_others_to_operate_drones = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    does_applicant_hire_others_to_operate_drones_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return AcordGeneralInformationType(**data)


class AcordFormSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    named_insured = fields.Nested(SensibleStringValueSchema, allow_none=True)


class Acord125Schema(AcordFormSchema):
    agency_information = fields.List(fields.Nested(AcordAgencyInformationTypeSchema), allow_none=True)
    lines_of_business = fields.List(fields.Nested(AcordLinesOfBusinessTypeSchema), allow_none=True)
    policy_information = fields.List(fields.Nested(AcordPolicyInformationTypeSchema), allow_none=True)
    proposed_effective_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    proposed_expiration_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    primary_naics = fields.Nested(SensibleStringValueSchema, allow_none=True, missing=None)
    description_of_primary_operations = fields.Nested(SensibleStringValueSchema, allow_none=True, missing=None)
    applicant_information = fields.List(fields.Nested(AcordApplicantInformationTypeSchema), allow_none=True)
    premises_info = fields.List(fields.Nested(AcordPremisesInfoTypeSchema), allow_none=True)
    nature_of_business = fields.List(fields.Nested(AcordNatureOfBusinessTypeSchema), allow_none=True)
    general_information = fields.List(fields.Nested(AcordGeneralInformationTypeSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord125Form:
        return Acord125Form(**data)


class Acord126ApplicationInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    phone = fields.Nested(SensibleStringValueSchema, allow_none=True)
    fax = fields.Nested(SensibleStringValueSchema, allow_none=True)
    agency = fields.Nested(SensibleStringValueSchema, allow_none=True)
    code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    sub_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    agency_customer_id = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicant_first_named_insured = fields.Nested(SensibleStringValueSchema, allow_none=True)
    effective_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    expiration_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    bill_direct = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    bill_agency = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    payment_plan = fields.Nested(SensibleStringValueSchema, allow_none=True)
    audit = fields.Nested(SensibleStringValueSchema, allow_none=True)
    for_company_use_only = fields.Nested(SensibleStringValueSchema, allow_none=True)
    date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    carrier = fields.Nested(SensibleStringValueSchema, allow_none=True)
    naic_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    policy_number = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126ApplicationInformation(**data)


class Acord126CoveragesSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    commercial_general_liability = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    claims_made = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    occurrence = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    owners_and_contractors_protective = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_custom_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    coverage_custom_value = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    deductibles_property_damage = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    deductibles_property_damage_amount = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    deductibles_bodily_injury = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    deductibles_bodily_injury_amount = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    deductibles_custom = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    deductibles_custom_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    deductibles_custom_value = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    deductibles_per_claim = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    deductibles_per_occurrence = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    limits_general_aggregate = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_general_aggregate_limit_applies_per_policy = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    limits_general_aggregate_limit_applies_per_location = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    limits_general_aggregate_limit_applies_per_project = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    limits_general_aggregate_limit_applies_per_other = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    limits_products_and_completed_operations_aggregate = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_personal_and_advertising_injury = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_each_occurrence = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_damage_to_rented_premises = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_medical_expense = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_employee_benefits = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_custom_limit_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    limits_custom_limit_amount = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    premiums_premises_operations = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    premiums_products = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    premiums_other = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    premiums_total = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    other_coverages = fields.Nested(SensibleStringValueSchema, allow_none=True)
    wisconsin_um_coverage_is_available = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    wisconsin_medical_payments_coverage_is_available = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126Coverages(**data)


class AcordHazardSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    hazard_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    class_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    premium_basis = fields.Nested(SensibleHazardsPremiumBasisSchema, allow_none=True)
    exposure = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    territorial_rating = fields.Nested(SensibleStringValueSchema, allow_none=True)
    rate_prem_ops = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    rate_products = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    premium_prem_ops = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    premium_products = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    classification = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return AcordHazard(**data)


class Acord126ClaimsMadeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    proposed_retroactive_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    entry_date_uninterrupted_claims_made_coverage = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    any_product_work_accident_location_been_excluded = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_product_work_accident_location_been_excluded_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    tail_coverage_purchased = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    tail_coverage_purchased_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126ClaimsMade(**data)


class Acord126EmployeeBenefitsLiabilitySchema(Schema):
    class Meta:
        unknown = EXCLUDE

    deductible_per_claim = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    number_of_employees = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_employees_covered = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    retroactive_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126EmployeeBenefitsLiability(**data)


class Acord126ContractorSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    applicant_draw_plans_for_others = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    applicant_draw_plans_for_others_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_operations_include_blasting_or_explosive_material = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_operations_include_blasting_or_explosive_material_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    any_operation_include_excavation = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_operation_include_excavation_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    subcontractors_carry_coverages_less = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    subcontractors_carry_coverages_less_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    subcontractors_allowed_to_work_without_insurance_certificate = fields.Nested(
        SensibleBoolValueSchema, allow_none=True
    )
    subcontractors_allowed_to_work_without_insurance_certificate_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    applicant_lease_equipment_to_others = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    applicant_lease_equipment_to_others_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    describe_type_of_work_subcontracted = fields.Nested(SensibleStringValueSchema, allow_none=True)
    money_paid_to_subcontractors = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    percentage_of_work_subcontracted = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    number_of_full_time_staff = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_part_time_staff = fields.Nested(SensibleIntegerValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126Contractor(**data)


class Acord126ProductsCompletedOperationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    products = fields.Nested(SensibleStringValueSchema, allow_none=True)
    annual_gross_sales = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    number_of_units = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    time_in_market = fields.Nested(SensibleStringValueSchema, allow_none=True)
    expected_life = fields.Nested(SensibleStringValueSchema, allow_none=True)
    intended_use = fields.Nested(SensibleStringValueSchema, allow_none=True)
    principal_components = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126ProductsCompletedOperation(**data)


class Acord126ProductSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    applicant_installs_services_demonstrates_products = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    applicant_installs_services_demonstrates_products_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    foreign_products = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    foreign_products_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    research_and_development_conducted = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    research_and_development_conducted_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    guarantees_warranties_hold_harmless_agreements = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    guarantees_warranties_hold_harmless_agreements_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    products_related_to_aircraft_space_industry = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    products_related_to_aircraft_space_industry_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    products_recalled_discontinued_changed = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    products_recalled_discontinued_changed_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    products_of_others_sold = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    products_of_others_sold_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    products_under_label_of_others = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    products_under_label_of_others_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    vendors_coverage_required = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    vendors_coverage_required_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_named_insureds_sell_to_others = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_named_insureds_sell_to_others_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126Product(**data)


class Acord126AdditionalInterestSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    interest_additional_insured = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    interest_loss_payee = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    interest_mortgagee = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    interest_lienholder = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    interest_employee_as_lessor = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    rank = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    name_and_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    reference_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    certificate_required = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    location = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    vehicle = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    boat = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    scheduled_item_number = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    other = fields.Nested(SensibleStringValueSchema, allow_none=True)
    item = fields.Nested(SensibleStringValueSchema, allow_none=True)
    item_class = fields.Nested(SensibleStringValueSchema, allow_none=True)
    item_description = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126AdditionalInterest(**data)


class Acord126EquipmentLoanedSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    equipment = fields.Nested(SensibleStringValueSchema, allow_none=True)
    instruction_given = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    small_tools = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    large_equipment = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126EquipmentLoaned(**data)


class AthleticTeamsSponsoredSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    type_of_sport = fields.Nested(SensibleStringValueSchema, allow_none=True)
    contact_sport = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    age_group_12_and_under = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    age_group_13_to_18 = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    age_group_over_18 = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return AthleticTeamsSponsored(**data)


class Acord126AthleticTeamsSponsoredSchema(AthleticTeamsSponsoredSchema):
    class Meta:
        unknown = EXCLUDE

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126AthleticTeamsSponsored(**data)


class Acord126LeasedEmployeesSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    wc_coverage = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126LeasedEmployees(**data)


class Acord126GeneralInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    any_medical_facilities_provided = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_medical_facilities_provided_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_exposure_radioactive_nuclear_materials = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_exposure_radioactive_nuclear_materials_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    hazardous_material = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    hazardous_material_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_operations_sold_acquired_discontinued = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_operations_sold_acquired_discontinued_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    equipment_loaned_or_rented = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    equipment_loaned_or_rented_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    equipment_loaned_or_rented_list = fields.List(
        fields.Nested(Acord126EquipmentLoanedSchema, allow_none=True), allow_none=True
    )
    watercraft_docks_floats = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    watercraft_docks_floats_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    parking_facilities_owned_rented = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    parking_facilities_owned_rented_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    fee_charged_for_parking = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    fee_charged_for_parking_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    recreation_facilities_provided = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    recreation_facilities_provided_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    lodging_operations = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    lodging_operations_number_of_apartments = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    lodging_operations_total_apartment_area = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    lodging_operations_other_lodging_operations_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    swimming_pool_on_premises = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    swimming_pool_on_premises_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    swimming_pool_on_premises_approved_fence = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    swimming_pool_on_premises_limited_access = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    swimming_pool_on_premises_diving_board = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    swimming_pool_on_premises_slide = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    swimming_pool_on_premises_above_ground = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    swimming_pool_on_premises_in_ground = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    swimming_pool_on_premises_life_guard = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    social_events = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    social_events_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    athletic_teams_sponsored = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    athletic_teams_sponsored_list = fields.List(
        fields.Nested(Acord126AthleticTeamsSponsoredSchema, allow_none=True), allow_none=True
    )
    structural_alterations_contemplated = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    structural_alterations_contemplated_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    any_demolition_exposure_contemplated = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    any_demolition_exposure_contemplated_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicant_involved_joint_ventures = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    applicant_involved_joint_ventures_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    lease_employees_other_employers = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    lease_employees_other_employers_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    lease_employees_other_employers_lease_to_list = fields.List(
        fields.Nested(Acord126LeasedEmployeesSchema, allow_none=True), allow_none=True
    )
    lease_employees_other_employers_lease_from_list = fields.List(
        fields.Nested(Acord126LeasedEmployeesSchema, allow_none=True), allow_none=True
    )
    labor_interchange_other_business_or_subsidiaries = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    labor_interchange_other_business_or_subsidiaries_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    day_care_facilities = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    day_care_facilities_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    crimes_occured_or_attempted = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    crimes_occured_or_attempted_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    formal_safety_policy = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    formal_safety_policy_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    does_business_promotion_represent_premises_safety_or_security = fields.Nested(
        SensibleBoolValueSchema, allow_none=True
    )
    does_business_promotion_represent_premises_safety_or_security_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126GeneralInformation(**data)

    @pre_load
    def process_input(self, data, **kwargs):
        data = self._process_equipment_loaned_or_rented(data)
        data = self._process_athletic_teams_sponsored(data)
        data = self._process_lease_employees_other_employers_lease(data)
        return data

    @staticmethod
    def _transform_input_data(data, columns: list[str], prefix: str, size: int):
        res = []
        for i in range(1, (size + 1)):
            elem = {}
            for col in columns:
                key = f"{prefix}_{col}_{i}"
                elem[col] = data.get(key, None)
                if key in data:
                    del data[key]
        data[f"{prefix}_list"] = res
        return data

    def _process_lease_employees_other_employers_lease(self, data):
        if "lease_employees_other_employers_lease_to_name_1" not in data:
            return data
        columns = ["name", "wc_coverage"]
        size = 2
        self._transform_input_data(data, columns, "lease_employees_other_employers_lease_to", size)
        return self._transform_input_data(data, columns, "lease_employees_other_employers_lease_from", size)

    def _process_equipment_loaned_or_rented(self, data):
        if "equipment_loaned_or_rented_equipment_1" not in data:
            return data
        return self._transform_input_data(
            data, ["equipment", "instruction_given", "small_tools", "large_equipment"], "equipment_loaned_or_rented", 2
        )

    def _process_athletic_teams_sponsored(self, data):
        if "athletic_teams_sponsored_type_of_sport_1" not in data:
            return data
        return self._transform_input_data(
            data,
            ["type_of_sport", "contact_sport", "age_group_12_and_under", "age_group_13_to_18", "age_group_over_18"],
            "athletic_teams_sponsored",
            2,
        )


class Acord126SignatureSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    producers_signature = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    producers_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    state_producer_license_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    applicants_signature = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    national_producers_number = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126Signature(**data)


class Acord126Schema(AcordFormSchema):
    application_information = fields.List(
        fields.Nested(Acord126ApplicationInformationSchema, allow_none=True), allow_none=True
    )
    coverages = fields.List(fields.Nested(Acord126CoveragesSchema, allow_none=True), allow_none=True)
    schedule_of_hazards = fields.List(fields.Nested(AcordHazardSchema, allow_none=True), allow_none=True)
    claims_made = fields.List(fields.Nested(Acord126ClaimsMadeSchema, allow_none=True), allow_none=True)
    employee_benefits_liability = fields.List(
        fields.Nested(Acord126EmployeeBenefitsLiabilitySchema, allow_none=True), allow_none=True
    )
    contractors = fields.List(fields.Nested(Acord126ContractorSchema, allow_none=True), allow_none=True)
    products_completed_operations = fields.List(
        fields.Nested(Acord126ProductsCompletedOperationSchema, allow_none=True), allow_none=True
    )
    products = fields.List(fields.Nested(Acord126ProductSchema, allow_none=True), allow_none=True)
    additional_interest = fields.List(fields.Nested(Acord126AdditionalInterestSchema, allow_none=True), allow_none=True)
    general_information = fields.List(fields.Nested(Acord126GeneralInformationSchema, allow_none=True), allow_none=True)
    remarks = fields.Nested(SensibleStringValueSchema, allow_none=True)
    signature = fields.List(fields.Nested(Acord126SignatureSchema, allow_none=True), allow_none=True)

    @pre_load
    def process_input(self, data, **kwargs):
        data = self._process_schedule_of_hazards_input(data)
        data = self._process_products_completed_operations_input(data)
        return data

    @post_load
    def make_object(self, data, **kwargs):
        return Acord126Form(**data)

    def _process_schedule_of_hazards_input(self, data):
        # because of how schedule of hazards are extracted depending on the acord year, we need to make
        # some transformations if the acord form is 126_2007
        mappings = [
            "location_number",
            "hazard_number",
            "class_code",
            "premium_basis",
            "exposure",
            "territorial_rating",
            "rate_prem_ops",
            "rate_products",
            "premium_prem_ops",
            "premium_products",
            "classification",
        ]
        return self._transform_input_data_to_list(data, mappings, "schedule_of_hazards", "schedule_of_hazards", 8)

    def _process_products_completed_operations_input(self, data):
        # because of how products completed operations are extracted depending on the acord year, we need to make
        # some transformations if the acord form is 126_2007
        mappings = [
            "products",
            "annual_gross_sales",
            "number_of_units",
            "time_in_market",
            "expected_life",
            "intended_use",
            "principal_components",
        ]
        return self._transform_input_data_to_list(data, mappings, "products", "products_completed_operations", 3)

    @staticmethod
    def _transform_input_data_to_list(data, columns: list[str], input_key: str, output_key: str, size: int):
        if not data.get(input_key, None) or f"{columns[0]}_1" not in data[input_key][0]:
            return data
        res = []
        for values_dict in data[input_key]:
            for i in range(1, (size + 1)):
                elem = {}
                for column in columns:
                    key = f"{column}_{i}"
                    elem[column] = values_dict.get(key, None)
                    if key in values_dict:
                        del values_dict[key]
                res.append(elem)
            data[output_key] = res
        return data


class Acord211Schema(AcordFormSchema):
    schedule_of_hazards = fields.List(fields.Nested(AcordHazardSchema, allow_none=True), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord211Form(**data)


class Applied126Schema(AcordFormSchema):
    schedule_of_hazards = fields.List(fields.Nested(AcordHazardSchema, allow_none=True), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Applied126Form(**data)


class Applied125Schema(AcordFormSchema):
    applicant_information = fields.List(fields.Nested(AcordApplicantInformationTypeSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Applied125Form(**data)


class SensibleDocumentTypeSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    id = fields.UUID(allow_none=False)
    name = fields.String(allow_none=False)

    @post_load
    def make_object(self, data, **kwargs):
        return SensibleDocumentType(**data)


class SensibleConfigurationVersionSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    version_id = fields.String(allow_none=False)
    environments = fields.List(fields.String(), allow_none=False, default=list, missing=list)
    last_updated = fields.DateTime(data_key="datetime")

    @post_load
    def make_object(self, data, **kwargs):
        return SensibleConfigurationVersion(**data)


class SensibleConfigurationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    name = fields.String(allow_none=False)
    versions = fields.List(
        fields.Nested(SensibleConfigurationVersionSchema), allow_none=False, default=list, missing=list
    )

    @post_load
    def make_object(self, data, **kwargs):
        return SensibleConfiguration(**data)


class AcordAgencyInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    agency = fields.Nested(SensibleStringValueSchema, allow_none=True)
    policy_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    carrier = fields.Nested(SensibleStringValueSchema, allow_none=True)
    effective_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    naic_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    named_insured = fields.Nested(SensibleStringValueSchema, allow_none=True)
    agency_customer_id = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> AcordAgencyInformation:
        return AcordAgencyInformation(**data)


class Acord823Schema(AcordFormSchema):
    agency_information = fields.List(fields.Nested(AcordAgencyInformationSchema), allow_none=True)
    premises_information = fields.List(fields.Nested(AcordPremisesInfoTypeSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord823Form:
        return Acord823Form(**data)


class Acord140PremisesInformationTableSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    subject_of_insurance = fields.Nested(SensibleStringValueSchema, allow_none=True)
    amount = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    coins_percentage = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    valuation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    causes_of_loss = fields.Nested(SensibleStringValueSchema, allow_none=True)
    inflation_guard_percentage = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    deductible = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    deductible_type = fields.Nested(SensibleStringValueSchema, allow_none=True)
    blanket_number = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    forms_and_conditions_to_apply = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord140PremisesInformationTable:
        return Acord140PremisesInformationTable(**data)


class Acord140BuildingInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    street_address = fields.Nested(SensibleEntityPartValueSchema, allow_none=True)
    building_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    description_of_property_covered = fields.Nested(SensibleStringValueSchema, allow_none=True)
    construction_type = fields.Nested(SensibleStringValueSchema, allow_none=True)
    distance_to_hydrant = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    distance_to_fire_station = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    fire_district = fields.Nested(SensibleStringValueSchema, allow_none=True)
    fire_district_code_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    fire_protection_class = fields.Nested(SensibleStringValueSchema, allow_none=True)
    number_of_stories = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_basements = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    year_built = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    total_area = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    building_improvements_wiring = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_improvements_wiring_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_improvements_plumbing = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_improvements_plumbing_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_improvements_roofing = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_improvements_roofing_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_improvements_heating = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_improvements_heating_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    burglar_alarm_type = fields.Nested(SensibleStringValueSchema, allow_none=True)
    burglar_alarm_certificate_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    burglar_alarm_expiration_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    burglar_alarm_serviced_by = fields.Nested(SensibleStringValueSchema, allow_none=True)
    burglar_alarm_extent = fields.Nested(SensibleStringValueSchema, allow_none=True)
    burglar_alarm_grade = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_guards_and_watchmen = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    premises_fire_protection = fields.Nested(SensibleStringValueSchema, allow_none=True)
    sprinkler_percentage = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    fire_alarm_manufacturer = fields.Nested(SensibleStringValueSchema, allow_none=True)
    premises_information_table = fields.List(fields.Nested(Acord140PremisesInformationTableSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord140BuildingInformation:
        return Acord140BuildingInformation(**data)


class Acord140Schema(AcordFormSchema):
    agency_information = fields.List(fields.Nested(AcordAgencyInformationSchema), allow_none=True)
    building_information_list = fields.List(fields.Nested(Acord140BuildingInformationSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord140Form:
        return Acord140Form(**data)


class Acord131PremisesInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    location = fields.Nested(SensibleStringValueSchema, allow_none=True)
    description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    annual_payroll = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    annual_gross_sales = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    foreign_gross_sales = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    employees = fields.Nested(SensibleIntegerValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord131PremisesInformation:
        return Acord131PremisesInformation(**data)


class Acord131AdditionalExposureSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    media_used = fields.Nested(SensibleStringValueSchema, allow_none=True)
    media_annual_cost = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    advertising_agency_services_used = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    advertising_agency_coverage = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    aircraft = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    dangerous_cargo = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    passengers_for_fee = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    units_not_insured_by_underlying = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    vehicles_leased_to_others = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    hired_coverages = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    bridge_dam_marine_work = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    typical_jobs_performed = fields.Nested(SensibleStringValueSchema, allow_none=True)
    agreement = fields.Nested(SensibleStringValueSchema, allow_none=True)
    crane_work = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    subcontractors_limit_less_than_applicant = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    applicant_self_insured = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    hospital_or_first_aid = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    doctor_nurse_coverage = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    number_of_doctors = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_nurses = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_beds = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    epa_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    hazardous_material_products = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pollution_coverage_standard_iso_exclusion = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pollution_coverage_standard_sudden_only = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pollution_coverage_endorsement = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pollution_coverage_separate = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    aircraft_installation = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    foreign_products_operations = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    product_liability_past_three_years = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    product_liability_past_three_years_details = fields.Nested(SensibleStringValueSchema, allow_none=True)
    independent_contractors_details = fields.Nested(SensibleStringValueSchema, allow_none=True)
    has_watercrafts = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    watercraft_location_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    watercraft_owned_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    watercraft_length_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    watercraft_horsepower_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    watercraft_location_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    watercraft_owned_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    watercraft_length_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    watercraft_horsepower_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_location_number_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_stories_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_units_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_pools_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_diving_boards_1 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_location_number_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_stories_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_units_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_pools_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    apartments_hotels_number_diving_boards_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord131AdditionalExposure:
        return Acord131AdditionalExposure(**data)


class Acord131PolicyInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    new = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    renew = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    umbrella = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    excess = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    occurrence = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    claims_made = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    voluntary = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    limits_occurrence = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limits_general_aggregate = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    proposed_retroactive_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    current_retroactive_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    retained_limit = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    first_dollar_defense = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord131PolicyInformation:
        return Acord131PolicyInformation(**data)


class Acord131Schema(AcordFormSchema):
    agency_information = fields.List(fields.Nested(AcordAgencyInformationSchema), allow_none=True)
    premises_information = fields.List(fields.Nested(Acord131PremisesInformationSchema), allow_none=True)
    additional_exposures = fields.List(fields.Nested(Acord131AdditionalExposureSchema), allow_none=True)
    policy_information = fields.List(fields.Nested(Acord131PolicyInformationSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord131Form:
        return Acord131Form(**data)


class Acord130LocationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    highest_floor = fields.Nested(SensibleIntegerValueSchema, allow_none=True, required=False)
    requested_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    raw_address = fields.Nested(SensibleStringValueSchema, allow_none=True, required=False)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130Location:
        return Acord130Location(**data)


class Acord130AgencyInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    agency_name_and_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    agency_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    producer_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    office_phone = fields.Nested(SensibleStringValueSchema, allow_none=True)
    mobile_phone = fields.Nested(SensibleStringValueSchema, allow_none=True)
    email = fields.Nested(SensibleStringValueSchema, allow_none=True)
    code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    sub_code = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130AgencyInformation:
        return Acord130AgencyInformation(**data)


class Acord130ApplicantInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    requested_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    office_phone = fields.Nested(SensibleStringValueSchema, allow_none=True)
    mobile_phone = fields.Nested(SensibleStringValueSchema, allow_none=True)
    requested_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    years_in_business = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    naics = fields.Nested(SensibleStringValueSchema, allow_none=True)
    website_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    org_type_sole_proprietorship = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    org_type_partnership = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    org_type_corporation = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    org_type_s_corp = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    org_type_llc = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    org_type_joint_venture = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    org_type_trust = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    org_type_unincorporated_association = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    federal_employer_id_number = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130ApplicantInformation:
        return Acord130ApplicantInformation(**data)


class Acord130SubmissionStatusSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    quote = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    issue_policy = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    bound = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    assigned_risk = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130SubmissionStatus:
        return Acord130SubmissionStatus(**data)


class Acord130BillingAuditInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    billing_plan_agency = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    billing_plan_direct = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    payment_plan_annual = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    payment_plan_semi_annual = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    payment_plan_quarterly = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    audit_expiration = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    audit_monthly = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    audit_semi_annual = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    audit_quarterly = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130BillingAuditInformation:
        return Acord130BillingAuditInformation(**data)


class Acord130PolicyInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    proposed_effective_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    proposed_expiration_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    policy_rating_effective_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    normal_anniversary_rating_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    participating = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    non_participating = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    retro_plan = fields.Nested(SensibleStringValueSchema, allow_none=True)
    workers_comp_states = fields.Nested(SensibleStringValueSchema, allow_none=True)
    employers_liability_each_accident = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    employers_liability_disease_policy_limit = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    employers_liability_disease_each_employee = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    workers_comp_state_other = fields.Nested(SensibleStringValueSchema, allow_none=True)
    deductible_medical = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    deductible_indemnity = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    policy_amount = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    other_coverages_usl = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_voluntary = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_foreign = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_managed_care = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    dividend_plan = fields.Nested(SensibleStringValueSchema, allow_none=True)
    additional_company_information = fields.Nested(SensibleStringValueSchema, allow_none=True)
    additional_coverages = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130PolicyInformation:
        return Acord130PolicyInformation(**data)


class Acord130RatingInformationRowSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    state = fields.Nested(SensibleStringValueSchema, allow_none=True)
    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    class_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    description_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    category = fields.Nested(SensibleStringValueSchema, allow_none=True)
    full_time_employees = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    part_time_employees = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_employees = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    sic = fields.Nested(SensibleStringValueSchema, allow_none=True)
    naics = fields.Nested(SensibleStringValueSchema, allow_none=True)
    payroll = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    rate = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    est_annual_manual_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130RatingInformationRow:
        return Acord130RatingInformationRow(**data)


class Acord130RatingInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    state = fields.Nested(SensibleStringValueSchema, allow_none=True)
    table = fields.List(fields.Nested(Acord130RatingInformationRowSchema), allow_none=True)

    @staticmethod
    def not_a_header_or_empty(row: Acord130RatingInformationRow) -> bool:
        """
        This is to prevent ingesting empty rows or row that is a list of headers
        """

        def _not_none(attr: str) -> bool:
            return getattr(row, attr, None) is not None

        return (
            _not_none("location_number")
            or _not_none("full_time_employees")
            or _not_none("part_time_employees")
            or _not_none("number_of_employees")
            or _not_none("rate")
            or _not_none("estimated_annual_manual_premium")
            or re.match("^0*[1-9]\\d*$", getattr(row.class_code, "value", ""))
        )

    @post_load
    def make_object(self, data, **kwargs) -> Acord130RatingInformation:
        data_updated = {"state": data.get("state")}
        try:
            table = data["table"] or []
            table = [row for row in table if self.not_a_header_or_empty(row)]
            data_updated["table"] = table
            return Acord130RatingInformation(**data_updated)
        except Exception:
            logger.error("Not urgent but Acord 130 parsing failed. Investigate.", exc_info=True)
            return Acord130RatingInformation(**data)


class Acord130PremiumInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    state = fields.Nested(SensibleStringValueSchema, allow_none=True)
    experience_mod = fields.Nested(SensibleFloatValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130PremiumInformation:
        return Acord130PremiumInformation(**data)


class Acord130PriorCarrierInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    carrier = fields.Nested(SensibleStringValueSchema, allow_none=True)
    policy_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    annual_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    mod = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    number_of_claims = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    amount_paid = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    reserve = fields.Nested(SensibleFloatValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130PriorCarrierInformation:
        return Acord130PriorCarrierInformation(**data)


class Acord130NatureOfBusinessSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    description_of_operations = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130PriorCarrierInformation:
        return Acord130NatureOfBusiness(**data)


class Acord130GeneralInformationSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    other_coverages_aircraft_or_watercraft = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_aircraft_or_watercraft_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_hazardous_material = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_hazerdous_material_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_underground_work = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_underground_work_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_work_over_water = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_work_over_water_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_other_business_engagement = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_other_business_engagement_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_subcontractors_used = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_subcontractors_used_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_work_sublet_without_insurance = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_work_sublet_without_insurance_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    other_coverages_written_safety_program = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_written_safety_program_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_group_transportation = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_group_transportation_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_employees_under_16_over_60 = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_employees_under_16_over_60_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_seasonal_employees = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_seasonal_employees_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_volunteer_or_donated_labor = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_volunteer_or_donated_labor_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_employees_with_physical_handicaps = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_employees_with_physical_handicaps_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    other_coverages_employees_out_of_state = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_employees_out_of_state_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_sponsors_athletic_teams = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_sponsors_athletic_teams_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_requires_employee_physical = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_requires_employee_physical_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_has_other_insurance = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_has_other_insurance_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_prior_college_declined = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_prior_college_declined_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_provides_employee_health_plans = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_provides_employee_health_plans_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    other_coverages_employees_perform_for_other_businesses = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_employees_perform_for_other_businesses_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )
    other_coverages_leases_employees = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_leases_employees_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_employees_work_from_home = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_employees_work_from_home_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_tax_liens_or_bankruptcy = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_tax_liens_or_bankruptcy_explanation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_coverages_undisputed_and_unpaid_workers_comp = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_coverages_undisputed_and_unpaid_workers_comp_explanation = fields.Nested(
        SensibleStringValueSchema, allow_none=True
    )

    @post_load
    def make_object(self, data, **kwargs) -> Acord130GeneralInformation:
        return Acord130GeneralInformation(**data)


class Acord130Schema(AcordFormSchema):
    locations = fields.List(fields.Nested(Acord130LocationSchema), allow_none=True)
    agency_information = fields.List(fields.Nested(Acord130AgencyInformationSchema), allow_none=True)
    applicant_information = fields.List(fields.Nested(Acord130ApplicantInformationSchema), allow_none=True)
    submission_status = fields.List(fields.Nested(Acord130SubmissionStatusSchema), allow_none=True)
    billing_audit_information = fields.List(fields.Nested(Acord130BillingAuditInformationSchema), allow_none=True)
    policy_information = fields.List(fields.Nested(Acord130PolicyInformationSchema), allow_none=True)
    rating_information = fields.List(fields.Nested(Acord130RatingInformationSchema), allow_none=True)
    premium_information = fields.List(fields.Nested(Acord130PremiumInformationSchema), allow_none=True)
    prior_carrier_information = fields.List(fields.Nested(Acord130PriorCarrierInformationSchema), allow_none=True)
    general_information = fields.List(fields.Nested(Acord130GeneralInformationSchema), allow_none=True)
    nature_of_business = fields.List(fields.Nested(Acord130NatureOfBusinessSchema), allow_none=True)
    proposed_effective_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    proposed_expiration_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord130Form:
        return Acord130Form(**data)


class Acord101Schema(AcordFormSchema):
    agency_information = fields.List(fields.Nested(AcordAgencyInformationSchema), allow_none=True)
    form_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    form_title = fields.Nested(SensibleStringValueSchema, allow_none=True)
    additional_remarks = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord101Form:
        return Acord101Form(**data)


class Acord829EndorsementsSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    vehicle_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    boat_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    item_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    form_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    form_name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    edition_date = fields.Nested(SensibleAcordDateValueSchema, allow_none=True)
    copyright_owner_code = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord829Endorsements:
        return Acord829Endorsements(**data)


class Acord829Schema(AcordFormSchema):
    agency_information = fields.List(fields.Nested(AcordAgencyInformationSchema), allow_none=True)
    forms_and_endorsements = fields.List(fields.Nested(Acord829EndorsementsSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Acord829Form:
        return Acord829Form(**data)


class Applied98Schema(AcordFormSchema):
    additional_coverages = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Applied98Form:
        return Applied98Form(**data)


class Applied130Schema(AcordFormSchema):
    locations = fields.List(fields.Nested(Acord130LocationSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> Applied130Form:
        return Applied130Form(**data)


class OFSCHHAZSchema(AcordFormSchema):
    schedule_of_hazards = fields.List(fields.Nested(AcordHazardSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs) -> OFSCHHAZForm:
        return OFSCHHAZForm(**data)


class Acord160SurroundingExposuresSchema(Schema):
    right_exposure = fields.Nested(SensibleStringValueSchema, allow_none=True)
    left_exposure = fields.Nested(SensibleStringValueSchema, allow_none=True)
    front_exposure = fields.Nested(SensibleStringValueSchema, allow_none=True)
    rear_exposure = fields.Nested(SensibleStringValueSchema, allow_none=True)
    right_distance = fields.Nested(SensibleStringValueSchema, allow_none=True)
    left_distance = fields.Nested(SensibleStringValueSchema, allow_none=True)
    front_distance = fields.Nested(SensibleStringValueSchema, allow_none=True)
    rear_distance = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160SurroundingExposures(**data)


class Acord160BuildingDetailsSchema(Schema):
    year_built = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    construction_type = fields.Nested(SensibleStringValueSchema, allow_none=True)
    number_of_stories = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    sprinkler_percentage = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    basement_present = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    basement_finished = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    wind_class = fields.Nested(SensibleStringValueSchema, allow_none=True)
    wind_class_resistive = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    wind_class_semi_resistive = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    wiring_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    roofing_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    plumbing_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    heating_year = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    roof_type = fields.Nested(SensibleStringValueSchema, allow_none=True)
    building_code_grade = fields.Nested(SensibleStringValueSchema, allow_none=True)
    inspected = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    grade_developed_for_community = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    grade_developed_for_specific_property = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    tax_code = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160BuildingDetails(**data)


class Acord160AthleticSponsorshipSchema(AthleticTeamsSponsoredSchema):
    extent_of_sponsorship = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160AthleticSponsorship(**data)


class Acord160LeasedEmployeeInfoSchema(Schema):
    name = fields.Nested(SensibleStringValueSchema, allow_none=True)
    wc_coverage = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160LeasedEmployeeInfo(**data)


class Acord160BusinessOperationSchema(Schema):
    street_city_state_zip = fields.Nested(SensibleStringValueSchema, allow_none=True)
    type_of_business_service = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    type_of_business_retail = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    type_of_business_office = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    type_of_business_wholesale = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_interest_own = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_interest_lease = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_interest_rent = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160BusinessOperation(**data)


class Acord160EquipmentRentalSchema(Schema):
    equipment = fields.Nested(SensibleStringValueSchema, allow_none=True)
    type_of_equipment_small_tools = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    type_of_equipment_large_equipment = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    instruction_given = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160EquipmentRental(**data)


class Acord160FireProtectionSchema(Schema):
    distance_to_hydrant = fields.Nested(SensibleStringValueSchema, allow_none=True)
    distance_to_fire_station = fields.Nested(SensibleStringValueSchema, allow_none=True)
    fire_district = fields.Nested(SensibleStringValueSchema, allow_none=True)
    fire_district_code_number = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160FireProtection(**data)


class Acord160CoverageDetailsSchema(Schema):
    limit = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    coinsurance_percentage = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    limit_valuation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    valuation_rc = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    valuation_fvrc = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    valuation_acv = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    inflation_percentage = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    deductible_type = fields.Nested(SensibleStringValueSchema, allow_none=True)
    deductible_amount = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    deductible_type_2 = fields.Nested(SensibleStringValueSchema, allow_none=True)
    deductible_amount_2 = fields.Nested(SensibleIntegerValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160CoverageDetails(**data)


class Acord160PremisesGeneralInformationSchema(Schema):
    has_heating_or_processing_boiler = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    boiler_last_inspection_date = fields.Nested(SensibleStringValueSchema, allow_none=True)
    boiler_carrier = fields.Nested(SensibleStringValueSchema, allow_none=True)
    has_specialized_equipment_over_100k = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    specialized_equipment_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    equipment_inspected_annually = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    has_swimming_pool = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pool_has_approved_fence = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pool_has_limited_access = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pool_has_diving_board = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pool_has_slide = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pool_is_above_ground = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pool_is_in_ground = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    pool_has_life_guard = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    is_under_construction = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160PremisesGeneralInformation(**data)


class Acord160ApartmentsAndCondominiumsSchema(Schema):
    has_playground = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    aluminum_wiring_used = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    aluminum_installation_date = fields.Nested(SensibleStringValueSchema, allow_none=True)
    aluminum_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    is_developer_or_contractor_board_member = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    is_property_manager_employed = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_applies_to_bare_walls = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    coverage_applies_to_finished_walls = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    smoke_detector_none = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    smoke_detector_battery = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    smoke_detector_wired = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    number_of_fire_divisions = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_units_per_fire_division = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    number_of_units_owner_occupied = fields.Nested(SensibleIntegerValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160ApartmentsAndCondominiums(**data)


class Acord160CrimeInformationSchema(Schema):
    alarm_hold_up = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    alarm_premises = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    alarm_safe_vault = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    alarm_description_local_gong = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    alarm_description_central_with_keys = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    alarm_description_central_without_keys = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    alarm_description_police_connect = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    alarm_cert_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    alarm_exp_date = fields.Nested(SensibleStringValueSchema, allow_none=True)
    grade = fields.Nested(SensibleStringValueSchema, allow_none=True)
    extent_of_protection = fields.Nested(SensibleStringValueSchema, allow_none=True)
    premises_alarm_level_1 = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    premises_alarm_level_2 = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    premises_alarm_level_3 = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    safe_vault_manufacturer = fields.Nested(SensibleStringValueSchema, allow_none=True)
    label_ul = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    label_smna = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    label_class = fields.Nested(SensibleStringValueSchema, allow_none=True)
    max_cash_on_premises = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    max_cash_with_messenger = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    money_on_premises_overnight = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    frequency_of_deposits = fields.Nested(SensibleStringValueSchema, allow_none=True)
    deadbolt_cylinder_door_locks = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    safe_door_construction = fields.Nested(SensibleStringValueSchema, allow_none=True)
    other_protection = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160CrimeInformation(**data)


class Acord160PremisesDetailsSchema(Schema):
    blanket_rate = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    building_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    occupancy_description = fields.Nested(SensibleStringValueSchema, allow_none=True)
    check_if_primary_premises = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    surrounding_exposures = fields.List(fields.Nested(Acord160SurroundingExposuresSchema), allow_none=True)
    fire_protection = fields.List(fields.Nested(Acord160FireProtectionSchema), allow_none=True)
    annual_sales_receipts = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_payroll = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    class_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    rate_number = fields.Nested(SensibleStringValueSchema, allow_none=True)
    rate_group = fields.Nested(SensibleStringValueSchema, allow_none=True)
    protection_class = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    rate_territory = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160PremisesDetails(**data)


class Acord160PremisesInformationSchema(Schema):
    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    premises_details = fields.List(fields.Nested(Acord160PremisesDetailsSchema), allow_none=True)
    property_coverage_building = fields.List(fields.Nested(Acord160CoverageDetailsSchema), allow_none=True)
    property_coverage_personal = fields.List(fields.Nested(Acord160CoverageDetailsSchema), allow_none=True)
    building_details = fields.List(fields.Nested(Acord160BuildingDetailsSchema), allow_none=True)
    general_information = fields.List(fields.Nested(Acord160PremisesGeneralInformationSchema), allow_none=True)
    apartments_and_condos = fields.List(fields.Nested(Acord160ApartmentsAndCondominiumsSchema), allow_none=True)
    crime_information = fields.List(fields.Nested(Acord160CrimeInformationSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160PremisesInformation(**data)


class Acord160PremiumInformationSchema(Schema):
    building_premium = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    personal_property_premium = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    liability_premium = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    optional_coverages_premium = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    minimum_premium = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    schedule_credits = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    deductible_credits = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    taxes_surcharge = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    total_estimated_premium = fields.Nested(SensibleIntegerValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160PremiumInformation(**data)


class Acord160AgencyInformationSchema(AcordAgencyInformationSchema):
    policy_type_standard = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    policy_type_special = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    policy_type = fields.Nested(SensibleStringValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160AgencyInformation(**data)


class Acord160GeneralInformationSchema(Schema):
    hazardous_material_involvement = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    athletic_teams_sponsored = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    athletic_teams_sponsored_list = fields.List(fields.Nested(Acord160AthleticSponsorshipSchema), allow_none=True)
    insurance_certificates_verified = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    leased_employees = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    leased_employees_list_to = fields.List(fields.Nested(Acord160LeasedEmployeeInfoSchema), allow_none=True)
    leased_employees_list_from = fields.List(fields.Nested(Acord160LeasedEmployeeInfoSchema), allow_none=True)
    other_businesses = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    other_businesses_list = fields.List(fields.Nested(Acord160BusinessOperationSchema), allow_none=True)
    involved_in_manufacturing_relabeling = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    involved_in_mixing_products = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    rent_or_loans_equipment_to_others = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    equipment_rental_list = fields.List(fields.Nested(Acord160EquipmentRentalSchema), allow_none=True)
    operation_after_hours = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    start_time = fields.Nested(SensibleStringValueSchema, allow_none=True)
    end_time = fields.Nested(SensibleStringValueSchema, allow_none=True)
    twenty_four_hour_operations = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160GeneralInformation(**data)


class Acord160FormSchema(AcordFormSchema):
    agency_information = fields.List(fields.Nested(Acord160AgencyInformationSchema), allow_none=True)
    general_information = fields.List(fields.Nested(Acord160GeneralInformationSchema), allow_none=True)
    premises_information = fields.List(fields.Nested(Acord160PremisesInformationSchema), allow_none=True)
    premium_information = fields.List(fields.Nested(Acord160PremiumInformationSchema), allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord160Form(**data)


class Acord139PropertyEntrySchema(Schema):
    class_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    location_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    building_number = fields.Nested(SensibleIntegerValueSchema, allow_none=True)
    description_of_property = fields.Nested(SensibleStringValueSchema, allow_none=True)
    street_address = fields.Nested(SensibleStringValueSchema, allow_none=True)
    city = fields.Nested(SensibleStringValueSchema, allow_none=True)
    state = fields.Nested(SensibleStringValueSchema, allow_none=True)
    zip_code = fields.Nested(SensibleStringValueSchema, allow_none=True)
    valuation = fields.Nested(SensibleStringValueSchema, allow_none=True)
    subject = fields.Nested(SensibleStringValueSchema, allow_none=True)
    value_100_percent = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    rate_or_loss_cost = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord139PropertyEntry(**data)


class Acord139LossInfoSectionSchema(Schema):
    coinsurance_percentage = fields.List(fields.Nested(SensibleFloatValueSchema, allow_none=True), allow_none=True)
    applicable_causes_of_loss = fields.List(fields.Nested(SensibleStringValueSchema, allow_none=True), allow_none=True)
    earthquake_cov = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    flood = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    sprinkler_leakage_excl = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    vandalism_excl = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    specific_average_rate_requested = fields.Nested(SensibleBoolValueSchema, allow_none=True)
    blanket_rate_requested = fields.Nested(SensibleBoolValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord139LossInfoSection(**data)


class Acord139TotalSectionSchema(Schema):
    total_value = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_rate_or_loss_cost = fields.Nested(SensibleFloatValueSchema, allow_none=True)
    total_premium = fields.Nested(SensibleFloatValueSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord139TotalSection(**data)


class Acord139FormSchema(AcordFormSchema):
    agency_information = fields.Nested(AcordAgencyInformationSchema, allow_none=True)
    loss_info_section = fields.Nested(Acord139LossInfoSectionSchema, allow_none=True)
    statement_of_values_table = fields.List(fields.Nested(Acord139PropertyEntrySchema), allow_none=True)
    total_section = fields.Nested(Acord139TotalSectionSchema, allow_none=True)

    @post_load
    def make_object(self, data, **kwargs):
        return Acord139Form(**data)

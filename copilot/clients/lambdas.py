from functools import partial
from typing import Any
from uuid import UUID
import json
import os

from common.utils.aws import (
    build_lambda_arn,
    detect_current_aws_account_id,
    detect_current_aws_region,
)
from events_common.model.types import SenderEvent
from infrastructure_common.logging import get_logger
from static_common.models.types import StrUUID
import boto3

logger = get_logger()


class LambdaClient:
    def __init__(self, interact_external: bool = True):
        lambda_arn = partial(build_lambda_arn, detect_current_aws_region(), detect_current_aws_account_id())

        self.client = boto3.client("lambda", region_name=detect_current_aws_region())
        self.sender_lambda_arn = lambda_arn(os.environ.get("EVENT_SENDER_LAMBDA_NAME"))
        self.select_scrapers_batch_arn = lambda_arn(os.environ.get("SELECT_SCRAPERS_BULK_LAMBDA_NAME"))
        self.exclude_facts_arn = lambda_arn("copilot-workers-exclude-facts")
        self.naics_classification_async_arn = lambda_arn(os.environ.get("NAICS_CLASSIFICATION_ASYNC_LAMBDA_NAME"))
        self.submission_business_deduplication_arn = lambda_arn(os.environ.get("BUSINESS_DEDUPLICATION_LN"))
        self.ims_creation_requests_details_arn = lambda_arn("infrastructure-events-ims-creation-requests-details")
        self.file_description_of_operations_generation_lambda_arn = lambda_arn(
            "copilot-workers-generate-file-desc-of-operations"
        )
        self.file_insights_arn = lambda_arn("insights-infer-file-insights-v2")
        self.interact_external = interact_external

    def invoke_sender_lambda(self, request: SenderEvent) -> None:
        if self.interact_external:
            self.client.invoke(FunctionName=self.sender_lambda_arn, InvocationType="Event", Payload=request.json())

    def invoke_select_scrapers_bulk_lambda(self, business_ids: list[str]) -> dict[str, dict[str, Any]]:
        if self.interact_external:
            payload = json.dumps({"kalepa_ids": business_ids, "skip_recently_ingested": False})
            response = self.client.invoke(
                FunctionName=self.select_scrapers_batch_arn,
                Payload=payload,
            )
            return json.loads(response["Payload"].read())
        return {}

    def invoke_exclude_facts(self, submission_id: str | UUID, entity_ids: list[str | UUID]) -> None:
        if self.interact_external:
            self.client.invoke(
                FunctionName=self.exclude_facts_arn,
                InvocationType="Event",
                Payload=json.dumps(
                    {
                        "submission_id": str(submission_id),
                        "entity_ids": [str(entity_id) for entity_id in entity_ids or []],
                    }
                ).encode("utf-8"),
            )

    def _invoke_lambda_async(self, request: str, lambda_arn: str) -> None:
        self.client.invoke(FunctionName=lambda_arn, InvocationType="Event", Payload=request)

    def _invoke_lambda(self, request: dict, lambda_arn: str) -> dict:
        res = {}
        try:
            logger.info("Starting lambda", lambda_arn=lambda_arn, input=request)
            res = self.client.invoke(
                FunctionName=lambda_arn, InvocationType="RequestResponse", Payload=json.dumps(request)
            )
            logger.info("Lambda finished", lambda_name=lambda_arn, input=request)
        except Exception:
            logger.exception("Failed to invoke lambda", request=request, lambda_arn=lambda_arn)
        return res

    def invoke_naics_classification_async(self, request: dict) -> None:
        self._invoke_lambda_async(json.dumps(request), self.naics_classification_async_arn)

    def invoke_submission_business_deduplication(self, request: dict) -> None:
        self._invoke_lambda_async(json.dumps(request), self.submission_business_deduplication_arn)

    def invoke_file_insights_async(self, request: dict) -> None:
        self._invoke_lambda_async(json.dumps(request), self.file_insights_arn)

    def invoke_get_ims_creation_requests_details_lambda(self, report_ids: list[str]) -> dict:
        if self.interact_external:
            payload = json.dumps({"report_ids": report_ids})
            response = self.client.invoke(
                FunctionName=self.ims_creation_requests_details_arn,
                Payload=payload,
            )
            return json.loads(response["Payload"].read())
        return {}

    def invoke_file_description_of_operations_generation_lambda(self, file_id: StrUUID) -> dict:
        request = {"file_id": str(file_id)}
        return self._invoke_lambda(request, self.file_description_of_operations_generation_lambda_arn)

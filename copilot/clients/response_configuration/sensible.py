from static_common.enums.burglar_alarm_type import BurglarAlarmType
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.coverage_names import CoverageName
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.smoke_detector_type import SmokeDetectorType
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.enums.wiring import Wiring
from static_common.taxonomies.industry_classification import NaicsCode

from copilot.models.mappers import ExtractionMethod
from copilot.models.types import CoverageType

ACORD_101_CONFIG = {
    "entity_sections": [
        {
            "name": "agency_information",
            "assign_fni": True,
            "fni_index": 0,
            "entity_type": SubmissionEntityType.BUSINESS.value,
            "address_is_required": False,
        },
    ],
    "raw_properties": [
        {
            "fields_mapping": [
                {
                    "name": "form_number",
                    "explanation": "Form Number",
                    "type": FieldType.TEXT.value,
                },
                {
                    "name": "form_title",
                    "explanation": "Form Title",
                    "type": FieldType.TEXT.value,
                },
                {
                    "name": "additional_remarks",
                    "explanation": "Additional Remarks",
                    "type": FieldType.TEXT.value,
                },
            ]
        }
    ],
}

ACORD_125_CONFIG = {
    "entity_sections": [
        {
            "name": "applicant_information",
            "use_as_fallback_requested_name": True,
            "assign_fni": True,
            "entity_type": SubmissionEntityType.BUSINESS.value,
            "fni_index": 0,
            "address_is_required": False,
            "fields_mapping": [
                {
                    "name": "applicant_info_gl_code_1",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.GL_CODE.value,
                },
                {
                    "name": "applicant_sic",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.SIC.value,
                },
                {
                    "name": "applicant_naics",
                    "type": FieldType.TEXT.value,
                    "infer_naics_subtype_from_value": True,
                },
                {
                    "name": "applicant_fein",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.FEIN.value,
                },
                {
                    "name": "applicant_website_address",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.WEBSITE.value,
                },
            ],
        },
        {
            "name": "premises_info",
            "generate_id": True,
            "create_parent_business": True,
            "entity_type": SubmissionEntityType.STRUCTURE.value,
            "remote_id_property_name": "building_number",
            "address_is_required": True,
            "fields_mapping": [
                {
                    "name": "inside_city_limits",
                    "subtype": FactSubtypeID.IN_CITY_LIMITS.value,
                    "type": FieldType.BOOLEAN.value,
                    "derived_prop_name": True,
                    "value_config": {
                        "extraction_method": ExtractionMethod.ONE_OF.value,
                        "values": [
                            {"name": "city_limits_inside", "value": True},
                            {"name": "city_limits_outside", "value": False},
                        ],
                    },
                },
                {
                    "name": "premises_interest_type",
                    "subtype": FactSubtypeID.PREMISES_INTEREST_TYPE.value,
                    "type": FieldType.TEXT.value,
                    "derived_prop_name": True,
                    "value_config": {
                        "extraction_method": ExtractionMethod.ONE_OF.value,
                        "values": [
                            {"name": "interest_owner", "value": "Owner"},
                            {"name": "interest_tenant", "value": "Tenant"},
                        ],
                    },
                },
                {
                    "name": "fulltime_employees",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.EMPLOYEE_COUNT.value,
                },
                {
                    "name": "parttime_employees",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.PT_EMPLOYEES_COUNT.value,
                },
                {
                    "name": "annual_revenues",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.TOTAL_SALES.value,
                    "value_config": {"extraction_method": ExtractionMethod.TIMESERIES},
                },
                {
                    "name": "occupied_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.OCCUPIED_AREA.value,
                },
                {
                    "name": "open_to_public_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.OPEN_TO_PUBLIC_AREA.value,
                },
                {
                    "name": "building_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.BUILDING_SIZE.value,
                },
                {
                    "name": "area_leased_to_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASED_AREA.value,
                },
                {
                    "name": "operations_description",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.PROPERTY_DESCRIPTION.value,
                },
            ],
        },
    ],
    "data_sections": [
        {
            "name": "nature_of_business",
            "fields_mapping": [
                {"name": "apartments", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_53.value},
                {"name": "condominiums", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_53.value},
                {"name": "contractor", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_23.value},
                {"name": "institutional", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_52.value},
                {"name": "manufacturing", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_31_33.value},
                {"name": "office", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_55.value},
                {"name": "restaurant", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_72.value},
                {"name": "retail", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_44_45.value},
                {"name": "service", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_56.value},
                {"name": "wholesale", "type": FieldType.BOOLEAN.value, "naics_code": NaicsCode.NAICS_42.value},
                {
                    "name": "description_of_primary_operations",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.OPERATIONS.value,
                },
                {
                    "name": "start_date",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.YEARS_IN_BUSINESS.value,
                    "value_config": {"extraction_method": ExtractionMethod.DIFFERENCE_IN_YEARS},
                },
                {
                    "name": "installation_service_repair_work_percentage",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.ON_PREMISE_INSTALLATION_SALES_PERCENT.value,
                },
                {
                    "name": "off_premises_work",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.OFF_PREMISE_INSTALLATION_SALES_PERCENT.value,
                },
                {
                    "name": "operations_description_of_other_named_insureds",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.OPERATIONS.value,
                },
            ],
        },
        {
            "name": "general_information",
            "fields_mapping": [
                {
                    "name": "is_applicant_subsidiary",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.IS_SUBSIDIARY.value,
                    "explanation": "subsidiary_parent_company_name",
                },
                {
                    "name": "subsidiary_percentage_owned",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.OWNED_BY_PARENT_PERCENT.value,
                },
                {
                    "name": "does_applicant_have_subsidiaries",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SUBSIDIARIES.value,
                    "explanation": "applicants_subsidiary_company_name",
                },
                {
                    "name": "applicants_subsidiary_percentage_owned",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.OWNER_OF_SUBSIDIARY_PERCENT.value,
                },
                {
                    "name": "is_formal_safety_plan_in_operation",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_SAFETY_PROGRAM.value,
                    "dependent_fields": {
                        "value_equal_to": True,
                        "fields": [
                            {
                                "name": "safety_program_manual",
                                "type": FieldType.BOOLEAN.value,
                                "subtype": FactSubtypeID.HAS_SAFETY_MANUAL.value,
                            },
                            {
                                "name": "safety_plan_monthly_meetings",
                                "type": FieldType.BOOLEAN.value,
                                "subtype": FactSubtypeID.HAS_MONTHLY_SAFETY_MEETINGS.value,
                            },
                            {
                                "name": "safety_plan_safety_position",
                                "type": FieldType.BOOLEAN.value,
                                "subtype": FactSubtypeID.HAS_SAFETY_POSITION.value,
                            },
                            {
                                "name": "safety_plan_osha",
                                "type": FieldType.BOOLEAN.value,
                                "subtype": FactSubtypeID.HAS_OSHA_SAFETY_PROGRAM.value,
                            },
                        ],
                    },
                },
                {
                    "name": "any_exposure_flammables",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_EXPOSURE_TO_FLAMMABLES.value,
                    "explanation": "flammables_explanation",
                },
                {
                    "name": "any_policy_or_coverage_declined",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.POLICY_OR_COVERAGE_DECLINED_CANCELED_PAST_THREE_YEARS.value,
                },
                {
                    "name": "reason_for_decline",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.REASON_FOR_DECLINE_CANCELLATION_NON_RENEWAL.value,
                    "derived_prop_name": True,
                    "value_config": {
                        "extraction_method": ExtractionMethod.ONE_OF.value,
                        "values": [
                            {"name": "coverage_declined_non_payment", "value": "Non-Payment"},
                            {"name": "coverage_declined_agent", "value": "Agent no longer represents carrier"},
                            {"name": "coverage_declined_non_renewal", "value": "Non-Renewal"},
                            {"name": "coverage_declined_underwriting", "value": "Underwriting"},
                            {"name": "coverage_declined_condition_corrected", "value": "Condition Corrected"},
                        ],
                    },
                },
                {
                    "name": "any_past_losses_relating_to_sexual_abuse",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_PAST_LOSSES_FOR_ABUSE_MOLESTATION_DISCRIMINATION.value,
                    "explanation": "any_past_losses_relating_to_sexual_abuse_explanation",
                },
                {
                    "name": "any_applicant_been_indicted",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ON_PREMISES_CRIME.value,
                    "explanation": "any_applicant_been_indicted_explanation",
                },
                {
                    "name": "any_uncorrected_fire_or_safety_violations",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SAFETY_CODE_VIOLATIONS.value,
                },
                {
                    "name": "has_applicant_had_forclosure",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BANKRUPTCY_RESTRUCTURING_LIQUIDATION_PAST_24_MONTHS.value,
                },
                {
                    "name": "has_applicant_had_judgement_or_lien",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_HAD_JUDGEMENT_OR_LIEN_PAST_FIVE_YEARS.value,
                },
                {
                    "name": "is_business_placed_in_trust",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_BEEN_PLACED_IN_A_TRUST.value,
                },
                {
                    "name": "name_of_trust",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.BUSINESS_TRUST.value,
                },
                {
                    "name": "any_foreign_operations",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_FOREIGN_OPERATIONS_PRODUCTS_OR_SALES.value,
                },
                {
                    "name": "does_applicant_have_other_business_ventures",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_OTHER_BUSINESSES_NOT_REQUESTING_COVERAGE.value,
                },
                {
                    "name": "does_applicant_have_other_business_ventures_explanation",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.OTHER_BUSINESSES_NOT_REQUESTING_COVERAGE.value,
                },
                {
                    "name": "does_applicant_own_lease_operate_any_drones",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.DRONE.value,
                    "explanation": "does_applicant_own_lease_operate_any_drones_explanation",
                },
                {
                    "name": "does_applicant_hire_others_to_operate_drones",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BUSINESS_HIRES_DRONES.value,
                    "explanation": "does_applicant_hire_others_to_operate_drones_explanation",
                },
            ],
        },
    ],
    "submission_properties": [
        {
            "path": "proposed_effective_date",
            "submission_property": "proposed_effective_date",
            "entity_information": "POLICY_EFFECTIVE_START_DATE",
        },
        {
            "path": "proposed_expiration_date",
            "submission_property": "policy_expiration_date",
            "entity_information": "POLICY_END_DATE",
            "apply_for_organizations": [ExistingOrganizations.Paragon.value, ExistingOrganizations.MarkelDemo.value],
        },
        {
            "path": "lines_of_business",
            "submission_property": "coverages",
            "entity_information": "COVERAGES_DETAILS",
            "apply_for_organizations": [
                ExistingOrganizations.BishopConifer.value,
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.agency",
            "submission_property": "agency",
            "entity_information": "AGENCY",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.carrier",
            "submission_property": "carrier",
            "entity_information": "CARRIER",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.fax",
            "submission_property": "contact_fax",
            "entity_information": "AGENCY_FAX",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.naic_code",
            "submission_property": "naic_code",
            "entity_information": "CARRIER_NAIC_CODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.phone",
            "submission_property": "contact_phone",
            "entity_information": "AGENCY_PHONE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.contact_name",
            "submission_property": "contact_name",
            "entity_information": "AGENCY_CONTACT_NAME",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.email",
            "submission_property": "contact_email",
            "entity_information": "AGENCY_EMAIL_ADDRESS",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.agency_customer_id",
            "submission_property": "agency_customer_id",
            "entity_information": "AGENCY_CUSTOMER_ID",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.policy_number",
            "submission_property": "policy_number",
            "entity_information": "CARRIER_POLICY_NUMBER",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.transaction_status",
            "submission_property": "transaction_status",
            "entity_information": "CARRIER_STATUS_OF_TRANSACTION",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.transaction_type",
            "submission_property": "transaction_type",
            "entity_information": "CARRIER_TRANSACTION_TYPE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.transaction_date",
            "submission_property": "transaction_date",
            "entity_information": "CARRIER_TRANSACTION_DATE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.company_policy_or_program_name",
            "submission_property": "company_policy_or_program_name",
            "entity_information": "CARRIER_COMPANY_POLICY_OR_PROGRAM_NAME",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.underwriter",
            "submission_property": "underwriter",
            "entity_information": "CARRIER_UNDERWRITER",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.underwriter_office",
            "submission_property": "underwriter_office",
            "entity_information": "CARRIER_UNDERWRITER_OFFICE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.program_code",
            "submission_property": "program_code",
            "entity_information": "CARRIER_PROGRAM_CODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.code",
            "submission_property": "code",
            "entity_information": "AGENCY_CODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.sub_code",
            "submission_property": "sub_code",
            "entity_information": "AGENCY_SUBCODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.date",
            "submission_property": "date",
            "entity_information": "AGENCY_DATE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
    ],
}

ACORD_126_CONFIG = {
    "entity_sections": [],
    "data_sections": [
        {
            "name": "contractors",
            "fields_mapping": [
                {
                    "name": "applicant_draw_plans_for_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_DRAW_PLANS.value,
                    "explanation": "applicant_draw_plans_for_others_explanation",
                },
                {
                    "name": "any_operations_include_blasting_or_explosive_material",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_BLASTING_WORK.value,
                    "explanation": "any_operations_include_blasting_or_explosive_material_explanation",
                },
                {
                    "name": "any_operation_include_excavation",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_EXCAVATION_WORK.value,
                    "explanation": "any_operation_include_excavation_explanation",
                },
                {
                    "name": "subcontractors_carry_coverages_less",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SUBCONTRACTOR_COVERAGES_OR_LIMITS_LESS_THAN_NAMED_INSURED.value,
                    "explanation": "subcontractors_carry_coverages_less_explanation",
                },
                {
                    "name": "subcontractors_allowed_to_work_without_insurance_certificate",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SUBCONTRACTORS_WITHOUT_INSURANCE.value,
                    "explanation": "subcontractors_allowed_to_work_without_insurance_certificate_explanation",
                },
                {
                    "name": "applicant_lease_equipment_to_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASES_EQUIPMENT.value,
                    "explanation": "applicant_lease_equipment_to_others_explanation",
                },
                {
                    "name": "applicant_lease_equipment_to_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASES_EQUIPMENT.value,
                    "explanation": "applicant_lease_equipment_to_others_explanation",
                },
                {
                    "name": "money_paid_to_subcontractors",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.PROJECT_SUBCONTRACTORS_COST.value,
                },
                {
                    "name": "percentage_of_work_subcontracted",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS.value,
                },
                {
                    "name": "number_of_full_time_staff",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.EMPLOYEE_COUNT.value,
                },
                {
                    "name": "number_of_part_time_staff",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.PT_EMPLOYEES_COUNT.value,
                },
            ],
        },
        {
            "name": "products",
            "fields_mapping": [
                {
                    "name": "applicant_installs_services_demonstrates_products",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.INSTALLS_SERVICES_OR_DEMONSTRATE_PRODUCTS.value,
                    "explanation": "applicant_installs_services_demonstrates_products_explanation",
                },
                {
                    "name": "foreign_products",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_FOREIGN_OPERATIONS_PRODUCTS_OR_SALES.value,
                    "explanation": "foreign_products_explanation",
                },
                {
                    "name": "research_and_development_conducted",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_RESEARCH_AND_DEVELOPMENT.value,
                    "explanation": "research_and_development_conducted_explanation",
                },
                {
                    "name": "guarantees_warranties_hold_harmless_agreements",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_WARRANTIES_OR_GUARANTEES.value,
                    "explanation": "guarantees_warranties_hold_harmless_agreements_explanation",
                },
                {
                    "name": "products_related_to_aircraft_space_industry",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_PRODUCTS_FOR_AIRCRAFT_OR_SPACE.value,
                    "explanation": "products_related_to_aircraft_space_industry_explanation",
                },
                {
                    "name": "products_recalled_discontinued_changed",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_PRODUCT_RECALLS.value,
                    "explanation": "products_recalled_discontinued_changed_explanation",
                },
                {
                    "name": "products_of_others_sold",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.REPACKAGES_PRODUCTS_FROM_OTHERS.value,
                    "explanation": "products_of_others_sold_explanation",
                },
                {
                    "name": "products_under_label_of_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_PRODUCTS_UNDER_LABEL_FROM_OTHERS.value,
                    "explanation": "products_under_label_of_others_explanation",
                },
                {
                    "name": "vendors_coverage_required",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.VENDORS_COVERAGE_REQUIRED.value,
                    "explanation": "vendors_coverage_required_explanation",
                },
                {
                    "name": "any_named_insureds_sell_to_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ANY_NAMED_INSURED_SELLS_TO_OTHER_NAMED_INSURED.value,
                    "explanation": "any_named_insureds_sell_to_others_explanation",
                },
            ],
        },
        {
            "name": "general_information",
            "fields_mapping": [
                {
                    "name": "any_medical_facilities_provided",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.MEDICAL_FACILITIES_OR_PROFESSIONALS.value,
                    "explanation": "any_medical_facilities_provided_explanation",
                },
                {
                    "name": "any_exposure_radioactive_nuclear_materials",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.USES_RADIOACTIVE_MATERIALS.value,
                    "explanation": "any_exposure_radioactive_nuclear_materials_explanation",
                },
                {
                    "name": "hazardous_material",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL.value,
                    "explanation": "hazardous_material_explanation",
                },
                {
                    "name": "any_operations_sold_acquired_discontinued",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SOLD_ACQUIRED_OR_DISCONTINUED_OPERATIONS_IN_LAST_FIVE_YEARS.value,
                    "explanation": "any_operations_sold_acquired_discontinued_explanation",
                },
                {
                    "name": "equipment_loaned_or_rented",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LOANS_OR_RENTS_MACHINERY.value,
                    "explanation": "equipment_loaned_or_rented_explanation",
                    # in cases where instead of a text description we have a table or lists in the acord
                    "list_explanations": [
                        {
                            "name": "List of loaned or rented equipment: ",
                            "property": "equipment_loaned_or_rented_list",
                        }
                    ],
                },
                {
                    "name": "watercraft_docks_floats",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_WATERCRAFT_OR_DOCKS.value,
                    "explanation": "watercraft_docks_floats_explanation",
                },
                {
                    "name": "parking_facilities_owned_rented",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PARKING_PRIVATE_LOT.value,
                    "explanation": "parking_facilities_owned_rented_explanation",
                },
                {
                    "name": "fee_charged_for_parking",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PARKING_FEES.value,
                    "explanation": "fee_charged_for_parking_explanation",
                },
                {
                    "name": "recreation_facilities_provided",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.RECREATIONAL_FACILITIES.value,
                    "explanation": "recreation_facilities_provided_explanation",
                },
                {
                    "name": "lodging_operations",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_LODGING_OPERATIONS.value,
                },
                {
                    "name": "lodging_operations_number_of_apartments",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.NUMBER_OF_UNITS.value,
                },
                {
                    "name": "lodging_operations_total_apartment_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.LODGING_OPERATIONS_AREA.value,
                },
                {
                    "name": "swimming_pool_on_premises",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SWIMMING_POOL.value,
                    "explanation": "swimming_pool_on_premises_explanation",
                },
                {
                    "name": "social_events",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SPONSORS_SOCIAL_EVENTS.value,
                    "explanation": "social_events_explanation",
                },
                {
                    "name": "athletic_teams_sponsored",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SPONSORS_ATHLETIC_TEAMS.value,
                    "list_explanations": [
                        {
                            "name": "List of sponsored teams: ",
                            "property": "athletic_teams_sponsored_list",
                        }
                    ],
                },
                {
                    "name": "structural_alterations_contemplated",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.CONTEMPLATING_STRUCTURAL_ALTERATIONS.value,
                    "explanation": "structural_alterations_contemplated_explanation",
                },
                {
                    "name": "any_demolition_exposure_contemplated",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.CONTEMPLATING_DEMOLITION.value,
                    "explanation": "any_demolition_exposure_contemplated_explanation",
                },
                {
                    "name": "applicant_involved_joint_ventures",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ACTIVE_IN_JOINT_VENTURES.value,
                    "explanation": "applicant_involved_joint_ventures_explanation",
                },
                {
                    "name": "lease_employees_other_employers",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASES_EMPLOYEES.value,
                    "explanation": "lease_employees_other_employers_explanation",
                    "list_explanations": [
                        {
                            "name": "Leasing employees to: ",
                            "property": "lease_employees_other_employers_lease_to_list",
                        },
                        {
                            "name": "Leasing employees from: ",
                            "property": "lease_employees_other_employers_lease_from_list",
                        },
                    ],
                },
                {
                    "name": "labor_interchange_other_business_or_subsidiaries",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LABOR_INTERCHANGE.value,
                    "explanation": "labor_interchange_other_business_or_subsidiaries_explanation",
                },
                {
                    "name": "day_care_facilities",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_DAY_CARE.value,
                    "explanation": "day_care_facilities_explanation",
                },
                {
                    "name": "crimes_occured_or_attempted",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ON_PREMISES_CRIME.value,
                    "explanation": "crimes_occured_or_attempted_explanation",
                },
                {
                    "name": "formal_safety_policy",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SAFETY_POLICY.value,
                    "explanation": "formal_safety_policy_explanation",
                },
                {
                    "name": "does_business_promotion_represent_premises_safety_or_security",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROMOTES_PREMISES_SAFETY.value,
                    "explanation": "does_business_promotion_represent_premises_safety_or_security_explanation",
                },
            ],
        },
        {
            "name": "schedule_of_hazards",
            "transient_data": [
                {
                    "serialized_name": "schedule_of_hazards",
                    "data_fields": [
                        "location_number",
                        "hazard_number",
                        "classification",
                        "class_code",
                        "premium_basis",
                        "exposure",
                        "territorial_rating",
                        "rate_prem_ops",
                        "rate_products",
                        "premium_prem_ops",
                        "premium_products",
                    ],
                }
            ],
        },
    ],
    "submission_properties": [
        {
            "path": "application_information.agency",
            "submission_property": "agency",
            "entity_information": "AGENCY",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "coverages",
            "submission_property": "coverages",
            "entity_information": "COVERAGES_DETAILS",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "application_information.carrier",
            "submission_property": "carrier",
            "entity_information": "CARRIER",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.agency_customer_id",
            "submission_property": "agency_customer_id",
            "entity_information": "AGENCY_CUSTOMER_ID",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.fax",
            "submission_property": "contact_fax",
            "entity_information": "AGENCY_FAX",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.phone",
            "submission_property": "contact_phone",
            "entity_information": "AGENCY_PHONE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.policy_number",
            "submission_property": "policy_number",
            "entity_information": "CARRIER_POLICY_NUMBER",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.naic_code",
            "submission_property": "naic_code",
            "entity_information": "CARRIER_NAIC_CODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.date",
            "submission_property": "date",
            "entity_information": "AGENCY_DATE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.code",
            "submission_property": "code",
            "entity_information": "AGENCY_CODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.sub_code",
            "submission_property": "sub_code",
            "entity_information": "AGENCY_SUBCODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.bill_agency",
            "submission_property": "agency_bill",
            "entity_information": "AGENCY_BILL",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.bill_direct",
            "submission_property": "direct_bill",
            "entity_information": "AGENCY_DIRECT_BILL",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.audit",
            "submission_property": "agency_audit",
            "entity_information": "AGENCY_AUDIT",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.payment_plan",
            "submission_property": "agency_payment_plan",
            "entity_information": "AGENCY_PAYMENT_PLAN",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.for_company_use_only",
            "submission_property": "for_company_use_only",
            "entity_information": "AGENCY_FOR_COMPANY_USE_ONLY",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.effective_date",
            "submission_property": "proposed_effective_date",
            "entity_information": "POLICY_EFFECTIVE_START_DATE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "application_information.expiration_date",
            "submission_property": "policy_expiration_date",
            "entity_information": "POLICY_END_DATE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
    ],
    "file_classification": ClassificationDocumentType.ACORD_126.value,
    "coverages": [
        {
            "coverage_name": CoverageName.Liability.name,
            "coverage_type": CoverageType.PRIMARY.value,
            "apply_for_organizations": [ExistingOrganizations.BishopConifer.value],
        }
    ],
}

ACORD_823_CONFIG = {
    "entity_sections": [
        {
            "name": "premises_information",
            "generate_id": True,
            "create_parent_business": True,
            "entity_type": SubmissionEntityType.STRUCTURE.value,
            "remote_id_property_name": "building_number",
            "address_is_required": True,
            "fields_mapping": [
                {
                    "name": "inside_city_limits",
                    "subtype": FactSubtypeID.IN_CITY_LIMITS.value,
                    "type": FieldType.BOOLEAN.value,
                    "derived_prop_name": True,
                    "value_config": {
                        "extraction_method": ExtractionMethod.ONE_OF.value,
                        "values": [
                            {"name": "city_limits_inside", "value": True},
                            {"name": "city_limits_outside", "value": False},
                        ],
                    },
                },
                {
                    "name": "premises_interest_type",
                    "subtype": FactSubtypeID.PREMISES_INTEREST_TYPE.value,
                    "type": FieldType.TEXT.value,
                    "derived_prop_name": True,
                    "value_config": {
                        "extraction_method": ExtractionMethod.ONE_OF.value,
                        "values": [
                            {"name": "interest_owner", "value": "Owner"},
                            {"name": "interest_tenant", "value": "Tenant"},
                        ],
                    },
                },
                {
                    "name": "fulltime_employees",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.EMPLOYEE_COUNT.value,
                },
                {
                    "name": "parttime_employees",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.PT_EMPLOYEES_COUNT.value,
                },
                {
                    "name": "annual_revenues",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.TOTAL_SALES.value,
                    "value_config": {"extraction_method": ExtractionMethod.TIMESERIES},
                },
                {
                    "name": "occupied_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.OCCUPIED_AREA.value,
                },
                {
                    "name": "open_to_public_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.OPEN_TO_PUBLIC_AREA.value,
                },
                {
                    "name": "building_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.BUILDING_SIZE.value,
                },
                {
                    "name": "area_leased_to_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASED_AREA.value,
                },
                {
                    "name": "operations_description",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.PROPERTY_DESCRIPTION.value,
                },
            ],
        }
    ],
}

ACORD_131_CONFIG = {
    "entity_sections": [
        {
            "name": "premises_information",
            "address_is_required": True,
            "entity_type": SubmissionEntityType.BUSINESS.value,
            "fields_mapping": [
                {
                    "name": "description",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.OPERATIONS.value,
                },
                {
                    "name": "annual_payroll",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.PAYROLL.value,
                    "value_config": {"extraction_method": ExtractionMethod.TIMESERIES},
                },
                {
                    "name": "annual_gross_sales",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.TOTAL_SALES.value,
                    "value_config": {"extraction_method": ExtractionMethod.TIMESERIES},
                },
                {
                    "name": "foreign_gross_sales",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.FOREIGN_TOTAL_SALES.value,
                    "value_config": {"extraction_method": ExtractionMethod.TIMESERIES},
                },
                {
                    "name": "employees",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.EMPLOYEE_COUNT.value,
                },
            ],
        }
    ],
    "data_sections": [
        {
            "name": "additional_exposures",
            "fields_mapping": [
                {
                    "name": "media_used",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.MEDIA_USED.value,
                },
                {
                    "name": "media_annual_cost",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.ANNUAL_MEDIA_COST.value,
                },
                {
                    "name": "advertising_agency_services_used",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.USES_ADVERTISING_AGENCY.value,
                },
                {
                    "name": "advertising_agency_coverage",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.COVERAGE_FROM_ADVERTISING_AGENCY.value,
                },
                {
                    "name": "aircraft",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.AIRCRAFT_EXPOSURE.value,
                },
                {
                    "name": "dangerous_cargo",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.TRANSPORTATION_TRANSPORTS_HAZARDOUS_MATERIALS.value,
                },
                {
                    "name": "passengers_for_fee",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.TRANSPORTATION_TRANSPORTS_PASSENGERS.value,
                },
                {
                    "name": "units_not_insured_by_underlying",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_VEHICLES_NOT_INSURED_BY_UNDERLYING_POLICIES.value,
                },
                {
                    "name": "vehicles_leased_to_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.RENTS_VEHICLES_TO_OTHERS.value,
                },
                {
                    "name": "hired_coverages",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_COVERAGE_FOR_HIRED_AND_NON_OWNED_VEHICLES.value,
                },
                {
                    "name": "bridge_dam_marine_work",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BRIDGE_WORK.value,
                },
                {
                    "name": "bridge_dam_marine_work",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BRIDGE_WORK.value,
                },
                {
                    "name": "typical_jobs_performed",
                    "type": FieldType.TEXT.value,
                },
                {
                    "name": "agreement",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.CONTRACTOR_AGREEMENT_DESCRIPTION.value,
                },
                {
                    "name": "crane_work",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_CRANE_WORK.value,
                },
                {
                    "name": "subcontractors_limit_less_than_applicant",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SUBCONTRACTOR_COVERAGES_OR_LIMITS_LESS_THAN_NAMED_INSURED.value,
                },
                {
                    "name": "applicant_self_insured",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.IS_SELF_INSURED.value,
                },
                {
                    "name": "hospital_or_first_aid",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_HOSPITAL.value,
                },
                {
                    "name": "doctor_nurse_coverage",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_COVERAGE_FOR_DOCTORS_AND_NURSES.value,
                },
                {
                    "name": "number_of_doctors",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.NUMBER_OF_DOCTORS.value,
                },
                {
                    "name": "number_of_nurses",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.NUMBER_OF_NURSES.value,
                },
                {
                    "name": "number_of_beds",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.NUMBER_OF_HOSPITAL_BEDS.value,
                },
                {
                    "name": "epa_number",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.EPA_REGISTRY_ID.value,
                },
                {
                    "name": "hazardous_material_products",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL.value,
                },
                {
                    "name": "pollution_coverage_standard_iso_exclusion",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_GL_COVERAGE_WITH_STANDARD_ISO_POLLUTION_EXCLUSION.value,
                },
                {
                    "name": "pollution_coverage_standard_sudden_only",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_GL_COVERAGE_WITH_STANDARD_SUDDEN_AND_ACCIDENTAL_ONLY.value,
                },
                {
                    "name": "pollution_coverage_endorsement",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_GL_COVERAGE_POLLUTION_COVERAGE_ENDORSEMENT.value,
                },
                {
                    "name": "pollution_coverage_separate",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SEPARATE_POLLUTION_COVERAGE.value,
                },
                {
                    "name": "aircraft_installation",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_PRODUCTS_FOR_AIRCRAFT_OR_SPACE.value,
                },
                {
                    "name": "foreign_products_operations",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_FOREIGN_OPERATIONS_PRODUCTS_OR_SALES.value,
                },
                {
                    "name": "product_liability_past_three_years",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_RECENT_PRODUCT_LIABILITY_LOSS.value,
                    "explanation": "product_liability_past_three_years_details",
                },
                {
                    "name": "foreign_products_operations",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_FOREIGN_OPERATIONS_PRODUCTS_OR_SALES.value,
                },
                {
                    "name": "independent_contractors_details",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.INDEPENDENT_CONTRACTORS_DESCRIPTION.value,
                },
                {
                    "name": "has_watercrafts",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.HAS_WATERCRAFT_OR_DOCKS.value,
                },
            ],
            "data_sections": [
                {
                    "name": "watercrafts",
                    "entity_match": "location_number",
                    "fields_mapping": [
                        {
                            "name": "owned",
                            "type": FieldType.INTEGER.value,
                            "subtype": FactSubtypeID.NUMBER_OF_WATERCRAFT.value,
                        },
                        {
                            "name": "length",
                            "type": FieldType.INTEGER.value,
                        },
                        {
                            "name": "horsepower",
                            "type": FieldType.INTEGER.value,
                        },
                    ],
                },
                {
                    "name": "apartments_hotels",
                    "entity_match": "location_number",
                    "fields_mapping": [
                        {
                            "name": "stories",
                            "type": FieldType.INTEGER.value,
                            "subtype": FactSubtypeID.NUMBER_OF_STORIES.value,
                        },
                        {
                            "name": "units",
                            "type": FieldType.INTEGER.value,
                            "subtype": FactSubtypeID.NUMBER_OF_UNITS.value,
                        },
                        {
                            "name": "pools",
                            "type": FieldType.INTEGER.value,
                            "subtype": FactSubtypeID.NUMBER_OF_SWIMMING_POOLS.value,
                        },
                        {
                            "name": "has_diving_board",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_SLIDE_OR_DIVING_BOARD.value,
                        },
                    ],
                },
            ],
        },
        {
            "name": "policy_information",
            "transient_data": [
                {
                    "serialized_name": "policy_information",
                    "data_fields": [
                        "new",
                        "renewal",
                        "umbrella",
                        "excess",
                        "occurrence",
                        "claims_made",
                        "voluntary",
                        "limits_occurrence",
                        "limits_general_aggregate",
                        "proposed_retroactive_date",
                        "current_retroactive_date",
                        "retained_limit",
                        "first_dollar_defense",
                    ],
                }
            ],
        },
    ],
    "submission_properties": [
        {
            "path": "agency_information.effective_date",
            "submission_property": "effective_date",
            "entity_information": "POLICY_EFFECTIVE_START_DATE",
        },
        {
            "path": "agency_information.agency",
            "submission_property": "agency",
            "entity_information": "AGENCY",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.carrier",
            "submission_property": "carrier",
            "entity_information": "CARRIER",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.naic_code",
            "submission_property": "naic_code",
            "entity_information": "CARRIER_NAIC_CODE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.agency_customer_id",
            "submission_property": "agency_customer_id",
            "entity_information": "AGENCY_CUSTOMER_ID",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.policy_number",
            "submission_property": "policy_number",
            "entity_information": "CARRIER_POLICY_NUMBER",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
        {
            "path": "agency_information.date",
            "submission_property": "date",
            "entity_information": "AGENCY_DATE",
            "apply_for_organizations": [
                ExistingOrganizations.MarkelDemo.value,
            ],
        },
    ],
    "use_named_insured_as_fallback": False,
    "file_classification": ClassificationDocumentType.ACORD_131.value,
}

ACORD_140_CONFIG = {
    "entity_sections": [
        {
            "name": "building_information_list",
            "generate_id": True,
            "create_parent_business": True,
            "entity_type": SubmissionEntityType.STRUCTURE.value,
            "remote_id_property_name": "building_number",
            "address_is_required": False,
            "fields_mapping": [
                {
                    "name": "property_description",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.PROPERTY_DESCRIPTION.value,
                    "derived_prop_name": True,
                    "value_config": {
                        "extraction_method": ExtractionMethod.ONE_OF.value,
                        "values": [
                            {"name": "building_description"},
                            {"name": "description_of_property_covered"},
                        ],
                    },
                },
                {
                    "name": "construction_type",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.CONSTRUCTION_CLASS.value,
                },
                {
                    "name": "distance_to_hydrant",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.DISTANCE_TO_FIRE_HYDRANT.value,
                    "units": "Feet",
                },
                {
                    "name": "distance_to_fire_station",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.DISTANCE_TO_FIRE_STATION.value,
                    "units": "Miles",
                },
                {
                    "name": "fire_district",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.FIRE_DISTRICT.value,
                },
                {
                    "name": "fire_district_code_number",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.FIRE_DISTRICT_CODE_NUMBER.value,
                },
                {
                    "name": "fire_protection_class",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.ISO_FIRE_PROTECTION_CLASS.value,
                },
                {
                    "name": "number_of_stories",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.NUMBER_OF_STORIES.value,
                },
                {
                    "name": "number_of_basements",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.NUMBER_OF_BASEMENTS.value,
                },
                {
                    "name": "year_built",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.YEAR_BUILT.value,
                },
                {
                    "name": "total_area",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.BUILDING_SIZE.value,
                },
                {
                    "name": "building_improvements_wiring",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING.value,
                },
                {
                    "name": "building_improvements_wiring_year",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING_YEAR.value,
                },
                {
                    "name": "building_improvements_plumbing",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING.value,
                },
                {
                    "name": "building_improvements_plumbing_year",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING_YEAR.value,
                },
                {
                    "name": "building_improvements_roofing",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_ROOFING.value,
                },
                {
                    "name": "building_improvements_roofing_year",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_ROOFING_YEAR.value,
                },
                {
                    "name": "building_improvements_heating",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING.value,
                },
                {
                    "name": "building_improvements_heating_year",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING_YEAR.value,
                },
                {
                    "name": "burglar_alarm_type",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.BURGLAR_ALARM_TYPE.value,
                },
                {
                    "name": "burglar_alarm_certificate_number",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.BURGLAR_ALARM_CERTIFICATE_NUMBER.value,
                },
                {
                    "name": "burglar_alarm_expiration_date",
                    "type": FieldType.DATETIME.value,
                    "subtype": FactSubtypeID.BURGLAR_ALARM_EXPIRATION_DATE.value,
                },
                {
                    "name": "burglar_alarm_serviced_by",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.BURGLAR_ALARM_SERVICED_BY.value,
                },
                {
                    "name": "burglar_alarm_extent",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.BURGLAR_ALARM_EXTENT.value,
                },
                {
                    "name": "burglar_alarm_grade",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.BURGLAR_ALARM_GRADE.value,
                },
                {
                    "name": "number_of_guards_and_watchmen",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.NUMBER_OF_GUARDS.value,
                },
                {
                    "name": "premises_fire_protection",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.FIRE_PROTECTION.value,
                },
                {
                    "name": "sprinkler_percentage",
                    "type": FieldType.NUMBER.value,
                    "subtype": FactSubtypeID.SPRINKLER_AREA_COVERAGE_PERCENT.value,
                },
                {
                    "name": "fire_alarm_manufacturer",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.FIRE_ALARM_MANUFACTURER.value,
                },
            ],
            "transient_data": [
                {
                    "sensible_name": "premises_information_table",
                    "serialized_name": "premises_info",
                    "data_fields": [
                        "subject_of_insurance",
                        "amount",
                        "coins_percentage",
                        "valuation",
                        "causes_of_loss",
                        "inflation_guard_percentage",
                        "deductible",
                        "deductible_type",
                        "blanket_number",
                        "forms_and_conditions_to_apply",
                    ],
                }
            ],
        }
    ],
    "file_classification": ClassificationDocumentType.ACORD_140.value,
    "coverages": [
        {
            "coverage_name": CoverageName.Property.name,
            "coverage_type": CoverageType.PRIMARY.value,
            "apply_for_organizations": [
                ExistingOrganizations.BishopConifer.value,
                ExistingOrganizations.MarkelDemo.value,
            ],
        }
    ],
}

ACORD_130_CONFIG = {
    "entity_sections": [
        {
            "name": "applicant_information",
            "use_as_fallback_requested_name": True,
            "assign_fni": True,
            "entity_type": SubmissionEntityType.BUSINESS.value,
            "fni_index": 0,
            "address_is_required": True,
            "fallback_as_data_section": True,
            "fields_mapping": [
                {
                    "name": "years_in_business",
                    "type": FieldType.INTEGER.value,
                    "subtype": FactSubtypeID.YEARS_IN_BUSINESS.value,
                },
                {
                    "name": "naics",
                    "type": FieldType.TEXT.value,
                    "infer_naics_subtype_from_value": True,
                },
                {
                    "name": "website_address",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.WEBSITE.value,
                },
                {
                    "name": "federal_employer_id_number",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.FEIN.value,
                },
            ],
        },
        {
            "name": "locations",
            "entity_type": SubmissionEntityType.BUSINESS.value,
            "address_is_required": True,
        },
    ],
    "data_sections": [
        {
            "name": "general_information",
            "fields_mapping": [
                {
                    "name": "other_coverages_aircraft_or_watercraft",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.OWN_OR_OPERATE_AIRCRAFT_OR_WATERCRAFT.value,
                    "explanation": "other_coverages_aircraft_or_watercraft_explanation",
                },
                {
                    "name": "other_coverages_hazardous_material",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL.value,
                    "explanation": "other_coverages_hazerdous_material_explanation",
                },
                {
                    "name": "other_coverages_underground_work",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.WORK_PERFORMED_UNDERGROUND_OR_ABOVE_15_FEET.value,
                    "explanation": "other_coverages_underground_work_explanation",
                },
                {
                    "name": "other_coverages_work_over_water",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.WORK_ON_BARGES_VESSELS_DOCKS_BRIDGES_OVER_WATER.value,
                    "explanation": "other_coverages_work_over_water_explanation",
                },
                {
                    "name": "other_coverages_other_business_engagement",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ENGAGED_IN_ANY_OTHER_TYPE_OF_BUSINESS.value,
                    "explanation": "other_coverages_other_business_engagement_explanation",
                },
                {
                    "name": "other_coverages_subcontractors_used",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SUBCONTRACTOR.value,
                    "explanation": "other_coverages_subcontractors_used_explanation",
                },
                {
                    "name": "other_coverages_work_sublet_without_insurance",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ANY_WORK_SUBLET_WITHOUT_CERTIFICATES_OF_INSURANCE.value,
                    "explanation": "other_coverages_work_sublet_without_insurance_explanation",
                },
                {
                    "name": "other_coverages_written_safety_program",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SAFETY_POLICY.value,
                    "explanation": "other_coverages_written_safety_program_explanation",
                },
                {
                    "name": "other_coverages_group_transportation",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.GROUP_TRANSPORTATION_PROVIDED.value,
                    "explanation": "other_coverages_group_transportation_explanation",
                },
                {
                    "name": "other_coverages_employees_under_16_over_60",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.EMPLOYEES_UNDER_16_OR_OVER_60.value,
                    "explanation": "other_coverages_employees_under_16_over_60_explanation",
                },
                {
                    "name": "other_coverages_seasonal_employees",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ANY_SEASONAL_EMPLOYEES.value,
                    "explanation": "other_coverages_seasonal_employees_explanation",
                },
                {
                    "name": "other_coverages_volunteer_or_donated_labor",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.VOLUNTEER_OR_DONATED_LABOR.value,
                    "explanation": "other_coverages_volunteer_or_donated_labor_explanation",
                },
                {
                    "name": "other_coverages_employees_with_physical_handicaps",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ANY_EMPLOYEES_WITH_PHYSICAL_HANDICAPS.value,
                    "explanation": "other_coverages_employees_with_physical_handicaps_explanation",
                },
                {
                    "name": "other_coverages_employees_out_of_state",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.DO_EMPLOYEES_TRAVEL_OUT_OF_STATE.value,
                    "explanation": "other_coverages_employees_out_of_state_explanation",
                },
                {
                    "name": "other_coverages_sponsors_athletic_teams",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SPONSORS_ATHLETIC_TEAMS.value,
                    "explanation": "other_coverages_sponsors_athletic_teams_explanation",
                },
                {
                    "name": "other_coverages_requires_employee_physical",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PHYSICALS_REQUIRED_AFTER_EMPLOYMENT_OFFERS.value,
                    "explanation": "other_coverages_requires_employee_physical_explanation",
                },
                {
                    "name": "other_coverages_has_other_insurance",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ANY_OTHER_INSURANCE_WITH_THIS_INSURER.value,
                    "explanation": "other_coverages_has_other_insurance_explanation",
                },
                {
                    "name": "other_coverages_prior_college_declined",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.POLICY_OR_COVERAGE_DECLINED_CANCELED_PAST_THREE_YEARS.value,
                    "explanation": "other_coverages_prior_college_declined_explanation",
                },
                {
                    "name": "other_coverages_provides_employee_health_plans",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.EMPLOYEE_HEALTH_PLANS_PROVIDED.value,
                    "explanation": "other_coverages_provides_employee_health_plans_explanation",
                },
                {
                    "name": "other_coverages_employees_perform_for_other_businesses",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.EMPLOYEES_PERFORM_WORK_FOR_OTHER_BUSINESSES_OR_SUBSIDIARIES.value,
                    "explanation": "other_coverages_employees_perform_for_other_businesses_explanation",
                },
                {
                    "name": "other_coverages_leases_employees",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASES_EMPLOYEES.value,
                    "explanation": "other_coverages_leases_employees_explanation",
                },
                {
                    "name": "other_coverages_employees_work_from_home",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ANY_EMPLOYEES_PREDOMINANTLY_WORK_AT_HOME.value,
                    "explanation": "other_coverages_employees_work_from_home_explanation",
                },
                {
                    "name": "other_coverages_tax_liens_or_bankruptcy",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.BANKRUPTCY_RESTRUCTURING_LIQUIDATION_PAST_24_MONTHS.value,
                    "explanation": "other_coverages_tax_liens_or_bankruptcy_explanation",
                },
                {
                    "name": "other_coverages_undisputed_and_unpaid_workers_comp",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.ANY_UNDISPUTED_AND_UNPAID_WORKERS_COMPENSATION_DUE.value,
                    "explanation": "other_coverages_undisputed_and_unpaid_workers_comp_explanation",
                },
            ],
        },
        {
            "name": "nature_of_business",
            "fields_mapping": [
                {
                    "name": "description_of_operations",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.OPERATIONS.value,
                }
            ],
        },
    ],
    "submission_properties": [
        {
            "path": "proposed_effective_date",
            "submission_property": "proposed_effective_date",
            "entity_information": "POLICY_EFFECTIVE_START_DATE",
        },
        {
            "path": "proposed_expiration_date",
            "submission_property": "policy_expiration_date",
            "entity_information": "POLICY_END_DATE",
            "apply_for_organizations": [ExistingOrganizations.Paragon.value, ExistingOrganizations.MarkelDemo.value],
        },
        {
            "path": "agency_information.agency_name_and_address",
            "submission_property": "agency",
            "entity_information": "AGENCY",
            "apply_for_organizations": [
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.office_phone",
            "submission_property": "contact_phone",
            "entity_information": "AGENCY_PHONE",
            "apply_for_organizations": [
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.email",
            "submission_property": "contact_email",
            "entity_information": "AGENCY_EMAIL_ADDRESS",
            "apply_for_organizations": [
                ExistingOrganizations.SECURA.value,
            ],
        },
        {
            "path": "agency_information.code",
            "submission_property": "code",
            "entity_information": "AGENCY_CODE",
            "apply_for_organizations": [
                ExistingOrganizations.SECURA.value,
            ],
        },
    ],
    "coverages": [
        {
            "coverage_name": CoverageName.WorkersComp.name,
            "coverage_type": CoverageType.PRIMARY.value,
            "apply_for_organizations": [
                ExistingOrganizations.BishopConifer.value,
                ExistingOrganizations.MarkelDemo.value,
                ExistingOrganizations.SECURA.value,
            ],
        }
    ],
}

APPLIED_130_CONFIG = {
    "entity_sections": [
        {
            "name": "locations",
            "entity_type": SubmissionEntityType.BUSINESS.value,
            "address_is_required": True,
        },
    ]
}

ACORD_829_CONFIG = {
    "entity_sections": [
        {
            "name": "agency_information",
            "assign_fni": True,
            "fni_index": 0,
            "entity_type": SubmissionEntityType.BUSINESS.value,
            "address_is_required": False,
        },
    ],
    "raw_properties": [
        {
            "name": "Forms and Endorsements",
            "path": "forms_and_endorsements",
            "entity_type": SubmissionEntityType.RAW_ROW.value,
            "fields_mapping": [
                {
                    "name": "location_number",
                    "explanation": "Location Number",
                    "type": FieldType.NUMBER.value,
                },
                {
                    "name": "vehicle_number",
                    "explanation": "Vehicle Number",
                    "type": FieldType.NUMBER.value,
                },
                {
                    "name": "boat_number",
                    "explanation": "Boat Number",
                    "type": FieldType.NUMBER.value,
                },
                {
                    "name": "item_number",
                    "explanation": "Item Number",
                    "type": FieldType.NUMBER.value,
                },
                {
                    "name": "form_number",
                    "explanation": "Form Number",
                    "type": FieldType.TEXT.value,
                },
                {
                    "name": "form_name",
                    "explanation": "Form Name",
                    "type": FieldType.TEXT.value,
                },
                {
                    "name": "edition_date",
                    "explanation": "Edition Date",
                    "type": FieldType.TEXT.value,
                },
                {
                    "name": "copyright_owner_code",
                    "explanation": "Copyright Owner Code",
                    "type": FieldType.TEXT.value,
                },
            ],
        }
    ],
}

ACORD_211_CONFIG = {
    "data_sections": [
        {
            "name": "schedule_of_hazards",
            "transient_data": [
                {
                    "serialized_name": "schedule_of_hazards",
                    "data_fields": [
                        "location_number",
                        "hazard_number",
                        "classification",
                        "class_code",
                        "premium_basis",
                        "exposure",
                        "territorial_rating",
                        "rate_prem_ops",
                        "rate_products",
                        "premium_prem_ops",
                        "premium_products",
                    ],
                }
            ],
        },
    ],
    "file_classification": ClassificationDocumentType.ACORD_211.value,
}

APPLIED_98_CONFIG = {
    "raw_properties": [
        {
            "fields_mapping": [
                {
                    "name": "additional_coverages",
                    "explanation": "Additional Coverages",
                    "type": FieldType.TEXT.value,
                },
            ]
        }
    ],
}

APPLIED_126_CONFIG = {
    "data_sections": [
        {
            "name": "schedule_of_hazards",
            "transient_data": [
                {
                    "serialized_name": "schedule_of_hazards",
                    "data_fields": [
                        "location_number",
                        "building_number",
                        "hazard_number",
                        "classification",
                        "class_code",
                        "premium_basis",
                        "exposure",
                        "territorial_rating",
                        "rate_prem_ops",
                        "rate_products",
                        "premium_prem_ops",
                        "premium_products",
                    ],
                }
            ],
        },
    ],
    "file_classification": ClassificationDocumentType.APPLIED_126.value,
}

APPLIED_125_CONFIG = {
    "entity_sections": [
        {
            "name": "applicant_information",
            "assign_fni": True,
            "entity_type": SubmissionEntityType.BUSINESS.value,
            # it will never be matched and all entities will be marked as other_named_insured
            "fni_index": -1,
            "address_is_required": False,
            "fields_mapping": [
                {
                    "name": "applicant_info_gl_code_1",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.GL_CODE.value,
                },
                {
                    "name": "applicant_sic",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.SIC.value,
                },
                {
                    "name": "applicant_naics",
                    "type": FieldType.TEXT.value,
                    "infer_naics_subtype_from_value": True,
                },
                {
                    "name": "applicant_fein",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.FEIN.value,
                },
                {
                    "name": "applicant_website_address",
                    "type": FieldType.TEXT.value,
                    "subtype": FactSubtypeID.WEBSITE.value,
                },
            ],
        },
    ]
}

OFSCHHAZ_CONFIG = {
    "data_sections": [
        {
            "name": "schedule_of_hazards",
            "transient_data": [
                {
                    "serialized_name": "schedule_of_hazards",
                    "data_fields": [
                        "location_number",
                        "hazard_number",
                        "classification",
                        "class_code",
                        "premium_basis",
                        "exposure",
                        "territorial_rating",
                        "rate_prem_ops",
                        "rate_products",
                        "premium_prem_ops",
                        "premium_products",
                    ],
                }
            ],
        },
    ],
    "file_classification": ClassificationDocumentType.OFSCHHAZ.value,
}


ACORD_160_CONFIG = {
    "submission_properties": [
        {
            "path": "agency_information.effective_date",
            "submission_property": "proposed_effective_date",
            "entity_information": "POLICY_EFFECTIVE_START_DATE",
        },
        {
            "path": "agency_information.agency",
            "submission_property": "agency",
            "entity_information": "AGENCY",
        },
        {"path": "agency_information.carrier", "submission_property": "carrier", "entity_information": "CARRIER"},
        {
            "path": "agency_information.naic_code",
            "submission_property": "naic_code",
            "entity_information": "CARRIER_NAIC_CODE",
        },
        {
            "path": "agency_information.agency_customer_id",
            "submission_property": "agency_customer_id",
            "entity_information": "AGENCY_CUSTOMER_ID",
        },
        {
            "path": "agency_information.policy_number",
            "submission_property": "policy_number",
            "entity_information": "CARRIER_POLICY_NUMBER",
        },
        {
            "path": "agency_information.policy_type",
            "submission_property": "policy_type",
            "entity_information": "CARRIER_POLICY_TYPE",
        },
        {
            "path": "agency_information.date",
            "submission_property": "date",
            "entity_information": "AGENCY_DATE",
        },
    ],
    "file_classification": ClassificationDocumentType.ACORD_160.value,
    "data_sections": [
        {
            "name": "general_information",
            "fields_mapping": [
                {
                    "name": "hazardous_material_involvement",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL.value,
                },
                {
                    "name": "insurance_certificates_verified",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.OBTAIN_PRODUCT_LIABILITY_CERTIFICATES_FROM_PARTNERS.value,
                },
                {
                    "name": "athletic_teams_sponsored",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.SPONSORS_ATHLETIC_TEAMS.value,
                    "list_explanations": [
                        {
                            "name": "List of sponsored teams: ",
                            "property": "athletic_teams_sponsored_list",
                        }
                    ],
                },
                {
                    "name": "leased_employees",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASES_EMPLOYEES.value,
                    "list_explanations": [
                        {
                            "name": "List of leased employees to: ",
                            "property": "leased_employees_list_to",
                        },
                        {
                            "name": "List of leased employees from: ",
                            "property": "leased_employees_list_from",
                        },
                    ],
                },
                {
                    "name": "other_businesses",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.HAS_SUBSIDIARIES.value,
                    "list_explanations": [
                        {
                            "name": "List of owned businesses: ",
                            "property": "other_businesses_list",
                        }
                    ],
                },
                {
                    "name": "involved_in_manufacturing_relabeling",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.REPACKAGES_PRODUCTS_FROM_OTHERS.value,
                },
                {
                    "name": "involved_in_mixing_products",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.MIXING_PRODUCTS_FROM_OTHERS.value,
                },
                {
                    "name": "rent_or_loans_equipment_to_others",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.LEASES_EQUIPMENT.value,
                    "list_explanations": [
                        {
                            "name": "List of leased equipment: ",
                            "property": "equipment_rental_list",
                        }
                    ],
                },
                {
                    "name": "operation_after_hours",
                    "type": FieldType.BOOLEAN.value,
                    "subtype": FactSubtypeID.AFTER_HOURS_OR_24H_OPERATIONS.value,
                },
            ],
        },
    ],
    "entity_sections": [
        {
            "name": "premises_information",
            "generate_id": True,
            "create_parent_business": True,
            "entity_type": SubmissionEntityType.STRUCTURE.value,
            "remote_id_property_name": "building_number",
            "address_is_required": False,
            "data_sections": [
                {
                    "name": "premises_details",
                    "fields_mapping": [
                        {
                            "name": "building_description",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.PROPERTY_DESCRIPTION.value,
                        },
                        {
                            "name": "occupancy_description",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.OCCUPANCY_TYPE.value,
                        },
                        {
                            "name": "annual_sales_receipts",
                            "type": FieldType.NUMBER.value,
                            "subtype": FactSubtypeID.TOTAL_SALES.value,
                            "value_config": {"extraction_method": ExtractionMethod.TIMESERIES},
                        },
                        {
                            "name": "total_payroll",
                            "type": FieldType.NUMBER.value,
                            "subtype": FactSubtypeID.PAYROLL.value,
                            "value_config": {"extraction_method": ExtractionMethod.TIMESERIES},
                        },
                        {
                            "name": "class_code",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.OCCUPANCY_CLASS.value,
                        },
                        {
                            "name": "protection_class",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.FIRE_PROTECTION_CLASS.value,
                        },
                    ],
                    "data_sections": [
                        {
                            "name": "fire_protection",
                            "fields_mapping": [
                                {
                                    "name": "distance_to_hydrant",
                                    "type": FieldType.TEXT.value,
                                    "subtype": FactSubtypeID.DISTANCE_TO_FIRE_HYDRANT.value,
                                },
                                {
                                    "name": "distance_to_fire_station",
                                    "type": FieldType.TEXT.value,
                                    "subtype": FactSubtypeID.DISTANCE_TO_FIRE_STATION.value,
                                },
                                {
                                    "name": "fire_district",
                                    "type": FieldType.TEXT.value,
                                    "subtype": FactSubtypeID.FIRE_DISTRICT.value,
                                },
                                {
                                    "name": "fire_district_code_number",
                                    "type": FieldType.TEXT.value,
                                    "subtype": FactSubtypeID.FIRE_DISTRICT_CODE_NUMBER.value,
                                },
                            ],
                        },
                    ],
                },
                {
                    "name": "general_information",
                    "fields_mapping": [
                        {
                            "name": "has_swimming_pool",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_SWIMMING_POOL.value,
                        },
                        {
                            "name": "pool_has_approved_fence",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_POOL_FENCE.value,
                        },
                        {
                            "name": "pool_has_diving_board",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_POOL_DIVING_BOARD.value,
                        },
                        {
                            "name": "pool_has_slide",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_SLIDE_OR_DIVING_BOARD.value,
                        },
                        {
                            "name": "pool_has_life_guard",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_POOL_LIFEGUARD.value,
                        },
                    ],
                },
                {
                    "name": "apartments_and_condos",
                    "fields_mapping": [
                        {
                            "name": "has_playground",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_PLAYGROUND.value,
                        },
                        {
                            "name": "wiring",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.WIRING.value,
                            "derived_prop_name": True,
                            "value_config": {
                                "extraction_method": ExtractionMethod.ONE_OF.value,
                                "values": [
                                    {"name": "aluminum_wiring_used", "value": Wiring.ALUMINUM},
                                ],
                            },
                        },
                        {
                            "name": "is_developer_or_contractor_board_member",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.DEVELOPER_OR_CONTRACTOR_ON_BOARD.value,
                        },
                        {
                            "name": "is_property_manager_employed",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_PROPERTY_MANAGER.value,
                        },
                        {
                            "name": "smoke_detector_type",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.SMOKE_DETECTOR_TYPE.value,
                            "derived_prop_name": True,
                            "value_config": {
                                "extraction_method": ExtractionMethod.ONE_OF.value,
                                "values": [
                                    {"name": "smoke_detector_none", "value": SmokeDetectorType.NONE},
                                    {"name": "smoke_detector_battery", "value": SmokeDetectorType.BATTERY_POWERED},
                                    {"name": "smoke_detector_wired", "value": SmokeDetectorType.HARDWIRED},
                                ],
                            },
                        },
                    ],
                },
                {
                    "name": "crime_information",
                    "fields_mapping": [
                        {
                            "name": "burglar_alarm_type",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BURGLAR_ALARM_TYPE.value,
                            "derived_prop_name": True,
                            "value_config": {
                                "extraction_method": ExtractionMethod.ONE_OF.value,
                                "values": [
                                    {"name": "alarm_hold_up", "value": BurglarAlarmType.HOLD_UP},
                                    {"name": "alarm_premises", "value": BurglarAlarmType.LOCAL},
                                    {"name": "alarm_safe_vault", "value": BurglarAlarmType.SAFE_VAULT},
                                ],
                            },
                        },
                        {
                            "name": "grade",
                            "type": FieldType.NUMBER.value,
                            "subtype": FactSubtypeID.BURGLAR_ALARM_GRADE.value,
                        },
                        {
                            "name": "alarm_cert_number",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BURGLAR_ALARM_CERTIFICATE_NUMBER.value,
                        },
                        {
                            "name": "alarm_exp_date",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BURGLAR_ALARM_EXPIRATION_DATE.value,
                        },
                    ],
                },
                {
                    "name": "building_details",
                    "fields_mapping": [
                        {
                            "name": "year_built",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.YEAR_BUILT.value,
                        },
                        {
                            "name": "construction_type",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.CONSTRUCTION_CLASS.value,
                        },
                        {
                            "name": "number_of_stories",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.NUMBER_OF_STORIES.value,
                        },
                        {
                            "name": "sprinkler_percentage",
                            "type": FieldType.NUMBER.value,
                            "subtype": FactSubtypeID.SPRINKLER_AREA_COVERAGE_PERCENT.value,
                        },
                        {
                            "name": "basement_present",
                            "type": FieldType.BOOLEAN.value,
                            "subtype": FactSubtypeID.HAS_BASEMENT.value,
                        },
                        {
                            "name": "wind_class",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.WIND_RISK.value,
                        },
                        {
                            "name": "wiring_year",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_WIRING_YEAR.value,
                        },
                        {
                            "name": "roofing_year",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_ROOFING_YEAR.value,
                        },
                        {
                            "name": "plumbing_year",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_PLUMBING_YEAR.value,
                        },
                        {
                            "name": "heating_year",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BUILDING_IMPROVEMENTS_HEATING_YEAR.value,
                        },
                        {
                            "name": "roof_type",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.ROOF_TYPE.value,
                        },
                    ],
                },
                {
                    "name": "property_coverage_building",
                    "fields_mapping": [
                        {
                            "name": "limit",
                            "type": FieldType.NUMBER.value,
                            "subtype": FactSubtypeID.BUILDING_VALUE.value,
                        },
                        {
                            "name": "limit_valuation",
                            "type": FieldType.TEXT.value,
                            "subtype": FactSubtypeID.BUILDING_VALUATION_METHOD.value,
                            "derived_prop_name": True,
                            "value_config": {
                                "extraction_method": ExtractionMethod.ONE_OF.value,
                                "values": [
                                    {"name": "valuation_fvrc", "value": "FVRC"},
                                    {"name": "valuation_acv", "value": "ACV"},
                                    {"name": "valuation_rc", "value": "RC"},
                                ],
                            },
                        },
                    ],
                },
                {
                    "name": "property_coverage_personal",
                    "fields_mapping": [
                        {
                            "name": "limit",
                            "type": FieldType.NUMBER.value,
                            "subtype": FactSubtypeID.BPP.value,
                        },
                    ],
                },
            ],
        }
    ],
}


ACORD_139_CONFIG = {
    "submission_properties": [
        {
            "path": "agency_information.agency",
            "submission_property": "agency",
            "entity_information": "AGENCY",
        },
        {
            "path": "agency_information.policy_number",
            "submission_property": "policy_number",
            "entity_information": "CARRIER_POLICY_NUMBER",
        },
        {"path": "agency_information.carrier", "submission_property": "carrier", "entity_information": "CARRIER"},
        {
            "path": "agency_information.effective_date",
            "submission_property": "proposed_effective_date",
            "entity_information": "POLICY_EFFECTIVE_START_DATE",
        },
        {
            "path": "agency_information.date",
            "submission_property": "date",
            "entity_information": "AGENCY_DATE",
        },
        {
            "path": "agency_information.naic_code",
            "submission_property": "naic_code",
            "entity_information": "CARRIER_NAIC_CODE",
        },
        {
            "path": "agency_information.agency_customer_id",
            "submission_property": "agency_customer_id",
            "entity_information": "AGENCY_CUSTOMER_ID",
        },
    ],
    "file_classification": ClassificationDocumentType.ACORD_139.value,
    "data_sections": [
        {
            "name": "statement_of_values_table",
            "transient_data": [
                {
                    "serialized_name": "premises_info",
                    "data_fields": [
                        "location_number",
                        "building_number",
                        "subject",
                        "value_100_percent",
                        "valuation",
                    ],
                }
            ],
        },
    ],
}

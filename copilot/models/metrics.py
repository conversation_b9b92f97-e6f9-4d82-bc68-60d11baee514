from collections import defaultdict
from collections.abc import Sequence
from dataclasses import dataclass
from datetime import datetime
from typing import cast
import statistics

from common.utils.collections import filter_none
from infrastructure_common.logging import get_logger
from sqlalchemy import Column, DateTime, Enum
from sqlalchemy import Float as Float_org
from sqlalchemy import ForeignKey, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import ARRAY, JSONB, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql.type_api import TypeEngine
from static_common.enums.metric import MetricType
from static_common.enums.parent import ParentType
from static_common.enums.units import SummarizationUnits

from copilot.metric_utils import (
    DEFAULT_GRADE_WEIGHTS,
    DEFAULT_SCORE_LABEL_THRESHOLDS,
    DISTANCE_MEAN_METRIC_SUMMARY_CONFIG_IDS,
    MIXED_FILTERING_MODE,
    NO_FILTERING_MODE,
    PREMISES_BASED_FILTERING_MODE,
    SINGLE_METRIC_SUMMARY_CONFIG_IDS,
    UNKNOWN_CATEGORY,
    <PERSON>ric<PERSON>ilter<PERSON><PERSON>ult,
    compute_interquintile_numbers,
    compute_units,
    display_numeric_value,
    filter_metric_data,
    get_grade_label,
    get_name_label,
    interpret_distance_risk,
)
from copilot.models._private import BaseModel, db
from copilot.models.metric_preferences import MetricPreference

"""
Note: sqlalchemy-stubs incorrectly returns Decimal type for Float columns
https://github.com/dropbox/sqlalchemy-stubs/issues/178
"""
Float = cast(type[TypeEngine[float]], Float_org)

logger = get_logger()


class MetricSource(BaseModel):
    __tablename__ = "metric_source"
    metric_v2_id = Column(UUID(as_uuid=True), ForeignKey("metric_v2.id", ondelete="CASCADE"), nullable=True, index=True)
    type = Column(String, nullable=False)
    properties = Column(JSONB, nullable=True)


@dataclass
class StatisticDistributionCategory:
    business_ids: list[UUID]
    parent_ids: list[UUID]
    values: list[float | None]
    label: str
    range_start: float | None
    range_end: float | None
    frequency: int
    percentage: float


@dataclass
class DateRangeDistributionCategory:
    business_ids: list[UUID]
    parent_ids: list[UUID]
    values: list[DateTime | None]
    label: str
    range_start: str | None
    range_end: str | None
    frequency: int
    percentage: float


@dataclass
class RangeDistributionCategory:
    business_ids: list[UUID]
    parent_ids: list[UUID]
    category: str
    frequency: int
    percentage: float


def compute_ranges(quantiles: Sequence[float]) -> list[tuple[float, float]]:
    if not quantiles:
        return []
    ranges = []
    for i in range(len(quantiles) + 1):
        if i == 0:
            ranges.append((None, quantiles[i]))
        elif i == len(quantiles):
            ranges.append((quantiles[i - 1], None))
        else:
            ranges.append((quantiles[i - 1], quantiles[i]))
    return ranges


@dataclass
class Range:
    distance: float | None
    interpretation: str | None
    business_id: UUID | None
    parent_id: UUID | None


@dataclass
class GradedParent:
    grade: str
    business_id: UUID | None
    parent_id: UUID | None


@dataclass
class ParentsWithGrade:
    grade: str
    label: str | None
    business_ids: list[UUID]
    parent_ids: list[UUID]
    frequency: int
    percentage: float


@dataclass
class CategorySummaryV2:
    business_ids: list[UUID]
    parent_ids: list[UUID]
    parent_types: list[ParentType]
    display_name: str
    percentage: float
    frequency: int


@dataclass
class GetMetricsV2Request:
    only_business_metrics: bool | None = True
    exclude: list[str] | None = None
    submission_business_id: str | None = None
    submission_business_ids: list[str] | None = None
    restricted_parent_ids: list[str] | None = None
    summary_config_ids: list[str] | None = None


ENFORCED_SUM_AGGREGATION = {"NUMBER_OF_UNITS_SUMMARY_CONFIG"}


class MetricV2(BaseModel):
    __tablename__ = "metric_v2"
    report_v2_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id"), nullable=True, index=True)
    submission_business_id = Column(
        UUID(as_uuid=True), ForeignKey("submission_businesses.id", ondelete="CASCADE"), nullable=True, index=True
    )
    execution_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    metric_type = Column(Enum(MetricType), nullable=False, index=True)
    name = Column(String, nullable=False, index=True)
    parent_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    parent_type = Column(Enum(ParentType), nullable=True, index=True)
    children_type = Column(String, nullable=False, default="BUSINESS")
    summary_config_id = Column(String, nullable=True, index=True)
    sources = relationship("MetricSource", uselist=True, cascade="all, delete-orphan", lazy="noload")
    # Required because RANGE_SUMMARY requires business id per float_value
    value_business_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True)
    value_parent_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True)
    value_parent_types = Column(ARRAY(Enum(ParentType)), nullable=True)
    float_values = Column(ARRAY(Float), nullable=True)
    datetime_values = Column(ARRAY(DateTime(timezone=True)), nullable=True)
    string_values = Column(ARRAY(String), nullable=True)

    # MetricType.Sum / MetricType.Mean
    units = Column(String(30), nullable=True)

    # MetricType.LIST_SUMMARY
    list_item_type = Column(String, nullable=True)

    # MetricType.NUMERIC_RANGE_SUMMARY
    minimums = Column(ARRAY(Float), nullable=True)
    maximums = Column(ARRAY(Float), nullable=True)

    # MetricType.RANGE_SUMMARY
    distance_threshold = Column(Float, nullable=True)

    __table_args__ = (UniqueConstraint("parent_id", "parent_type", "summary_config_id", "execution_id"),)

    @property
    def computed_units(self) -> SummarizationUnits:
        return compute_units(self.units, self.name, self.float_values)

    @property
    def is_multi_metric(self) -> bool:
        if (
            self.parent_type != ParentType.REPORT
            or (self.children_type not in {ParentType.PREMISES, ParentType.BUSINESS})
            or self.summary_config_id in SINGLE_METRIC_SUMMARY_CONFIG_IDS
        ):
            return False

        return self.metric_type in {MetricType.MEAN, MetricType.SUM}

    def __delete_cached_data(self) -> None:
        for attr in [
            "_filtered_values",
            "_filtered_business_ids",
            "_filtered_parent_ids",
            "_filtered_parent_types",
            "_interquintile_numbers",
            "_quintiles",
            "_has_structures_data",
        ]:
            if hasattr(self, attr):
                delattr(self, attr)

    @property
    def restricted_parent_ids(self) -> set | None:
        if hasattr(self, "_restricted_parent_ids"):
            return self._restricted_parent_ids  # type: ignore
        else:
            return None

    @restricted_parent_ids.setter
    def restricted_parent_ids(self, value: set) -> None:
        self._restricted_parent_ids = value

    @property
    def filtering_mode(self) -> str:
        """
        We filter existing metrics when ParentType=REPORT, ChildrenType=PREMISES, BUSINESS
        STRUCTURES_BASED_FILTERING_MODE and CUSTOM_FILTERING_MODE are only set manually with
        filtering_mode.setter.
        """
        if hasattr(self, "_filtering_mode"):
            return self._filtering_mode  # type: ignore
        if self.parent_type == ParentType.REPORT and (self.children_type in {ParentType.PREMISES, ParentType.BUSINESS}):
            self._filtering_mode = (
                PREMISES_BASED_FILTERING_MODE
                if self.metric_type in {MetricType.MEAN, MetricType.SUM}
                else MIXED_FILTERING_MODE
            )
        else:
            self._filtering_mode = NO_FILTERING_MODE

        return self._filtering_mode

    @filtering_mode.setter
    def filtering_mode(self, value: str | None) -> None:
        self.__delete_cached_data()
        self._filtering_mode = value

    @property
    def __values_for_metric(self):
        if self.metric_type in {
            MetricType.MEAN,
            MetricType.SUM,
            MetricType.RANGE_SUMMARY,
        }:
            return self.float_values

        if self.metric_type == MetricType.NUMERIC_RANGE_SUMMARY:
            return list(zip(self.minimums, self.maximums))

        if self.metric_type == MetricType.DATE_RANGE_SUMMARY:
            return self.datetime_values

        return self.string_values

    def __filter_data_if_necessary(self):
        if (
            len(self.__values_for_metric)
            != len(self.value_parent_ids)
            != len(self.value_parent_types)
            != len(self.value_business_ids)
        ):
            raise Exception(
                "Metric has unbalanced value_business_ids, value_parent_ids, value_parent_types, and values"
            )

        aggregation_fn = statistics.fmean if self.metric_type == MetricType.MEAN else sum
        # TODO: https://kalepa.atlassian.net/browse/ENG-20438
        if self.summary_config_id in ENFORCED_SUM_AGGREGATION:
            aggregation_fn = sum

        metric_filter_result: MetricFilterResult = filter_metric_data(
            filtering_mode=self.filtering_mode,
            aggregation_fn=aggregation_fn,
            values=self.__values_for_metric,
            parent_types=self.value_parent_types,  # type: ignore
            parent_ids=self.value_parent_ids,  # type: ignore
            business_ids=self.value_business_ids,  # type: ignore
            restricted_parent_ids=self.restricted_parent_ids,
            metric_type=self.metric_type,
            parent_type=self.parent_type,
            parent_id=self.parent_id,
            metric_name=self.name,
        )

        self._filtered_values = metric_filter_result.filtered_values
        self._filtered_parent_types = metric_filter_result.filtered_parent_types
        self._filtered_parent_ids = metric_filter_result.filtered_parent_ids
        self._filtered_business_ids = metric_filter_result.filtered_business_ids
        self._has_structures_data = metric_filter_result.has_structures_data

    @property
    def filtered_values(self):
        if hasattr(self, "_filtered_values"):
            return self._filtered_values

        self.__filter_data_if_necessary()

        return self._filtered_values

    @filtered_values.setter
    def filtered_values(self, values: list[str] | list[datetime] | list[float]) -> None:
        self._filtered_values = values

    @property
    def filtered_minimums(self):
        if self.metric_type != MetricType.NUMERIC_RANGE_SUMMARY:
            return None

        if hasattr(self, "_filtered_minimums"):
            return self._filtered_minimums

        self._filtered_minimums, self._filtered_maximums = list(zip(*self.filtered_values))

        return self._filtered_minimums

    @filtered_minimums.setter
    def filtered_minimums(self, values: list[float]) -> None:
        self._filtered_minimums = values

    @property
    def filtered_maximums(self):
        if self.metric_type != MetricType.NUMERIC_RANGE_SUMMARY:
            return None

        if hasattr(self, "_filtered_maximums"):
            return self._filtered_maximums

        self._filtered_minimums, self._filtered_maximums = list(zip(*self.filtered_values))

        return self._filtered_maximums

    @filtered_maximums.setter
    def filtered_maximums(self, values: list[float]) -> None:
        self._filtered_maximums = values

    @property
    def filtered_parent_ids(self) -> list[UUID] | None:
        if hasattr(self, "_filtered_parent_ids"):
            return self._filtered_parent_ids  # type: ignore

        self.__filter_data_if_necessary()

        return self._filtered_parent_ids  # type: ignore

    @filtered_parent_ids.setter
    def filtered_parent_ids(self, values: list[UUID] | None) -> None:
        self._filtered_parent_ids = values

    @property
    def filtered_parent_types(self) -> list[ParentType] | None:
        if hasattr(self, "_filtered_parent_types"):
            return self._filtered_parent_types

        self.__filter_data_if_necessary()
        return self._filtered_parent_types

    @filtered_parent_types.setter
    def filtered_parent_types(self, values: list[ParentType] | None) -> None:
        self._filtered_parent_types = values

    @property
    def filtered_business_ids(self) -> list[UUID] | None:
        if hasattr(self, "_filtered_business_ids"):
            return self._filtered_business_ids  # type: ignore

        self.__filter_data_if_necessary()

        return self._filtered_business_ids  # type: ignore

    @filtered_business_ids.setter
    def filtered_business_ids(self, values: list[UUID] | None) -> None:
        self._filtered_business_ids = values

    @property
    def values(self) -> list[str] | None:
        if self.metric_type in {
            MetricType.MEAN,
            MetricType.SUM,
            MetricType.GRADE_SUMMARY,
            MetricType.RANGE_SUMMARY,
            MetricType.NUMERIC_RANGE_SUMMARY,
            MetricType.DATE_RANGE_SUMMARY,
        }:
            return [str(value) for value in self.filtered_values] if self.filtered_values is not None else None

        return self.filtered_values  # type: ignore

    @property
    def business_ids(self) -> list[UUID] | None:
        # Required because RANGE_SUMMARY and DATE_RANGE_SUMMARY require business id per value
        if self.metric_type in {
            MetricType.RANGE_SUMMARY,
            MetricType.NUMERIC_RANGE_SUMMARY,
        }:
            return self.filtered_business_ids  # type: ignore[return-value]

        # No other MetricType requires business_ids
        return None

    @property
    def has_structures_data(self) -> bool | None:
        if hasattr(self, "_has_structures_data"):
            return self._has_structures_data  # type: ignore

        self.__filter_data_if_necessary()

        return self._has_structures_data  # type: ignore

    @has_structures_data.setter
    def has_structures_data(self, values: list[UUID] | None) -> None:
        self._has_structures_data = values

    @property
    def children_ids(self) -> list[UUID] | None:
        return self.filtered_parent_ids  # type: ignore[return-value]

    def __get_none_filtered_float_values(self) -> list[float] | None:
        if self.metric_type not in {MetricType.MEAN, MetricType.SUM} or self.filtered_values is None:
            return None

        filtered_values: list[float] = [v for v in self.filtered_values if v != None]  # type: ignore
        if len(filtered_values) == 0:
            return None

        return filtered_values

    def __get_none_filtered_datetime_values(self) -> list[datetime] | None:
        if self.metric_type != MetricType.DATE_RANGE_SUMMARY or self.filtered_values is None:
            return None

        filtered_values: list[datetime] = filter_none(self.filtered_values)  # type: ignore
        if len(filtered_values) == 0:
            return None

        return filtered_values

    @property
    def mean(self) -> float | None:
        filtered_values: list[float] | None = self.__get_none_filtered_float_values()
        if filtered_values is None:
            return None

        return statistics.fmean(filtered_values)

    @property
    def sum(self) -> float | None:
        filtered_values: list[float] | None = self.__get_none_filtered_float_values()
        if filtered_values is None:
            return None

        return sum(filtered_values)

    @property
    def min(self) -> float | None:
        filtered_values: list[float] | None = self.__get_none_filtered_float_values()
        if filtered_values is None:
            return None

        return min(filtered_values)

    @property
    def max(self) -> float | None:
        filtered_values: list[float] | None = self.__get_none_filtered_float_values()
        if filtered_values is None:
            return None

        return max(filtered_values)

    @property
    def earliest(self) -> datetime | None:
        filtered_values: list[datetime] | None = self.__get_none_filtered_datetime_values()
        if filtered_values is None:
            return None

        return min(filtered_values)

    @property
    def latest(self) -> datetime | None:
        filtered_values: list[datetime] | None = self.__get_none_filtered_datetime_values()
        if filtered_values is None:
            return None

        return max(filtered_values)

    @property
    def metric_preference(self) -> MetricPreference | None:
        if hasattr(self, "_metric_preference"):
            return self._metric_preference

        if self.children_type == ParentType.STRUCTURE and self.parent_type != ParentType.REPORT:
            return None

        mp = (
            db.session.query(MetricPreference)
            .filter(MetricPreference.parent_id == self.parent_id)
            .filter(MetricPreference.parent_type == self.parent_type)
            .filter(MetricPreference.report_id == self.report_v2_id)
            .filter(MetricPreference.summary_config_id == self.summary_config_id)
            .first()
        )
        self._metric_preference: MetricPreference | None = mp
        return mp

    @metric_preference.setter
    def metric_preference(self, mp: MetricPreference) -> None:
        self._metric_preference = mp

    @property
    def metric_group_name(self) -> str | None:
        if not self.summary_config_id or not self.metric_preference:
            return None
        return self.metric_preference.metric_group_name

    @property
    def category_summaries(self) -> list[CategorySummaryV2] | None:
        if self.metric_type != MetricType.CATEGORICAL_DATA_SUMMARY:
            return None

        if (
            self.filtered_business_ids is None
            or self.filtered_parent_ids is None
            or self.filtered_parent_types is None
            or self.filtered_values is None
        ):
            return None

        if (
            len(self.filtered_business_ids)
            != len(self.filtered_parent_ids)
            != len(self.filtered_parent_types)
            != len(self.filtered_values)
        ):
            raise Exception("Metric has unbalanced value_business_ids, value_parent_ids, and string_values")

        category_groupings: dict[str, list[tuple[UUID | None, UUID, ParentType]]] = defaultdict(list)
        for value, business_id, parent_id, parent_type in zip(
            self.filtered_values, self.filtered_business_ids, self.filtered_parent_ids, self.filtered_parent_types
        ):
            category_groupings[value].append((business_id, parent_id, parent_type))  # type: ignore

        num_items: int = len(self.filtered_values)
        category_summaries: list[CategorySummaryV2] = []
        for category, items in category_groupings.items():
            business_ids, parent_ids, parent_types = map(list, zip(*items))
            frequency: int = len(parent_ids)
            percentage: float = frequency / num_items
            category_summary: CategorySummaryV2 = CategorySummaryV2(
                display_name=category,
                parent_ids=parent_ids,
                parent_types=parent_types,  # type: ignore
                business_ids=business_ids,
                percentage=percentage,
                frequency=frequency,  # type: ignore
            )
            category_summaries.append(category_summary)

        return category_summaries

    @property
    def threshold(self) -> str | None:
        if self.metric_type != MetricType.RANGE_SUMMARY or self.distance_threshold is None or self.units is None:
            return None

        return f"{self.distance_threshold:g} {self.units}"

    @property
    def inside_range_label(self) -> str | None:
        if self.metric_type != MetricType.RANGE_SUMMARY or self.threshold is None:
            return None
        return f"< {self.threshold}"

    @property
    def outside_range_label(self) -> str | None:
        if self.metric_type != MetricType.RANGE_SUMMARY or self.threshold is None:
            return None
        return f"> {self.threshold}"

    @property
    def ranges(self) -> list[Range] | None:
        if self.metric_type != MetricType.RANGE_SUMMARY or self.distance_threshold is None or self.units is None:
            return None

        if self.filtered_business_ids is None or self.filtered_parent_ids is None or self.filtered_values is None:
            return None

        if len(self.filtered_business_ids) != len(self.filtered_parent_ids) != len(self.filtered_values):
            raise Exception("Metric has unbalanced value_business_ids, value_parent_ids, and float_values")

        ranges: list[Range] = []
        for value, business_id, parent_id in zip(
            self.filtered_values, self.filtered_business_ids, self.filtered_parent_ids
        ):
            distance = value
            if self.units and self.units.lower() == "feet":
                distance = float(int(value * 5280)) if value else None

            interpretation: str = (
                "Unknown" if distance is None else interpret_distance_risk(distance, self.distance_threshold)
            )
            _range: Range = Range(
                business_id=business_id, parent_id=parent_id, distance=distance, interpretation=interpretation
            )
            ranges.append(_range)

        return ranges

    def _get_parent_ids_by_interpretation(self, interpretation: str) -> list[UUID] | None:
        if self.ranges is None:
            return None

        parent_ids = []
        for range in self.ranges:
            if not range.parent_id:
                range.parent_id = range.business_id
            if interpretation.lower() in range.interpretation.lower():
                parent_ids.append(range.parent_id)
        return parent_ids  # type: ignore[return-value]

    def _get_business_ids_by_interpretation(self, interpretation: str) -> list[UUID] | None:
        if self.ranges is None:
            return None

        business_ids = []
        for range in self.ranges:
            if interpretation.lower() in range.interpretation.lower():
                business_ids.append(range.business_id)
        return business_ids  # type: ignore

    @property
    def inside_range(self) -> list[UUID] | None:
        if self.metric_type != MetricType.RANGE_SUMMARY:
            return None

        return self._get_business_ids_by_interpretation("Inside")

    @property
    def outside_range(self) -> list[UUID] | None:
        if self.metric_type != MetricType.RANGE_SUMMARY:
            return None

        return self._get_business_ids_by_interpretation("Outside")

    @property
    def unknown_range(self) -> list[UUID] | None:
        if self.metric_type != MetricType.RANGE_SUMMARY:
            return None

        return self._get_business_ids_by_interpretation("Unknown")

    @property
    def range_distribution(self) -> list[RangeDistributionCategory] | None:
        if self.metric_type != MetricType.RANGE_SUMMARY or self.ranges is None:
            return None

        business_id_by_parent_id = {r.parent_id: r.business_id for r in self.ranges}
        inside_range = self._get_parent_ids_by_interpretation("Inside")
        outside_range = self._get_parent_ids_by_interpretation("Outside")
        unknown_range = self._get_parent_ids_by_interpretation("Unknown")
        if inside_range is None or outside_range is None or unknown_range is None:
            return None

        total = len(inside_range) + len(outside_range) + len(unknown_range)
        if not total:
            return []
        distribution = [
            RangeDistributionCategory(
                category="Inside",
                business_ids=[business_id_by_parent_id.get(parent_id) for parent_id in inside_range],  # type: ignore
                frequency=len(inside_range),
                percentage=len(inside_range) / total,
                parent_ids=inside_range,
            ),
            RangeDistributionCategory(
                category="Outside",
                business_ids=[business_id_by_parent_id.get(parent_id) for parent_id in outside_range],  # type: ignore
                frequency=len(outside_range),
                percentage=len(outside_range) / total,
                parent_ids=outside_range,
            ),
            RangeDistributionCategory(
                category="Unknown",
                business_ids=[business_id_by_parent_id.get(parent_id) for parent_id in unknown_range],  # type: ignore
                frequency=len(unknown_range),
                percentage=len(unknown_range) / total,
                parent_ids=unknown_range,
            ),
        ]
        return [row for row in distribution if row.frequency > 0]

    @property
    def list_item_values(self) -> list[str] | None:
        if self.metric_type != MetricType.LIST_SUMMARY:
            return None

        return self.filtered_values  # type: ignore

    @property
    def _graded_businesses(self) -> list[GradedParent] | None:
        if self.metric_type != MetricType.GRADE_SUMMARY:
            return None

        if self.filtered_business_ids is None or self.filtered_parent_ids is None or self.filtered_values is None:
            return None

        if len(self.filtered_business_ids) != len(self.filtered_parent_ids) != len(self.filtered_values):
            raise Exception("Metric has unbalanced value_business_ids, value_parent_ids, and string_values")

        graded_parents: list[GradedParent] = []
        for value, business_id, parent_id in zip(
            self.filtered_values, self.filtered_business_ids, self.filtered_parent_ids
        ):
            graded_parent: GradedParent = GradedParent(business_id=business_id, parent_id=parent_id, grade=value)
            graded_parents.append(graded_parent)

        return graded_parents

    @property
    def _scores(self) -> list[float] | None:
        if self.metric_type != MetricType.GRADE_SUMMARY or self._graded_businesses is None:
            return None

        scores: list[float] = [
            DEFAULT_GRADE_WEIGHTS[graded_business.grade]
            for graded_business in self._graded_businesses
            if graded_business.grade != UNKNOWN_CATEGORY
        ]

        return scores

    @property
    def score(self) -> float | None:
        return round(statistics.fmean(self._scores), ndigits=2) if self._scores else 0.0

    @property
    def label(self) -> str | None:
        if self.metric_type != MetricType.GRADE_SUMMARY or not self.score:
            return None

        return get_grade_label(self.score, DEFAULT_SCORE_LABEL_THRESHOLDS)

    @property
    def grade_distribution(self) -> list[ParentsWithGrade] | None:
        if self.metric_type != MetricType.GRADE_SUMMARY or self._graded_businesses is None:
            return None

        grade_to_parent_ids = defaultdict(list)
        for graded_business in self._graded_businesses:
            if not graded_business.parent_id:
                graded_business.parent_id = graded_business.business_id
            grade_to_parent_ids[graded_business.grade].append(graded_business.parent_id)
        business_id_by_parent_id: dict[UUID, UUID] = {r.parent_id: r.business_id for r in self._graded_businesses}
        distribution = []
        for graded, parent_ids in grade_to_parent_ids.items():
            label_mapping = get_name_label(self.name)
            label = label_mapping.get(graded) if label_mapping else None
            distribution.append(
                ParentsWithGrade(
                    grade=graded,
                    label=label,
                    business_ids=[business_id_by_parent_id.get(parent_id) for parent_id in parent_ids],  # type: ignore
                    parent_ids=parent_ids,  # type: ignore
                    frequency=len(parent_ids),
                    percentage=len(parent_ids) / len(self._graded_businesses),
                )
            )
        return distribution

    @property
    def __average_min(self) -> float | None:
        if self.metric_type != MetricType.NUMERIC_RANGE_SUMMARY or self.minimums is None or self.maximums is None:
            return None

        if not self.filtered_business_ids:
            self.filtered_business_ids = []
        children_ids = self.children_ids or self.business_ids
        no_data = not (children_ids and self.minimums and self.maximums)
        if no_data:
            return None
        values = [m for m in self.minimums if m is not None]
        if not values:
            return None
        return statistics.mean(values)

    @property
    def __average_max(self) -> float | None:
        if self.metric_type != MetricType.NUMERIC_RANGE_SUMMARY or self.minimums is None or self.maximums is None:
            return None

        if not self.filtered_business_ids:
            self.filtered_business_ids = []
        children_ids = self.children_ids or self.business_ids
        no_data = not (children_ids and self.minimums and self.maximums)
        if no_data:
            return None
        values = [m for m in self.maximums if m is not None]
        if not values:
            return None
        return statistics.mean([m for m in self.maximums if m is not None])

    @property
    def average_min_max(self) -> float | None:
        if self.metric_type != MetricType.NUMERIC_RANGE_SUMMARY or self.minimums is None or self.maximums is None:
            return None

        if not self.filtered_business_ids:
            self.filtered_business_ids = []
        children_ids = self.children_ids or self.business_ids
        no_data = not (children_ids and self.minimums and self.maximums)
        if no_data:
            return None
        if self.__average_min is None or self.__average_max is None:
            return None
        return statistics.mean([self.__average_min, self.__average_max])

    def __get_quintiles_and_interquintiles(self) -> tuple[list[float], list[int | None]]:
        quintiles = []
        interquintiles = []
        if self.summary_config_id in DISTANCE_MEAN_METRIC_SUMMARY_CONFIG_IDS:
            quintiles = [1.0, 2.0, 3.0, 5.0]
            _, interquintiles = compute_interquintile_numbers(
                self.filtered_values,
                custom_quintiles=quintiles,  # type: ignore
            )
        else:
            quintiles, interquintiles = compute_interquintile_numbers(self.filtered_values)  # type: ignore

        return quintiles, interquintiles

    @property
    def interquintile_numbers(self) -> list[int | None] | None:
        if self.metric_type not in {MetricType.MEAN, MetricType.SUM} or self.filtered_values is None:
            return None

        if hasattr(self, "_interquintile_numbers") and self._interquintile_numbers is not None:
            return self._interquintile_numbers

        quintiles, interquintiles = self.__get_quintiles_and_interquintiles()

        self._quintiles = quintiles
        self._interquintile_numbers = interquintiles

        return interquintiles

    @property
    def quintiles(self) -> list[float] | None:
        if self.metric_type not in {MetricType.MEAN, MetricType.SUM} or self.filtered_values is None:
            return None

        if hasattr(self, "_quintiles") and self._quintiles is not None:
            return self._quintiles

        if self.custom_quintiles:
            _, interquintiles = compute_interquintile_numbers(
                self.filtered_values, custom_quintiles=self.custom_quintiles
            )
            self._quintiles = self.custom_quintiles
            self._interquintile_numbers = interquintiles
            return self.custom_quintiles
        else:
            quintiles, interquntiles = self.__get_quintiles_and_interquintiles()
            self._quintiles = quintiles
            self._interquintile_numbers = interquntiles
            return quintiles

    @property
    def custom_quintiles(self) -> list[float] | None:
        if hasattr(self, "_custom_quintiles"):
            return self._custom_quintiles
        return None

    @custom_quintiles.setter
    def custom_quintiles(self, value: list[float | None]) -> None:
        self._custom_quintiles = value

    @property
    def date_range_quintiles(self) -> list[DateRangeDistributionCategory] | None:
        if self.metric_type != MetricType.DATE_RANGE_SUMMARY or self.filtered_values is None:
            return None

        if not self.filtered_business_ids:
            self.filtered_business_ids = []

        children_ids = self.children_ids or self.filtered_business_ids
        no_data = not (children_ids and self.filtered_values)
        if no_data:
            return []
        int_values = []
        for date_value in self.filtered_values:
            if date_value:
                int_values.append(int(date_value.timestamp()))  # type: ignore
        quintile_data = statistics.quantiles(int_values, n=5, method="inclusive") if len(int_values) > 1 else []
        if not quintile_data:
            return [
                DateRangeDistributionCategory(
                    business_ids=children_ids,  # type: ignore
                    parent_ids=children_ids,  # type: ignore
                    values=self.filtered_values,  # type: ignore
                    label="Unknown",
                    range_start=None,
                    range_end=None,
                    frequency=len(children_ids),  # type: ignore
                    percentage=len(children_ids) / len(children_ids) if len(children_ids) else None,  # type: ignore
                )
            ]
        unknown_children_ids = [
            children_ids[j] for j in range(len(children_ids)) if self.filtered_values[j] is None  # type: ignore
        ]
        unknown_business_ids = [
            self.filtered_business_ids[j]
            for j in range(len(self.filtered_business_ids))
            if self.filtered_values[j] is None
        ]
        distribution = [
            DateRangeDistributionCategory(
                business_ids=unknown_business_ids,  # type: ignore
                parent_ids=unknown_children_ids,  # type: ignore
                values=[None] * len(unknown_children_ids),
                label="Unknown",
                range_start=None,
                range_end=None,
                frequency=len(unknown_children_ids),
                percentage=len(unknown_children_ids) / len(children_ids) if len(children_ids) else None,  # type: ignore
            )
        ]
        for i, value_range in enumerate(compute_ranges(quintile_data)):
            start, end = value_range
            range_start = datetime.fromtimestamp(start).strftime("%m/%d/%Y") if start else None
            range_end = datetime.fromtimestamp(end).strftime("%m/%d/%Y") if end else None
            has_start = start is not None
            has_end = end is not None
            range_parent_ids = [
                children_ids[j]  # type: ignore
                for j in range(len(children_ids))  # type: ignore
                if self.filtered_values[j]
                and (start is None or int(self.filtered_values[j].timestamp()) > start)  # type: ignore
                and (end is None or int(self.filtered_values[j].timestamp()) <= end)  # type: ignore
            ]
            range_business_ids = [
                self.filtered_business_ids[j]  # type: ignore
                for j in range(len(self.filtered_business_ids))  # type: ignore
                if self.filtered_values[j]
                and (start is None or int(self.filtered_values[j].timestamp()) > start)
                and (end is None or int(self.filtered_values[j].timestamp()) <= end)
            ]
            if has_end and not has_start:
                label = f"< {range_end}"
            elif has_start and not has_end:
                label = f"> {range_start}"
            elif has_start and has_end:
                label = f"{range_start} - {range_end}"
            else:
                label = ""

            distribution.append(
                DateRangeDistributionCategory(
                    business_ids=range_business_ids,
                    parent_ids=range_parent_ids,
                    label=label,
                    range_start=range_start,
                    range_end=range_end,
                    values=[
                        self.filtered_values[j]  # type: ignore
                        for j in range(len(children_ids))  # type: ignore
                        if self.filtered_values[j]
                        and (start is None or int(self.filtered_values[j].timestamp()) > start)
                        and (end is None or int(self.filtered_values[j].timestamp()) <= end)
                    ],
                    frequency=len(range_parent_ids),
                    percentage=len(range_parent_ids) / len(children_ids) if len(children_ids) else None,  # type: ignore
                )
            )
        return distribution

    def __quantile_distribution(
        self, quantiles: Sequence[float], quantile_numbers: Sequence[float | None]
    ) -> list[StatisticDistributionCategory]:
        if not self.filtered_business_ids:
            self.filtered_business_ids = []

        children_ids = self.children_ids or self.filtered_business_ids
        no_data = not (children_ids and self.filtered_values)
        if no_data:
            return []
        if not quantile_numbers:
            return [
                StatisticDistributionCategory(
                    business_ids=children_ids,  # type: ignore
                    parent_ids=children_ids,  # type: ignore
                    values=self.filtered_values,  # type: ignore
                    label="Unknown",
                    range_start=None,
                    range_end=None,
                    frequency=len(children_ids),  # type: ignore
                    percentage=len(children_ids) / len(children_ids) if len(children_ids) else None,  # type: ignore
                )
            ]
        metric_incompatible = not (
            len(children_ids) == len(quantile_numbers) and len(children_ids) == len(self.filtered_values)
        )
        if metric_incompatible:
            logger.warning(
                "Stale data warning: Incompatible metric",
                report_id=self.report_v2_id,
                summary_config_id=self.summary_config_id,
                parent_type=self.parent_type,
                children_type=self.children_type,
                filtering_mode=self.filtering_mode,
            )
            return []
        unknown_children_ids = [children_ids[j] for j in range(len(children_ids)) if quantile_numbers[j] is None]
        unknown_business_ids = [
            self.filtered_business_ids[j]
            for j in range(len(self.filtered_business_ids))
            if quantile_numbers[j] is None  # type: ignore
        ]
        distributions: list[StatisticDistributionCategory] = [
            StatisticDistributionCategory(
                business_ids=unknown_business_ids,  # type: ignore
                parent_ids=unknown_children_ids,  # type: ignore
                values=[None] * len(unknown_children_ids),
                label="Unknown",
                range_start=None,
                range_end=None,
                frequency=len(unknown_children_ids),
                percentage=len(unknown_children_ids) / len(children_ids) if len(children_ids) else None,  # type: ignore
            )
        ]
        n_children = len(children_ids)  # type: ignore
        for i, value_range in enumerate(compute_ranges(quantiles)):
            start, end = value_range
            range_children_ids = [
                children_ids[j] for j in range(n_children) if quantile_numbers[j] == i + 1  # type: ignore
            ]
            range_business_ids = [
                self.filtered_business_ids[j]  # type: ignore
                for j in range(len(self.filtered_business_ids))  # type: ignore
                if quantile_numbers[j] == i + 1
            ]
            previous_distribution: StatisticDistributionCategory = distributions[-1]
            values = [self.filtered_values[j] for j in range(n_children) if quantile_numbers[j] == i + 1]
            # Case: 1.0-1.0 followed by 1.0-1.0
            is_the_same = previous_distribution.range_end == end and previous_distribution.range_start == start
            # Case: 0-1.0, followed by 1.0-1.0
            is_contained_in_previous = previous_distribution.range_end == end and start == end
            if is_the_same or is_contained_in_previous:
                previous_distribution.business_ids.extend(range_business_ids)  # type: ignore
                previous_distribution.parent_ids.extend(range_children_ids)  # type: ignore
                previous_distribution.values.extend(values)  # type: ignore
                previous_distribution.frequency += len(range_children_ids)
                previous_distribution.percentage = (
                    previous_distribution.frequency / n_children if n_children else previous_distribution.percentage
                )
            else:
                distributions.append(
                    StatisticDistributionCategory(
                        business_ids=range_business_ids,  # type: ignore
                        parent_ids=range_children_ids,  # type: ignore
                        label=(
                            f"{display_numeric_value(start, self.computed_units)} "
                            f"- {display_numeric_value(end, self.computed_units)}"
                            if end is not None
                            else f"> {display_numeric_value(start, self.computed_units)}"
                        ),
                        range_start=start,
                        range_end=end,
                        values=values,  # type: ignore
                        frequency=len(range_children_ids),
                        percentage=len(range_children_ids) / n_children if n_children else None,  # type: ignore
                    )
                )
        return distributions

    @property
    def quintile_distribution(self) -> list[StatisticDistributionCategory] | None:
        if self.metric_type not in {MetricType.MEAN, MetricType.SUM} or self.filtered_values is None:
            return None

        # Compensate for bug that was present in summarization and did not handle single non-null values well.
        quintiles = self.quintiles or []
        quantile_numbers = self.interquintile_numbers or []
        if not quintiles and not quantile_numbers:
            filtered_values = [x for x in self.filtered_values if x is not None]
            if len(filtered_values) == 1 or set(filtered_values) == {0}:
                v = filtered_values[0]
                quintiles = [v, v, v, v]
                for value in self.filtered_values:
                    if value is not None:
                        quantile_numbers.append(1)
                    else:
                        quantile_numbers.append(None)
        return self.__quantile_distribution(quantiles=quintiles, quantile_numbers=quantile_numbers)


@dataclass
class BulkMetricV2Request:
    metric_requests: list[MetricV2]

import os

from infrastructure_common.logging import get_logger

logger = get_logger()


def start_copilot_in_adhoc_task_mode():
    # We don't want to disturb main app importing process
    from adhoc_tasks_sdk.adhoc_task_base import exec_adhoc_task_function
    from flask_login import login_user

    from copilot.init.db_initializer import (
        attach_dynamic_password_provider_for_new_connections,
    )
    from copilot.models import User, db

    adhoc_task_name = os.getenv("ADHOC_TASK_NAME")
    bound_log = logger.bind(adhoc_task_name=adhoc_task_name, is_adhoc_task=True)

    adhoc_task_input_json = os.getenv("ADHOC_TASK_INPUT_JSON")
    adhoc_task_executing_capi_user_email = os.getenv("ADHOC_TASK_EXECUTING_CAPI_USER_EMAIL")
    bound_log.info(
        "Starting Copilot-API in ad-hoc task mode",
        adhoc_task_input_json=adhoc_task_input_json,
        executing_capi_user_email=adhoc_task_executing_capi_user_email,
    )

    if not adhoc_task_executing_capi_user_email:
        raise ValueError("ADHOC_TASK_EXECUTING_CAPI_USER_EMAIL environment variable is not set!")
    bound_log = bound_log.bind(executing_capi_user_email=adhoc_task_executing_capi_user_email)

    attach_dynamic_password_provider_for_new_connections(db.engine)

    bound_log.info("Logging in ad-hoc task user")
    user = User.query.filter(User.email == adhoc_task_executing_capi_user_email).first()
    if not user:
        raise RuntimeError(f"Adhoc task error - User with email '{adhoc_task_executing_capi_user_email}' not found!")
    login_user(user)
    bound_log.info("Logged in ad-hoc task user")

    exec_adhoc_task_function(adhoc_task_name, adhoc_task_executing_capi_user_email, adhoc_task_input_json)

    bound_log.info("Ad-hoc task completed. Shutting down.", adhoc_task_name=adhoc_task_name)

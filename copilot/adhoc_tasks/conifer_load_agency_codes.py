from io import StringIO
import csv

from adhoc_tasks_sdk.adhoc_task_base import AdhocTask, adhoc_task
from flask import current_app
from static_common.enums.organization import ExistingOrganizations

from copilot.clients import S3Client
from copilot.models import db
from copilot.models.brokerage_client_codes import BrokerageClientCode

BISHOP_CONIFER_CHANNEL = "C07ETUGM17E"


@adhoc_task("conifer_load_agency_codes")
class ConiferAgencyLoaderTask(AdhocTask):
    def __init__(self, adhoc_task_name: str, executing_capi_user_email: str, adhoc_task_input: dict):
        super().__init__(adhoc_task_name, executing_capi_user_email, adhoc_task_input)
        self.s3_client: S3Client = current_app.glue_s3_client

    def execute(self):
        s3_file = self.adhoc_task_input.get("file_location")

        try:
            file_content = self.s3_client.get_file_as_bytes(s3_file)
            file_stream = StringIO(file_content.decode("utf-8", errors="ignore"))
            file_stream.seek(0)

            # fetch all records from db and compare them to the csv file
            # if there are new records insert them
            # existing do an update
            # rest of the records delete them

            conifer_agencies: list[BrokerageClientCode] = BrokerageClientCode.query.filter(
                BrokerageClientCode.organization_id == ExistingOrganizations.BishopConifer.value
            ).all()
            brokerage_org_to_agency = {agency.derived_key: agency for agency in conifer_agencies}

            self.bound_logger.info("Processing Conifer agencies file", file=s3_file)

            csv_reader = csv.DictReader(file_stream)
            for row in csv_reader:
                row: dict
                if not row["brokerage_id"]:
                    continue
                brokerage_id = row["brokerage_id"]
                agency_name = row["brokerage_agency"] if row["brokerage_agency"] else None
                agent_name = row["agent_name"] if row["agent_name"] else None
                agency_code = row["agent_agency_code"] if row["agent_agency_code"] else None
                agent_code = row["agent_code"] if row["agent_code"] else None

                agency = BrokerageClientCode(
                    brokerage_id=brokerage_id,
                    agency_name=agency_name,
                    agent_name=agent_name,
                    agent_code=agency_code,
                    agency_code=agent_code,
                    organization_id=ExistingOrganizations.BishopConifer.value,
                )
                if agency.derived_key in brokerage_org_to_agency:
                    to_update = brokerage_org_to_agency.pop(agency.derived_key)
                    to_update.agent_code = agent_code
                    to_update.agency_code = agency_code
                    db.session.add(to_update)
                else:
                    db.session.add(agency)

            for agency in brokerage_org_to_agency.values():
                db.session.delete(agency)

            db.session.commit()
            self.bound_logger.info("Conifer agency file processed", file=s3_file)
        except:
            db.session.rollback()
            self.bound_logger.exception("Failed to process Conifer agency file", file=s3_file)
            current_app.slack_client.send_slack_message(
                BISHOP_CONIFER_CHANNEL,
                f"<!here>:warning: *Failed to process Conifer agency file: {s3_file}* :warning:",
            )

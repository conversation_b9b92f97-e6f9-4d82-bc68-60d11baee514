from collections import Counter
from collections.abc import Sequence
from typing import Any
from uuid import UUID
import re

from datascience_common.fact_subtypes.field_subtype_selection.multiple_fields_subtype_selection import (
    assign_fact_subtypes_to_fields,
)
from datascience_common.fact_subtypes.field_type_inference.field_type_inference import (
    FieldTypeInference,
)
from datascience_common.fact_subtypes.suggestion.field_value_suggestion import (
    perform_suggestions,
)
from events_common.model.types import StepFunctionStatus
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import load_only
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.fields import FieldType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.sensible import SensibleStatus
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.file_onboarding import (
    ExtractedEntityInfo,
    FileEntitiesExtractionInfo,
    OnboardedFile,
)
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
import flask

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.exceptions import NotReady
from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler
from copilot.logic.dao.file_dao import FileDAO
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.onboarded_files_transformation import (
    ProcessedFileWithClassification,
    load_data_multiple_files,
    merge_onboarded_data,
    split_data_to_files,
)
from copilot.logic.pds.file_onboarding_merging import FileOnboardingMerger
from copilot.models import db
from copilot.models.files import File, ProcessedFile, SubmissionFilesData
from copilot.models.files_merging import MergeDataRequest
from copilot.models.types import PermissionType
from copilot.utils import convert_to_uuid, lock_func_params
from copilot.v3.utils.submission import get_submission_data

logger = get_logger()
onboarded_file_schema = LeanOnboardedFileSchema()
field_type_inference = FieldTypeInference()

_LOSS_RUN_SUCCESS = [
    SensibleStatus.COMPLETE,
    SensibleStatus.UNCERTAIN,
    SensibleStatus.NO_LOSSES,
    SensibleStatus.NO_CLAIM_NUMBERS,
]

STATE_FOR_FIELD = {
    "onboarded_data": FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
    "entity_mapped_data": FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
}


FE_SCHEMA_FIELDS = [
    "fields.name",
    "fields.value_type",
    "fields.fact_subtype_id",
    "fields.naics_code",
    "fields.unit",
    "fields.group_ids",
    "fields.aggregation_type",
    "fields.values.value",
    "fields.values.suggested_values",
    "fields.values.entity_idx",
    "fields.values.file_idx",
    "fields.values.validation_result",
    "fields.values.observed_value",
    "fields.values.evidences",
    "fields.fact_subtype_suggestions",
    "entity_information.name",
    "entity_information.value_type",
    "entity_information.values.value",
    "entity_information.values.entity_idx",
    "entity_information.values.file_idx",
    "entity_information.values.external_file_id",
    "entity_information.values.related_entity_idx",
    "entity_information.values.evidences",
    "entities.type",
    "entities.id",
    "entities.parent_idx",
    "entities.entity_named_insured",
    "entities.entity_role",
    "entities.resolved",
    "files",
    "additional_data.finished_do_sub_step",
    "additional_data.extracted_entities_info",
    "additional_data.fe_properties",
    "transient_data",
]


def compare_filenames(filename_a: str, filename_b: str) -> bool:
    filename_a = _remove_file_numbers(filename_a)
    filename_b = _remove_file_numbers(filename_b)
    return filename_a.lower().replace(" ", "") == filename_b.lower().replace(" ", "")


def _remove_file_numbers(filename: str) -> str:
    return re.sub(r"\(\d+\)", "", filename)


def update_file_state(file_ids: Sequence[UUID], state: FileProcessingState) -> None:
    files = db.session.query(File).filter(File.id.in_(file_ids)).all()
    for file in files:
        file.processing_state = state
    db.session.flush()
    db.session.commit()


def update_processed_files_data(
    data: dict[UUID, OnboardedFile],
    data_field: str,
) -> None:
    file_ids = [file_id for file_id in data]
    processed_files = db.session.query(ProcessedFile).filter(ProcessedFile.file_id.in_(file_ids)).all()
    for processed_file in processed_files:
        setattr(processed_file, data_field, onboarded_file_schema.dump(data[processed_file.file_id]))


def _get_processed_files_data(
    file_ids: Sequence[UUID | str], data_field: str, requested_columns: list[str], clean: bool
) -> list[ProcessedFileWithClassification]:
    processed_files = (
        db.session.query(*[ProcessedFile.__table__.c[column] for column in requested_columns], File.classification)
        .join(File, File.id == ProcessedFile.file_id)
        .filter(ProcessedFile.file_id.in_(file_ids))
        .all()
    )
    columns = [*requested_columns, "classification"]
    processed_files = [dict(zip(columns, row)) for row in processed_files]
    if clean:
        for file in processed_files:
            file[data_field] = None
    return [
        ProcessedFileWithClassification(
            classification=file.pop("classification"),
            data=ProcessedFile(**file),
        )
        for file in processed_files
    ]


def _extract_entities_info(merge_request: list[MergeDataRequest]) -> list[FileEntitiesExtractionInfo]:
    extracted_entities_info = []
    for i, merge_request_file in enumerate(merge_request):
        entities_count_map = Counter(entity.type for entity in merge_request_file.data.entities)

        extracted_entities = [
            ExtractedEntityInfo(entity_type=entity_type, count=count)
            for entity_type, count in entities_count_map.items()
        ]
        extracted_entities_info.append(FileEntitiesExtractionInfo(file_idx=i, extracted_entities=extracted_entities))
    return extracted_entities_info


def get_merged_files(
    submission_id: str | UUID,
    files_to_merge: list[File],
    data_field: str,
    allowed_file_states: list[FileProcessingState],
    requested_columns: list[str],
    clean: bool = False,
    if_submission_data: bool = False,
    drop_empty_values: bool = False,
    enhance_with_resolution_data: bool = False,
    flatten_single_structures: bool = True,
) -> OnboardedFile | dict:
    submission_id = convert_to_uuid(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission_id):
        flask.abort(403)
    # If not clean and the merged data already exists return it directly
    if not clean and (submission_data := get_submission_data(submission_id)) and getattr(submission_data, data_field):
        # Compare ids of cached files with ids of files_to_merge. If equal, we can return cached data.
        cached_data = getattr(submission_data, data_field)
        cached_data_file_ids = {str(f) for f in cached_data.get("files", [])}
        files_to_merge_ids = {str(f.id) for f in files_to_merge}
        if cached_data_file_ids == files_to_merge_ids:
            return onboarded_file_schema.load(cached_data)
        else:
            logger.warning(
                "Cached data has changed!",
                data_field=data_field,
                submission_id=submission_id,
                cached_data_file_ids=cached_data_file_ids,
                files_to_merge_ids=files_to_merge_ids,
            )
            setattr(submission_data, data_field, None)
            db.session.commit()

    if not_ready := [str(f.id) for f in files_to_merge if f.processing_state not in allowed_file_states]:
        raise NotReady(file_ids=not_ready)

    processed_files = _get_processed_files_data(
        file_ids=[file.id for file in files_to_merge],
        data_field=data_field,
        requested_columns=requested_columns,
        clean=clean,
    )
    not_processed_files = list(
        {str(file.id) for file in files_to_merge} - {str(file.data.file_id) for file in processed_files}
    )
    if len(processed_files) != len(files_to_merge):
        logger.warning("There are missing files!")
    load_data_multiple_files(
        processed_files, data_field=data_field, enhance_with_resolution_data=enhance_with_resolution_data
    )
    merge_request = [
        MergeDataRequest(
            file_id=file.data.file_id,  # type: ignore[arg-type]
            data=getattr(file.data, data_field),
            classification=file.classification,
        )
        for file in processed_files
    ]
    extracted_entities_info = _extract_entities_info(merge_request=merge_request)
    submission = SubmissionDAO.get_minimal_submission(
        submission_id,
        raise_404=True,
        include_user=True,
        owner_additional_fields=["email"],
    )
    allow_multiple_observations = FeatureFlagsClient.is_feature_enabled(
        FeatureType.ENABLE_MULTIPLE_VALUES_PER_ENTITY, submission.user.email
    )
    merged_data = merge_onboarded_data(
        files=merge_request,
        if_submission_data=if_submission_data,
        drop_empty_values=drop_empty_values,
        # Only create dummy FNI for EM
        create_dummy_fni=True if data_field == "entity_mapped_data" else False,
        submission_id=submission_id,
        allow_multiple_observations=allow_multiple_observations,
        flatten_single_structures=flatten_single_structures,
        keep_project_gc=True if data_field == "entity_mapped_data" else False,
    )
    merged_data.files.extend(not_processed_files)  # type: ignore[arg-type]
    merged_data.additional_data.extracted_entities_info = extracted_entities_info
    return merged_data


def _has_entities(files: Sequence[MergeDataRequest], if_submission_data: bool) -> bool:
    for file in files:
        for entity in file.data.entities:
            if entity.type != SubmissionEntityType.SUBMISSION or if_submission_data:
                return True
    return False


def populate_missing_value_type(data: dict[str, Any]) -> None:
    for field in data.get("fields", []) + data.get("entity_information", []):
        if not field.get("value_type"):
            field["value_type"] = FieldType.TEXT


@lock_func_params(
    params_to_lock=["submission_id", "data_field"], lock_name_prefix="update_processed_file_data", lock_expire=120
)
def update_processed_file_data(
    submission_id: str | UUID,
    body: dict,
    data_field: str,
    resolve_fact_subtypes: bool,
) -> None:
    submission_id: UUID = convert_to_uuid(submission_id)  # type: ignore[no-redef]
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission_id):
        flask.abort(403)

    fe_data: dict[str, Any] = body.get("data")
    for field in fe_data.get("entity_information", []):
        field["values"] = [v for v in field.get("values", []) if v.get("entity_idx") != -1]
    fe_data["entity_information"] = [f for f in fe_data.get("entity_information", []) if f.get("values")]
    populate_missing_value_type(fe_data)
    # Validate the input before persisting in the db
    data_obj = onboarded_file_schema.load(fe_data)
    files = FileDAO.get_submission_files_for_state(submission_id, STATE_FOR_FIELD[data_field])
    db_files = {f.id for f in files}
    fe_data_files = set(data_obj.files)
    if fe_data_files - db_files:
        flask.abort(409, "File was removed!")
    if db_files - fe_data_files:
        flask.abort(409, "Update data is stale!")

    # Merge incoming data with what we have in the DB
    submission_data = (
        db.session.query(SubmissionFilesData)
        .options(load_only(data_field))
        .filter(SubmissionFilesData.submission_id == submission_id)
        .first()
    )
    if not submission_data:
        logger.error("Submission data not found!", submission_id=submission_id)
        submission_data = SubmissionFilesData(submission_id=submission_id, **{data_field: {}})
    be_data = getattr(submission_data, data_field)
    if not be_data:
        logger.warning("Onboarded data not found!", submission_id=submission_id)
        # As the data is obviously missing, there is no difference if we use get or post endpoint. However,
        # get endpoint returns a lightweight schema designed for the FE, so it has missing fields.
        if data_field == "onboarded_data":
            from copilot.v3.controllers.data_onboarding import merge_data_for_onboarding

            be_data, _ = merge_data_for_onboarding(submission_id)
        elif data_field == "entity_mapped_data":
            from copilot.v3.controllers.entity_mapping import (
                merge_files_for_entity_mapping,
            )

            be_data, _ = merge_files_for_entity_mapping(submission_id)
        else:
            raise ValueError(f"Unknown data_field: {data_field}")
    merger = FileOnboardingMerger(submission_id=submission_id, user_email=current_user.email, data_field=data_field)
    try:
        merged_data = merger.merge_onboarded_file_jsons(be_data, fe_data)
    except Exception as e:
        logger.error("Failed to merge onboarded data!", submission_id=submission_id, exception=str(e))
        merged_data = fe_data
    merged_data_obj = onboarded_file_schema.load(merged_data)
    # Add Fact Subtype suggestions
    if resolve_fact_subtypes:
        try:
            fact_subtypes = current_app.facts_client_v2.get_fact_subtypes_as_list()
            assign_fact_subtypes_to_fields(onboarded_file=merged_data_obj, fact_subtypes=fact_subtypes, logger=logger)
            perform_suggestions(onboarded_file=merged_data_obj, id_to_fact_subtype={x.id: x for x in fact_subtypes})
        except Exception as ex:
            logger.warning("Failed to suggest facts after entity mapping", ex=ex, ex_message=str(ex))

    setattr(submission_data, data_field, merged_data)
    db.session.add(submission_data)
    split_data = split_data_to_files(merged_data_obj)
    update_processed_files_data(split_data, data_field)
    db.session.commit()


def load_skip_onboarding_files(
    submission_id: UUID,
    enhance_with_resolution_data: bool = False,
) -> dict[UUID, tuple[OnboardedFile, ClassificationDocumentType]]:
    files = (
        db.session.query(File.classification, ProcessedFile)
        .join(ProcessedFile, ProcessedFile.file_id == File.id, isouter=True)
        .filter(File.submission_id == submission_id)
        .filter(File.processing_state == FileProcessingState.WAITING_FOR_COMPLETION)
        .all()
    )

    processed_files = [
        ProcessedFileWithClassification(
            classification=classification,
            data=processed_file,
        )
        for classification, processed_file in files
        if processed_file
    ]
    load_data_multiple_files(
        processed_files, "onboarded_data", enhance_with_resolution_data=enhance_with_resolution_data
    )
    return {
        file.data.file_id: (file.data.onboarded_data, file.classification)  # type: ignore[misc]
        for file in processed_files
    }


def update_file_processing_state(
    file: File,
    processing_state: FileProcessingState,
    sensible_status: str | None,
    send_event: bool,
    error_message: str | None = None,
) -> None:
    file.processing_state = processing_state
    file.sensible_status = sensible_status
    if file.processing_state == FileProcessingState.PROCESSING_FAILED and not file.classification:
        file.classification = ClassificationDocumentType.UNKNOWN.value
    db.session.add(file)
    db.session.commit()
    if send_event:
        status = (
            StepFunctionStatus.FAILURE
            if processing_state in [FileProcessingState.PROCESSING_FAILED, FileProcessingState.CLASSIFICATION_FAILED]
            else StepFunctionStatus.SUCCESS
        )
        if processing_state in [FileProcessingState.CLASSIFIED, FileProcessingState.CLASSIFICATION_FAILED]:
            KalepaEventsHandler.send_submission_file_classified_event(
                submission_id=file.submission_id,  # type: ignore[arg-type]
                file_id=file.id,  # type: ignore[arg-type]
                status=status,
                error=error_message,
            )
        else:
            KalepaEventsHandler.send_file_processing_finished_event(
                submission_id=file.submission_id,  # type: ignore[arg-type]
                file_id=file.id,  # type: ignore[arg-type]
                status=status,
                error=error_message,
            )


def maybe_update_parent_file_status(file: File) -> None:
    if file.parent_file_id is None:
        return
    child_files = db.session.query(File).filter(File.parent_file_id == file.parent_file_id).all()
    parent_file = File.query.get(file.parent_file_id)
    if parent_file and all(
        (child_file.sensible_status in _LOSS_RUN_SUCCESS or not child_file.sensible_status)
        for child_file in child_files
    ):
        parent_file.sensible_status = SensibleStatus.COMPLETE
        db.session.add(parent_file)
        db.session.commit()
        logger.info("Parent file sensible_status updated", id=parent_file.id, child_file_id=file.id)

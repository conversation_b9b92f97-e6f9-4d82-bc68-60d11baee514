from flask_login import current_user
from sqlalchemy.orm import joinedload
import flask

from copilot.models import NotebookMessage, NotebookThread, db
from copilot.models.types import PermissionType
from copilot.schemas.notebook import NotebookMessageSchema, NotebookThreadSchema
from copilot.v3.utils.notebook import (
    soft_delete_notebook_thread_if_all_messages_deleted,
)

notebook_thread_schema = NotebookThreadSchema()
notebook_message_schema = NotebookMessageSchema()


def get_notebook_threads_by_submission(submission_id: str):
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission_id):
        flask.abort(403)
    threads = (
        db.session.query(NotebookThread)
        .options(joinedload(NotebookThread.messages))
        .filter_by(submission_id=submission_id, is_deleted=False)
        .all()
    )
    return notebook_thread_schema.dump(threads, many=True)


def create_notebook_thread(submission_id: str, body: dict):
    if not current_user.has_submission_permission(PermissionType.COMMENTER, submission_id):
        flask.abort(403)
    new_thread = notebook_thread_schema.load(body)
    new_thread.submission_id = submission_id
    for message in new_thread.messages:
        if message.author_id != current_user.id:
            flask.abort(403, "Users are not allowed to create messages on behalf of other users")
    db.session.add(new_thread)
    db.session.commit()
    return notebook_thread_schema.dump(new_thread)


def delete_notebook_thread(id: str):
    thread = NotebookThread.query.get_or_404(id)
    if not current_user.has_submission_permission(PermissionType.COMMENTER, thread.submission_id):
        flask.abort(403)
    thread.is_deleted = True
    db.session.add(thread)
    db.session.commit()
    return {}, 204


def create_new_message_in_thread(submission_id: str, thread_id: str, body: dict):
    if not current_user.has_submission_permission(PermissionType.COMMENTER, submission_id):
        flask.abort(403, "Users are not allowed to change messages on behalf of other users")
    new_message = notebook_message_schema.load(body)
    new_message.thread_id = thread_id
    if new_message.author_id != current_user.id:
        flask.abort(403, "Users are not allowed to create messages on behalf of other users")
    db.session.add(new_message)
    db.session.commit()
    return notebook_message_schema.dump(new_message)


def update_notebook_message(submission_id: str, id: str, body: dict):
    if not current_user.has_submission_permission(PermissionType.COMMENTER, submission_id):
        flask.abort(403)
    message = NotebookMessage.query.get_or_404(id)
    updated_message = NotebookMessageSchema().load(body, instance=message)
    if current_user.id != updated_message.author_id or current_user.id != message.author_id:
        flask.abort(403, "Users are not allowed to change messages on behalf of other users")
    db.session.add(updated_message)
    db.session.commit()
    db.session.refresh(updated_message)
    return notebook_message_schema.dump(updated_message)


def soft_delete_notebook_message(submission_id: str, id: str):
    if not current_user.has_submission_permission(PermissionType.COMMENTER, submission_id):
        flask.abort(403)
    message = NotebookMessage.query.get_or_404(id)
    if current_user.id != message.author_id and not current_user.has_submission_permission(
        PermissionType.OWNER, submission_id
    ):
        flask.abort(
            403,
            "Users are not allowed to change messages on behalf of other users unless they are admins or report owners",
        )
    message.is_deleted = True
    db.session.add(message)
    db.session.commit()
    soft_delete_notebook_thread_if_all_messages_deleted(thread_id=message.thread_id)
    return None, 204

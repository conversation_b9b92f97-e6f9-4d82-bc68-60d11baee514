from flask_login import current_user

from copilot.models import db
from copilot.models.reports import ReadSubmission


def read_submission(id: str):
    user_id = current_user.id
    existing = ReadSubmission.query.filter(
        ReadSubmission.submission_id == id, ReadSubmission.user_id == user_id
    ).first()

    if existing:
        return None, 200

    try:
        db.session.add(ReadSubmission(user_id=user_id, submission_id=id))
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        if "duplicate key value violates unique constraint" in str(e):
            return None, 200
        if "violates foreign key constraint" in str(e):
            return None, 200
        raise e

    return None, 201


def unread_submission(id: str):
    user_id = current_user.id
    existing = ReadSubmission.query.filter(
        ReadSubmission.submission_id == id, ReadSubmission.user_id == user_id
    ).first()

    if existing:
        db.session.delete(existing)
        db.session.commit()

    return None, 200

from common.clients.slack import SlackClient
from infrastructure_common.logging import get_logger
from structlog import BoundLogger

from copilot.clients.datadog import DatadogClient
from copilot.models import Submission, db
from copilot.models.emails import Email, ReportEmailCorrespondence
from copilot.models.secura_uw_mappings import SecuraUWMapping
from copilot.services.orgs.secura_mapping.pipeline import SecuraMappingPipeline
from copilot.services.orgs.secura_mapping.strategies.agency_code import (
    AgencyCodeMappingStrategy,
)
from copilot.services.orgs.secura_mapping.strategies.agency_id import (
    AgencyIdMappingStrategy,
)
from copilot.services.orgs.secura_mapping.strategies.agency_name import (
    AgencyNameMappingStrategy,
)
from copilot.services.orgs.secura_mapping.strategies.agency_phone_number import (
    PhoneNumberMappingStrategy,
)
from copilot.services.orgs.secura_mapping.strategies.agri_sic_codes import (
    AgriSicCodesMappingStrategy,
)
from copilot.services.orgs.secura_mapping.strategies.directly_from_manual_override import (
    DirectlyFromManualOverrideMappingStrategy,
)
from copilot.services.orgs.secura_mapping.strategies.directly_from_uw import (
    DirectlyFromUwMappingStrategy,
)
from copilot.services.orgs.secura_mapping.strategies.domain import DomainMappingStrategy
from copilot.services.orgs.secura_mapping.strategies.events_coverage import (
    EventsCoverageRejectingMappingStrategy,
)
from copilot.services.orgs.secura_mapping.utils import (
    SECURA_DEBUG_SLACK_CHANNEL,
    SECURA_PILOT_UWS,
    SECURA_SENDER_TO_LOB,
    SecuraLobs,
    SecuraUW,
)

logger = get_logger()


class SecuraService:
    @staticmethod
    def get_secura_sender_emails(submission: Submission) -> set[str]:
        emails = (
            db.session.query(Email)
            .join(Email.correspondence)
            .filter(ReportEmailCorrespondence.id == submission.report.correspondence_id)
            .all()
        )
        return {
            email.email_from.lower()
            for email in emails
            if email.email_from
            if "@secura.net" in email.email_from.lower()
            if email.email_to and "<EMAIL>" in email.email_to.lower()
        }

    @staticmethod
    def determine_secura_lob_and_uw(
        submission: Submission, log: BoundLogger | None = None
    ) -> tuple[SecuraLobs | None, SecuraUW | None]:
        bnd_log = log or logger

        # 1. Get secura senders that <NAME_EMAIL>
        secura_senders = SecuraService.get_secura_sender_emails(submission)

        # 2. If we have pilot UW as direct sender, treat it as priority and a direct UW
        possible_uw = SecuraService.get_first_matching_secura_uw(secura_senders)
        if possible_uw:
            bnd_log.info("Found pilot UW as direct sender", direct_uw=possible_uw, secura_lob=possible_uw.lob)
            return possible_uw.lob, possible_uw

        # 3. We don't have direct UW so check if it is one of Secura LOB inboxes that forwarded to us
        for secura_sender, lob in SECURA_SENDER_TO_LOB.items():
            for email in secura_senders:
                if secura_sender in email:
                    log.info("Found LOB for Secura LOB inbox", email=email, secura_lob=lob)
                    return lob, None

        # 4. If we don't have any LOB, log the situation, which is not great and return None.
        #    - in reality this can happen if for example it is Kalepa employee that sent the email.
        #      In such cases we will fully load the submission but UW will need to be manually assigned.
        bnd_log.warning("No LOB found for Secura submission", secura_senders=secura_senders)
        return None, None

    @staticmethod
    def is_pilot_uw(kalepa_user_id: int) -> bool:
        return kalepa_user_id in SECURA_PILOT_UWS

    @staticmethod
    def get_first_matching_secura_uw(secura_senders: set[str]) -> SecuraUW | None:
        for id, (email, name, lob) in SECURA_PILOT_UWS.items():
            lowered_email = email.lower()
            for sender in secura_senders:
                if lowered_email in sender.lower():
                    return SecuraUW(id=id, name=name, email=lowered_email, lob=lob)
        return None

    @staticmethod
    def find_kalepa_mapping(
        submission: Submission,
        bnd_log: BoundLogger | None = None,
        context: str = "Unknown",
        datadog_client: DatadogClient | None = None,
        slack_client: SlackClient | None = None,
    ) -> SecuraUWMapping | None:
        broker_email = submission.brokerage_contact_or_broker_email
        log = bnd_log or logger
        log = log.bind(
            submission_id=submission.id,
            broker_id=submission.broker_id,
            brokerage_id=submission.brokerage_id,
            broker_email=broker_email if broker_email else "None",
            context=context,
            is_secura_mapping=True,
        )
        log.info(f"Running Secura mapping resolution in context: {context}")

        secura_lob, direct_uw = SecuraService.determine_secura_lob_and_uw(submission, log)
        if not secura_lob:
            SecuraService._send_no_lob_slack_debug(submission, context, slack_client)
            return None

        if secura_lob == SecuraLobs.FARM_LINES:
            log.info("Got FARM_LINES LOB, using AGRIBUSINESS as an alias")
            secura_lob = SecuraLobs.AGRIBUSINESS

        # Build and execute the mapping pipeline with strategies in order of priority
        pipeline = SecuraMappingPipeline(
            logger=log, call_context=context, datadog_client=datadog_client, slack_client=slack_client
        )

        # Add strategies in order of priority (highest to lowest)

        # -- existence of 'directly from manual override' for report ID should be checked first - if it exists, we
        # -- should always load and not check anything else (it will be successful and abort processing)
        pipeline.add_strategy(DirectlyFromManualOverrideMappingStrategy(log, datadog_client, slack_client))

        # -- existence of 'events' coverage means we should not load submission, immediately reject
        pipeline.add_strategy(EventsCoverageRejectingMappingStrategy(log, datadog_client, slack_client))

        if direct_uw:
            # -- No need for anything else, it will always succeed - if sent by pilot UW
            pipeline.add_strategy(DirectlyFromUwMappingStrategy(log, direct_uw, datadog_client, slack_client))
        else:
            # -- not events coverage and not directly from UW or manual override - try to find mapping
            pipeline.add_strategy(AgriSicCodesMappingStrategy(log, datadog_client, slack_client))
            pipeline.add_strategy(AgencyIdMappingStrategy(log, datadog_client, slack_client))
            pipeline.add_strategy(AgencyCodeMappingStrategy(log, datadog_client, slack_client))
            pipeline.add_strategy(PhoneNumberMappingStrategy(log, datadog_client, slack_client))
            pipeline.add_strategy(DomainMappingStrategy(log, datadog_client, slack_client))
            pipeline.add_strategy(AgencyNameMappingStrategy(log, datadog_client, slack_client))

        # Execute the pipeline
        result = pipeline.execute(submission, secura_lob)

        return result.mapping if result.successful else None

    @staticmethod
    def _send_no_lob_slack_debug(submission: Submission, call_context: str, slack_client: SlackClient | None) -> None:
        if not slack_client:
            return

        rep_name = submission.name
        rep_id = str(submission.report.id)
        sub_id = str(submission.id)
        secura_emails = SecuraService.get_secura_sender_emails(submission)

        slack_client.send_slack_message(
            channel=SECURA_DEBUG_SLACK_CHANNEL,
            text=(
                f"No Secura LOB found in *{call_context}*\n"
                f"Report name: {rep_name}\nReport url:"
                f" https://copilot.kalepa.com/report/{rep_id}\nSubmission ID: {sub_id}\n"
                f"*Secura sender emails:* {secura_emails}\n"
            ),
        )

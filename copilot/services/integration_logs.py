import hashlib
import json

from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import Session
from static_common.enums.clients.integrations import (
    IntegrationFlow,
    IntegrationOperation,
    IntegrationStatus,
)
from structlog.stdlib import BoundLogger

from copilot.models import db
from copilot.models.integration_logs import IntegrationLog
from copilot.schemas.integration_logs import (
    IntegrationLogsCreateRequestSchema,
    IntegrationLogsUpdateRequestSchema,
)
from copilot.utils import convert_to_uuid

logger = get_logger()

integration_logs_create_schema = IntegrationLogsCreateRequestSchema()
integration_logs_update_schema = IntegrationLogsUpdateRequestSchema()


class IntegrationLogsService:
    @staticmethod
    def get_integration_log_by_id(integration_log_id: str, db_session: Session | None = None) -> IntegrationLog | None:
        db_session = IntegrationLogsService._get_db_session(db_session)
        query = db_session.query(IntegrationLog).filter(IntegrationLog.id == convert_to_uuid(integration_log_id))
        integration_logs = query.first()
        return integration_logs

    @staticmethod
    def create_new_integration_log(
        body: dict, db_session: Session | None = None, commit: bool = False
    ) -> IntegrationLog:
        db_session = IntegrationLogsService._get_db_session(db_session)
        log = IntegrationLogsService._get_logger()
        integration_log = integration_logs_create_schema.load(body)
        log.info(
            "Creating new integration log",
            report_id=integration_log.report_id,
            submission_id=integration_log.submission_id,
            operation=integration_log.operation,
        )

        if integration_log.payload_json is not None:
            payload_json_hash = hashlib.sha256(
                json.dumps(integration_log.payload_json, sort_keys=True, separators=(",", ":")).encode("utf-8")
            ).hexdigest()
        else:
            payload_json_hash = None

        if integration_log.response_json is not None:
            response_json_hash = hashlib.sha256(
                json.dumps(integration_log.response_json, sort_keys=True, separators=(",", ":")).encode("utf-8")
            ).hexdigest()
        else:
            response_json_hash = None

        integration_log.payload_json_hash = payload_json_hash
        integration_log.response_json_hash = response_json_hash
        if not integration_log.attempts:
            integration_log.attempts = 1

        db_session.add(integration_log)
        if commit:
            db_session.commit()
        return integration_log

    @staticmethod
    def add_pending_integration_log(
        organization_id: int,
        report_id: str,
        submission_id: str,
        operation: IntegrationOperation,
        flow: IntegrationFlow,
        logical_identifier: str | None = None,
        executing_user_email: str | None = None,
        db_session: Session | None = None,
        commit: bool = False,
    ) -> IntegrationLog:
        body = {
            "organization_id": organization_id,
            "report_id": report_id,
            "submission_id": submission_id,
            "status": IntegrationStatus.PENDING.value,
            "flow": flow.value,
            "operation": operation.value,
            "payload_json": None,
            "response_json": None,
            "error_details": None,
            "additional_data": None,
            "logical_identifier": logical_identifier,
            "attempts": 1,
            "executing_user_email": executing_user_email,
        }
        return IntegrationLogsService.create_new_integration_log(body, db_session=db_session, commit=commit)

    @staticmethod
    def get_latest_integration_log_by_report_and_operation(
        report_id: str, operation: IntegrationOperation, db_session: Session | None = None
    ) -> IntegrationLog | None:
        db_session = IntegrationLogsService._get_db_session(db_session)
        log = IntegrationLogsService._get_logger()
        query = db_session.query(IntegrationLog).filter(
            IntegrationLog.report_id == convert_to_uuid(report_id),
            IntegrationLog.operation == operation,
        )
        query = query.order_by(IntegrationLog.created_at.desc()).limit(1)
        integration_log = query.first()
        if integration_log:
            if current_user.organization_id != integration_log.organization_id:
                log.warn(
                    "User does not have access to this integration log",
                    integration_log_id=integration_log.id,
                    user_org_id=current_user.organization_id,
                    integration_log_org_id=integration_log.organization_id,
                )
                return None
        return integration_log

    @staticmethod
    def update_integration_log_by_id(
        integration_log_id: str,
        body: dict,
        db_session: Session | None = None,
        commit: bool = False,
    ) -> IntegrationLog | None:
        db_session = IntegrationLogsService._get_db_session(db_session)
        log = IntegrationLogsService._get_logger()
        existing_integration_log = (
            db_session.query(IntegrationLog).filter(IntegrationLog.id == convert_to_uuid(integration_log_id)).first()
        )
        if not existing_integration_log:
            log.error("Integration log not found", integration_log_id=integration_log_id)
            return None

        integration_log = integration_logs_update_schema.load(body, instance=existing_integration_log)

        if integration_log.payload_json is not None:
            payload_json_hash = hashlib.sha256(
                json.dumps(integration_log.payload_json, sort_keys=True, separators=(",", ":")).encode("utf-8")
            ).hexdigest()
        else:
            payload_json_hash = None

        if integration_log.response_json is not None:
            response_json_hash = hashlib.sha256(
                json.dumps(integration_log.response_json, sort_keys=True, separators=(",", ":")).encode("utf-8")
            ).hexdigest()
        else:
            response_json_hash = None

        integration_log.payload_json_hash = payload_json_hash
        integration_log.response_json_hash = response_json_hash

        db_session.add(integration_log)
        if commit:
            db_session.commit()
        return integration_log

    @staticmethod
    def _get_db_session(db_session: Session) -> Session:
        if db_session is None:
            db_session = db.session
        return db_session

    @staticmethod
    def _get_logger(bound_log: BoundLogger | None = None) -> BoundLogger:
        if bound_log is None:
            bound_log = logger
        return bound_log

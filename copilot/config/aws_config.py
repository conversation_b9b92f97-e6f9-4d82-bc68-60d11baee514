from dataclasses import dataclass
from typing import ClassVar, Optional
import os


@dataclass
class AwsConfig:
    aws_region: str | None
    aws_profile: str | None

    AWS_REGION_KEY: ClassVar[str] = "AWS_REGION"
    AWS_PROFILE_KEY: ClassVar[str] = "AWS_PROFILE"

    @staticmethod
    def from_env_vars() -> Optional["AwsConfig"]:
        if AwsConfig.AWS_REGION_KEY in os.environ or AwsConfig.AWS_PROFILE_KEY in os.environ:
            return AwsConfig(
                aws_region=os.environ.get(AwsConfig.AWS_REGION_KEY),
                aws_profile=os.environ.get(AwsConfig.AWS_PROFILE_KEY),
            )

        return None


aws_config: AwsConfig = AwsConfig.from_env_vars()

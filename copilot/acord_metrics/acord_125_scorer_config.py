from infrastructure_common.logging import get_logger

from copilot.acord_metrics.utils import (
    PROPOSED_EFFECTIVE_DATE,
    PROPOSED_EXPIRATION_DATE,
    DateRange<PERSON>he<PERSON>,
    _score_is_valid_zip,
    not_null_scorer,
    valid_yes_or_no_answer,
)
from copilot.models.acord_metrics.metrics import (
    AcordScorer,
    ElementScorer,
    PropertyScorer,
    SectionScorer,
    ValueScorer,
)

logger = get_logger()

ACORD_PARTS = ["mailing", "gl code", "sic", "naics", "fein or"]

ACORD_125_SCORING_CONFIG = AcordScorer(
    "ACORD_125",
    excluded_keys={
        "lines_of_business",
        "policy_information",
        "nature_of_business",
        "primary_naics",
        "agency_information",
    },
    property_scorers=[
        PropertyScorer(
            PROPOSED_EFFECTIVE_DATE,
            checks=[
                not_null_scorer,
                ValueScorer(name="valid_date_range", scoring_function=DateRangeChecker(days_range=720).score_date),
            ],
            weight=2,
        ),
        PropertyScorer(
            PROPOSED_EXPIRATION_DATE,
            checks=[
                not_null_scorer,
                ValueScorer(name="valid_date_range", scoring_function=DateRangeChecker(days_range=720).score_date),
            ],
            weight=2,
        ),
        PropertyScorer("description_of_primary_operations", checks=[not_null_scorer], weight=6),
    ],
    section_scorers=[
        # If Applicant information is missing, then we apply penalty, so that the score is close to 0.
        # If this field is null, then max score is . If the field is not null, then min score is ~0.49.
        # Similarly, when first element corresponding to FNI is None.
        # This is a dominant. Score must be above 0.49.
        SectionScorer(
            "applicant_information",
            weight=50,
            null_score=-1,
            expected_number_of_elements=3,
            element_index_weights={0: 98},
            element_scorer=ElementScorer(
                excluded_keys={
                    "applicant_address",
                    "applicant_info_gl_code_1",
                    "applicant_sic",
                    "applicant_naics",
                    "applicant_fein",
                    "applicant_website_address",
                },
                score_all_nulls_or_blanks=True,
                null_or_blank_values_score=-1,
                property_scorers=[
                    PropertyScorer(name="applicant_name_and_address", checks=[not_null_scorer]),
                ],
            ),
        ),
        # If Premises information is missing, then we apply penalty, so that the score is close to 0.
        # If this field is null, then max score is 0.49.
        # Similarly, when first premises is None.
        # If there is at least one non-empty premises, we will build on top of 0.49.
        # To require the first premise to be valid, we can set a expect a score of ~0.64
        # We can set a threshold higher, but I don't want to fail too much.
        SectionScorer(
            "premises_info",
            weight=30,
            null_score=-1,
            expected_number_of_elements=4,
            element_index_weights={0: 97},
            element_scorer=ElementScorer(
                excluded_keys={
                    "location_number",
                    "building_number",
                    "county",
                    "city_limits_inside",
                    "city_limits_outside",
                    "interest_owner",
                    "interest_tenant",
                    "fulltime_employees",
                    "parttime_employees",
                    "annual_revenues",
                    "occupied_area",
                    "open_to_public_area",
                    "building_area",
                    "area_leased_to_others",
                    "operations_description",
                    "full_address",
                },
                score_all_nulls_or_blanks=True,
                null_or_blank_values_score=0,
                property_scorers=[
                    PropertyScorer(name="street", checks=[not_null_scorer]),
                    PropertyScorer(name="city", checks=[not_null_scorer]),
                    PropertyScorer(name="state", checks=[not_null_scorer]),
                    PropertyScorer(
                        name="zip",
                        checks=[not_null_scorer, ValueScorer("is_valid_zip", scoring_function=_score_is_valid_zip)],
                    ),
                ],
            ),
        ),
        SectionScorer(
            "general_information",
            weight=10,
            null_score=-1,
            element_scorer=ElementScorer(
                score_all_nulls_or_blanks=True,
                null_or_blank_values_score=1.0,
                excluded_keys={
                    "subsidiary_parent_company_name",
                    "subsidiary_percentage_owned",
                    "applicants_subsidiary_company_name",
                    "applicants_subsidiary_percentage_owned",
                    "safety_program_manual",
                    "safety_plan_monthly_meetings",
                    "safety_plan_safety_position",
                    "safety_plan_osha",
                    "coverage_declined_non_payment",
                    "coverage_declined_agent",
                    "coverage_declined_non_renewal",
                    "coverage_declined_underwriting",
                    "coverage_declined_condition_corrected",
                    "flammables_explanation",
                    "any_past_losses_relating_to_sexual_abuse_explanation",
                    "any_applicant_been_indicted_explanation",
                    "name_of_trust",
                    "does_applicant_have_other_business_ventures_explanation",
                    "does_applicant_own_lease_operate_any_drones_explanation",
                    "does_applicant_hire_others_to_operate_drones_explanation",
                    "any_foreign_operations_explanation",
                    "past_conviction_arson",
                    "past_conviction_arson_explanation",
                    "catastrophe_exposure",
                    "catastrophe_exposure_explanation",
                    "has_other_insurance",
                    "other_insurance_explanation",
                },
                property_scorers=[
                    PropertyScorer("is_applicant_subsidiary", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("does_applicant_have_subsidiaries", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("is_formal_safety_plan_in_operation", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("any_exposure_flammables", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("any_policy_or_coverage_declined", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("any_past_losses_relating_to_sexual_abuse", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("any_applicant_been_indicted", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("any_uncorrected_fire_or_safety_violations", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("has_applicant_had_forclosure", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("has_applicant_had_judgement_or_lien", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("is_business_placed_in_trust", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("any_foreign_operations", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("does_applicant_have_other_business_ventures", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("does_applicant_own_lease_operate_any_drones", checks=[valid_yes_or_no_answer]),
                    PropertyScorer("does_applicant_hire_others_to_operate_drones", checks=[valid_yes_or_no_answer]),
                ],
            ),
        ),
    ],
)

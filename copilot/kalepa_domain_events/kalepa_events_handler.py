from collections.abc import Sequence
from dataclasses import dataclass
from datetime import datetime
from uuid import UUID

from events_common.kalepa_events import KalepaEvents
from events_common.lambdas.constants import (
    ASSIGN_CLIENT_IDS_KEY,
    CLIENT_FILE_TAGS_KEY,
    CLIENT_SPECIFIC_ADDITIONAL_CONTEXT_KEY,
    DECLINING_REASON_KEY,
    DECLINING_USER_KEY,
    DUPLICATE_FILE_KEY,
    EMAIL_ACCOUNT_KEY,
    EMAIL_ID_KEY,
    EXTERNAL_FACING_DATA_UPDATE_TYPE_KEY,
    FORCE_MANUAL_REQUEST,
    HIGH_PRIORITY_KEY,
    IS_DEPENDENT_SUBMISSION_KEY,
    IS_SHELL_SUBMISSION_KEY,
    IS_SOFT_DELETE_KEY,
    IS_SUBMISSION_VERIFIED_KEY,
    IS_SYNC_SHELL_SUBMISSION_KEY,
    IS_VERIFIED_SHELL_KEY,
    LOST_REASONS_KEY,
    MATCHING_RUN_STATUS_KEY,
    ME<PERSON>AGE_IDS_KEY,
    NAICS_CHANGED_FOR_COMPLETED_SUBMISSION_KEY,
    NEW_STAGE_KEY,
    OLD_STAGE_KEY,
    SENSIBLE_CONFIGURATION_KEY,
    SENSIBLE_DOCUMENT_TYPE_KEY,
    SENSIBLE_PROMOTE_TO_PROD_KEY,
    START_FILE_PROCESSING_KEY,
    SUBMISSION_CLIENT_ID_KEY,
    SUBMISSION_ORIGIN_KEY,
    SYNC_RUN_ID_KEY,
    TRIAGE_RESULT_KEY,
    UNDERWRITERS_CHANGE_KEY,
    UPDATED_FILE_PROPERTIES_KEY,
    USER_EMAIL,
    USER_ID_KEY,
)
from events_common.model.types import SenderEvent, StepFunctionStatus
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from static_common.enums.external import ExternalFacingDataUpdateType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_sync import SyncMatchingRunStatus

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.models import File, Organization, ReportV2
from copilot.models.reports import Submission, SubmissionBusiness, SubmissionClientId
from copilot.models.submission_sync import SyncMatchingRun
from copilot.models.types import ReportTriageResult

logger = get_logger()


@dataclass
class StageChangePayload:
    submission_id: UUID | str
    old_stage: SubmissionStage | None
    new_stage: SubmissionStage | None
    lost_reasons: list[str]
    decline_reason: str | None
    user_id: int
    user_email: str
    organization_id: int


class KalepaEventsHandler:
    @staticmethod
    def send_submission_duplicated(
        submission: Submission,
        original_report: ReportV2,
        target_stage: SubmissionStage,
        declined_date: datetime = None,
        additional_data: dict | None = None,
    ) -> None:
        original_submission = original_report.submission
        original_submission_id = str(original_submission.id) if original_submission else None
        original_report_id = str(original_report.id)
        update_body = additional_data or {}
        update_body.update({"stage": target_stage.value, "declined_date": declined_date})
        additional_data = {
            "original_submission_id": original_submission_id,
            "original_report_id": original_report_id,
            "update_body": update_body,
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_DUPLICATED, submission_id=submission.id, additional_data=additional_data
        )

    @staticmethod
    def send_new_report_created_event(submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.NEW_REPORT_CREATED,
            submission_id=submission.id,
        )

    @staticmethod
    def send_submission_client_id_added(submission: Submission) -> None:
        if not submission.client_submission_ids:
            return
        client_id: SubmissionClientId = sorted(submission.client_submission_ids, key=lambda x: x.created_at)[-1]

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_CLIENT_ID_ADDED,
            submission_id=submission.id,
            report_id=submission.report_id,
            organization_id=submission.organization_id,
            additional_data={
                SUBMISSION_CLIENT_ID_KEY: client_id.client_submission_id,
                IS_SUBMISSION_VERIFIED_KEY: submission.is_verified,
            },
        )

    @staticmethod
    def send_submission_updated_event(
        submission: Submission,
        underwriters_change_payload: dict | None = None,
        naics_changed_for_completed_submission: bool = False,
        additional_data: dict | None = None,
    ) -> None:
        if additional_data is None:
            additional_data = {}

        additional_data["is_deleted"] = submission.is_deleted or False
        additional_data[NAICS_CHANGED_FOR_COMPLETED_SUBMISSION_KEY] = naics_changed_for_completed_submission

        if underwriters_change_payload:
            additional_data[UNDERWRITERS_CHANGE_KEY] = underwriters_change_payload

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_UPDATED,
            submission_id=submission.id,
            report_id=submission.reports[0].id,
            organization_id=submission.organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_permissions_updated_event(submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_PERMISSIONS_UPDATED,
            submission_id=submission.id,
            report_id=submission.reports[0].id,
        )

    @staticmethod
    def send_submission_business_confirmed_event(
        submission_business: SubmissionBusiness, submission: Submission
    ) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_BUSINESS_CONFIRMED,
            submission_id=submission.id,
            submission_business_id=submission_business.id,
            business_id=submission_business.business_id,
        )

    @staticmethod
    def send_submission_business_disconfirmed_event(
        submission_business: SubmissionBusiness, submission: Submission
    ) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_BUSINESS_DISCONFIRMED,
            submission_id=submission.id,
            submission_business_id=submission_business.id,
            business_id=submission_business.business_id,
            organization_id=submission.organization_id,
            report_id=submission.report_id,
            requested_name=submission_business.requested_name,
            requested_address=submission_business.requested_address,
        )

    @staticmethod
    def send_submission_business_added_event(submission_business: SubmissionBusiness, submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_BUSINESS_ADDED,
            submission_id=submission.id,
            submission_business_id=submission_business.id,
            business_id=submission_business.business_id,
        )

    @staticmethod
    def send_submission_business_removed_event(submission_business: SubmissionBusiness, submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_BUSINESS_REMOVED,
            submission_id=submission.id,
            submission_business_id=submission_business.id,
            business_id=submission_business.business_id,
        )

    @staticmethod
    def send_all_submission_business_confirmed_event(submission: Submission) -> None:
        s3_key = f"first-party-data-fields/{submission.id!s}.json"
        additional_data = {}

        if current_app.submission_s3_client.check_if_file_exists(s3_key):
            logger.info("First party data found", submission_id=submission.id)
            additional_data = {"s3_key": s3_key}

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_ALL_BUSINESSES_CONFIRMED,
            submission_id=submission.id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_light_clearing_finished_event(submission_id: UUID, is_dependent_submission: bool) -> None:
        additional_data = {IS_DEPENDENT_SUBMISSION_KEY: is_dependent_submission}
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.LIGHT_CLEARING_FINISHED,
            submission_id=submission_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_external_clearing_finished_event(submission: Submission, additional_data: dict | None = None) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.EXTERNAL_CLEARING_FINISHED,
            submission_id=submission.id,
            organization_id=submission.organization_id,
            report_id=submission.report_id,
            additional_data=additional_data or {},
        )

    @staticmethod
    def send_submission_file_uploaded_event(submission: Submission, s3_key: str) -> None:
        additional_data = {"s3_key": s3_key}
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_UPLOADED,
            submission_id=submission.id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_file_processing_cancelled(submission_id: UUID) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_PROCESSING_CANCELLED,
            submission_id=submission_id,
        )

    @staticmethod
    def send_start_report_processing_request(
        submission_id: UUID | str, report_id: UUID | str, organization_id: int
    ) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.START_REPORT_PROCESSING_REQUESTED,
            submission_id=str(submission_id),
            report_id=str(report_id),
            organization_id=organization_id,
        )

    @staticmethod
    def send_submission_processing_finished(submission_id: UUID) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_PROCESSING_FINISHED,
            submission_id=submission_id,
        )

    @staticmethod
    def send_submission_light_clearing_finished(submission_id: UUID) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.LIGHT_CLEARING_FINISHED,
            submission_id=submission_id,
        )

    @staticmethod
    def send_business_deduplication_finished_event(submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_BUSINESS_DEDUPLICATION_FINISHED,
            submission_id=submission.id,
        )

    @staticmethod
    def send_autoconfirming_finished_event(submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_AUTOCONFIRMING_FINISHED,
            submission_id=submission.id,
        )

    @staticmethod
    def send_shell_submission_created_event(submission: Submission) -> None:
        additional_data = {
            IS_VERIFIED_SHELL_KEY: submission.is_verified_shell,
            SUBMISSION_ORIGIN_KEY: submission.origin.value,
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SHELL_SUBMISSION_CREATED,
            submission_id=submission.id,
            additional_data=additional_data,
            organization_id=submission.organization_id,
        )

    @staticmethod
    def send_request_recommendation_event(
        submission: Submission | None = None,
        force_manual: bool | None = False,
        submission_id: UUID | None = None,
        high_prio: bool = False,
    ) -> None:
        additional_data = {FORCE_MANUAL_REQUEST: force_manual}
        if (submission is None or submission.id is None) and submission_id is None:
            raise ValueError("Either submission or submission_id must be provided")
        if high_prio:
            additional_data[HIGH_PRIORITY_KEY] = high_prio

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.RECOMMENDATION_REQUESTED,
            submission_id=submission_id or submission.id,  # type: ignore
            additional_data=additional_data,
        )

    @staticmethod
    def send_summarization_request(submission_id: UUID, force: bool = False):
        additional_data = {
            "force_manual_summarization": force,
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUMMARIZATION_REQUESTED,
            submission_id=submission_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_data_onboarding_finished_event(submission_id: UUID) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_DATA_ONBOARDING_FINISHED,
            submission_id=submission_id,
        )

    @staticmethod
    def send_business_confirmation_finished_event(submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_BUSINESS_CONFIRMATION_FINISHED,
            submission_id=submission.id,
        )

    @staticmethod
    def send_submission_file_added_event(
        submission: Submission, file: File, start_file_processing: bool = True
    ) -> None:
        additional_data = {
            START_FILE_PROCESSING_KEY: start_file_processing,
        }
        if file.client_file_tags:
            additional_data[CLIENT_FILE_TAGS_KEY] = file.client_file_tags

        if submission:
            origin = submission.origin.value if submission.origin else Origin.EMAIL.value
            additional_data[SUBMISSION_ORIGIN_KEY] = origin
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_ADDED,
            submission_id=file.submission_id,
            report_id=submission.report_id,
            file_id=file.id,
            organization_id=file.organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_file_classified_event(
        submission: Submission, file: File, status: StepFunctionStatus | None = None, error: str | None = None
    ) -> None:
        additional_data = {"file_type": file.file_type.value, "origin": file.origin.value}

        if file.client_file_tags:
            additional_data[CLIENT_FILE_TAGS_KEY] = file.client_file_tags

        if submission:
            origin = submission.origin.value if submission.origin else Origin.EMAIL.value
            additional_data[SUBMISSION_ORIGIN_KEY] = origin
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_CLASSIFIED,
            submission_id=file.submission_id,
            report_id=submission.report_id,
            file_id=file.id,
            additional_data=additional_data,
            step_function_status=status,
            error=error,
            organization_id=file.organization_id,
        )

    @staticmethod
    def send_submission_declined_event(submission: Submission, user_id: int, user_email: str | None) -> None:
        additional_data = {
            DECLINING_USER_KEY: user_id,
            USER_EMAIL: user_email,
            "submission_external_id": submission.first_client_submission_id,
            "company": Organization.get_company_for_id(submission.user.organization_id),
            "declining_reason": submission.reason_for_declining,
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_DECLINED, submission_id=submission.id, additional_data=additional_data
        )

    @staticmethod
    def send_submission_file_type_updated(file: File) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_TYPE_UPDATED, submission_id=file.submission_id, file_id=file.id
        )

    @staticmethod
    def send_submission_changed_requested_coverage(submission: Submission) -> None:
        additional_data = {
            IS_SHELL_SUBMISSION_KEY: submission.is_shell_from_sync or bool(not submission.businesses),
            IS_SYNC_SHELL_SUBMISSION_KEY: submission.is_shell_from_sync or (
                submission.origin == Origin.SYNC and not submission.businesses
            ),
        }

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_CHANGED_REQUESTED_COVERAGE,
            submission_id=submission.id,
            additional_data=additional_data,
            time=submission.created_at,
        )

    @staticmethod
    def send_verify_submission(submission: Submission) -> None:
        KalepaEventsHandler._send_event(event_type=KalepaEvents.VERIFY_SUBMISSION, submission_id=submission.id)

    @staticmethod
    def send_submission_auto_verified(submission: Submission) -> None:
        KalepaEventsHandler._send_event(event_type=KalepaEvents.SUBMISSION_AUTO_VERIFIED, submission_id=submission.id)

    @staticmethod
    def _send_event(event_type: KalepaEvents, **kwargs) -> None:
        request = SenderEvent(event_type=event_type, **kwargs)
        current_app.lambda_client.invoke_sender_lambda(request)

    @staticmethod
    def send_file_processing_finished_event(
        submission_id: UUID, file_id: UUID, status: StepFunctionStatus, error: str | None = None
    ) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_PROCESSING_FINISHED,
            submission_id=submission_id,
            file_id=file_id,
            cause=None,
            step_function_status=status,
            error=error,
        )

    @staticmethod
    def send_data_extracted_event(submission_id: UUID, file_id: UUID) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_DATA_EXTRACTED,
            submission_id=submission_id,
            file_id=file_id,
        )

    @staticmethod
    def send_submission_data_consolidated(submission_id: UUID, is_auto_processed: bool) -> None:
        pds_customizable_classifiers = FeatureFlagsClient.is_feature_enabled_in_submission_context(
            FeatureType.PDS_CUSTOMIZABLE_CLASSIFIERS, submission_id=submission_id
        )

        additional_data = {
            "is_auto_processed": is_auto_processed,
            "pds_customizable_classifiers_enabled": pds_customizable_classifiers,
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_DATA_CONSOLIDATED,
            submission_id=submission_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_entity_mapping_finished_event(submission_id: UUID) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_ENTITY_MAPPING_FINISHED, submission_id=submission_id
        )

    @staticmethod
    def send_submission_files_clearing_finished_event(submission_id: UUID) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILES_CLEARING_FINISHED, submission_id=submission_id
        )

    @staticmethod
    def send_submission_stuck_event(submission_id: UUID) -> None:
        KalepaEventsHandler._send_event(event_type=KalepaEvents.SUBMISSION_STUCK, submission_id=submission_id)

    @staticmethod
    def send_submission_stage_changed_event_bulk(events: Sequence[StageChangePayload]) -> None:
        additional_data = {"events": []}
        for e in events:
            data = {
                USER_EMAIL: e.user_email,
                USER_ID_KEY: e.user_id,
                DECLINING_REASON_KEY: e.decline_reason,
                LOST_REASONS_KEY: e.lost_reasons or [],
                OLD_STAGE_KEY: e.old_stage,
                NEW_STAGE_KEY: e.new_stage,
            }
            additional_data.get("events").append(
                {
                    "event_type": KalepaEvents.SUBMISSION_STAGE_CHANGE,
                    "submission_id": str(e.submission_id),
                    "organization_id": e.organization_id,
                    "additional_data": data,
                }
            )
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.BULK_EVENTS,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_stage_changed_event(
        submission_id: UUID | str,
        old_stage: SubmissionStage | None,
        new_stage: SubmissionStage | None,
        lost_reasons: list[str],
        decline_reason: str | None,
        user_id: int,
        user_email: str,
        organization_id: int,
    ) -> None:
        if old_stage is None or new_stage is None:
            logger.warning(
                "Not sending SUBMISSION_STAGE_CHANGE event, one of the stages is None",
                submission_id=submission_id,
                old_stage=old_stage,
                new_stage=new_stage,
            )
            return

        additional_data = {
            USER_EMAIL: user_email,
            USER_ID_KEY: user_id,
            DECLINING_REASON_KEY: decline_reason,
            LOST_REASONS_KEY: lost_reasons or [],
            OLD_STAGE_KEY: old_stage,
            NEW_STAGE_KEY: new_stage,
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_STAGE_CHANGE,
            submission_id=str(submission_id),
            organization_id=organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_email_requested_event(
        submission_id: UUID | str,
        report_id: UUID | str,
        organization_id: int,
        email_id: UUID | str,
    ):
        additional_data = {EMAIL_ID_KEY: str(email_id)}

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_EMAIL_REQUESTED,
            submission_id=str(submission_id),
            report_id=str(report_id),
            organization_id=organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_verified_event(
        submission_id: UUID | str,
        report_id: UUID | str,
        organization_id: int,
        submission_origin: Origin,
    ):
        additional_data = {SUBMISSION_ORIGIN_KEY: submission_origin.value if submission_origin else Origin.EMAIL.value}
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_VERIFIED,
            submission_id=str(submission_id),
            report_id=str(report_id),
            organization_id=organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_sensible_template_validation_requested(document_type: str, configuration: str, promote_to_prod: bool):
        additional_data = {
            SENSIBLE_DOCUMENT_TYPE_KEY: document_type,
            SENSIBLE_CONFIGURATION_KEY: configuration,
            SENSIBLE_PROMOTE_TO_PROD_KEY: promote_to_prod,
        }

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SENSIBLE_TEMPLATE_VALIDATION_REQUESTED,
            additional_data=additional_data,
        )

    @staticmethod
    def send_report_triage_finished_event(
        submission_id: UUID | str,
        report_id: UUID | str,
        organization_id: int,
        is_verified_shell: bool,
        triage_result: ReportTriageResult,
        client_specific_event_data: dict[str, any] = {},
    ):
        additional_data = {
            IS_VERIFIED_SHELL_KEY: is_verified_shell,
            TRIAGE_RESULT_KEY: triage_result.value,
        }
        if client_specific_event_data:
            additional_data[CLIENT_SPECIFIC_ADDITIONAL_CONTEXT_KEY] = client_specific_event_data

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.REPORT_TRIAGE_FINISHED,
            submission_id=str(submission_id),
            report_id=str(report_id),
            organization_id=organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def __get_external_facing_data_updated_event_data(
        submission: Submission,
        update_type: ExternalFacingDataUpdateType = ExternalFacingDataUpdateType.DEFAULT_UPDATE,
        additional_data: dict | None = None,
    ) -> dict | None:
        def make_event(sub: Submission, update: ExternalFacingDataUpdateType, add_data: dict | None) -> dict:
            extra_data = {
                IS_SUBMISSION_VERIFIED_KEY: sub.is_verified,
                SUBMISSION_ORIGIN_KEY: sub.origin.value,  # type: ignore
                EXTERNAL_FACING_DATA_UPDATE_TYPE_KEY: update.value,
            }
            if add_data:
                extra_data.update(add_data)

            return {
                "event_type": KalepaEvents.EXTERNAL_FACING_DATA_UPDATED,
                "submission_id": str(sub.id),
                "report_id": str(sub.report_id),
                "organization_id": sub.organization_id,
                "additional_data": extra_data,
            }

        if submission.organization_id == ExistingOrganizations.Nationwide.value and submission.origin == Origin.API:
            return make_event(submission, update_type, additional_data)

        if (
            submission.organization_id == ExistingOrganizations.ARU.value
            and submission.origin == Origin.EMAIL
            and update_type == ExternalFacingDataUpdateType.CLIENT_STATUS_UPDATE
        ):
            return make_event(submission, update_type, additional_data)

        return None

    @staticmethod
    def send_external_facing_data_updated_event(
        submission: Submission,
        update_type: ExternalFacingDataUpdateType = ExternalFacingDataUpdateType.DEFAULT_UPDATE,
        additional_data: dict | None = None,
    ):
        event_data: dict | None = KalepaEventsHandler.__get_external_facing_data_updated_event_data(
            submission, update_type, additional_data=additional_data
        )
        if event_data is not None:
            KalepaEventsHandler._send_event(**event_data)

    @staticmethod
    def send_nw_boss_test_call_received_event(raw_data: dict):
        raw_data["event_type"] = KalepaEvents.EXTERNAL_FACING_DATA_UPDATED
        KalepaEventsHandler._send_event(**raw_data)

    @staticmethod
    def send_external_facing_data_updated_event_bulk(submissions: Sequence[Submission]) -> None:
        events: list[dict | None] = [
            KalepaEventsHandler.__get_external_facing_data_updated_event_data(submission) for submission in submissions
        ]
        filtered_events: list[dict] = [event for event in events if event is not None]
        if len(filtered_events) > 0:
            additional_data = {"events": filtered_events}
            KalepaEventsHandler._send_event(
                event_type=KalepaEvents.BULK_EVENTS,  # type: ignore
                additional_data=additional_data,
            )

    @staticmethod
    def send_submission_file_updated_event(submission: Submission, file: File, updated_fields: list[str]) -> None:
        additional_data = {UPDATED_FILE_PROPERTIES_KEY: updated_fields}
        if submission:
            origin = submission.origin.value if submission.origin else Origin.EMAIL.value
            additional_data[SUBMISSION_ORIGIN_KEY] = origin
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_UPDATED,
            submission_id=file.submission_id,
            report_id=submission.report_id,
            file_id=file.id,
            organization_id=file.organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_submission_file_deleted_event(
        submission: Submission,
        file: File,
        is_duplicate_file: bool = False,
        is_soft_delete: bool = False,
        client_file_tags: dict | None = None,
    ) -> None:
        additional_data = {
            CLIENT_FILE_TAGS_KEY: file.client_file_tags or client_file_tags,
            DUPLICATE_FILE_KEY: is_duplicate_file,
            IS_SOFT_DELETE_KEY: is_soft_delete,
        }
        if submission:
            origin = submission.origin.value if submission.origin else Origin.EMAIL.value
            additional_data[SUBMISSION_ORIGIN_KEY] = origin

        if current_user:
            additional_data[USER_EMAIL] = current_user.email

        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_FILE_DELETED,
            submission_id=file.submission_id,
            report_id=submission.report_id,
            file_id=file.id,
            organization_id=file.organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_project_role_added_event(submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.PROJECT_ROLE_ADDED, submission_id=submission.id, report_id=submission.report_id
        )

    @staticmethod
    def send_submission_business_hide_property_facts_removed_event(submission: Submission) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SUBMISSION_BUSINESS_HIDE_PROPERTY_FACTS_REMOVED,
            submission_id=submission.id,
            report_id=submission.report_id,
        )

    @staticmethod
    def send_email_classified_event(email_id: UUID | str, report: ReportV2) -> None:
        additional_data = {
            EMAIL_ID_KEY: str(email_id),
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.EMAIL_CLASSIFICATION_FINISHED,
            submission_id=report.submission.id,
            report_id=report.id,
            organization_id=report.organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_email_classification_requested_event(email_id: UUID | str, report: ReportV2) -> None:
        additional_data = {
            EMAIL_ID_KEY: str(email_id),
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.EMAIL_CLASSIFICATION_REQUESTED,
            submission_id=report.submission.id,
            report_id=report.id,
            organization_id=report.organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_email_without_verified_submission_event(
        organization_id: int, message_ids: list[str], email_account: str
    ) -> None:
        additional_data = {
            EMAIL_ACCOUNT_KEY: email_account,
            MESSAGE_IDS_KEY: message_ids,
        }
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.EMAIL_WITHOUT_VERIFIED_SUBMISSION,
            organization_id=organization_id,
            additional_data=additional_data,
        )

    @staticmethod
    def send_sync_matching_run_created_event(req: SyncMatchingRun, assign_client_ids: bool = False) -> None:
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SYNC_MATCHING_RUN_CREATED,
            organization_id=req.organization_id,
            additional_data={
                SYNC_RUN_ID_KEY: str(req.id),
                ASSIGN_CLIENT_IDS_KEY: assign_client_ids,
            },
        )

    @staticmethod
    def send_sync_matching_run_finished_event(req: SyncMatchingRun) -> None:
        status = req.status
        if isinstance(status, SyncMatchingRunStatus):
            status = status.value
        KalepaEventsHandler._send_event(
            event_type=KalepaEvents.SYNC_MATCHING_RUN_FINISHED,
            organization_id=req.organization_id,
            additional_data={
                SYNC_RUN_ID_KEY: str(req.id),
                MATCHING_RUN_STATUS_KEY: status,
            },
        )

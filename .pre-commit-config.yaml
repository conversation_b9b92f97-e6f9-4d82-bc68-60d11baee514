repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-ast
      - id: check-case-conflict
      - id: check-shebang-scripts-are-executable
      - id: check-toml
      - id: end-of-file-fixer
      - id: mixed-line-ending
        args: [--fix=lf]
  - repo: local
    hooks:
      - id: stdlib_logging
        name: check if standard logging is not used
        args: [--multiline]
        types: [python]
        entry: "import logging(?!.*# allow-stdlib-logging\t*$)|from logging(?!.*# allow-stdlib-logging\t*$)"
        files: "copilot/"
        exclude: "copilot/app.py"
        language: pygrep
      - id: dockerfile_refresh_tag
        name: check if there's no force refresh comment in Dockerfile
        args: [--multiline]
        entry: "# Force refresh"
        files: "pyproject.toml"
        language: pygrep
      - id: no_poetry_cfg
        name: check if there's no Poetry config in pyproject.toml
        args: [--multiline]
        entry: "tool.poetry"
        files: "pyproject.toml"
        language: pygrep
      - id: no_poetry_lock
        name: check if poetry.lock is present in repository root
        entry: ".+"
        files: "^poetry.lock$"
        language: pygrep
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.0.272
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.6.0
    hooks:
      - id: pyupgrade
        args: [--py311-plus]
        exclude: ^migrations/versions/.*\.py$
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        exclude: ^migrations/versions/.*\.py$

"""Add server default to classifiers tables

Revision ID: c5e591933085
Revises: 29c6fad5fdd6
Create Date: 2025-04-16 13:09:30.386650+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'c5e591933085'
down_revision = '29c6fad5fdd6'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('customizable_classifiers_v2', 'created_at',
                    existing_type=sa.DateTime(),
                    server_default=sa.text('now()'))
    op.alter_column('classifier_versions', 'created_at',
                    existing_type=sa.DateTime(),
                    server_default=sa.text('now()'))
    op.alter_column('classifier_config', 'created_at',
                    existing_type=sa.DateTime(),
                    server_default=sa.text('now()'))
    op.alter_column('classifier_config_versions', 'created_at',
                    existing_type=sa.DateTime(),
                    server_default=sa.text('now()'))
    op.alter_column('classifier_phrases', 'created_at',
                    existing_type=sa.DateTime(),
                    server_default=sa.text('now()'))
    op.alter_column('classifier_to_config_version', 'created_at',
                    existing_type=sa.DateTime(),
                    server_default=sa.text('now()'))

    op.alter_column('customizable_classifiers_v2', 'updated_at',
                    existing_type=sa.DateTime(),
                    nullable=True,
                    existing_nullable=False)
    op.alter_column('classifier_versions', 'updated_at',
                    existing_type=sa.DateTime(),
                    nullable=True,
                    existing_nullable=False)
    op.alter_column('classifier_config', 'updated_at',
                    existing_type=sa.DateTime(),
                    nullable=True,
                    existing_nullable=False)
    op.alter_column('classifier_config_versions', 'updated_at',
                    existing_type=sa.DateTime(),
                    nullable=True,
                    existing_nullable=False)
    op.alter_column('classifier_phrases', 'updated_at',
                    existing_type=sa.DateTime(),
                    nullable=True,
                    existing_nullable=False)
    op.alter_column('classifier_to_config_version', 'updated_at',
                    existing_type=sa.DateTime(),
                    nullable=True,
                    existing_nullable=False)

def downgrade():
    pass

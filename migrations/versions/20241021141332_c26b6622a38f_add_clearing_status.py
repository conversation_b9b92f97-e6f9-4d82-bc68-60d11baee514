"""Add clearing_status

Revision ID: c26b6622a38f
Revises: 57500d698f09
Create Date: 2024-10-21 14:13:32.349533+00:00

"""
from time import sleep

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c26b6622a38f"
down_revision = "2e9b08a067ba"
branch_labels = None
depends_on = None
SLEEP_TIME = 0.1


def upgrade():
    op.execute("""CREATE TYPE clearingstatus AS ENUM ('CLEARED', 'IN_CLEARING_CONFLICT', 'BLOCKED', 'PRE_CLEARING');""")

    while True:
        try:
            with op.get_context().begin_transaction():
                op.execute("SET statement_timeout TO '2 s';")
                op.execute("LOCK TABLE ONLY submissions in ACCESS EXCLUSIVE MODE;")
                op.add_column(
                    "submissions",
                    sa.Column(
                        "clearing_status",
                        sa.Enum("CLEARED", "IN_CLEARING_CONFLICT", "BLOCKED", "PRE_CLEARING", name="clearingstatus"),
                        nullable=True,
                    ),
                )
                break
        except Exception as e:
            print(f"Retrying... {e}")
            sleep(SLEEP_TIME)


def downgrade():
    pass

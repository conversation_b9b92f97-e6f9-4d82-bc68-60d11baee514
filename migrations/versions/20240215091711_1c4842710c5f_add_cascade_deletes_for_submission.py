"""Add cascade deletes for submission

Revision ID: 1c4842710c5f
Revises: ccce4f45d06d
Create Date: 2024-02-15 09:17:11.440363+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1c4842710c5f"
down_revision = "ccce4f45d06d"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '900 s';")  # 15 min
        op.drop_constraint(
            "stuck_submission_feedback_submission_id_fkey", "stuck_submission_feedback", type_="foreignkey"
        )
        op.drop_constraint("scheduled_emails_submission_id_fkey", "scheduled_emails", type_="foreignkey")
        op.drop_constraint(
            "copilot_worker_execution_event_submission_id_fkey", "copilot_worker_execution_event", type_="foreignkey"
        )
        op.drop_constraint("workers_comp_experience_submission_id_fkey", "workers_comp_experience", type_="foreignkey")
        op.drop_constraint("ifta_data_submission_id_fkey", "ifta_data", type_="foreignkey")
        op.drop_constraint(
            "workers_comp_state_rating_info_submission_id_fkey", "workers_comp_state_rating_info", type_="foreignkey"
        )
        op.create_foreign_key(
            "stuck_submission_feedback_submission_id_fkey",
            "stuck_submission_feedback",
            "submissions",
            ["submission_id"],
            ["id"],
            ondelete="CASCADE",
            postgresql_not_valid=True,
        )
        op.create_foreign_key(
            "scheduled_emails_submission_id_fkey",
            "scheduled_emails",
            "submissions",
            ["submission_id"],
            ["id"],
            ondelete="CASCADE",
            postgresql_not_valid=True,
        )
        op.create_foreign_key(
            "copilot_worker_execution_event_submission_id_fkey",
            "copilot_worker_execution_event",
            "submissions",
            ["submission_id"],
            ["id"],
            ondelete="CASCADE",
            postgresql_not_valid=True,
        )
        op.create_foreign_key(
            "workers_comp_experience_submission_id_fkey",
            "workers_comp_experience",
            "submissions",
            ["submission_id"],
            ["id"],
            ondelete="CASCADE",
            postgresql_not_valid=True,
        )
        op.create_foreign_key(
            "ifta_data_submission_id_fkey",
            "ifta_data",
            "submissions",
            ["submission_id"],
            ["id"],
            ondelete="CASCADE",
            postgresql_not_valid=True,
        )
        op.create_foreign_key(
            "workers_comp_state_rating_info_submission_id_fkey",
            "workers_comp_state_rating_info",
            "submissions",
            ["submission_id"],
            ["id"],
            ondelete="CASCADE",
            postgresql_not_valid=True,
        )
        op.execute(
            "ALTER TABLE stuck_submission_feedback VALIDATE CONSTRAINT stuck_submission_feedback_submission_id_fkey"
        )
        op.execute("ALTER TABLE scheduled_emails VALIDATE CONSTRAINT scheduled_emails_submission_id_fkey")
        op.execute(
            "ALTER TABLE copilot_worker_execution_event VALIDATE CONSTRAINT"
            " copilot_worker_execution_event_submission_id_fkey"
        )
        op.execute("ALTER TABLE workers_comp_experience VALIDATE CONSTRAINT workers_comp_experience_submission_id_fkey")
        op.execute("ALTER TABLE ifta_data VALIDATE CONSTRAINT ifta_data_submission_id_fkey")
        op.execute(
            "ALTER TABLE workers_comp_state_rating_info VALIDATE CONSTRAINT"
            " workers_comp_state_rating_info_submission_id_fkey"
        )


def downgrade():
    pass

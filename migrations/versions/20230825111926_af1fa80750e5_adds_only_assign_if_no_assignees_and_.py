"""Adds only_assign_if_no_assignees and updates arch config

Revision ID: af1fa80750e5
Revises: d3753ef9083d
Create Date: 2023-08-25 11:19:26.337918+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "af1fa80750e5"
down_revision = "d3753ef9083d"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
            update sync_configuration
            set configuration = jsonb_set(configuration, '{handlers,3,config,only_assign_if_no_assignees}',
                                          to_jsonb('false'::boolean))
            where organization_id = 6;
            
            update sync_configuration
            set configuration = jsonb_set(configuration, '{handlers,3,config,only_assign_if_no_assignees}',
                                          to_jsonb('false'::boolean))
            where organization_id = 36;
            
            update sync_configuration
            set configuration = jsonb_set(configuration, '{handlers,3,config,only_assign_if_no_assignees}',
                                          to_jsonb('true'::boolean))
            where organization_id = 10;
            
            update sync_configuration
            set configuration = jsonb_set(configuration, '{handlers,3,config,remove_previous_assignees}',
                                          to_jsonb('false'::boolean))
            where organization_id = 10;
            update sync_configuration
            set configuration = jsonb_set(configuration, '{handlers,1,config,allow_backwards_status_update}',
                                          to_jsonb('false'::boolean))
            where organization_id = 10;
        """)


def downgrade():
    op.execute("""
            update sync_configuration
            set configuration = jsonb_set(configuration, '{handlers,3,config,remove_previous_assignees}',
                                          to_jsonb('true'::boolean))
            where organization_id = 10;
            
            update sync_configuration
            set configuration = jsonb_delete_path(configuration, '{handlers,3,config,only_assign_if_no_assignees}')
            where organization_id = 10;
            
            update sync_configuration
            set configuration = jsonb_set(configuration, '{handlers,1,config,allow_backwards_status_update}',
                                          to_jsonb('true'::boolean))
            where organization_id = 10;
            
            update sync_configuration
            set configuration = jsonb_delete_path(configuration, '{handlers,3,config,only_assign_if_no_assignees}')
            where organization_id = 6;
            
            update sync_configuration
            set configuration = jsonb_delete_path(configuration, '{handlers,3,config,only_assign_if_no_assignees}')
            where organization_id = 36;
        """)

"""add WAITING_FOR_DATA_CONSOLIDATION file processing state

Revision ID: 98ba324a2159
Revises: 7gy8gt6590ds
Create Date: 2023-07-04 11:27:17.315617+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "98ba324a2159"
down_revision = "7gy8gt6590ds"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""ALTER TYPE fileprocessingstate ADD VALUE IF NOT EXISTS 'WAITING_FOR_DATA_CONSOLIDATION';""")


def downgrade():
    pass

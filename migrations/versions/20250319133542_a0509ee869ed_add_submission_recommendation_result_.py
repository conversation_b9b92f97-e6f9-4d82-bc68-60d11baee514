"""add_submission_recommendation_result_table

Revision ID: a0509ee869ed
Revises: f92f298ad632
Create Date: 2025-03-19 13:35:42.290285+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'a0509ee869ed'
down_revision = 'f92f298ad632'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "submission_recommendation_results",
        sa.Column("submission_id", postgresql.UUID(), nullable=False),
        sa.Column("action", postgresql.ENUM(name="recommendationactionenum", create_type=False), nullable=True,),
        sa.Column("priority", sa.DECIMAL(), nullable=True),
        sa.Column("is_refer", sa.Boolean(), nullable=True),
        sa.Column("score", sa.Integer(), nullable=True),
        sa.Column("score_ml", sa.Float(), nullable=True),
        sa.Column("pm_rules_modifier", sa.Float(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("submission_id"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"],  ondelete="CASCADE"),
    )
    op.create_index(
        op.f("ix_submission_recommendation_results_action"), "submission_recommendation_results", ["action"], unique=False
    )
    op.create_index(
        op.f("ix_submission_recommendation_results_priority"), "submission_recommendation_results", ["priority"], unique=False
    )


def downgrade():
    op.drop_table("submission_recommendation_results")

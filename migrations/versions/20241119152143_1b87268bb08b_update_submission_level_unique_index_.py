"""update unique key for submission level extracted data

Revision ID: 1b87268bb08b
Revises: ddfecf029906
Create Date: 2024-11-19 15:21:43.202175+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "1b87268bb08b"
down_revision = "ddfecf029906"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '600 s';")  # 10 min
        op.execute(
            """
DELETE
FROM submission_level_extracted_data
WHERE id IN (SELECT id
         FROM (SELECT id,
                      ROW_NUMBER()
                      OVER ( PARTITION BY submission_id, file_id, field, source_details, generation_method ORDER BY id ) AS row_num
               FROM submission_level_extracted_data) t
         WHERE t.row_num > 1);
        """
        )
        op.execute(
            "CREATE UNIQUE INDEX CONCURRENTLY uq_submission_level_extracted_data_nn ON submission_level_extracted_data (submission_id, file_id, field, source_details, generation_method) NULLS NOT DISTINCT"
        )


def downgrade():
    pass

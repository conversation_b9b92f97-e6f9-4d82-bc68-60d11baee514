"""Adds missing verification alert types

Revision ID: 65b34e5d9c17
Revises: 3a56ea26b47d
Create Date: 2024-02-27 16:30:07.769655+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "65b34e5d9c17"
down_revision = "3a56ea26b47d"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE alert_type ADD VALUE IF NOT EXISTS 'MISSING_VERIFICATION_L3';""")
        op.execute("""ALTER TYPE alert_type ADD VALUE IF NOT EXISTS 'MISSING_VERIFICATION_L4';""")
        op.execute("""ALTER TYPE alert_type ADD VALUE IF NOT EXISTS 'MISSING_VERIFICATION_L5';""")
        op.execute("""ALTER TYPE alert_type ADD VALUE IF NOT EXISTS 'MISSING_VERIFICATION_L6';""")


def downgrade():
    pass

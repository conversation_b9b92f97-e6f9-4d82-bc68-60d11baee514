"""Adds 'direct_match_only' to 'submission_sync'

Revision ID: c7cfc85c0bdc
Revises: 484eee110f51
Create Date: 2024-02-02 16:32:37.142036+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c7cfc85c0bdc"
down_revision = "484eee110f51"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "submission_sync", sa.Column("direct_match_only", sa.<PERSON>(), nullable=False, server_default=sa.false())
    )


def downgrade():
    op.drop_column("submission_sync", "direct_match_only")

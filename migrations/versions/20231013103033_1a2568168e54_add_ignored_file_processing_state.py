"""Add ignored file processing state

Revision ID: 1a2568168e54
Revises: 3dd568168e54
Create Date: 2023-10-13 10:30:33.812748+00:00

"""
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

from copilot.models.sensible_calls import DAYS_OF_MONTH

# revision identifiers, used by Alembic.
revision = "1a2568168e54"
down_revision = "3dd568168e54"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(f"ALTER TYPE fileprocessingstate ADD VALUE IF NOT EXISTS 'IGNORED';")


def downgrade():
    pass

"""classifier is not applicable to

Revision ID: 05ff30a08346
Revises: fcee8233593e
Create Date: 2023-12-06 13:42:50.646311+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "05ff30a08346"
down_revision = "fcee8233593e"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "customizable_classifiers", sa.Column("is_not_applicable_to_naics_codes", sa.ARRAY(sa.String()), nullable=True)
    )


def downgrade():
    op.drop_column("customizable_classifiers", "is_not_applicable_to_naics_codes")

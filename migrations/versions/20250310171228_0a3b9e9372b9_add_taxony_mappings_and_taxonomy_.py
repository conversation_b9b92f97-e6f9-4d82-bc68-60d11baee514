"""add taxony mappings and taxonomy description tables

Revision ID: 0a3b9e9372b9
Revises: 82abcac43978
Create Date: 2025-03-10 17:12:28.131967+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '0a3b9e9372b9'
down_revision = '82abcac43978'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "taxonomy_descriptions",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("code", sa.String(), nullable=False, unique=True),
        sa.Column("description", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )

    op.create_table(
        "taxonomy_mappings",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "mapping_type",
            postgresql.ENUM(
        "NAICS_TO_SIC",
                "NAICS_TO_SIC5",
                "SIC_TO_NAICS",
                "SIC5_TO_NAICS",
                "NAICS_TO_GL",
                "GL_TO_NAICS",
                name="mapping_type_enum",
            ),
            nullable=False
        ),
        sa.Column("code", sa.String(), nullable=False),
        sa.Column("mapped_codes", postgresql.ARRAY(sa.String()), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.ForeignKeyConstraint(["code"], ["taxonomy_descriptions.code"], ondelete="CASCADE"),
        sa.UniqueConstraint("mapping_type", "code"),
    )


def downgrade():
    op.drop_table("taxonomy_mappings")
    op.drop_table("taxonomy_descriptions")

"""Resolve clearing issues

Revision ID: 97d37b6e377f
Revises: eb26600a18d2
Create Date: 2024-04-10 10:51:22.258397+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "97d37b6e377f"
down_revision = "eb26600a18d2"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        delete from submission_clearing_issue where id in (
            select sci.id from submissions s join submission_clearing_issue sci on sci.submission_id = s.id
            where s.processing_state != 'NEEDS_CLEARING' and sci.is_light and not sci.is_resolved
        )
    """)


def downgrade():
    pass

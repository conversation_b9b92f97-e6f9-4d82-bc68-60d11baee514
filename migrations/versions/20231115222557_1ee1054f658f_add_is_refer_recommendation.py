"""Add is refer recommendation

Revision ID: 1ee1054f658f
Revises: 95494a49e6f7
Create Date: 2023-11-15 22:25:57.530954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1ee1054f658f"
down_revision = "95494a49e6f7"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("recommendation_v2_is_refer", sa.<PERSON>(), nullable=True))
    op.create_index(
        op.f("ix_submissions_recommendation_v2_is_refer"), "submissions", ["recommendation_v2_is_refer"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_submissions_recommendation_v2_is_refer"), table_name="submissions")
    op.drop_column("submissions", "recommendation_v2_is_refer")

"""Add 'submission_premises' table

Revision ID: a9887e565808
Revises: 87f0e778dbd9
Create Date: 2025-05-18 09:42:31.889489+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'a9887e565808'
down_revision = '87f0e778dbd9'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "submission_premises",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("named_insured", sa.String(), nullable=True, index=False),
        sa.Column("address", sa.String(), nullable=False, index=False),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("premises_id", sa.String(), nullable=True),
        sa.Column("submission_premises_type", sa.String(), nullable=False),
        sa.Column("additional_data", postgresql.JSONB(astext_type=sa.String(), none_as_null=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.ForeignKeyConstraint(("submission_id",), ["submissions.id"], ondelete="CASCADE"),
    )

    op.create_index(
        op.f("ix_submission_premises_submission_id"), "submission_premises", ["submission_id"], unique=False
    )
    op.create_index(op.f("ix_submission_premises_premises_id"), "submission_premises", ["premises_id"], unique=False)
    op.create_index(
        op.f("ix_submission_premises_submission_id_submission_premises_type"),
        "submission_premises",
        ["submission_premises_type", "submission_id"],
        unique=True,
    )


def downgrade():
    op.drop_table("submission_premises")

"""set submission flag is deleted for deleted reports

Revision ID: 7c1eac9322a2
Revises: 654eac9322a2
Create Date: 2024-01-22 07:52:51.034271+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7c1eac9322a2"
down_revision = "654eac9322a2"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '300 s';")
    op.execute("""
            UPDATE submissions
            SET is_deleted = TRUE
            FROM reports_v2
            WHERE submissions.report_id = reports_v2.id AND reports_v2.is_deleted = TRUE;
        """)


def downgrade():
    pass

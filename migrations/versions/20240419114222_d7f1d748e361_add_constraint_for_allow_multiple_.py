"""add constraint for allow_multiple_assigned_underwriters_per_submission

Revision ID: d7f1d748e361
Revises: 8f4ea5d20ab3
Create Date: 2024-04-19 11:42:22.245600+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d7f1d748e361"
down_revision = "8f4ea5d20ab3"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    UPDATE settings
    SET allow_multiple_assigned_underwriters_per_submission = TRUE
    WHERE organization_id IS NOT NULL AND organization_id != 10;
    """)

    op.execute("""
    ALTER TABLE settings
    ADD CONSTRAINT allow_multiple_uws_per_submission_not_null_for_orgs CHECK (organization_id is NULL OR allow_multiple_assigned_underwriters_per_submission IS NOT NULL)
    """)


def downgrade():
    op.execute("""
    ALTER TABLE settings
    DROP CONSTRAINT allow_multiple_uws_per_submission_not_null_for_orgs;
    """)

    op.execute("""
    UPDATE settings
    SET allow_multiple_assigned_underwriters_per_submission = NULL
    WHERE organization_id IS NOT NULL AND organization_id != 10;
    """)

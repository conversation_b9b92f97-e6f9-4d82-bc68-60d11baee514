"""Add LLM models to email task definitions

Revision ID: c9d8e7f6a5b4
Revises: 85743acdcb34
Create Date: 2025-03-24 12:27:06.000000+00:00

"""
from uuid import uuid4

from alembic import op

# revision identifiers, used by Alembic.
revision = "c9d8e7f6a5b4"
down_revision = "85743acdcb34"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Get existing generic consolidation model ID (GPT 4o)
    generic_consolidation_id = "0707b5ee-9537-4f7e-8557-12c2ed4e5ad3"

    # Define task codes to update
    task_codes = [
        "EXTRACT_ENTITIES",
        "EXTRACT_BROKER_DATA",
        "EXTRACT_POLICY_DATA",
        "EXTRACT_COVERAGES",
        "EXTRACT_PROJECT_DATA",
        "EXTRACT_GC_DATA",
    ]

    # Task definition descriptions
    task_descriptions = {
        "EXTRACT_ENTITIES": """From email sent from broker to insurance company extract following information:
- name of the applicant
- valid US address of the applicant
- years in business
- financial data for each year mentioned in the email
- description of applicant operation
- applicant valid website URL
- other entities where business is operating (name and valid US address)
If any of the information is not present in the email, leave the field empty.""",
        "EXTRACT_BROKER_DATA": """From email sent from broker (or on behalf of them) to insurance company extract information about the broker, correspondence contact and brokerage.
In the output there could be additional internal data coming from the database records.""",
        "EXTRACT_POLICY_DATA": """From email sent from broker to insurance company extract following information:
- effective date of requested policy (if no year is given, infer it based on the email sent date; if day is missing, use first day of the month)
- boolean flag whether the policy is a renewal
If any of the information is not present in the email, leave the field empty.""",
        "EXTRACT_COVERAGES": """From email sent from broker to insurance company extract requested coverage types together with following information:
- self insurance retention
- aggregate limit 
- excess attachment point (only applicable for excess coverages)
- target premium
- expiring premium
If any of the information is not present in the email, leave the field empty.""",
        "EXTRACT_PROJECT_DATA": """From email sent from broker to insurance company extract data related to construction projects requested in the application (only in case if application is for one on more construction projects):
- name of the project
- main address of the project
- estimated cost of the project
- subcontractor cost of the project
- project start date
- project end date
If any of the information is not present in the email, leave the field empty.""",
        "EXTRACT_GC_DATA": """From email sent from broker to insurance company extract data related to General Contractor (GC) (only in case if application is for construction project and GC is mentioned in the email):
- name of the General Contractor
- valid US address of the General Contractor
If any of the information is not present in the email, leave the field empty.""",
    }

    # Handler classes for task models
    handler_classes = {
        "EXTRACT_ENTITIES": "ExtractEntities",
        "EXTRACT_BROKER_DATA": "ExtractBrokerData",
        "EXTRACT_POLICY_DATA": "ExtractPolicyData",
        "EXTRACT_COVERAGES": "ExtractCoverages",
        "EXTRACT_PROJECT_DATA": "ExtractProjectData",
        "EXTRACT_GC_DATA": "ExtractGCData",
    }

    # Update task definitions
    for code in task_codes:
        conn.execute(
            f"""
            UPDATE task_definition 
            SET llm_task_description = '{task_descriptions[code]}'
            WHERE code = '{code}'
            """
        )

        # Get task definition ID
        task_definition = conn.execute(f"""SELECT id FROM task_definition WHERE code = '{code}'""").fetchall()
        if not task_definition:
            continue

        task_definition_id = task_definition[0][0]

        # Create IDs for new models
        gemini_model_id = str(uuid4())
        claude_model_id = str(uuid4())

        # Create task models for Gemini and Claude
        handler_class = handler_classes[code]
        model_config = f'{{"handler_class": "{handler_class}"}}'

        # Insert Gemini model
        conn.execute(
            f"""
            INSERT INTO task_model (id, name, type, execution_config, execution_type, processing_type, llm_model, use_task_output_processor)
            VALUES ('{gemini_model_id}', '{handler_class} (Gemini 2.0 Flash)', 'LLM_PROMPT',
            '{model_config}', 'LAMBDA', 'EXTRACTION', 'GEMINI_2_0_FLASH', TRUE)
            """
        )

        # Insert Claude model
        conn.execute(
            f"""
            INSERT INTO task_model (id, name, type, execution_config, execution_type, processing_type, llm_model, use_task_output_processor)
            VALUES ('{claude_model_id}', '{handler_class} (Claude 3.7 Sonnet)', 'LLM_PROMPT',
            '{model_config}', 'LAMBDA', 'EXTRACTION', 'CLAUDE_3_7_SONNET', TRUE)
            """
        )

        # Create task definition models
        for model_id, is_benchmark in [
            (gemini_model_id, True),
            (claude_model_id, True),
            (generic_consolidation_id, False),
        ]:
            is_consolidation = model_id == generic_consolidation_id

            conn.execute(
                f"""
                INSERT INTO task_definition_model (
                    id, task_definition_id, task_model_id, validation_task_model_id, "order",
                    is_always_run, is_aware_of_previous_runs, is_refinement_run, is_preprocessing_input_run, 
                    can_self_reflect, is_consolidation_run, is_validation_run, is_benchmark_run, is_disabled
                )
                VALUES (
                    '{uuid4()}', '{task_definition_id}', '{model_id}', NULL, 
                    {0 if not is_consolidation else 'NULL'}, 
                    FALSE, FALSE, FALSE, FALSE, FALSE, {is_consolidation}, FALSE, {is_benchmark}, FALSE
                )
                """
            )


def downgrade():
    conn = op.get_bind()

    # Get task definition IDs
    task_codes = [
        "EXTRACT_ENTITIES",
        "EXTRACT_BROKER_DATA",
        "EXTRACT_POLICY_DATA",
        "EXTRACT_COVERAGES",
        "EXTRACT_PROJECT_DATA",
        "EXTRACT_GC_DATA",
    ]

    for code in task_codes:
        # Get task definition ID
        task_definition = conn.execute(f"""SELECT id FROM task_definition WHERE code = '{code}'""").fetchall()
        if not task_definition:
            continue

        task_definition_id = task_definition[0][0]

        # Get model IDs created in this migration (Gemini and Claude models)
        task_models = conn.execute(
            f"""
            SELECT tm.id FROM task_model tm
            JOIN task_definition_model tdm ON tdm.task_model_id = tm.id
            WHERE tdm.task_definition_id = '{task_definition_id}'
            AND (
                tm.name LIKE '%Gemini 2.0 Flash%' 
                OR tm.name LIKE '%Claude 3.7 Sonnet%'
                OR (tdm.is_consolidation_run = TRUE AND tm.processing_type = 'CONSOLIDATION')
            )
            """
        ).fetchall()

        # Delete task definition models first
        for row in task_models:
            model_id = row[0]
            conn.execute(
                f"""
                DELETE FROM task_definition_model 
                WHERE task_definition_id = '{task_definition_id}' AND task_model_id = '{model_id}'
                """
            )

            # Only delete task models that we created (Gemini and Claude)
            if (
                "Gemini" in conn.execute(f"SELECT name FROM task_model WHERE id = '{model_id}'").fetchone()[0]
                or "Claude" in conn.execute(f"SELECT name FROM task_model WHERE id = '{model_id}'").fetchone()[0]
            ):
                conn.execute(f"DELETE FROM task_model WHERE id = '{model_id}'")

        # Remove llm_task_description

        conn.execute(
            f"""
            UPDATE task_definition 
            SET llm_task_description = NULL
            WHERE code = '{code}'
            """
        )

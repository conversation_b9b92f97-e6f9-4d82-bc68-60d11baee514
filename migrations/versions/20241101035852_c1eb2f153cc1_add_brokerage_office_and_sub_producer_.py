"""Add brokerage_office and sub_producer info to 'submissions'

Revision ID: c1eb2f153cc1
Revises: 36593b093556
Create Date: 2024-11-01 03:58:52.953992+00:00

"""

import time

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c1eb2f153cc1"
down_revision = "36593b093556"
branch_labels = None
depends_on = None
SLEEP_TIME = 0.1
TIMEOUT = 1200


def upgrade():
    with op.get_context().begin_transaction():
        counter = float(TIMEOUT) / SLEEP_TIME
        table_locked = False
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions {counter}")
                op.execute("LOCK TABLE ONLY submissions in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.add_column("submissions", sa.Column("brokerage_office", sa.String(), nullable=True))
            op.add_column("submissions", sa.Column("sub_producer_name", sa.String(), nullable=True))
            op.add_column("submissions", sa.Column("sub_producer_email", sa.String(), nullable=True))
        else:
            raise Exception("Could not lock submissions table. Retry the migration")


def downgrade():
    with op.get_context().begin_transaction():
        table_locked = False
        counter = float(TIMEOUT) / SLEEP_TIME
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions {counter}")
                op.execute("LOCK TABLE ONLY submissions in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.drop_column("submissions", "brokerage_office")
            op.drop_column("submissions", "sub_producer_name")
            op.drop_column("submissions", "sub_producer_email")
        else:
            raise Exception("Could not lock submissions table. Retry the migration")

"""add new uw mappings for admiral

Revision ID: 4b34ba21348e
Revises: 657f22c4e91e
Create Date: 2024-10-29 14:03:19.365383+00:00

"""
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4b34ba21348e"
down_revision = "657f22c4e91e"
branch_labels = None
depends_on = None


def upgrade():
    connection = op.get_bind()
    mappings = connection.execute(
        """
        SELECT id 
        FROM admiral_uw_mappings 
        WHERE producer_no = '0222D'
        """
    ).fetchall()

    assignments = []
    for (mapping_id,) in mappings:
        assignments.extend(
            [
                {
                    "id": str(uuid4()),
                    "uw_mapping_id": mapping_id,
                    "user_email": "<EMAIL>",
                    "for_renewals": False,
                },
                {
                    "id": str(uuid4()),
                    "uw_mapping_id": mapping_id,
                    "user_email": "b<PERSON><EMAIL>",
                    "for_renewals": False,
                },
                {
                    "id": str(uuid4()),
                    "uw_mapping_id": mapping_id,
                    "user_email": "<EMAIL>",
                    "for_renewals": True,
                },
            ]
        )

    print(f"Adding {len(assignments)} new assignments for Admiral UW mappings")
    if assignments:
        op.bulk_insert(
            sa.table(
                "admiral_uw_mapping_assignments",
                sa.Column("id", postgresql.UUID(as_uuid=True)),
                sa.Column("uw_mapping_id", postgresql.UUID(as_uuid=True)),
                sa.Column("user_email", sa.String),
                sa.Column("for_renewals", sa.Boolean),
            ),
            assignments,
        )


def downgrade():
    pass

"""Added new filemetricname for IFTA

Revision ID: 9523a92aa042
Revises: f1aab6cbdc17
Create Date: 2024-02-09 12:08:32.094291+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9523a92aa042"
down_revision = "f1aab6cbdc17"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'IFTA_QUARTERS_COUNT';")
        op.execute(
            "ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'IFTA_PERCENTAGE_OF_QUARTERS_WITH_TOTAL_MATCHED';"
        )
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'IFTA_ALL_QUARTERS_MATCHED';")
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'IFTA_TOTAL_IDENTIFIED';")


def downgrade():
    pass

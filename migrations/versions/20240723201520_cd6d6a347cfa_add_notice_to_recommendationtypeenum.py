"""Add NOTICE to recommendationtypeenum

Revision ID: cd6d6a347cfa
Revises: d528071122c6
Create Date: 2024-07-23 20:15:20.732657+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cd6d6a347cfa"
down_revision = "d528071122c6"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE recommendationtype ADD VALUE IF NOT EXISTS 'NOTICE';")
        op.execute("ALTER TYPE recommendationactionenum ADD VALUE IF NOT EXISTS 'NOTICE';")


def downgrade():
    pass

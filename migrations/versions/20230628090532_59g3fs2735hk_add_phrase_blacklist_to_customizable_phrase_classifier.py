"""Add invalid to submission history

Revision ID: 59g3fs2735hk
Revises: 39f2ge7620dx
Create Date: 2023-06-28 09:05:32.679664+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "59g3fs2735hk"
down_revision = "39f2ge7620dx"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "phrase_matching_classifier_phrase", sa.Column("phrase_blacklist", sa.ARRAY(sa.String()), nullable=True)
    )


def downgrade():
    op.drop_column("phrase_matching_classifier_phrase", "phrase_blacklist")

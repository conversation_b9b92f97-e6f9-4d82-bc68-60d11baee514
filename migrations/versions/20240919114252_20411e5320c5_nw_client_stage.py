"""nw client stage

Revision ID: 20411e5320c5
Revises: 35525e64b044
Create Date: 2024-09-19 11:42:52.606341+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "20411e5320c5"
down_revision = "35525e64b044"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
INSERT INTO client_submission_stage_config (organization_id, client_stage, tags, tag_labels, copilot_stage)
VALUES (6, 'Binder', '{Bound - Not Sent}', '{}', 'QUOTED_BOUND'),
       (6, 'Binder', '{Bound-Sent}', '{}', 'QUOTED_BOUND'),
       (6, 'Policy', '{Active}', '{}', 'QUOTED_BOUND'),
       (6, 'Policy', '{Completed}', '{}', 'QUOTED_BOUND'),
       (6, 'Prepare to Issue', '{Bound-Sent}', '{}', 'QUOTED_BOUND'),
       (6, 'Prepare to Issue', '{Bound - Not Sent}', '{}', 'QUOTED_BOUND'),
       (6, 'Quote', '{In Process}', '{}', 'ON_MY_PLATE'),
       (6, 'Quote', '{In Process, Blocked}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Declined}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Blocked, Closed, Bound by Competitor}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Blocked, Closed, Broker Lost Account}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Blocked, Closed, Duplicate Submission}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed, Incomplete Submission}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed, No Response From Broker}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed, Not Reviewed}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed, Other}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed, Pricing}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Blocked, Closed, Renewed by Incumbent}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Blocked, Closed, Secondary}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed, Submitted in Error}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Blocked, Closed, Unacceptable Coverage Need}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released}', '{}', 'DECLINED'),
       --(6, 'Quote', '{In Process, Released, Closed}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, Duplicate Submission}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, No Response From Broker}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, Other}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, Pricing}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Released, Closed, Renewed by Incumbent}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Released, Closed, Secondary}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, Submitted in Error}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, System Closed - Pended Timeout}', '{}', 'EXPIRED'),
       (6, 'Quote', '{In Process, Released, Closed, Incomplete Submission}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, Bound by Competitor}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Released, Closed, Not Reviewed}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Closed, Broker Lost Account}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{In Process, Released, Closed, Unacceptable Coverage Need}', '{}', 'DECLINED'),
       --(6, 'Quote', '{In Process, Released, Declined}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Declined, Unacceptable Exposure}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Declined, Outside of Guidelines/Appetite}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Declined, Other}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Declined, Unacceptable Coverage Need}', '{}', 'DECLINED'),
       (6, 'Quote', '{In Process, Released, Declined, Account Size Too Small}', '{}', 'DECLINED'),
       (6, 'Quote', '{Quoted}', '{}', 'QUOTED'),
       (6, 'Quote', '{Quoted, Released}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked, Declined}', '{}', 'DECLINED'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Bound by Competitor}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Broker Lost Account}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked, Closed, No Response From Broker}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Other}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Pricing}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Renewed by Incumbent}', '{}', 'QUOTED_LOST'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Unacceptable Coverage Need}', '{}', 'DECLINED'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Incomplete Submission}', '{}', 'DECLINED'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Duplicate Submission}', '{}', 'DECLINED'),
       (6, 'Quote', '{Quoted, Blocked, Closed, Secondary}', '{}', 'DECLINED'),
       (6, 'Renewal', '{In Process}', '{}', 'ON_MY_PLATE'),
       (6, 'Renewal Quote', '{In Process}', '{}', 'ON_MY_PLATE'),
       (6,  'Blocked', '{}', '{}', 'BLOCKED');
                   """)


def downgrade():
    pass

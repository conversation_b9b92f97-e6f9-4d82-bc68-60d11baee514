"""Add more task models

Revision ID: 1c22d87fc4e1
Revises: 20bcf3c654e0
Create Date: 2025-02-20 09:34:08.643079+00:00

"""
from uuid import uuid4

from alembic import op

# revision identifiers, used by Alembic.
revision = "1c22d87fc4e1"
down_revision = "20bcf3c654e0"
branch_labels = None
depends_on = None


def upgrade():
    # LLM Versions

    gpt_o1_id = "c83d5e96-4aa4-4b50-9f16-85cb7711be21"
    gpt_o3_mini_id = "4f370c92-82eb-4691-8f63-2424f5a7af42"
    sonar_id = "54630d52-a5cc-4001-8309-73d8a40c5217"

    conn = op.get_bind()
    conn.execute(
        f"""
                    insert into llm_version (id, name, version, cost_per_input_token, cost_per_output_token) values 
                ('{gpt_o1_id}', 'GPT o1', 'OPENAI_GPT_O1', 0.000015, 0.000060),
                ('{gpt_o3_mini_id}', 'GPT o3 mini', 'OPENAI_GPT_O3_MINI', 0.0000011, 0.0000044), 
                ('{sonar_id}', 'SONAR', 'SONAR', 0.000001, 0.000001)                      
                """
    )

    gpt_4o_llm_version = conn.execute("""select id from llm_version where version = 'OPENAI_GPT_4O';""").fetchall()
    gpt_4o_id = gpt_4o_llm_version[0][0]

    # Task Models

    description_gpt_03_mini_id = "7bb132bc-8639-4513-b554-116c1fae6ad8"
    description_sonar_id = "72f41ee4-cad5-43da-bddc-a5f6d40aa447"

    naics_gpt_o1_id = "69ed7a4b-16c9-4dfa-bfed-8748f017daff"
    naics_gpt_03_mini_id = "6d22da16-be25-41f7-a8bc-93510cc40d1e"
    naics_sonar_id = "34978141-2638-4410-b6c9-c756552ec6e0"

    generic_consolidation_id = "0707b5ee-9537-4f7e-8557-12c2ed4e5ad3"
    generic_validation_id = "be6c1293-9dd3-486d-ae69-d3ddd1e86bd2"

    generate_desc_config = '{"handler_class": "GenerateDescriptionHandler"}'
    extract_naics_config = '{"handler_class": "GenerateNaics", "return_json": true}'
    validation_config = '{"handler_class": "GenericValidationHandler"}'
    consolidation_config = '{"handler_class": "GenericConsolidationHandler", "return_json": false}'

    conn.execute(
        f"""
            insert into task_model (id, name, type, execution_config, execution_type, processing_type, llm_version_id, use_task_output_processor) values            
            ('{description_gpt_03_mini_id}', 'Generate Description (GPT o3 mini)', 'LLM_PROMPT', 
            '{generate_desc_config}', 'LAMBDA', 'EXTRACTION', '{gpt_o3_mini_id}', TRUE),
            ('{description_sonar_id}', 'Generate Description (SONAR)', 'LLM_PROMPT', 
            '{generate_desc_config}', 'LAMBDA', 'EXTRACTION', '{sonar_id}', TRUE),
            ('{naics_gpt_03_mini_id}', 'Extract NAICS (GPT o3 mini)', 'LLM_PROMPT', 
            '{extract_naics_config}', 'LAMBDA', 'EXTRACTION', '{gpt_o3_mini_id}', TRUE),
            ('{naics_gpt_o1_id}', 'Extract NAICS (GPT o1)', 'LLM_PROMPT',
            '{extract_naics_config}', 'LAMBDA', 'EXTRACTION', '{gpt_o1_id}', TRUE),
            ('{naics_sonar_id}', 'Extract NAICS (SONAR)', 'LLM_PROMPT',
            '{extract_naics_config}', 'LAMBDA', 'EXTRACTION', '{sonar_id}', TRUE),
            ('{generic_consolidation_id}', 'Generic Consolidation (GPT 4o)', 'LLM_PROMPT',
            '{consolidation_config}', 'LAMBDA', 'CONSOLIDATION', '{gpt_4o_id}', FALSE),
            ('{generic_validation_id}', 'Generic Validation (GPT 4o)', 'LLM_PROMPT',
            '{validation_config}', 'LAMBDA', 'VALIDATION', '{gpt_4o_id}', FALSE)
            """
    )

    description_validation_id = "332f1b24-8cea-4237-8f56-8872cdf24f82"

    # Task Definition Models

    description_task_definition_id = "e5f6a7b8-c9d0-1e2f-3a4b-5c6d7e8f9a0b"
    naics_task_definition_model_id = "feaed7a9-6fdf-4ec1-9b4f-69a779e9fc21"

    conn.execute(
        f"""update task_definition_model set "order" = 1 where task_definition_id = '{description_task_definition_id}' and is_preprocessing_input_run is false;"""
    )
    conn.execute(
        f"""
        insert into task_definition_model (id, task_definition_id, task_model_id, validation_task_model_id, "order", 
        is_always_run, is_aware_of_previous_runs, is_refinement_run, is_preprocessing_input_run, can_self_reflect, is_consolidation_run, is_validation_run) values
        ('{uuid4()}', '{description_task_definition_id}', '{description_sonar_id}', '{description_validation_id}', 1, TRUE, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE),
        ('{uuid4()}', '{description_task_definition_id}', '{description_gpt_03_mini_id}', '{description_validation_id}', 1, TRUE, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE),
        ('{uuid4()}', '{description_task_definition_id}', '{generic_consolidation_id}', NULL, NULL, TRUE, FALSE, FALSE, FALSE, FALSE, TRUE, FALSE),
        ('{uuid4()}', '{naics_task_definition_model_id}', '{naics_sonar_id}', NULL, 0, TRUE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE),        
        ('{uuid4()}', '{naics_task_definition_model_id}', '{naics_gpt_o1_id}', NULL, 0, TRUE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE),
        ('{uuid4()}', '{naics_task_definition_model_id}', '{naics_gpt_03_mini_id}', NULL, 0, TRUE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE),
        ('{uuid4()}', '{naics_task_definition_model_id}', '{generic_consolidation_id}', NULL, NULL, TRUE, FALSE, FALSE, FALSE, FALSE, TRUE, FALSE)
        """
    )


def downgrade():
    pass

"""Add consolidated financial statement file type

Revision ID: 39f2ge7620dx
Revises: 6b6bcaab5d01
Create Date: 2023-06-28 09:05:32.679664+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "39f2ge7620dx"
down_revision = "6b6bcaab5d01"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'CONSOLIDATED_FINANCIAL_STATEMENT';""")


def downgrade():
    pass

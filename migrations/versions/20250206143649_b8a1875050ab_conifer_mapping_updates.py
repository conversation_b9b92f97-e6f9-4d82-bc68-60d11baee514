"""Conifer mapping updates

Revision ID: b8a1875050ab
Revises: e9d225e785d5
Create Date: 2025-02-06 14:36:49.095347+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'b8a1875050ab'
down_revision = 'e9d225e785d5'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("ALTER TABLE conifer_uw_mappings ALTER COLUMN org_group DROP NOT NULL;")
    op.execute("ALTER TABLE conifer_uw_mappings ALTER COLUMN broker_email DROP NOT NULL;")
    op.execute("ALTER TABLE conifer_uw_mappings ADD COLUMN IF NOT EXISTS brokerage_domain VARCHAR;")


def downgrade():
    pass

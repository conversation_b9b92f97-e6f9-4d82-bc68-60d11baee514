"""Add highly accurate flag to support users

Revision ID: f31d5449cd1a
Revises: c1eb2f153cc1
Create Date: 2024-11-01 16:58:52.953992+00:00

"""

from alembic import op
import sqlalchemy as sa

from migrations.utils import safe_migration_pattern

# revision identifiers, used by Alembic.
revision = "f31d5449cd1a"
down_revision = "c1eb2f153cc1"
branch_labels = None
depends_on = None


def upgrade():

    def add_is_highly_accurate():
        op.add_column("support_users", sa.Column("is_highly_accurate", sa.<PERSON>(), nullable=True))

    def add_highly_accurate_preferred():
        op.add_column("settings", sa.Column("highly_accurate_preferred", sa.<PERSON>(), nullable=True))

    safe_migration_pattern("support_users", add_is_highly_accurate)
    with op.get_context().autocommit_block():
        op.create_index('ix_support_users_is_highly_accurate', 'support_users', ['is_highly_accurate'], postgresql_concurrently=True, unique=False)
    op.execute(
        """
        update support_users su
        set is_highly_accurate= True
        from users u
        where su.user_id = u.id
          and u.email in ('<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>',
                          '<EMAIL>');
    """)
    safe_migration_pattern("settings", add_highly_accurate_preferred)
    op.execute(
        """
        update settings
        set highly_accurate_preferred= True
        from organization
        where settings.organization_id = organization.id
          and organization.name = 'ISC MGA'
        """
    )



def downgrade():
    def drop_is_highly_accurate():
        op.drop_column("support_users", "is_highly_accurate")
        op.drop_index(op.f("ix_support_users_is_highly_accurate"), table_name="support_users")

    def drop_highly_accurate_preferred():
        op.drop_column("settings", "highly_accurate_preferred")

    safe_migration_pattern("support_users", drop_is_highly_accurate)
    safe_migration_pattern("settings", drop_highly_accurate_preferred)

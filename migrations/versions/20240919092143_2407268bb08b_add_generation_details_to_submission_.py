"""add generation details to submission level data

Revision ID: 2407268bb08b
Revises: 35525e64b044
Create Date: 2024-09-19 09:21:43.202175+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2407268bb08b"
down_revision = "35525e64b044"
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table("submission_level_extracted_data", schema=None) as batch_op:
        batch_op.add_column(sa.Column("generation_method", sa.String(), nullable=True))
        batch_op.drop_constraint("uq_submission_level_extracted_data", type_="unique")
        batch_op.create_unique_constraint(
            "uq_submission_level_extracted_data",
            ["submission_id", "file_id", "field", "source_details", "generation_method"],
        )


def downgrade():
    with op.batch_alter_table("submission_level_extracted_data", schema=None) as batch_op:
        batch_op.drop_constraint("uq_submission_level_extracted_data", type_="unique")
        batch_op.create_unique_constraint(
            "uq_submission_level_extracted_data",
            ["submission_id", "file_id", "field", "source_details"],
        )
        batch_op.drop_column("generation_method")

"""Adds BUSINESS_DEDUPLICATION file state

Revision ID: c918500fc630
Revises: 45bff83a8def
Create Date: 2023-08-16 15:08:23.098692+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "c918500fc630"
down_revision = "45bff83a8def"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE fileprocessingstate ADD VALUE IF NOT EXISTS 'BUSINESS_DEDUPLICATION';")


def downgrade():
    pass

"""Adds conditional constraints to 'workers_comp_experience'

Revision ID: e0e74d648645
Revises: 9677eeb7d56d
Create Date: 2024-01-03 15:22:19.099679+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "e0e74d648645"
down_revision = "9677eeb7d56d"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    alter table public.workers_comp_experience
        alter column rating_effective_date drop not null;

    alter table public.workers_comp_experience
        alter column state drop not null;
    
    alter table public.workers_comp_experience
        add constraint rating_eff_date_not_null_for_non_acords
            check (type = 'ACORD_130' or workers_comp_experience.rating_effective_date is not null);
    
    alter table public.workers_comp_experience
        add constraint state_not_null_for_non_acords
            check (type = 'ACORD_130' or workers_comp_experience.state is not null);
    """)


def downgrade():
    op.execute("""
    alter table public.workers_comp_experience
        alter column rating_effective_date set not null;

    alter table public.workers_comp_experience
        alter column state set not null;
    
    alter table public.workers_comp_experience
        drop constraint rating_eff_date_not_null_for_non_acords;
    
    alter table public.workers_comp_experience
        drop constraint state_not_null_for_non_acords;
    """)

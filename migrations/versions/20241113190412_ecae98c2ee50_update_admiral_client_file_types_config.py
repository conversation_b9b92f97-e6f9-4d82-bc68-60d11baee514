"""update admiral client_file_types_config

Revision ID: ecae98c2ee50
Revises: 2d9f360bb4f9
Create Date: 2024-11-13 19:04:12.481429+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'ecae98c2ee50'
down_revision = '2d9f360bb4f9'
branch_labels = None
depends_on = None


ADMIRAL_CONFIG = """
{
  "tags": {
    "special_tags": [
      {
        "tag_key": "comments",
        "tag_display": "Comments"
      },
      {
        "tag_key": "file_description",
        "tag_display": "Description",
        "use_as_display_name": true
      }
    ]
  }
}
"""

def upgrade():
    op.execute(f"update settings set client_file_types_config = '{ADMIRAL_CONFIG}' where organization_id = 49")

def downgrade():
    op.execute("update settings set client_file_types_config = null where organization_id = 49")

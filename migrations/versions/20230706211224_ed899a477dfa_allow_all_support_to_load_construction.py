"""Allow all support to load construction

Revision ID: ed899a477dfa
Revises: 7c5cec635e40
Create Date: 2023-07-06 21:12:24.994119+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed899a477dfa"
down_revision = "7c5cec635e40"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    update support_users
    set can_load_construction = true;
    """)


def downgrade():
    op.execute("""
    update support_users
    set can_load_construction = false;
    """)

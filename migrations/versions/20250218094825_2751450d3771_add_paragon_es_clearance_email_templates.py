"""add_paragon_es_clearance_email_templates

Revision ID: 2751450d3771
Revises: fc61c9f7a90d
Create Date: 2025-02-18 09:48:25.731735+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '2751450d3771'
down_revision = 'fc61c9f7a90d'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    
    es_results = conn.execute("""select id from users where email ilike '<EMAIL>';""").fetchall()
    es_user_id = es_results[0][0] if any(es_results) else None
    
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE emailtemplatetype ADD VALUE IF NOT EXISTS 'BLOCKED_ON_CLEARING_BY_DECLINED_SUBMISSION';")
        
        if es_user_id:
            op.execute(f"""
                INSERT INTO email_templates (id, name, html_content, owner_id, is_shared_with_organization, type)
                VALUES (
                    '723bae7f-1412-4f30-9377-aaea6a595b58',
                    'Submission Blocked At Market for Paragon E&S',
                    '<p>Hello [broker name],</p>
                    <p>Thank you for your submission to Paragon Insurance. We have previously received and 
                    accepted a submission for this insured. Our procedures allow us to review 1 broker of 
                    record (BOR) and 1 countermanding letter per insured. If you would like to submit a BOR 
                    for this account, please make sure it includes the following:</p>
                    <p>1. Written request must be on the insured letterhead</p>
                    <p>2. Must include the name Paragon Insurance and the name of your agency.</p>
                    <p>3. Must state all coverage lines of business that will be transferred to your agency</p>
                    <p>4. The BOR must be signed by an officer of the company</p>
                    <p>5. The BOR must be dated within the last 90 days.</p>
                    <p>Once your BOR is accepted, we will grant the current broker 5 business days to refute the BOR or release the submission.</p>
                    <p>If you have any additional questions, please direct your questions to your assigned underwriter.</p>
                    <p>Thank you,</p>
                    <p>Submission Clearance Team</p>',
                    {es_user_id},
                    false,
                    'BLOCKED_ON_CLEARING'
                )
            """)
            
            op.execute(f"""
                INSERT INTO email_templates (id, name, html_content, owner_id, is_shared_with_organization, type)
                VALUES (
                    '018a0642-3b01-46ae-99e3-4ac323c5d2a0',
                    'Submission Blocked At Market for Paragon E&S by pre-existing declined submission',
                    '<p>Hello [broker name],</p>
                    <p>Thank you for your submission to Paragon Insurance. We have previously received and 
                    accepted a submission for this insured. Our procedures allow us to review 1 broker of 
                    record (BOR) and 1 countermanding letter per insured. If you would like to submit a BOR 
                    for this account, please make sure it includes the following:</p>
                    <p>1. Written request must be on the insured letterhead</p>
                    <p>2. Must include the name Paragon Insurance and the name of your agency.</p>
                    <p>3. Must state all coverage lines of business that will be transferred to your agency</p>
                    <p>4. The BOR must be signed by an officer of the company</p>
                    <p>5. The BOR must be dated within the last 90 days.</p>
                    <p>Once your BOR is accepted, we will grant the current broker 5 business days to refute the BOR or release the submission.
                    PLEASE NOTE HOWEVER THIS IS NOT A SUBMISSION WE CAN ENTERTAIN.</p>
                    <p>If you have any additional questions, please direct your questions to your assigned underwriter.</p>
                    <p>Thank you,</p>
                    <p>Submission Clearance Team</p>',
                    {es_user_id},
                    false,
                    'BLOCKED_ON_CLEARING_BY_DECLINED_SUBMISSION'
                )
            """)
        
        
        

def downgrade():
    
    pass

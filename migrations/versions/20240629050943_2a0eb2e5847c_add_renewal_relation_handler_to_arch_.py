"""Add 'RENEWAL_RELATION_HANDLER' to arch config

Revision ID: 2a0eb2e5847c
Revises: 484345579c60
Create Date: 2024-06-29 05:09:43.263296+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "2a0eb2e5847c"
down_revision = "484345579c60"
branch_labels = None
depends_on = None

RENEWAL_RELATION_HANDLER = """
{
  "config": {},
  "handler_type": "RENEWAL_RELATION_HANDLER"
}
"""


def upgrade():
    op.execute(f"""
            update sync_configuration
            set configuration = jsonb_set(configuration, '{{handlers}}', 
            configuration -> 'handlers' || to_jsonb('{RENEWAL_RELATION_HANDLER}'::jsonb))
            where organization_id = 10;
        """)


def downgrade():
    pass

"""create_aig_uw_mappings_table

Revision ID: a93637d6d2c5
Revises: 820d2b340c5c
Create Date: 2025-05-22 20:00:01.000000

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a93637d6d2c5"
down_revision = "820d2b340c5c"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "aig_uw_mappings",
        sa.<PERSON>umn("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("broker_contact_email", sa.String(), nullable=False),
        sa.Column("ww_uw_name", sa.String(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_aig_uw_mappings_broker_contact_email"), "aig_uw_mappings", ["broker_contact_email"], unique=True
    )
    op.create_index(op.f("ix_aig_uw_mappings_user_id"), "aig_uw_mappings", ["user_id"], unique=False)
    op.create_index(op.f("ix_aig_uw_mappings_ww_uw_name"), "aig_uw_mappings", ["ww_uw_name"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_aig_uw_mappings_ww_uw_name"), table_name="aig_uw_mappings")
    op.drop_index(op.f("ix_aig_uw_mappings_user_id"), table_name="aig_uw_mappings")
    op.drop_index(op.f("ix_aig_uw_mappings_broker_contact_email"), table_name="aig_uw_mappings")
    op.drop_table("aig_uw_mappings")

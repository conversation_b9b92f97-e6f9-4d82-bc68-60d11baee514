"""fix coverages in unapplied changes in sync

Revision ID: 62ddc11c0c41
Revises: 1a8feae4845d
Create Date: 2023-07-20 09:47:35.308598+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "62ddc11c0c41"
down_revision = "1a8feae4845d"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '600 s';")
    op.execute("""
        UPDATE submission_sync
        SET pending_changes = (
          SELECT jsonb_agg(
                   CASE
                     WHEN json_item ->> 'coverage_name' IN ('umbrella') THEN jsonb_set(jsonb_set(json_item, '{coverage_name}', '"liability"', true), '{coverage_type}', '"EXCESS"', true)
                     WHEN json_item ->> 'coverage_name' IN ('generalLiability') THEN jsonb_set(jsonb_set(json_item, '{coverage_name}', '"liability"', true), '{coverage_type}', '"PRIMARY"', true)
                     ELSE json_item
                   END
                 )
          FROM jsonb_array_elements(pending_changes) AS json_item
        )
        WHERE EXISTS (
          SELECT 1
          FROM jsonb_array_elements(pending_changes) AS json_item
          WHERE json_item ->> 'coverage_name' IN ('umbrella', 'generalLiability')
        );
    """)
    op.execute("""
            UPDATE submission_sync SET coverage_name = 'liability', coverage_type = 'EXCESS' WHERE coverage_name = 'umbrella';
        """)
    op.execute("""
            UPDATE submission_sync SET coverage_name = 'liability', coverage_type = 'PRIMARY' WHERE coverage_name = 'generalLiability';
        """)


def downgrade():
    pass

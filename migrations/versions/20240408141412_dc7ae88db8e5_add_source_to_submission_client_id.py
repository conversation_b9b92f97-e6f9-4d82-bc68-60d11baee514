"""Add 'source' to submission_client_id

Revision ID: dc7ae88db8e5
Revises: 9bc21901dc95
Create Date: 2024-04-08 14:14:12.987766+00:00

"""

import time

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "dc7ae88db8e5"
down_revision = "9bc21901dc95"
branch_labels = None
depends_on = None

SLEEP_TIME = 0.1
TIMEOUT = 1200


def upgrade():
    with op.get_context().begin_transaction():
        counter = float(TIMEOUT) / SLEEP_TIME
        table_locked = False
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions_client_ids {counter}")
                op.execute("LOCK TABLE ONLY submissions_client_ids in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.add_column("submissions_client_ids", sa.Column("source", sa.String(), nullable=True))
        else:
            raise Exception("Could not lock submissions_client_ids table. Retry the migration")


def downgrade():
    with op.get_context().begin_transaction():
        table_locked = False
        counter = float(TIMEOUT) / SLEEP_TIME
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions_client_ids {counter}")
                op.execute("LOCK TABLE ONLY submissions_client_ids in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.drop_column("submissions_client_ids", "source")
        else:
            raise Exception("Could not lock submissions_client_ids table. Retry the migration")

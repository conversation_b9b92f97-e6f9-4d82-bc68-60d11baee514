"""Backfills loss runs with swapped dates

Revision ID: 68b6b67ca6a5
Revises: af1fa80750e5
Create Date: 2023-08-27 00:18:11.549171+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "68b6b67ca6a5"
down_revision = "af1fa80750e5"
branch_labels = None
depends_on = None


deployment_definitely_finished_at = "06-28-2023 12:00PM GMT+9"
max_week = 9


def upgrade():
    conn = op.get_bind()
    for current_week in range(1, max_week + 1):
        conn.execute("SET statement_timeout TO '360 s';")
        conn.execute(f"""
        UPDATE loss l
        SET claim_date = loss_date, loss_date = claim_date
        FROM files f
        WHERE
            l.file_id = f.id
            AND l.created_at > now() - interval '{current_week} week'
            AND l.created_at < now() - interval '{current_week - 1} week'
            AND l.created_at > '{deployment_definitely_finished_at}'
            AND NOT l.is_manual
            AND lower(f.name) !~ '.xlsx$|.xls$|.csv$'
        """)


def downgrade():
    ...

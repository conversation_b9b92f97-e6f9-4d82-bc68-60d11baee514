"""message
Revision ID: 332cf6e5d11a
Revises: 705c68c0db38
Create Date: 2025-04-10 17:42:41.975789+00:00
"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '332cf6e5d11a'
down_revision = '290cb77c821f'
branch_labels = None
depends_on = None


def upgrade():
   conn = op.get_bind()
   conn.execute(
      f"""
         DELETE FROM subtypes_benchmark_data
             WHERE id IN (
                 SELECT id
                 FROM (
                     SELECT id,
                            ROW_NUMBER() OVER (PARTITION BY fact_subtype_id, "name", "values" ORDER BY created_at DESC) AS rn
                     FROM subtypes_benchmark_data
                 ) AS subquery
                 WHERE rn > 1
             );
     """
   )

   conn.execute(
       f"""
             ALTER TABLE subtypes_benchmark_data
             ADD CONSTRAINT unique_fact_subtype_name_values
             UNIQUE (fact_subtype_id, name, values)
         """
   )


def downgrade():
   pass

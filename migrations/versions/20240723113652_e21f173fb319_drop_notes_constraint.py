"""drop notes constraint

Revision ID: e21f173fb319
Revises: d528071122c6
Create Date: 2024-07-23 11:36:52.781404+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e21f173fb319"
down_revision = "d528071122c6"
branch_labels = None
depends_on = None


# We currently cannot update constraints in one migration (drop and then create updated)
# due to our limitations in "Check migration changes" CI step
# To be fixed in: https://kalepa.atlassian.net/browse/ENG-23180
#
# So, this migration will drop constraint, next one will fix DB and create a new one
def upgrade():
    op.execute("""
    alter table submission_note
        drop constraint cc_submission_note_generated_note_dont_have_an_author;
    """)


def downgrade():
    op.execute("""
    alter table submission_note
        add constraint cc_submission_note_generated_note_dont_have_an_author
            check (is_generated_note IS FALSE OR author_id IS NULL);
    """)

"""Update Nationwide client configuration

Revision ID: d2e6fdc690b6
Revises: c1b07a6cb373
Create Date: 2024-09-30 13:59:47.441367+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d2e6fdc690b6"
down_revision = "c1b07a6cb373"
branch_labels = None
depends_on = None

NATIONWIDE_CONFIG = """
{
  "tags": {
    "special_tags": [
      {
        "tag_key": "comments",
        "tag_display": "Comments"
      },
      {
        "tag_key": "file_description",
        "tag_display": "Description",
        "use_as_display_name": true
      },
      {
        "tag_key": "omniview_doc_type",
        "tag_display": "Omniview Document Type",
        "restricted_values": [
          "Submission",
          "U/W Policy Information"
        ]
      }
    ]
  },
  "client_file_types": [
    "Cancellation",
    "Application",
    "Supplemental Application",
    "Indication",
    "Quote",
    "Broker of Record",
    "Correspondence",
    "Reinsurance",
    "Inspection",
    "Loss Runs",
    "Non Renewal",
    "Renewal",
    "Premium Finance",
    "Schedule",
    "UW Information",
    "Underlying Policy",
    "Certificate of Insurance",
    "Other",
    "Unfiled",
    "Copilot"
  ],
  "file_types_to_client_file_types_map": {
    "MVR": "Other",
    "SOV": "Schedule",
    "EEOC": "Other",
    "IFTA": "Other",
    "EMAIL": "Correspondence",
    "EMPTY": "Unfiled",
    "OTHER": "Other",
    "BUDGET": "Other",
    "MERGED": "Other",
    "RESUME": "Other",
    "ARCHIVE": "Other",
    "DRIVERS": "Schedule",
    "UNKNOWN": "Unfiled",
    "LOSS_RUN": "Loss Runs",
    "VEHICLES": "Schedule",
    "RAW_EMAIL": "Correspondence",
    "ACORD_FORM": "Application",
    "CAB_REPORT": "Other",
    "CAT_RESULT": "Other",
    "COVER_SHEET": "Application",
    "SITE_REPORT": "Other",
    "ALLY_AUTO_SOV": "Other",
    "HTML_DOCUMENT": "Other",
    "SAFETY_MANUAL": "Other",
    "WELL_SCHEDULE": "Schedule",
    "COMPANY_BYLAWS": "Other",
    "GEOTECH_REPORT": "Other",
    "ERISA_FORM_5500": "Other",
    "CAT_REQUEST_FORM": "Other",
    "LOSS_RUN_SUMMARY": "Loss Runs",
    "COMPANY_STRUCTURE": "Other",
    "EMPLOYEE_HANDBOOK": "Other",
    "HIRING_GUIDELINES": "Other",
    "SUPPLEMENTAL_FORM": "Supplemental Application",
    "WORK_COMP_PAYROLL": "Other",
    "FINANCIAL_STATEMENT": "Other",
    "CORRESPONDENCE_EMAIL": "Correspondence",
    "WORK_COMP_EXPERIENCE": "Other",
    "DIRECTORS_AND_OFFICERS": "Other",
    "NAMED_INSURED_SCHEDULE": "Schedule",
    "VEHICLES_UNDERLYING_POLICY": "Other",
    "COPILOT_TERMS_AND_CONDITIONS": "Copilot",
    "CONSOLIDATED_FINANCIAL_STATEMENT": "Other"
  }
}
"""


def upgrade():
    op.execute(f"update settings set client_file_types_config = '{NATIONWIDE_CONFIG}' where organization_id = 6")


def downgrade():
    pass

"""add new client file tags

Revision ID: 3bf80470835a
Revises: e744e40ed429
Create Date: 2024-08-05 11:15:06.996139+00:00

"""

from time import sleep

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3bf80470835a"
down_revision = "e744e40ed429"
branch_labels = None
depends_on = None

SLEEP_TIME = 0.1


def upgrade():
    tries = 0
    while tries < 10:
        try:
            tries += 1
            with op.get_context().begin_transaction():
                op.execute("SET statement_timeout TO '10 s';")
                op.execute("LOCK TABLE ONLY files in ACCESS EXCLUSIVE MODE;")
                op.add_column("files", sa.Column("client_file_tags", postgresql.JSONB, nullable=True))
                break
        except Exception:
            sleep(SLEEP_TIME)


def downgrade():
    tries = 0
    while tries < 10:
        try:
            tries += 1
            with op.get_context().begin_transaction():
                op.execute("SET statement_timeout TO '10 s';")
                op.execute("LOCK TABLE ONLY files in ACCESS EXCLUSIVE MODE;")
                op.drop_column("files", "client_file_tags")
                break
        except Exception:
            sleep(SLEEP_TIME)

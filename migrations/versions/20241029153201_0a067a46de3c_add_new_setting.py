"""add new setting

Revision ID: 0a067a46de3c
Revises: 4b4340a57412
Create Date: 2024-10-29 15:32:01.135250+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '0a067a46de3c'
down_revision = '4b4340a57412'
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(f"ALTER TABLE settings ADD COLUMN if not exists client_id_required_for_clearing boolean;")


def downgrade():
    pass

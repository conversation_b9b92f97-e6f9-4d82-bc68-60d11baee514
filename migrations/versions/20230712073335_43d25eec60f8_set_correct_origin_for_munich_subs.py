"""set correct origin for munich subs

Revision ID: 43d25eec60f8
Revises: 93a78d6751af
Create Date: 2023-07-12 07:33:35.531845+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "43d25eec60f8"
down_revision = "93a78d6751af"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '600 s';")
    op.execute("""
        UPDATE submissions SET origin = 'SYNC', stuck_reason = NULL WHERE id IN (
            SELECT s.id
            FROM submissions s
            INNER JOIN reports_v2 r ON s.report_id = r.id
            WHERE r.organization_id = 36 AND s.origin = 'API'
        )
    """)


def downgrade():
    pass

"""Adds stuck_issue column to stuck_submission_feedback

Revision ID: d3753ef9083d
Revises: 2c17cdb308a4
Create Date: 2023-08-25 01:17:31.917830+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d3753ef9083d"
down_revision = "2c17cdb308a4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    op.add_column("stuck_submission_feedback", sa.Column("stuck_issue", sa.String(), nullable=True))
    conn.execute("UPDATE stuck_submission_feedback SET stuck_issue = ''")
    op.alter_column("stuck_submission_feedback", "stuck_issue", nullable=False)


def downgrade():
    op.drop_column("stuck_submission_feedback", "stuck_issue")

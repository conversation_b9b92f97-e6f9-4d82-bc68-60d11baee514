"""Clearing status not null

Revision ID: ddfecf029906
Revises: 69be83b06de7
Create Date: 2024-11-19 09:32:45.334701+00:00

"""
from alembic import op

from migrations.utils import safe_migration_pattern

# revision identifiers, used by Alembic.
revision = 'ddfecf029906'
down_revision = '69be83b06de7'
branch_labels = None
depends_on = None


def upgrade():
    def set_not_null():
        op.execute("ALTER TABLE submissions ALTER COLUMN clearing_status SET NOT NULL")

    safe_migration_pattern("submissions", set_not_null, ignore_errors=True)


def downgrade():
    pass

"""message
Revision ID: 2560f5f87649
Revises: 290cb77c821f
Create Date: 2025-04-10 17:42:41.975789+00:00
"""
from alembic import op
from static_common.enums.file_type import FileType
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '2560f5f87649'
down_revision = '290cb77c821f'
branch_labels = None
depends_on = None


def upgrade():
   conn = op.get_bind()

   # Add column organization_id
   conn.execute(
       f"""
             ALTER TABLE subtypes_benchmark_data
             ADD COLUMN organization_id INT NULL,
             ADD COLUMN file_type VARCHAR(65) NULL;
         """
   )

   conn.execute(
       f"""
            ALTER TABLE fact_subtype_selection_benchmark
            ADD COLUMN organization_id INT NULL;
        """
   )


def downgrade():
   pass

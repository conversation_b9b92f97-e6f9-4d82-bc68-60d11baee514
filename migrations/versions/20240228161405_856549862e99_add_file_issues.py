"""Add issues column to files

Revision ID: 856549862e99
Revises: 696be2da033b
Create Date: 2024-02-28 16:14:05.302232+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "856549862e99"
down_revision = "696be2da033b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("files", sa.Column("issues", postgresql.ARRAY(sa.String()), nullable=True))


def downgrade():
    op.drop_column("files", "issues")

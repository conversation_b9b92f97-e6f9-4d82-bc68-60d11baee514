"""Adds MVR Filetype

Revision ID: 1ddb4241836a
Revises: 3cca7160950a
Create Date: 2024-01-12 16:52:06.167035+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "1ddb4241836a"
down_revision = "3cca7160950a"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'MVR';""")


def downgrade():
    pass

"""Remove 'sync_only_last_change' from sync_config

Revision ID: ac7e89d8aee8
Revises: fe455e11b667
Create Date: 2024-03-27 03:24:32.567459+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ac7e89d8aee8"
down_revision = "fe455e11b667"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
            update sync_configuration
            set configuration = jsonb_delete_path(configuration, '{sync_only_last_change}')
            where organization_id = 10;
            update sync_configuration
            set configuration = jsonb_delete_path(configuration, '{sync_only_last_change}')
            where organization_id = 49;
            update sync_configuration
            set configuration = jsonb_delete_path(configuration, '{sync_only_last_change}')
            where organization_id = 54;
        """)


def downgrade():
    op.execute("""
            update sync_configuration
            set configuration = jsonb_set(configuration, '{sync_only_last_change}', to_jsonb('true'::boolean))
            where organization_id = 10;
            update sync_configuration
            set configuration = jsonb_set(configuration, '{sync_only_last_change}', to_jsonb('true'::boolean))
            where organization_id = 49;
            update sync_configuration
            set configuration = jsonb_set(configuration, '{sync_only_last_change}', to_jsonb('false'::boolean))
            where organization_id = 54;
        """)

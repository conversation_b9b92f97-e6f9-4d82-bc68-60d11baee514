"""Add 'policyPremium' coverage for Conifer

Revision ID: 960d81801829
Revises: caab739fc250
Create Date: 2025-03-25 01:43:35.910779+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '960d81801829'
down_revision = 'caab739fc250'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
INSERT INTO coverages (id, name, display_name, is_disabled, organization_id, coverage_types)
VALUES
    (gen_random_uuid(), 'policyPremium', 'Policy Premium', FALSE, 58, '{PRIMARY}')
        """)


def downgrade():
    pass

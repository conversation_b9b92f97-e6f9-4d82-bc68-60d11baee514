"""Backfills sync for deleted arch reports

Revision ID: cec1cfcfa73d
Revises: 55b1e2b6e413
Create Date: 2024-02-26 21:59:02.923629+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cec1cfcfa73d"
down_revision = "55b1e2b6e413"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # Resyncs deleted arch submissions
    conn.execute("""
        with data_to_resync as (select ss.policy_number
                                from submissions s
                                        inner join reports_v2 r on s.report_id = r.id
                                        inner join submissions_client_ids sci on s.id = sci.submission_id
                                        inner join submission_sync ss on sci.client_submission_id = ss.policy_number and
                                                                        ss.organization_id = r.organization_id
                                where (s.is_deleted or r.is_deleted)
                                and r.organization_id = 10
                                order by s.created_at desc)
        update submission_sync
        set applied = false
        where policy_number in (select policy_number from data_to_resync)
        and organization_id = 10
        """)
    pass


def downgrade():
    pass

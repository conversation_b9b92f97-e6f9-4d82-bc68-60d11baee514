"""Add task models

Revision ID: 16b5a2196fbc
Revises: 16b5a2196fbc
Create Date: 2025-02-28 14:43:19.883025+00:00

"""
from uuid import uuid4

from alembic import op
from llm_common.models.llm_model import LLMModel
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "67b5a2196fbc"
down_revision = "16b5a2196fbc"
branch_labels = None
depends_on = None


def upgrade():
    # Task Models
    description_claude_37_id = "6395ac47-0541-467f-b27a-5dce7f7def62"
    naics_claude_37_id = "0b19ed20-6d1e-46bb-a206-e6173d2dc80b"

    generate_desc_config = '{"handler_class": "GenerateDescriptionHandler"}'
    extract_naics_config = '{"handler_class": "GenerateNaics", "return_json": true}'

    conn = op.get_bind()
    conn.execute(
        f"""
            insert into task_model (id, name, type, execution_config, execution_type, processing_type, llm_model, use_task_output_processor) values            
            ('{description_claude_37_id}', 'Generate Description (Claude 3.7 Sonnet)', 'LLM_PROMPT', 
            '{generate_desc_config}', 'LAMBDA', 'EXTRACTION', '{LLMModel.CLAUDE_3_7_SONNET}', TRUE),           
            ('{naics_claude_37_id}', 'Extract NAICS (Claude 3.7 Sonnet)', 'LLM_PROMPT', 
            '{extract_naics_config}', 'LAMBDA', 'EXTRACTION', '{LLMModel.CLAUDE_3_7_SONNET}', TRUE)     
            """
    )

    description_validation_id = "332f1b24-8cea-4237-8f56-8872cdf24f82"

    # Task Definition Models

    description_task_definition_id = "e5f6a7b8-c9d0-1e2f-3a4b-5c6d7e8f9a0b"
    naics_task_definition_model_id = "feaed7a9-6fdf-4ec1-9b4f-69a779e9fc21"

    conn.execute(
        f"""
        insert into task_definition_model (id, task_definition_id, task_model_id, validation_task_model_id, "order", 
        is_always_run, is_aware_of_previous_runs, is_refinement_run, is_preprocessing_input_run, can_self_reflect, is_consolidation_run, is_validation_run) values
        ('{uuid4()}', '{description_task_definition_id}', '{description_claude_37_id}', '{description_validation_id}', 1, TRUE, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE),        
        ('{uuid4()}', '{naics_task_definition_model_id}', '{naics_claude_37_id}', NULL, 0, TRUE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE)               
        """
    )


def downgrade():
    pass

"""backfill coverage_types for NW coverages

Revision ID: 3714b9136968
Revises: 97b881a3dee9
Create Date: 2023-06-21 10:02:06.277421+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3714b9136968"

# down_revision = "97b881a3dee9"
down_revision = None

branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
    op.execute("""
        UPDATE submission_coverages
        SET coverage_type = 'EXCESS'
        WHERE id IN (
            SELECT sc.id
            FROM submission_coverages sc
                     INNER JOIN coverages c ON sc.coverage_id = c.id
            WHERE c.name = 'umbrella' AND c.organization_id = 6 AND sc.coverage_type IS NULL
        );
    """)

    op.execute("""
        UPDATE submission_coverages
        SET coverage_type = 'PRIMARY'
        WHERE id IN (
            SELECT sc.id
            FROM submission_coverages sc
                     INNER JOIN coverages c ON sc.coverage_id = c.id
            WHERE c.name = 'generalLiability' AND c.organization_id = 6 AND sc.coverage_type IS NULL
        );
    """)


def downgrade():
    op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
    op.execute("""
            UPDATE submission_coverages
            SET coverage_type = NULL
            WHERE id IN (
                SELECT sc.id
                FROM submission_coverages sc
                         INNER JOIN coverages c ON sc.coverage_id = c.id
                WHERE c.name IN ('umbrella', 'generalLiability') AND c.organization_id = 6
            );
        """)

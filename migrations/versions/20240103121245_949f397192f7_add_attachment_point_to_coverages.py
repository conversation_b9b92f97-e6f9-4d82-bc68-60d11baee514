"""add attachment_point to coverages

Revision ID: 949f397192f7
Revises: f81cd9141cba
Create Date: 2024-01-03 12:12:45.402464+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "949f397192f7"
down_revision = "f81cd9141cba"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_coverages", sa.Column("attachment_point", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("submission_coverages", "attachment_point")

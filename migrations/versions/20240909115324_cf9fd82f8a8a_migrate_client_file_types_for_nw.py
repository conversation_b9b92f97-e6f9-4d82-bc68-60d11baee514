"""migrate client file types for nw

Revision ID: cf9fd82f8a8a
Revises: ef4e6f65c52f
Create Date: 2024-09-09 11:53:24.094560+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cf9fd82f8a8a"
down_revision = "ef4e6f65c52f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().begin_transaction():
        op.execute("SET statement_timeout TO '240 s';")

        chunk_size = 20000
        num_chunks = 30

        for i in range(num_chunks):
            update_query = f"""
              UPDATE files f
                SET client_file_type = s.client_file_types_config->'file_types_to_client_file_types_map'->>(subquery.file_type::text)
                FROM (
                    SELECT files.id, files.file_type
                    FROM files
                    WHERE organization_id = 6 and client_file_type is null
                    ORDER BY id
                    LIMIT {chunk_size}
                    ) AS subquery
                JOIN settings s ON s.organization_id = 6
                WHERE f.id = subquery.id;
            """
            op.execute(update_query)


def downgrade():
    with op.get_context().begin_transaction():
        op.execute("SET statement_timeout TO '120 s';")

        chunk_size = 20000
        num_chunks = 30

        for i in range(num_chunks):
            update_query = f"""
              UPDATE files f
                SET client_file_type = null
                FROM (
                    SELECT files.id, files.file_type
                    FROM files
                    WHERE organization_id = 6 and client_file_type is not null
                    ORDER BY id
                    LIMIT {chunk_size}
                    ) AS subquery
                JOIN settings s ON s.organization_id = 6
                WHERE f.id = subquery.id;
            """
            op.execute(update_query)

"""Add Gemini and DeepSeek to Generate Description

Revision ID: f7e6d5c4b3a2
Revises: aa519877f4ba
Create Date: 2025-03-27 12:54:42.000000+00:00

"""
from uuid import uuid4

from alembic import op

# revision identifiers, used by Alembic.
revision = "f7e6d5c4b3a2"
down_revision = "aa519877f4ba"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Get task definition ID for GENERATE_DESCRIPTION
    description_task_definition_id = "e5f6a7b8-c9d0-1e2f-3a4b-5c6d7e8f9a0b"

    # Create IDs for the new models
    gemini_model_id = "b5a4c3d2-e1f0-4321-9876-fedcba987654"
    deepseek_model_id = "a1b2c3d4-e5f6-7890-abcd-ef1234567890"

    # Get existing validation model ID
    validation_model = conn.execute(
        f"""
        SELECT validation_task_model_id 
        FROM task_definition_model 
        WHERE task_definition_id = '{description_task_definition_id}' 
        AND validation_task_model_id IS NOT NULL
        LIMIT 1
        """
    ).fetchone()

    validation_task_model_id = f"'{validation_model[0]}'" if validation_model else "NULL"

    # Configuration for the task models
    generate_desc_config = """{"system_prompt": "You are writing short and formal descriptions of operations for government documentation given non-formatted text snippets about an insured/company. The description needs to be maximum 2 sentences long, it has to contain only information about services offered or nature of business that is available in the snippets. The description needs to be written in impersonal form. IMPORTANT: do not use opinionating statements, such as ''successful'', ''superior'' or ''excellent''; provide bare facts. The description mustn''t contain any information about insurance or insurance-related terms like policy, coverage etc. Return the results as a JSON in following format {''descriptions'':[{''raw_description'':'''', ''description'': ''''}]} where raw_description is the input description.", "return_json": true}"""

    # Create Gemini model
    conn.execute(
        f"""
        INSERT INTO task_model (id, name, type, execution_config, execution_type, processing_type, llm_model, use_task_output_processor)
        VALUES (
            '{gemini_model_id}', 
            'Generate Description (Gemini 2.0 Flash)', 
            'LLM_PROMPT',
            '{generate_desc_config}', 
            'LAMBDA', 
            'EXTRACTION', 
            'GEMINI_2_0_FLASH', 
            TRUE
        )
        """
    )

    generate_desc_config = '{"handler_class": "GenerateDescriptionHandler"}'
    # Create DeepSeek model
    conn.execute(
        f"""
        INSERT INTO task_model (id, name, type, execution_config, execution_type, processing_type, llm_model, use_task_output_processor)
        VALUES (
            '{deepseek_model_id}', 
            'Generate Description (DeepSeek V3)', 
            'LLM_PROMPT',
            '{generate_desc_config}', 
            'LAMBDA', 
            'EXTRACTION', 
            'TOGETHER_AI_DEEPSEEK_V3', 
            TRUE
        )
        """
    )

    # Create task definition model for Gemini (available to all organizations)
    conn.execute(
        f"""
        INSERT INTO task_definition_model (
            id, 
            task_definition_id, 
            task_model_id, 
            validation_task_model_id, 
            "order",
            is_always_run, 
            is_aware_of_previous_runs, 
            is_refinement_run, 
            is_preprocessing_input_run, 
            can_self_reflect, 
            is_consolidation_run, 
            is_validation_run,
            is_benchmark_run,
            is_disabled
        )
        VALUES (
            '{uuid4()}', 
            '{description_task_definition_id}', 
            '{gemini_model_id}', 
            {validation_task_model_id}, 
            0, 
            FALSE, 
            FALSE, 
            FALSE, 
            FALSE, 
            TRUE, 
            FALSE, 
            FALSE,
            TRUE,
            FALSE
        )
        """
    )

    # Create task definition model for DeepSeek (only for organization_id 37)
    conn.execute(
        f"""
        INSERT INTO task_definition_model (
            id, 
            task_definition_id, 
            task_model_id, 
            validation_task_model_id, 
            "order",
            is_always_run, 
            is_aware_of_previous_runs, 
            is_refinement_run, 
            is_preprocessing_input_run, 
            can_self_reflect, 
            is_consolidation_run, 
            is_validation_run,
            is_benchmark_run,
            is_disabled,
            only_organization_ids
        )
        VALUES (
            '{uuid4()}', 
            '{description_task_definition_id}', 
            '{deepseek_model_id}', 
            {validation_task_model_id}, 
            0, 
            FALSE, 
            FALSE, 
            FALSE, 
            FALSE, 
            TRUE, 
            FALSE, 
            FALSE,
            TRUE,
            FALSE,
            ARRAY[37,54,59,60,41]
        )
        """
    )


def downgrade():
    conn = op.get_bind()

    # Model IDs
    gemini_model_id = "b5a4c3d2-e1f0-4321-9876-fedcba987654"
    deepseek_model_id = "a1b2c3d4-e5f6-7890-abcd-ef1234567890"

    # Delete task definition models
    conn.execute(
        f"""
        DELETE FROM task_definition_model 
        WHERE task_model_id IN ('{gemini_model_id}', '{deepseek_model_id}')
        """
    )

    # Delete task models
    conn.execute(
        f"""
        DELETE FROM task_model 
        WHERE id IN ('{gemini_model_id}', '{deepseek_model_id}')
        """
    )

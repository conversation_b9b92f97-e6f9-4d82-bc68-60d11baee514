"""add client facing file types mapping for nationwide

Revision ID: 352963254eca
Revises: e0fca437dd5f
Create Date: 2024-08-02 04:50:57.836816+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "352963254eca"
down_revision = "e0fca437dd5f"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        UPDATE settings
            SET use_client_file_types = true,
            client_file_types_config = '{
                  "tags": {
                      "allowed": ["Submission", "U/W Policy Information"]
                  },
                  "file_types_to_client_file_types_map": {
                      "CAT_RESULT": "Other",
                      "LOSS_RUN": "Loss Runs",
                      "LOSS_RUN_SUMMARY": "Loss Runs",
                      "OTHER": "Other",
                      "CAT_REQUEST_FORM": "Other",
                      "SOV": "Schedule",
                      "NAMED_INSURED_SCHEDULE": "Schedule",
                      "SAFETY_MANUAL": "Other",
                      "SITE_REPORT": "Other",
                      "DRIVERS": "Schedule",
                      "VEHICLES": "Schedule",
                      "GEOTECH_REPORT": "Other",
                      "SUPPLEMENTAL_FORM": "Supplemental Application",
                      "EMAIL": "Correspondence",
                      "RAW_EMAIL": "Correspondence",
                      "CORRESPONDENCE_EMAIL": "Correspondence",
                      "VEHICLES_UNDERLYING_POLICY": "Other",
                      "ACORD_FORM": "Application",
                      "COMPANY_STRUCTURE": "Other",
                      "EMPLOYEE_HANDBOOK": "Other",
                      "HIRING_GUIDELINES": "Other",
                      "COMPANY_BYLAWS": "Other",
                      "RESUME": "Other",
                      "BUDGET": "Other",
                      "FINANCIAL_STATEMENT": "Other",
                      "CONSOLIDATED_FINANCIAL_STATEMENT": "Other",
                      "WORK_COMP_EXPERIENCE": "Other",
                      "WORK_COMP_PAYROLL": "Other",
                      "WELL_SCHEDULE": "Schedule",
                      "MVR": "Other",
                      "IFTA": "Other",
                      "MERGED": "Other",
                      "ARCHIVE": "Other",
                      "COPILOT_TERMS_AND_CONDITIONS": "Copilot",
                      "ALLY_AUTO_SOV": "Other",
                      "UNKNOWN": "Unfiled",
                      "EMPTY": "Unfiled",
                      "CAB_REPORT": "Other",
                      "HTML_DOCUMENT": "Other",
                      "COVER_SHEET": "Application",
                      "DIRECTORS_AND_OFFICERS": "Other",
                      "ERISA_FORM_5500": "Other",
                      "EEOC": "Other"
                  }
                }'
            WHERE organization_id = 6;
    """)


def downgrade():
    op.execute("""
        UPDATE settings
            SET use_client_file_types = NULL,
            client_file_types_config = NULL
            WHERE organization_id = 6;
    """)

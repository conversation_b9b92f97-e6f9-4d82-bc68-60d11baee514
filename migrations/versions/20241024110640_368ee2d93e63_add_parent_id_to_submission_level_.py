"""Add parent_id to submission_level_extracted_data

Revision ID: 368ee2d93e63
Revises: b42ee69b6543
Create Date: 2024-10-24 11:06:40.303505+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "368ee2d93e63"
down_revision = "b42ee69b6543"
branch_labels = None
depends_on = None


def upgrade():
    with op.batch_alter_table("submission_level_extracted_data", schema=None) as batch_op:
        batch_op.add_column(sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=True))
        batch_op.create_index(batch_op.f("ix_submission_level_extracted_data_parent_id"), ["parent_id"], unique=False)
        batch_op.create_foreign_key(
            "parent_id_fk", "submission_level_extracted_data", ["parent_id"], ["id"], ondelete="CASCADE"
        )


def downgrade():
    with op.batch_alter_table("submission_level_extracted_data", schema=None) as batch_op:
        batch_op.drop_constraint("parent_id_fk", type_="foreignkey")
        batch_op.drop_index(batch_op.f("ix_submission_level_extracted_data_parent_id"))
        batch_op.drop_column("parent_id")

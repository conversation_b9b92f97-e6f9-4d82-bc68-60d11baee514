"""Fix synchronize sources between submissions

Revision ID: 21e53666e351
Revises: 3594a61777cc
Create Date: 2025-04-15 13:54:48.206495+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '21e53666e351'
down_revision = 'c5e591933085'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index(
        index_name="submission_field_name_unique_idx",
        table_name="submission_field_source",
        if_exists=True,
    )


def downgrade():
    pass

"""Added new filemetricname for IFTA - IFTA_TOTALS_COUNT and IFTA_QUARTERS_TOTAL_MATCHED_COUNT

Revision ID: 47e842710c5f
Revises: 01d41ade2d67
Create Date: 2024-02-13 15:17:11.440363+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "47e842710c5f"
down_revision = "01d41ade2d67"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'IFTA_TOTALS_COUNT';")
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'IFTA_QUARTERS_TOTAL_MATCHED_COUNT';")


def downgrade():
    pass

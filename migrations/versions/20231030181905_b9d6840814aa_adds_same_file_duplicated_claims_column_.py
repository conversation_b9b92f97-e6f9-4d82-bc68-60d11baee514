"""Adds same_file_duplicated_claims column to loss_run_sensible_extraction_document

Revision ID: b9d6840814aa
Revises: 1e2742dab9cb
Create Date: 2023-10-30 18:19:05.011546+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b9d6840814aa"
down_revision = "1e2742dab9cb"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "loss_run_sensible_extraction_document", sa.Column("same_file_duplicated_claims", sa.Integer(), nullable=True)
    )


def downgrade():
    op.drop_column("loss_run_sensible_extraction_document", "same_file_duplicated_claims")

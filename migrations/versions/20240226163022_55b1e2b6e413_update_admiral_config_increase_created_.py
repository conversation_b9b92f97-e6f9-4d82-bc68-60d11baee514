"""Update admiral config. Increase 'created_at_days_range' and 'original_max_days_old' to 120

Revision ID: 55b1e2b6e413
Revises: d6e37e2dfbcb
Create Date: 2024-02-26 16:30:22.602919+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "55b1e2b6e413"
down_revision = "d6e37e2dfbcb"
branch_labels = None
depends_on = None

ADMIRAL_CONFIG = """
{
  "handlers": [
    {
      "config": {},
      "handler_type": "POLICY_HANDLER"
    },
    {
      "config": {
        "allow_backwards_status_update": true,
        "not_allowed_stage_transitions": [
          {
            "to_stages": [
              "ON_MY_PLATE"
            ],
            "from_stages": [
              "INDICATED",
              "WAITING_FOR_OTHERS",
              "EXPIRED"
            ]
          }
        ]
      },
      "handler_type": "SUBMISSION_HANDLER"
    },
    {
      "config": {
        "removal_strategy": "ALL"
      },
      "handler_type": "COVERAGE_HANDLER"
    },
    {
      "config": {
        "default_primary_naics": {},
        "remove_previous_assignees": true,
        "only_assign_if_no_assignees": false
      },
      "handler_type": "UNDERWRITER_HANDLER"
    }
  ],
  "max_attempts": 0,
  "sync_only_last_change": true,
  "submission_producer_config": {
    "filters": [
      {
        "config": {
          "cutoff_threshold": 0.7
        },
        "filter_type": "BROKERAGE_FILTER"
      },
      {
        "config": {
          "cutoff_threshold": 0.8
        },
        "filter_type": "BROKER_FILTER"
      }
    ],
    "lookup_config": {
      "scorers": [
        {
          "config": {
            "matcher_types": [
              "FUZZY_MATCHER",
              "NAME_SIMILARITY_MATCHER"
            ]
          },
          "scorer_type": "SUBMISSION_NAME_SCORER"
        },
        {
          "config": {
            "score_boost": 0.15,
            "matcher_types": [
              "EMAIL_MATCHER"
            ]
          },
          "scorer_type": "EMAIL_SCORER"
        },
        {
          "config": {
            "matcher_types": [
              "RELATED_POLICY_MATCHER"
            ]
          },
          "scorer_type": "RELATED_POLICY_SCORER"
        }
      ],
      "scoring_policy": "HIGHEST_SCORE",
      "direct_matchers": [
        {
          "matcher_type": "POLICY_MATCHER"
        }
      ],
      "indirect_matchers": [
        {
          "config": {
            "created_at_days_range": 120,
            "additional_sub_sync_fields": [
              "effective_date",
              "broker",
              "brokerage"
            ]
          },
          "matcher_type": "RELATED_POLICY_MATCHER"
        },
        {
          "config": {
            "fuzzy_matching_level": 4,
            "created_at_days_range": 7
          },
          "matcher_type": "FUZZY_MATCHER"
        },
        {
          "config": {
            "created_at_days_range": 7
          },
          "matcher_type": "EMAIL_MATCHER"
        },
        {
          "config": {
            "created_at_days_range": 7
          },
          "matcher_type": "NAME_SIMILARITY_MATCHER"
        }
      ],
      "matching_score_threshold": 0.7
    },
    "shell_owner_email": "<EMAIL>",
    "duplicate_submissions": true,
    "original_max_days_old": 120,
    "original_min_days_old": 2,
    "enable_multiple_matches": true,
    "create_shell_submissions": true,
    "always_ready_after_num_days": 14,
    "shell_submissions_days_passed": 8
  }
}
"""


def upgrade():
    op.execute(f"update sync_configuration set configuration = '{ADMIRAL_CONFIG}' where organization_id = 49")


def downgrade():
    pass

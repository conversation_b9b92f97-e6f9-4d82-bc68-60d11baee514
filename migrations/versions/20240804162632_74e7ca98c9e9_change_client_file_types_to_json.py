"""change client file types to json

Revision ID: 74e7ca98c9e9
Revises: 7cc272676282
Create Date: 2024-08-04 16:26:32.791287+00:00

"""

from time import sleep

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "74e7ca98c9e9"
down_revision = "7cc272676282"
branch_labels = None
depends_on = None

SLEEP_TIME = 0.1


def upgrade():
    pass


def downgrade():
    pass

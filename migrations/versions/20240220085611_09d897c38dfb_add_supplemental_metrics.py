"""add metric

Revision ID: 09d897c38dfb
Revises: 7a6d268b8ed8
Create Date: 2024-02-20 08:56:11.294420+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "09d897c38dfb"
down_revision = "7a6d268b8ed8"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'TEMPLATE_NUMBER_OF_EXTRACTED_DATA_POINTS';")
        op.execute(
            "ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'TEMPLATE_PERCENTAGE_OF_REFERENCE_FIELDS_EXTRACTED';"
        )
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'FR_NUMBER_OF_EXTRACTED_DATA_POINTS';")
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'FR_PERCENTAGE_OF_REFERENCE_FIELDS_EXTRACTED';")
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'SUPP_NUMBER_OF_EXTRACTED_DATA_POINTS';")
        op.execute("ALTER TYPE filemetricname ADD VALUE IF NOT EXISTS 'SUPP_PERCENTAGE_OF_EXTRACTED_DATA_POINTS';")


def downgrade():
    pass

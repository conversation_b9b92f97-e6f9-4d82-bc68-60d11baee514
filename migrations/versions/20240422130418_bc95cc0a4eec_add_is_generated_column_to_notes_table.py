"""add is_generated column to notes table

Revision ID: bc95cc0a4eec
Revises: 57a2c16eab97
Create Date: 2024-04-22 13:04:18.167960+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bc95cc0a4eec"
down_revision = "57a2c16eab97"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "submission_note", sa.Column("is_generated_note", sa.<PERSON>(), nullable=False, server_default=sa.text("false"))
    )
    op.execute("""
    alter table submission_note
        add constraint cc_submission_note_generated_note_dont_have_an_author
            check (is_generated_note IS FALSE OR author_id IS NULL);
    """)


def downgrade():
    op.execute("""
    alter table submission_note
        drop constraint cc_submission_note_generated_note_dont_have_an_author;
    """)
    op.drop_column("submission_note", "is_generated_note")

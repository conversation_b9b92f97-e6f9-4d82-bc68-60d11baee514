"""Add is_pre_renewal flag to submission

Revision ID: 23dca34cb7ec
Revises: 68aca34cb7ec
Create Date: 2025-01-23 14:27:49.751171+00:00

"""
from alembic import op
import sqlalchemy as sa

from migrations.utils import safe_migration_pattern

# revision identifiers, used by Alembic.
revision = "23dca34cb7ec"
down_revision = "68aca34cb7ec"
branch_labels = None
depends_on = None


def upgrade():
    def add_is_pre_renewal_to_submissions():
        op.add_column("submissions", sa.<PERSON>umn("is_pre_renewal", sa.<PERSON>(), nullable=True))
        op.add_column("submissions", sa.Column("is_renewal_shell", sa.<PERSON>(), nullable=True))

    safe_migration_pattern("submissions", add_is_pre_renewal_to_submissions)


def downgrade():
    def remove_is_pre_renewal_to_submissions():
        op.drop_column("submissions", "is_pre_renewal")
        op.drop_column("submissions", "is_renewal_shell")

    safe_migration_pattern("submissions", remove_is_pre_renewal_to_submissions)

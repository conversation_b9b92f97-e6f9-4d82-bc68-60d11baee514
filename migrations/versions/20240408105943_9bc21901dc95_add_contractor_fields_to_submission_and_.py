"""Add contractor fields to submission and submission_sync

Revision ID: 9bc21901dc95
Revises: 075e005b027f
Create Date: 2024-04-08 10:59:43.614261+00:00

"""

import time

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9bc21901dc95"
down_revision = "075e005b027f"
branch_labels = None
depends_on = None
SLEEP_TIME = 0.1
TIMEOUT = 1200


def upgrade():
    with op.get_context().begin_transaction():
        counter = float(TIMEOUT) / SLEEP_TIME
        table_locked = False
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions {counter}")
                op.execute("LOCK TABLE ONLY submissions in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.add_column("submissions", sa.Column("contractor_submission_type", sa.String(), nullable=True))
            op.add_column("submissions", sa.Column("project_insurance_type", sa.String(), nullable=True))
            # just add the same columns to submission_sync. The table is used only during sync and it can be locked
            # without any issues
            op.add_column("submission_sync", sa.Column("contractor_submission_type", sa.String(), nullable=True))
            op.add_column("submission_sync", sa.Column("project_insurance_type", sa.String(), nullable=True))
        else:
            raise Exception("Could not lock submissions table. Retry the migration")


def downgrade():
    with op.get_context().begin_transaction():
        table_locked = False
        counter = float(TIMEOUT) / SLEEP_TIME
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions {counter}")
                op.execute("LOCK TABLE ONLY submissions in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.drop_column("submissions", "contractor_submission_type")
            op.drop_column("submissions", "project_insurance_type")
            op.drop_column("submission_sync", "contractor_submission_type")
            op.drop_column("submission_sync", "project_insurance_type")
        else:
            raise Exception("Could not lock submissions table. Retry the migration")

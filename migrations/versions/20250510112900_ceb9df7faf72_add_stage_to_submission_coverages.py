"""Add 'stage' to submission_coverages

Revision ID: ceb9df7faf72
Revises: 8770b053f5a6
Create Date: 2025-05-10 11:29:00.932267+00:00

"""
from alembic import op
import sqlalchemy as sa

from migrations.utils import safe_migration_pattern

# revision identifiers, used by Alembic.
revision = 'ceb9df7faf72'
down_revision = '8770b053f5a6'
branch_labels = None
depends_on = None


def upgrade():
    def add_stage_to_submission_coverages():
        op.add_column("submission_coverages", sa.Column("stage", sa.String(), nullable=True))

    safe_migration_pattern("submission_coverages", add_stage_to_submission_coverages)


def downgrade():
    def drop_stage_to_submission_coverages():
        op.drop_column("submission_coverages", "stage")

    safe_migration_pattern("submission_coverages", drop_stage_to_submission_coverages)

"""Add template_type to HubTemplate

Revision ID: fc61c9f7a90d
Revises: 101c9dcc19f4
Create Date: 2025-02-17 15:14:56.913786+00:00

"""
from alembic import op
import sqlalchemy as sa

from migrations.utils import safe_migration_pattern

# revision identifiers, used by Alembic.
revision = 'fc61c9f7a90d'
down_revision = '101c9dcc19f4'
branch_labels = None
depends_on = None


def upgrade():
    def add_column():
        op.add_column(
            "hub_templates",
            sa.Column("template_type", sa.Enum("HUB", "DASHBOARD", native_enum=False), nullable=False, default="HUB", server_default="HUB"),
        )

    safe_migration_pattern("hub_templates", add_column)
    with op.get_context().autocommit_block():
        op.drop_index('ix_hub_templates_user_id', table_name='hub_templates', if_exists=True)
        op.create_index('ix_hub_template_user_id_template_type', 'hub_templates', ['user_id', 'template_type'], if_not_exists=True, postgresql_concurrently=True, unique=False)


def downgrade():
    with op.get_context().autocommit_block():
        op.drop_index('ix_hub_template_user_id_template_type', table_name='hub_templates', if_exists=True)
        op.create_index('ix_hub_templates_user_id', 'hub_templates', ['user_id'], if_not_exists=True, postgresql_concurrently=True, unique=False)

    def drop_column():
        op.drop_column("hub_templates", "template_type")

    safe_migration_pattern("hub_templates", drop_column)

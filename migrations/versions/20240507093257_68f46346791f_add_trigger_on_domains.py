"""Add trigger on domains

Revision ID: 68f46346791f
Revises: faaa24f09d31
Create Date: 2024-05-07 09:32:57.817132+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "68f46346791f"
down_revision = "faaa24f09d31"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        CREATE OR REPLACE FUNCTION check_brokerage_employee_domain_matches() RETURNS trigger AS $check_brokerage_employee_domain_matches$
        DECLARE
            brokerage_domains varchar[];
        BEGIN
            brokerage_domains := (select domains from brokerages_v2 where id = NEW.brokerage_id);
            IF brokerage_domains != '{}' AND brokerage_domains is not null AND NEW.email is not null AND split_part(NEW.email, '@', 2) not ilike all(brokerage_domains) THEN
                RAISE EXCEPTION 'Brokerage employee email domain does not match brokerage domain';
            END IF;
            RETURN NEW;
        END;
    $check_brokerage_employee_domain_matches$ LANGUAGE plpgsql;
    CREATE TRIGGER check_brokerage_employee_domain_matches BEFORE INSERT OR UPDATE OF email, brokerage_id ON brokerage_employees FOR EACH ROW EXECUTE PROCEDURE check_brokerage_employee_domain_matches();
    """)


def downgrade():
    pass

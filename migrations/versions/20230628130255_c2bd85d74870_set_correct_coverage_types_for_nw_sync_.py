"""set correct coverage types for NW sync records


Revision ID: c2bd85d74870
Revises: 27c1hd3941st
Create Date: 2023-06-29 08:37:55.585069+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c2bd85d74870"
down_revision = "27c1hd3941st"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '600 s';")  # 10 min
    op.execute("""
            UPDATE submission_sync 
            SET coverage_type = 'EXCESS' 
            WHERE coverage_name = 'umbrella' AND organization_id = 6 AND coverage_type IS NULL
        """)

    op.execute("""
            UPDATE submission_sync 
            SET coverage_type = 'PRIMARY' 
            WHERE coverage_name = 'generalLiability' AND organization_id = 6 AND coverage_type IS NULL
        """)


def downgrade():
    op.execute("SET statement_timeout TO '600 s';")  # 10 min
    op.execute("""
            UPDATE submission_sync 
            SET coverage_type = NULL
            WHERE coverage_name IN ('umbrella', 'generalLiability') AND organization_id = 6 AND coverage_type IS NULL
        """)

"""Add columns to task model

Revision ID: db4cf9848f1f
Revises: 3a2cf9848f1f
Create Date: 2025-01-14 14:27:13.352766+00:00

"""
from time import sleep

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "db4cf9848f1f"
down_revision = "3a2cf9848f1f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.add_column(
            "task_model", sa.Column("use_task_output_processor", sa.<PERSON>, nullable=False, server_default="false")
        )
        op.add_column(
            "task_execution", sa.Column("validated_task_execution_id", postgresql.UUID(as_uuid=True), nullable=True)
        )
        op.add_column("task_execution", sa.Column("processed_output", sa.JSON, nullable=True))
        op.create_foreign_key(
            "task_output_task_execution_fk", "task_execution", "task_execution", ["validated_task_execution_id"], ["id"]
        )
        op.create_unique_constraint(
            "uq_task_definition_task_model", "task_definition_model", ["task_definition_id", "task_model_id"]
        )
        op.execute("UPDATE task_model SET use_task_output_processor = true WHERE processing_type != 'VALIDATION'")


def downgrade():
    pass

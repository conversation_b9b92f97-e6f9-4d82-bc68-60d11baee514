"""Add policy status to submission and sync

Revision ID: 6de43ecd0249
Revises: e015dd28e35d
Create Date: 2025-02-12 22:49:30.515400+00:00

"""

from alembic import op
import sqlalchemy as sa

from migrations.utils import safe_migration_pattern

# revision identifiers, used by Alembic.
revision = "6de43ecd0249"
down_revision = "e015dd28e35d"
branch_labels = None
depends_on = None


def upgrade():
    def add_policy_status_to_submission_sync():
        op.add_column("submission_sync", sa.Column("policy_status", sa.String(), nullable=True))
        op.add_column("submission_sync", sa.Column("policy_expiration_date", sa.DateTime(), nullable=True))

    def add_policy_status_to_submissions():
        op.add_column("submissions", sa.Column("policy_status", sa.String(), nullable=True))

    safe_migration_pattern("submission_sync", add_policy_status_to_submission_sync)
    safe_migration_pattern("submissions", add_policy_status_to_submissions)


def downgrade():
    def remove_policy_status_to_submission_sync():
        op.drop_column("submission_sync", "policy_status")
        op.drop_column("submission_sync", "policy_expiration_date")

    def remove_policy_status_to_submissions():
        op.drop_column("submissions", "policy_status")

    safe_migration_pattern("submission_sync", remove_policy_status_to_submission_sync)
    safe_migration_pattern("submissions", remove_policy_status_to_submissions)

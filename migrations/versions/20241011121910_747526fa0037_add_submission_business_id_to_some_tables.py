"""Add submission_business_id to some tables

Revision ID: 747526fa0037
Revises: 9a08534f8d98
Create Date: 2024-10-11 12:19:10.001383+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "747526fa0037"
down_revision = "9a08534f8d98"
branch_labels = None
depends_on = None


def upgrade():
    # shareholders
    op.add_column("shareholders", sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(
        op.f("ix_shareholders_submission_business_id"), "shareholders", ["submission_business_id"], unique=False
    )
    op.create_foreign_key(
        "fk_shareholders_submission_business",
        "shareholders",
        "submission_businesses",
        ["submission_business_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # workers_comp_state_rating_info
    op.add_column(
        "workers_comp_state_rating_info",
        sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.create_index(
        op.f("ix_wcsri_submission_business_id"),
        "workers_comp_state_rating_info",
        ["submission_business_id"],
        unique=False,
    )
    op.create_foreign_key(
        "fk_wcsri_submission_business",
        "workers_comp_state_rating_info",
        "submission_businesses",
        ["submission_business_id"],
        ["id"],
        ondelete="SET NULL",
    )


def downgrade():
    # shareholders
    op.drop_constraint("fk_shareholders_submission_business", "shareholders", type_="foreignkey")
    op.drop_index(op.f("ix_shareholders_submission_business_id"), table_name="shareholders")
    op.drop_column("shareholders", "submission_business_id")
    # workers_comp_state_rating_info
    op.drop_constraint("fk_wcsri_submission_business", "workers_comp_state_rating_info", type_="foreignkey")
    op.drop_index(op.f("ix_wcsri_submission_business_id"), table_name="workers_comp_state_rating_info")
    op.drop_column("workers_comp_state_rating_info", "submission_business_id")

"""Adds the classify_files_for_shells setting to the settings table.

Revision ID: 35ba7427ea44
Revises: edba5c26e124
Create Date: 2024-04-26 08:50:04.063554+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "35ba7427ea44"
down_revision = "edba5c26e124"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("settings", sa.Column("classify_files_for_shells", sa.<PERSON>(), nullable=True))
    op.execute("""
        UPDATE settings
        SET classify_files_for_shells = false
        WHERE organization_id is not null;
        """)


def downgrade():
    op.drop_column("settings", "classify_files_for_shells")

"""Drop foreign_keys and indexes for 'submission_relations'

Revision ID: 9db650c39a25
Revises: d7f1d748e361
Create Date: 2024-04-20 09:28:47.636488+00:00

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9db650c39a25"
down_revision = "d7f1d748e361"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("ix_submission_relations_from_submission_id", "submission_relations")
    op.drop_index("ix_submission_relations_to_submission_id", "submission_relations")
    op.drop_constraint("submission_relations_from_submission_id_fkey", "submission_relations", type_="foreignkey")
    op.drop_constraint("submission_relations_to_submission_id_fkey", "submission_relations", type_="foreignkey")


def downgrade():
    pass

"""Adds submission_id to workers comp

Revision ID: b4f2cc898071
Revises: 59f6e7258e90
Create Date: 2023-09-05 03:26:24.756293+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b4f2cc898071"
down_revision = "59f6e7258e90"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("workers_comp_experience", sa.Column("submission_id", postgresql.UUID(), nullable=True))
    op.create_index(
        op.f("ix_workers_comp_experience_submission_id"), "workers_comp_experience", ["submission_id"], unique=False
    )
    op.create_foreign_key(None, "workers_comp_experience", "submissions", ["submission_id"], ["id"])

    op.add_column("workers_comp_state_rating_info", sa.Column("submission_id", postgresql.UUID(), nullable=True))
    op.create_index(
        op.f("ix_workers_comp_state_rating_info_submission_id"),
        "workers_comp_state_rating_info",
        ["submission_id"],
        unique=False,
    )
    op.create_foreign_key(None, "workers_comp_state_rating_info", "submissions", ["submission_id"], ["id"])

    op.execute(
        "update workers_comp_experience set submission_id = (select submission_id from files where id ="
        " workers_comp_experience.file_id)"
    )
    op.execute(
        "update workers_comp_state_rating_info set submission_id = (select submission_id from files where id ="
        " workers_comp_state_rating_info.file_id)"
    )
    op.alter_column("workers_comp_experience", "submission_id", nullable=False)
    op.alter_column("workers_comp_state_rating_info", "submission_id", nullable=False)


def downgrade():
    op.drop_index("ix_workers_comp_experience_submission_id", "workers_comp_experience")
    op.drop_column("workers_comp_experience", "submission_id")
    op.drop_index("ix_workers_comp_state_rating_info_submission_id", "workers_comp_state_rating_info")
    op.drop_column("workers_comp_state_rating_info", "submission_id")

"""Adds brokerage and broker to 'submission_sync'

Revision ID: 332066cc8795
Revises: 3d4568168e54
Create Date: 2023-10-19 12:23:21.710469+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "332066cc8795"
down_revision = "3d4568168e54"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_sync", sa.Column("brokerage", sa.String(), nullable=True))
    op.add_column("submission_sync", sa.Column("broker", sa.String(), nullable=True))
    op.execute("""
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('4'::integer)) where organization_id=6;
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('4'::integer)) where organization_id=10;
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('4'::integer)) where organization_id=36;
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,use_broker_filter_on_mm}', to_jsonb('true'::boolean)) where organization_id=10;
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,use_brokerage_filter_on_mm}', to_jsonb('true'::boolean)) where organization_id=10;
        """)


def downgrade():
    op.drop_column("submission_sync", "brokerage")
    op.drop_column("submission_sync", "broker")
    op.execute("""
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('3'::integer)) where organization_id=6;
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('3'::integer)) where organization_id=10;
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('3'::integer)) where organization_id=36;
            update sync_configuration set configuration = jsonb_delete_path(configuration, '{submission_matcher_config,use_broker_filter_on_mm}') where organization_id=10;
            update sync_configuration set configuration = jsonb_delete_path(configuration, '{submission_matcher_config,use_brokerage_filter_on_mm}') where organization_id=10;
            update sync_configuration set configuration = jsonb_delete_path(configuration, '{submission_matcher_config,filter_cutoff_threshold}') where organization_id=10;
        """)

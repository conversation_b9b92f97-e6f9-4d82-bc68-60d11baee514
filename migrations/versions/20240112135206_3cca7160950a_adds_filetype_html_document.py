"""Adds filetype 'HTML_DOCUMENT'

Revision ID: 3cca7160950a
Revises: 1a296c523cf2
Create Date: 2024-01-12 13:52:06.167035+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "3cca7160950a"
down_revision = "1a296c523cf2"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'HTML_DOCUMENT';""")


def downgrade():
    pass

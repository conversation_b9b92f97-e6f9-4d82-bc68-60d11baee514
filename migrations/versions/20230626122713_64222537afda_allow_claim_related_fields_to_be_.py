"""allow claim related fields to be nullable for loss

Revision ID: 64222537afda
Revises: fba2fb4687cd
Create Date: 2023-06-26 12:27:13.917913+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "64222537afda"
down_revision = "bc48a21e7994"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("loss", "claim_number", existing_type=sa.VARCHAR(length=128), nullable=True)
    op.alter_column("loss", "loss_date", existing_type=sa.DATE(), nullable=True)
    op.alter_column("loss", "claim_date", existing_type=sa.DATE(), nullable=True)


def downgrade():
    op.alter_column("loss", "claim_date", existing_type=sa.DATE(), nullable=False)
    op.alter_column("loss", "loss_date", existing_type=sa.DATE(), nullable=False)
    op.alter_column("loss", "claim_number", existing_type=sa.VARCHAR(length=128), nullable=False)

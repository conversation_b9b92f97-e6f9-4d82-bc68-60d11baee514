"""Add TL Coverages

Revision ID: 312405b274e3
Revises: 2f6836ea763b
Create Date: 2024-11-20 13:35:42.437584+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '312405b274e3'
down_revision = '2f6836ea763b'
branch_labels = None
depends_on = None

def upgrade():
    conn = op.get_bind()

    conn.execute("""
INSERT INTO coverages (id, name, display_name, is_disabled, organization_id, coverage_types)
VALUES
    (gen_random_uuid(), 'repsWarranties', 'Representations and Warranties', FALSE, 9, '{PRIMARY}'),
    (gen_random_uuid(), 'tax', 'Tax', FALSE, 9, '{PRIMARY}');
        """)


def downgrade():
    pass

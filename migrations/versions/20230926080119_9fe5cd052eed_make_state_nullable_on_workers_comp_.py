"""Make 'state' nullable on 'workers_comp_sate_rating_info'

Revision ID: 9fe5cd052eed
Revises: 884845bc1062
Create Date: 2023-09-26 08:01:19.981739+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9fe5cd052eed"
down_revision = "884845bc1062"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("alter table workers_comp_state_rating_info alter column state drop not null;")


def downgrade():
    op.execute("alter table workers_comp_state_rating_info alter column state set not null;")

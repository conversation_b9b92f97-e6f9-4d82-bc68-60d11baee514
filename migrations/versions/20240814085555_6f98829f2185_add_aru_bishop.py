"""Add ARU/Bishop

Revision ID: 6f98829f2185
Revises: 1c5d384d0b3f
Create Date: 2024-08-14 08:55:55.444885+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6f98829f2185"
down_revision = "1c5d384d0b3f"
branch_labels = None
depends_on = None


def upgrade():
    # BISHOP
    op.execute("""
        insert into organization values
        (58, 'Bishop-Conifier', null, null, INTERVAL '90 day', 'coniferinsurance.com', null, null, true)
    """)
    op.execute("""
        INSERT INTO settings (
            id, created_at, updated_at, organization_id, is_map_enabled_by_default, support_email, email_domains, default_tier, max_tier_for_auto_processing, show_coverage_filter, allow_multiple_assigned_underwriters_per_submission
        )
        values (
            uuid_generate_v4(), now(), null, 58, false, '<EMAIL>', '{coniferinsurance.com, bishopstreetuw.com, redbirdcap.com, kalepa.co, kalepa.com}', 0, 0, true, false
        )
    """)
    op.execute("""
        insert into users values
        (default, '<EMAIL>', null, 'auth0|66bc7088f72538f51da5e4c4', 58, 'manager', '<EMAIL>', null, null, now(), null, false, null, false, null, true, null, null, null, null, true)
    """)
    conn = op.get_bind()
    conn.execute("insert into sensible_quota values (uuid_generate_v4(), 58, 10000)")
    conn.execute("UPDATE settings SET loss_runs_enabled = true WHERE organization_id = 58;")
    conn.execute("""
                    INSERT INTO coverages (id, name, display_name, organization_id, coverage_types, is_disabled)
                    VALUES
                        (uuid_generate_v4(), 'liability', 'Liability', 58, '{PRIMARY, EXCESS}', false),
                        (uuid_generate_v4(), 'property', 'Commercial Property', 58, '{PRIMARY, EXCESS}', false),
                        (uuid_generate_v4(), 'liquorLiability', 'Liquor Liability', 58, '{PRIMARY}', false),
                        (uuid_generate_v4(), 'businessAuto', 'Business Auto', 58, '{PRIMARY}', false),
                        (uuid_generate_v4(), 'workersComp', 'Workers Compensation', 58, '{PRIMARY}', false)
                    """)

    # ARU
    op.execute("""
                insert into organization values
                (59, 'ARU', null, null, INTERVAL '90 day', 'agrisku.com', null, null, true)
            """)
    op.execute("""
                INSERT INTO settings (
                    id, created_at, updated_at, organization_id, is_map_enabled_by_default, support_email, email_domains, default_tier, max_tier_for_auto_processing, show_coverage_filter, allow_multiple_assigned_underwriters_per_submission
                )
                values (
                    uuid_generate_v4(), now(), null, 59, false, '<EMAIL>', '{agrisku.com, kalepa.co, kalepa.com}', 0, 0, true, false
                )
            """)
    op.execute("""
                        insert into users values
                        (default, '<EMAIL>', null, 'auth0|66bc70e0b1f8b03de54476f2', 59, 'manager', '<EMAIL>', null, null, now(), null, false, null, false, null, true, null, null, null, null, true)
                    """)
    conn = op.get_bind()
    conn.execute("insert into sensible_quota values (uuid_generate_v4(), 59, 10000)")
    conn.execute("UPDATE settings SET loss_runs_enabled = true WHERE organization_id = 59;")
    conn.execute("""
                    INSERT INTO coverages (id, name, display_name, organization_id, coverage_types, is_disabled)
                    VALUES
                        (uuid_generate_v4(), 'property', 'Commercial Property', 59, '{PRIMARY}', false)
                    """)


def downgrade():
    pass

"""Add computed total_premium column to submission_coverages

Revision ID: 6c00e6f28748
Revises: 665ca7fa3beb
Create Date: 2024-05-19 21:05:31.215053+00:00

"""
from time import sleep

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6c00e6f28748"
down_revision = "665ca7fa3beb"
branch_labels = None
depends_on = None

SLEEP_TIME = 0.1
TIMEOUT = 1200


def upgrade():
    with op.get_context().begin_transaction():
        counter = float(TIMEOUT) / SLEEP_TIME
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submission_coverages {counter}")
                op.execute("LOCK TABLE ONLY submission_coverages in ACCESS EXCLUSIVE MODE NOWAIT;")
                break
            except Exception:
                sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        op.execute("SET statement_timeout TO '600 s';")
        op.add_column(
            "submission_coverages",
            sa.Column(
                "total_premium_or_bound_premium",
                sa.Float(),
                sa.Computed("COALESCE(total_premium, bound_premium)", persisted=True),
            ),
        )


def downgrade():
    op.drop_column("submission_coverages", "total_premium_or_bound_premium")

"""Change report_triage index on report_id to unique

Revision ID: f450fa92219f
Revises: 293b4c48ba06
Create Date: 2024-06-24 09:45:50.256508+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f450fa92219f"
down_revision = "293b4c48ba06"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
        op.execute("DROP INDEX CONCURRENTLY IF EXISTS ix_report_triage_report_id;")
        op.execute("CREATE UNIQUE INDEX CONCURRENTLY ix_report_triage_report_id ON report_triage (report_id);")


def downgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
        op.execute("DROP INDEX CONCURRENTLY IF EXISTS ix_report_triage_report_id;")
        op.execute("CREATE INDEX CONCURRENTLY ix_report_triage_report_id ON report_triage (report_id);")

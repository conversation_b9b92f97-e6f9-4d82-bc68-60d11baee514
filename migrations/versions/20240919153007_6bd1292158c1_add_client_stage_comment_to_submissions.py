"""Add 'client_stage_comment' to 'submissions'

Revision ID: 6bd1292158c1
Revises: 4af586b5897a
Create Date: 2024-09-19 15:30:07.295480+00:00

"""

import time

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6bd1292158c1"
down_revision = "4af586b5897a"
branch_labels = None
depends_on = None

SLEEP_TIME = 0.1
TIMEOUT = 1200


def upgrade():
    with op.get_context().begin_transaction():
        counter = float(TIMEOUT) / SLEEP_TIME
        table_locked = False
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions {counter}")
                op.execute("LOCK TABLE ONLY submissions in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.add_column("submissions", sa.Column("client_stage_comment", sa.String(), nullable=True))
        else:
            raise Exception("Could not lock submissions table. Retry the migration")


def downgrade():
    with op.get_context().begin_transaction():
        table_locked = False
        counter = float(TIMEOUT) / SLEEP_TIME
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submissions {counter}")
                op.execute("LOCK TABLE ONLY submissions in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.drop_column("submissions", "client_stage_comment")
        else:
            raise Exception("Could not lock submissions table. Retry the migration")

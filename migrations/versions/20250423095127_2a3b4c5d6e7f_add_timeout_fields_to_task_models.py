"""
Add timeout fields to task_definition and task_model tables

Revision ID: 2a3b4c5d6e7f
Revises: 1a2b3c4d5e6f
Create Date: 2025-04-23 09:51:27
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2a3b4c5d6e7f"
down_revision = "1a2b3c4d5e6f"
branch_labels = None
depends_on = None


def upgrade():
    # Add timeout column to task_definition table
    op.add_column("task_definition", sa.Column("timeout", sa.Integer(), nullable=True))

    # Add timeout column to task_model table
    op.add_column("task_model", sa.Column("timeout", sa.Integer(), nullable=True))

    # Create a partial index for PENDING tasks
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '600 s';")  # 10 min
        op.execute(
            "CREATE INDEX CONCURRENTLY ix_task_pending_task_definition ON task (task_definition_id, created_at) WHERE status = 'PENDING'"
        )


def downgrade():
    with op.get_context().autocommit_block():
        op.execute("DROP INDEX CONCURRENTLY IF EXISTS ix_task_pending_task_definition")

    # Remove timeout column from task_definition table
    op.drop_column("task_definition", "timeout")

    # Remove timeout column from task_model table
    op.drop_column("task_model", "timeout")

"""Add execution type flags to task execution

Revision ID: 5fdf67a9391a
Revises: 1b6eb52d5e64
Create Date: 2025-03-07 08:10:38.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5fdf67a9391a"
down_revision = "1b6eb52d5e64"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("task_execution", sa.<PERSON>umn("is_validation_run", sa.<PERSON>(), nullable=True))
    op.add_column("task_execution", sa.Column("is_self_reflection_run", sa.<PERSON>(), nullable=True))
    op.add_column("task_execution", sa.<PERSON>umn("is_consolidation_run", sa.<PERSON>(), nullable=True))
    op.add_column("task_execution", sa.<PERSON>umn("is_benchmark_run", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("task_execution", "is_benchmark_run")
    op.drop_column("task_execution", "is_consolidation_run")
    op.drop_column("task_execution", "is_self_reflection_run")
    op.drop_column("task_execution", "is_validation_run")

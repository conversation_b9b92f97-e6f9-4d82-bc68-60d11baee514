"""Add BUSINESS_NAME_MATCHER for Admiral

Revision ID: acfc6fed58de
Revises: 72808eaae62d
Create Date: 2024-06-18 10:19:50.256508+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "acfc6fed58de"
down_revision = "72808eaae62d"
branch_labels = None
depends_on = None


new_config = """
{
  "handlers": [
    {
      "config": {},
      "handler_type": "POLICY_HANDLER"
    },
    {
      "config": {
        "allow_backwards_status_update": true,
        "not_allowed_stage_transitions": [
          {
            "to_stages": [
              "ON_MY_PLATE"
            ],
            "from_stages": [
              "INDICATED",
              "WAITING_FOR_OTHERS",
              "EXPIRED",
              "DECLINED"
            ]
          }
        ]
      },
      "handler_type": "SUBMISSION_HANDLER"
    },
    {
      "config": {
        "removal_strategy": "ALL"
      },
      "handler_type": "COVERAGE_HANDLER"
    },
    {
      "config": {
        "default_primary_naics": {},
        "remove_previous_assignees": true,
        "only_assign_if_no_assignees": false
      },
      "handler_type": "UNDERWRITER_HANDLER"
    }
  ],
  "max_attempts": 0,
  "submission_producer_config": {
    "filters": [
      {
        "config": {
          "cutoff_threshold": 0.7
        },
        "filter_type": "BROKERAGE_FILTER"
      },
      {
        "config": {
          "cutoff_threshold": 0.8
        },
        "filter_type": "BROKER_FILTER"
      }
    ],
    "lookup_config": {
      "scorers": [
        {
          "config": {
            "matcher_types": [
              "FUZZY_MATCHER",
              "NAME_SIMILARITY_MATCHER"
            ],
            "calculate_score_penalty": true
          },
          "scorer_type": "SUBMISSION_NAME_SCORER"
        },
        {
          "config": {
            "score_boost": 0.15,
            "matcher_types": [
              "EMAIL_MATCHER"
            ]
          },
          "scorer_type": "EMAIL_SCORER"
        },
        {
          "config": {
            "matcher_types": [
              "RELATED_POLICY_MATCHER"
            ]
          },
          "scorer_type": "RELATED_POLICY_SCORER"
        }
      ],
      "scoring_policy": "HIGHEST_SCORE",
      "direct_matchers": [
        {
          "matcher_type": "POLICY_MATCHER"
        }
      ],
      "indirect_matchers": [
        {
          "config": {
            "created_at_days_range": 120,
            "additional_sub_sync_fields": [
              "effective_date",
              "broker",
              "brokerage"
            ]
          },
          "matcher_type": "RELATED_POLICY_MATCHER"
        },
        {
          "config": {
            "fuzzy_matching_level": 4,
            "created_at_days_range": 7
          },
          "matcher_type": "FUZZY_MATCHER"
        },
        {
          "config": {
            "created_at_days_range": 7
          },
          "matcher_type": "EMAIL_MATCHER"
        },
        {
          "config": {
            "created_at_days_range": 7
          },
          "matcher_type": "NAME_SIMILARITY_MATCHER"
        },
        {
          "config": {
            "query_similarity_threshold": 0.35
          },
          "matcher_type": "BUSINESS_NAME_MATCHER"
        }
      ],
      "matching_score_threshold": 0.7
    },
    "shell_owner_email": "<EMAIL>",
    "duplicate_submissions": true,
    "original_max_days_old": 120,
    "original_min_days_old": 2,
    "enable_multiple_matches": true,
    "create_shell_submissions": true,
    "always_ready_after_num_days": 14,
    "shell_submissions_days_passed": 8
  }
}
"""


def upgrade():
    op.execute(f"""
    update sync_configuration set configuration = '{new_config}' where organization_id=49;
    """)


def downgrade():
    pass

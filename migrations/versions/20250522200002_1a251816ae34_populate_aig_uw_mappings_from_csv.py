"""populate_aig_uw_mappings_from_csv

Revision ID: 1a251816ae34
Revises: a93637d6d2c5
Create Date: 2025-05-22 20:00:02.000000

"""

import csv
import os

from alembic import op
from sqlalchemy.sql import column, table
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1a251816ae34"
down_revision = "a93637d6d2c5"
branch_labels = None
depends_on = None

aig_uw_mappings_table = table(
    "aig_uw_mappings", column("broker_contact_email", sa.String), column("ww_uw_name", sa.String)
)

CSV_FILE_PATH = f"migrations/data/{revision}.csv"


def upgrade():
    data_dir = os.path.dirname(CSV_FILE_PATH)
    if not os.path.isdir(data_dir):
        raise Exception(f"Data directory not found at {data_dir}")

    if not os.path.exists(CSV_FILE_PATH):
        raise Exception(f"CSV file not found at {CSV_FILE_PATH}")

    mappings_to_insert = []
    seen_emails = set()
    try:
        with open(CSV_FILE_PATH, mode="r", encoding="utf-8-sig") as csvfile:
            reader = csv.DictReader(csvfile, delimiter=";")
            for row_number, row in enumerate(reader):
                if not row.get("BrokerContactEmail") or not row.get("WW_UW_Name"):
                    print(f"Skipping row {row_number + 2} (CSV line number) due to missing required fields: {row}")
                    continue

                broker_email = row["BrokerContactEmail"].strip()
                uw_name = row["WW_UW_Name"].strip()

                if broker_email and uw_name:
                    if broker_email in seen_emails:
                        print(f"Skipping duplicate BrokerContactEmail on row {row_number + 2}: {broker_email}")
                        continue
                    seen_emails.add(broker_email)
                    mappings_to_insert.append({"broker_contact_email": broker_email, "ww_uw_name": uw_name})
                else:
                    print(
                        f"Skipping row {row_number + 2} (CSV line number) due to empty BrokerContactEmail or WW_UW_Name after stripping: {row}"
                    )

    except Exception as e:
        print(f"Error reading or processing CSV file at {CSV_FILE_PATH}: {e}")
        raise

    if mappings_to_insert:
        try:
            op.bulk_insert(aig_uw_mappings_table, mappings_to_insert)
            print(f"Successfully inserted {len(mappings_to_insert)} rows into aig_uw_mappings from {CSV_FILE_PATH}.")
        except Exception as e:
            print(f"Error during bulk insert: {e}")
            if "violates unique constraint" in str(e) and "aig_uw_mappings_broker_contact_email_key" in str(e):
                print(f"Warning: Some rows may not have been inserted due to duplicate broker_contact_email: {e}")
            else:
                raise
    else:
        print(f"No valid mappings found in CSV file {CSV_FILE_PATH} to insert.")


def downgrade():
    op.execute("DELETE FROM aig_uw_mappings")

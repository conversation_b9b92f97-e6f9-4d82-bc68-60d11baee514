"""add replaced_by_file_ids to files

Revision ID: a2c31740bd90
Revises: 45031740bd90
Create Date: 2024-01-30 11:20:57.170554+00:00

"""
from uuid import UUID

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a2c31740bd90"
down_revision = "45031740bd90"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "files", sa.Column("replaced_by_file_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True)
    )


def downgrade():
    op.drop_column("files", "replaced_by_file_ids")

"""Removes unique index from loss table

Revision ID: ed9dc42eb1b6
Revises: d2c25cc7588b
Create Date: 2023-09-27 16:34:00.180728+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed9dc42eb1b6"
down_revision = "d2c25cc7588b"
branch_labels = None
depends_on = None


def upgrade():
    connection = op.get_bind()
    result = connection.execute(
        "SELECT exists(SELECT 1 FROM pg_indexes WHERE indexname = 'ix_loss_claim_number_policy_id_organization_id')"
    )
    exists = result.scalar()
    if exists:
        op.drop_index(op.f("ix_loss_claim_number_policy_id_organization_id"), table_name="loss")


def downgrade():
    op.create_index(
        op.f("ix_loss_claim_number_policy_id_organization_id"),
        "loss",
        ["claim_number", "policy_id", "organization_id"],
        unique=True,
    )

"""Add loss_run_sensible_extraction_document table

Revision ID: c7797b6269f3
Revises: 33ccab464fd8
Create Date: 2023-07-31 11:59:19.366217+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c7797b6269f3"
down_revision = "33ccab464fd8"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "loss_run_sensible_extraction_document",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("extracted_claims", sa.Integer(), nullable=False, server_default=sa.text("0")),
        sa.Column("invalid_claims", sa.Integer(), nullable=False, server_default=sa.text("0")),
        sa.Column("not_loadable_claims", sa.Integer(), nullable=False, server_default=sa.text("0")),
        sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(["id"], ["sensible_extraction_document.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_loss_run_sensible_extraction_document_file_id"),
        "loss_run_sensible_extraction_document",
        ["file_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(
        "ix_loss_run_sensible_extraction_document_file_id", table_name="loss_run_sensible_extraction_document"
    )
    op.drop_table("loss_run_sensible_extraction_document")

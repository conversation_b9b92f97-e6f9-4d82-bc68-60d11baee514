"""add submission_brokerage_employees and submission_brokerage tables

Revision ID: fb72f75f9e5a
Revises: 9bc21901dc95
Create Date: 2024-04-05 15:06:23.294480+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fb72f75f9e5a"
down_revision = "9bc21901dc95"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "submission_brokerage",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("brokerage_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "source", sa.Enum("MANUAL", "API", "EMAIL", "COPY", "AUTO", name="brokerageemployeesource"), nullable=True
        ),
        sa.ForeignKeyConstraint(["brokerage_id"], ["brokerages_v2.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("submission_id"),
    )

    op.create_table(
        "submission_brokerage_employees",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("brokerage_employee_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("role", postgresql.ENUM(name="brokerageemployeeroles", create_type=False), nullable=False),
        sa.Column(
            "source", sa.Enum("MANUAL", "API", "EMAIL", "COPY", "AUTO", name="brokerageemployeesource"), nullable=True
        ),
        sa.ForeignKeyConstraint(["brokerage_employee_id"], ["brokerage_employees.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("submission_id", "role"),
    )

    conn = op.get_bind()
    conn.execute("""
    CREATE OR REPLACE FUNCTION brokerage_employee_update_brokerage_for_submissions() RETURNS TRIGGER AS $$
    BEGIN
        UPDATE submission_brokerage
        SET brokerage_id = NEW.brokerage_id
        WHERE submission_id IN (
            SELECT id
            FROM submissions
            WHERE broker_id = NEW.id OR brokerage_contact_id = NEW.id
        );
    
        UPDATE submissions
        SET brokerage_id = NEW.brokerage_id
        WHERE broker_id = NEW.id OR brokerage_contact_id = NEW.id;
    
        RETURN NEW;
    END;
    $$ LANGUAGE 'plpgsql';
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
        CREATE OR REPLACE FUNCTION brokerage_employee_update_brokerage_for_submissions() RETURNS TRIGGER AS $$
        BEGIN
            UPDATE submissions
            SET brokerage_id = NEW.brokerage_id
            WHERE broker_id = NEW.id OR brokerage_contact_id = NEW.id;

            RETURN NEW;
        END;
        $$ LANGUAGE 'plpgsql';
        """)

    op.drop_table("submission_brokerage_employees")
    op.drop_table("submission_brokerage")
    # ### end Alembic commands ###

"""add_user_groups_to_submission_sync

Revision ID: 7e698bf25273
Revises: c91c5130960d
Create Date: 2024-12-11 13:02:12.459469+00:00

"""
import time

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '7e698bf25273'
down_revision = '17ccc114a740'
branch_labels = None
depends_on = None

SLEEP_TIME = 0.1
TIMEOUT = 1200

def upgrade():
    with op.get_context().begin_transaction():
        counter = float(TIMEOUT) / SLEEP_TIME
        table_locked = False
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submission_sync {counter}")
                op.execute("LOCK TABLE ONLY submission_sync in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.add_column("submission_sync", sa.Column("user_groups_to_match", sa.ARRAY(sa.String()), nullable=True))
        else:
            raise Exception("Could not lock submission_sync table. Retry the migration")


def downgrade():
    with op.get_context().begin_transaction():
        table_locked = False
        counter = float(TIMEOUT) / SLEEP_TIME
        while counter > 0:
            try:
                if counter % 10 == 1:
                    print(f"Trying to lock table submission_sync {counter}")
                op.execute("LOCK TABLE ONLY submission_sync in ACCESS EXCLUSIVE MODE NOWAIT;")
                table_locked = True
                break
            except Exception:
                time.sleep(SLEEP_TIME)
            counter -= SLEEP_TIME

        if table_locked:
            op.drop_column("submission_sync", "user_groups_to_match")
        else:
            raise Exception("Could not lock submission_sync table. Retry the migration")

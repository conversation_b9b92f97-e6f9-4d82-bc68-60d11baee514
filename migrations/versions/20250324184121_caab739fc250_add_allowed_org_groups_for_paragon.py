"""Add allowed org groups for Paragon

Revision ID: caab739fc250
Revises: 85743acdcb34
Create Date: 2025-03-24 18:41:21.731842+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'caab739fc250'
down_revision = '85743acdcb34'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""update settings set 
    org_groups = ARRAY['PARAGON_PSP_E3', 'PARAGON_ALLY_AUTO', 'PARAGON_WC', 'PARAGON_XS'] 
    where organization_id = 37""")


def downgrade():
    pass

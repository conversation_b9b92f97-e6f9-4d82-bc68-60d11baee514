"""Add more fields to 'submission_sync'

Revision ID: d528071122c6
Revises: 5ae8ce48da78
Create Date: 2024-07-21 02:55:46.286104+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d528071122c6"
down_revision = "5ae8ce48da78"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_sync", sa.Column("broker_email", sa.String(), nullable=True))
    op.add_column("submission_sync", sa.Column("premium", sa.Float(), nullable=True))
    op.add_column("submission_sync", sa.Column("expired_premium", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("submission_sync", "broker_email")
    op.drop_column("submission_sync", "premium")
    op.drop_column("submission_sync", "expired_premium")

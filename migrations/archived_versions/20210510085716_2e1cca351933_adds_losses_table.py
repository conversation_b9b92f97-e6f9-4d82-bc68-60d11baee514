"""Adds losses table

Revision ID: 2e1cca351933
Revises: e6bf148384e7
Create Date: 2021-05-06 06:57:16.758686-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2e1cca351933"
down_revision = "e6bf148384e7"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "loss",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("coverage_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("claim_number", sa.String(length=128), nullable=True),
        sa.Column("insured_name", sa.String(), nullable=True),
        sa.Column("claimant_name", sa.String(), nullable=False),
        sa.Column("loss_date", sa.Date(), nullable=False),
        sa.Column("report_date", sa.Date(), nullable=True),
        sa.Column("claim_date", sa.Date(), nullable=False),
        sa.Column("policy_effective_date", sa.Date(), nullable=True),
        sa.Column("exposure_close_date", sa.Date(), nullable=True),
        sa.Column("loss_address", sa.String(), nullable=True),
        sa.Column("claim_description", sa.String(), nullable=True),
        sa.Column("line_of_business", postgresql.ENUM(name="lineofbusinesstype", create_type=False), nullable=False),
        sa.Column("sum_of_loss_reserve", sa.Float(), nullable=True),
        sa.Column("sum_of_alae_reserve", sa.Float(), nullable=True),
        sa.Column("sum_of_net_paid_loss", sa.Float(), nullable=True),
        sa.Column("sum_of_net_outstanding_loss", sa.Float(), nullable=True),
        sa.Column("sum_of_net_paid_alae", sa.Float(), nullable=True),
        sa.Column("sum_of_net_outstanding_alae", sa.Float(), nullable=True),
        sa.Column("sum_of_total_net_incurred", sa.Float(), nullable=False),
        sa.ForeignKeyConstraint(["coverage_id"], ["coverages.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_business_id"], ["submission_businesses.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_loss_claim_date"), "loss", ["claim_date"], unique=False)
    op.create_index(op.f("ix_loss_loss_date"), "loss", ["loss_date"], unique=False)
    op.create_index(op.f("ix_loss_submission_business_id"), "loss", ["submission_business_id"], unique=False)
    op.create_index(op.f("ix_loss_submission_id"), "loss", ["submission_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_loss_submission_id"), table_name="loss")
    op.drop_index(op.f("ix_loss_submission_business_id"), table_name="loss")
    op.drop_index(op.f("ix_loss_loss_date"), table_name="loss")
    op.drop_index(op.f("ix_loss_claim_date"), table_name="loss")
    op.drop_table("loss")

"""Update project location cards

Revision ID: 96eda8326580
Revises: 7ac2c05fca71
Create Date: 2023-02-02 16:47:00.035849+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "96eda8326580"
down_revision = "7ac2c05fca71"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update mode_cards
    set type='MAP_CARD', props='{"parentType": "BUSINESS", "businessSelect": "contractorProject", "markerSelect": "positionFromPremises", "source": "SUBMISSION", "height": 423}'
    where id='5eda0384-3162-427e-b4f9-af4c540daadb';
    
    update mode_cards
    set type='SUBMISSION_BUSINESSES_DATA_CARD', props='{"businessSelect": "contractorProject", "path": "entity_data.entity.premises.premises.formatted_address"}'
    where id='449ef4fd-6000-492b-b4a0-d582c0901ffb';
    """)


def downgrade():
    pass

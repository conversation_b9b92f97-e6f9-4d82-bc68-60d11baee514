"""Adds metric_group_name and display_name to MetricPreference

Revision ID: ac0cec4d3cce
Revises: 984c0f1fd815
Create Date: 2022-05-18 19:21:27.128523+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ac0cec4d3cce"
down_revision = "984c0f1fd815"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric_preference", sa.Column("display_name", sa.String(), nullable=True))
    op.add_column("metric_preference", sa.Column("metric_group_name", sa.String(), nullable=True))


def downgrade():
    op.drop_column("metric_preference", "metric_group_name")
    op.drop_column("metric_preference", "display_name")

"""Adds Report Summary Preferences tables

Revision ID: eae6af9161ca
Revises: 4dee7c7bfbf5
Create Date: 2020-06-25 18:05:04.183360

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "eae6af9161ca"
down_revision = "4dee7c7bfbf5"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "summary_preference",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("display_name", sa.String(), nullable=False),
        sa.Column("group_display_name", sa.String(), nullable=False),
        sa.Column("is_default", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_summary_preference_display_name"), "summary_preference", ["display_name"], unique=False)
    op.create_index(op.f("ix_summary_preference_is_default"), "summary_preference", ["is_default"], unique=False)
    op.create_table(
        "report_summary_preference",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("report_id", sa.Integer(), nullable=False),
        sa.Column("summary_preference_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(["report_id"], ["reports.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["summary_preference_id"],
            ["summary_preference.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_report_summary_preference_report_id"), "report_summary_preference", ["report_id"], unique=False
    )
    op.create_index(
        op.f("ix_report_summary_preference_summary_preference_id"),
        "report_summary_preference",
        ["summary_preference_id"],
        unique=False,
    )

    conn = op.get_bind()
    conn.execute("""
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('4b3e5756-9584-4caf-8c96-6a1ebe4c7bd4', 'Building Size', 'Property Statistics', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('4b3e5756-9584-4caf-8c96-6a1ebe4c7bd3', 'Building Age', 'Property Statistics', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('456b2b9a-7709-4eba-8ac8-30eb31fb93fa', 'Lot Size', 'Property Statistics', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('6535e812-2e40-44ec-a331-6fbdfa530155', 'Crime', 'Crime Risks', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('abf3b1a3-4a57-4f06-9209-01483b31edc3', 'Distance to Police Station', 'Crime Risks', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('a6b08e78-b733-4781-b4dd-2ee9c9f8fb67', 'Elevation', 'Weather Risks', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('debcbc66-1ae7-46b0-9edb-9c7d0c9f3e4d', 'Earthquake', 'Weather Risks', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('62aa3f16-9a95-4161-977c-ae7e12c066cf', 'Distance to Coast', 'Weather Risks', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('cc78afce-f60d-455f-b32b-0ed057bcbb4d', 'Distance to Hospital', 'Location Risks', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('1ce4b0b5-e75c-48b8-b6fa-f7b04222043a', 'Distance to Fire Station', 'Fire Risk', true);
    INSERT INTO public.summary_preference (id, display_name, group_display_name, is_default) VALUES ('2d4f9bab-88a9-40d8-bb8d-b1af0d9ebb3b', 'Distance to Fire Hydrant', 'Fire Risk', true);
    ;""")


def downgrade():
    op.drop_index(op.f("ix_report_summary_preference_summary_preference_id"), table_name="report_summary_preference")
    op.drop_index(op.f("ix_report_summary_preference_report_id"), table_name="report_summary_preference")
    op.drop_table("report_summary_preference")
    op.drop_index(op.f("ix_summary_preference_is_default"), table_name="summary_preference")
    op.drop_index(op.f("ix_summary_preference_display_name"), table_name="summary_preference")
    op.drop_table("summary_preference")

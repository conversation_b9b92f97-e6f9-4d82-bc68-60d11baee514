"""Replace "null" with null for additional_data

Revision ID: b6afda01aa2f
Revises: da0912cb35ab
Create Date: 2021-10-21 08:19:22.076135+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b6afda01aa2f"
down_revision = "da0912cb35ab"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""update reports_v2 set additional_data = null where additional_data = 'null';""")


def downgrade():
    pass

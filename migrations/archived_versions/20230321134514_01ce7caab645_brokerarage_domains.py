"""Brokerage domains

Revision ID: 01ce7caab645
Revises: 1e2381fc4e20
Create Date: 2023-03-21 13:45:14.749655+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "01ce7caab645"
down_revision = "1e2381fc4e20"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("brokerages", sa.Column("domains", postgresql.ARRAY(sa.String()), nullable=True))
    op.execute("""
        UPDATE brokerages AS b SET
            domains = v.domains
        FROM (VALUES
            ('SPECIALTY WHOLESALE INSURANCE SOLUTIONS', ARRAY ['spgswis.com']),
            ('Southwest Risk', ARRAY ['swrisk.com']),
            ('xs brokers', ARRAY ['xsbrokers.com']),
            ('Citadel Insurance Services, LC', ARRAY ['citadelus.com']),
            ('morstan general agency', ARRAY ['morstan.com']),
            ('CRC GROUP', ARRAY ['crcgroup.com','crcinsgroup.com','crcins.com']),
            ('peachtree special risk brokers', ARRAY ['psrllc.com']),
            ('Brown & Riding', ARRAY ['brcins.com ']),
            ('maximum', ARRAY ['maxib.com']),
            ('RT SPECIALTY', ARRAY ['rtspecialty.com']),
            ('RLA Insurance', ARRAY ['rlainsurance.com ']),
            ('burns & wilcox', ARRAY ['burns-wilcox.com']),
            ('arlington roe', ARRAY ['arlingtonroe.com']),
            ('JENCAP SPECIALTY INSURANCE SERVICES', ARRAY ['jencapgroup.com']),
            ('k&k insurance group', ARRAY ['kandkinsurance.com']),
            ('INSURICA', ARRAY ['insurica.com']),
            ('one80 intermediaries', ARRAY ['one80intermediaries.com']),
            ('AMWINS', ARRAY ['amwins.com']),
            ('coastal brokers', ARRAY ['coastalbrokers.com']),
            ('rps', ARRAY ['rpsins.com']),
            ('Hometown Insurance', ARRAY ['hometowninsurance.com'])
        ) AS v(name, domains) 
        WHERE b.name ilike v.name;
        """)


def downgrade():
    op.drop_column("brokerages", "domains")

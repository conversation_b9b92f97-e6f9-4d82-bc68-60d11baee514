"""adds fleet to metric group

Revision ID: ef2fe2564f98
Revises: dd4de24af217
Create Date: 2022-04-05 10:45:31.573653+00:00

"""
import uuid

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.

revision = "ef2fe2564f98"
down_revision = "dd4de24af217"
branch_labels = None
depends_on = None

CATEGORICAL_SUMMARY_FLEET_SUBTYPES = [
    "Transportation safety rating",
    "Driver Fitness Rating",
    "Controlled Substances and Alcohol Rating",
    "Vehicle Maintenance Rating",
    "Hours-of-Service Compliance Rating",
    "Unsafe Driving Rating",
]

SUM_SUMMARY_FLEET_SUBTYPES = ["Vehicle Miles Traveled"]

MEAN_SUMMARY_FLEET_SUBTYPES = [
    "Number of vehicles",
    "Number of Registered Drivers",
    "Vehicle Out of Service %",
    "Driver Out of Service %",
    "Hazmat Out of Service %",
]


def upgrade():
    conn = op.get_bind()
    metric_group_id = uuid.uuid4()
    conn.execute(f"""INSERT INTO metric_group(id, display_name)
            SELECT '{metric_group_id}', 'Fleet'
            WHERE NOT EXISTS(SELECT 1 FROM metric_group WHERE metric_group.display_name = 'Fleet');
            """)

    for display_name in CATEGORICAL_SUMMARY_FLEET_SUBTYPES:
        conn.execute(
            sa.text("""
            INSERT INTO metric_config (id, created_at, display_name, metric_type, metric_group_id, parent_metric) 
            VALUES (:id, DEFAULT, :display_name, 'CATEGORICAL_DATA_SUMMARY', :metric_group_id, null);
            """),
            id=uuid.uuid4(),
            display_name=display_name,
            metric_group_id=metric_group_id,
        )

    for display_name in MEAN_SUMMARY_FLEET_SUBTYPES:
        conn.execute(
            sa.text("""
            INSERT INTO metric_config (id, created_at, display_name, metric_type, metric_group_id, parent_metric) 
            VALUES (:id, DEFAULT, :display_name, 'MEAN', :metric_group_id, null);
            """),
            id=uuid.uuid4(),
            display_name=display_name,
            metric_group_id=metric_group_id,
        )

    for display_name in SUM_SUMMARY_FLEET_SUBTYPES:
        conn.execute(
            sa.text("""
            INSERT INTO metric_config (id, created_at, display_name, metric_type, metric_group_id, parent_metric) 
            VALUES (:id, DEFAULT, :display_name, 'SUM', :metric_group_id, null);
            """),
            id=uuid.uuid4(),
            display_name=display_name,
            metric_group_id=metric_group_id,
        )


def downgrade():
    pass

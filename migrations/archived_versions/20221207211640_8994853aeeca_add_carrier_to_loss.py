"""Turn on LR Internal

Revision ID: 8994853aeeca
Revises: 50dc54e5a4d3
Create Date: 2022-12-07 21:16:40.233293+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8994853aeeca"
down_revision = "50dc54e5a4d3"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("alter table loss add column carrier varchar(128);")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

"""Add <PERSON> menager permissions

Revision ID: e9d0896c4a80
Revises: 17e55aaf9e9a
Create Date: 2023-02-07 15:16:38.104533+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e9d0896c4a80"
down_revision = "17e55aaf9e9a"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users
    set role='manager'
    where email='<EMAIL>'
    """)


def downgrade():
    pass

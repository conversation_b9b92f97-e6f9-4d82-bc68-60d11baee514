"""Adds fact_subtype_id column

Revision ID: 7b4e2fd5a189
Revises: f506bc2b066e
Create Date: 2021-06-03 16:07:43.414701+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7b4e2fd5a189"
down_revision = "f506bc2b066e"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.add_column("customizable_classifiers", sa.Column("fact_subtype_id", sa.String(), nullable=True))
        op.create_unique_constraint(
            "customizable_classifiers_fact_subtype_id_unique_constraint",
            "customizable_classifiers",
            ["fact_subtype_id"],
        )
    with op.get_context().autocommit_block():
        op.execute("""
        CREATE EXTENSION IF NOT EXISTS unaccent;
        CREATE OR REPLACE FUNCTION public.slugify(
          v TEXT
        ) RETURNS TEXT
          LANGUAGE plpgsql
          STRICT IMMUTABLE AS
        $function$
        BEGIN
          RETURN trim(BOTH '-' FROM regexp_replace(lower(unaccent(trim(v))), '[^a-z0-9\\-_]+', '-', 'gi'));
        END;
        $function$;
        """)
        op.execute(
            """UPDATE customizable_classifiers SET fact_subtype_id = slugify(CONCAT(label, '-', 'org-', organization_id))"""
        )
    op.alter_column("customizable_classifiers", "fact_subtype_id", existing_type=sa.String(), nullable=False)
    with op.get_context().autocommit_block():
        op.execute("""UPDATE customizable_classifiers SET is_applicable_to_all_industries = TRUE 
            WHERE is_applicable_to_all_industries IS NULL""")


def downgrade():
    op.drop_constraint(
        "customizable_classifiers_fact_subtype_id_unique_constraint", "customizable_classifiers", type_="unique"
    )
    op.drop_column("customizable_classifiers", "fact_subtype_id")

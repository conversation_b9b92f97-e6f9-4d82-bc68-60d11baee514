"""Adds report permissions table

Revision ID: eff5605c4027
Revises: 6d7b3a49a2ad
Create Date: 2020-09-02 16:49:16.815773

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "eff5605c4027"
down_revision = "59fddc4817e9"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "report_permissions",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("grantee_user_id", sa.Integer(), nullable=False),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "permission_type", sa.Enum("OWNER", "EDITOR", "COMMENTER", "VIEWER", name="permissiontype"), nullable=False
        ),
        sa.ForeignKeyConstraint(
            ["report_id"],
            ["reports_v2.id"],
        ),
        sa.ForeignKeyConstraint(
            ["grantee_user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_report_permissions_report_id"), "report_permissions", ["report_id"], unique=False)
    op.create_index(
        op.f("ix_report_permissions_grantee_user_id"), "report_permissions", ["grantee_user_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_report_permissions_grantee_user_id"), table_name="report_permissions")
    op.drop_index(op.f("ix_report_permissions_report_id"), table_name="report_permissions")
    op.drop_table("report_permissions")

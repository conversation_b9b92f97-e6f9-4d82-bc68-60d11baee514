"""Add loss runs enabled setting

Revision ID: f3d4434e644b
Revises: ae9e33f2c6a1
Create Date: 2022-11-18 18:18:20.880592+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f3d4434e644b"
down_revision = "ae9e33f2c6a1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("settings", sa.Column("loss_runs_enabled", sa.<PERSON>(), nullable=True))
    op.execute("UPDATE settings SET loss_runs_enabled = false;")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

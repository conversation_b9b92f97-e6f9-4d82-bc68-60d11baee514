"""contractor project mode adjustments

Revision ID: 7f51f6debe33
Revises: f8df290c3d42
Create Date: 2023-02-17 12:00:44.345978+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7f51f6debe33"
down_revision = "f8df290c3d42"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_rows
SET title = 'Project Images'
where id IN
('20efd68e-5d38-465d-b692-50a32e325a68','feb2611c-7032-45bc-82c3-4b282c848b5d' );


UPDATE mode_rows
SET title = 'General Information'
WHERE id IN ('dd114578-3e7a-4821-b118-21ade1d2d097', 'fc8a33da-4bd5-4851-9820-ee95362a5a34');

UPDATE mode_cards
SET title = 'Details',
    card_id = 'general-contractor-details'
WHERE id IN ('3786a024-2929-4793-8448-dbf10ee2bb49', 'dfaec1f1-daf8-42d3-b3ad-88d633bc01f2');


delete from mode_cards where column_id IN
('0dce139d-4c5b-42ec-96bc-917d8152cb5b','7d09de5c-1985-491a-b912-5d719b545fc0');

delete from mode_columns
where row_id IN ('2faed8f4-2a0a-4883-aa31-923c6621bd35', '32e11d7f-1b06-436d-ab12-aabd178e4195');

delete from mode_rows
where id IN ('2faed8f4-2a0a-4883-aa31-923c6621bd35', '32e11d7f-1b06-436d-ab12-aabd178e4195');

UPDATE mode_cards
SET title = 'Work Experience',
    card_id = 'ce-work-experience'
WHERE id IN ('105f4e08-677a-494d-bd1c-4d25616b49a0', '2d2ae574-3874-44a2-aa68-a3786300cb0b');

UPDATE mode_cards
SET title = 'Subcontractors',
    card_id = 'subcontractors'
WHERE id IN ('92d60cf4-fce6-485e-9e98-7c2412a97604', '3d7e3b41-c0ed-46ee-9249-a5c6a3bdffa1');
    """)


def downgrade():
    pass

"""Add orig_value in AdditionalField

Revision ID: de108bbeaeea
Revises: 4f2b5e2a4807
Create Date: 2021-01-18 10:54:04.223585-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "de108bbeaeea"
down_revision = "4f2b5e2a4807"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submission_business_field_values", sa.Column("orig_value", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submission_business_field_values", "orig_value")
    # ### end Alembic commands ###

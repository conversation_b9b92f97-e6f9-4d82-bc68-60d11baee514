"""Adding sent_at

Revision ID: 1226f4c2dee2
Revises: acf18f9c840b
Create Date: 2023-03-19 09:58:09.656173+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1226f4c2dee2"
down_revision = "acf18f9c840b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("emails", sa.Column("email_sent_at", sa.DateTime(timezone=True), nullable=True))


def downgrade():
    op.drop_column("emails", "email_sent_at")

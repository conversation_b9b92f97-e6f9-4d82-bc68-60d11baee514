"""Remove reports created by CS

Revision ID: 9969a79434b5
Revises: 3de08ad0b6c6
Create Date: 2021-02-25 16:24:26.906703+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9969a79434b5"
down_revision = "3de08ad0b6c6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        """update reports_v2 set is_deleted = true where owner_id = 85 and created_at < now() - interval '1 day';"""
    )


def downgrade():
    pass

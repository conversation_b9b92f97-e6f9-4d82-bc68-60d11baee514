"""Adds channel to Notification object

Revision ID: 80cb05fc4c04
Revises: d201deec9459
Create Date: 2021-04-19 14:45:57.684264-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "80cb05fc4c04"
down_revision = "d201deec9459"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("notification", sa.Column("channel", sa.String(length=256), nullable=True))


def downgrade():
    op.drop_column("notification", "channel")

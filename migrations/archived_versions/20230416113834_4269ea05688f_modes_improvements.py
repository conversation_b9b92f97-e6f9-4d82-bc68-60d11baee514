"""modes-improvements

Revision ID: 4269ea05688f
Revises: 618f7682e5c7
Create Date: 2023-04-16 11:38:34.205104+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4269ea05688f"
down_revision = "618f7682e5c7"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("modes", sa.Column("is_public", sa.<PERSON>(), nullable=False, server_default="FALSE"))
    op.execute("""
        UPDATE modes SET is_public = true WHERE id IN 
            ('1e0d051b-f0e0-42ba-b872-430b2c583417',
            'bee6c9bf-92b3-403e-a53e-63f0b2f13e86',
            'a4430e79-3077-4f9e-88b9-0477ce7eb13b',
            '37d3b340-2c83-468a-9f42-fdd429f7e95d',
            'f63d74c4-105b-4a27-b1bb-f4c53fcbb673',
            'cba0324f-c2ef-4614-a3c3-eeb3ce55268e',
            '1d3c3366-774f-46fe-9372-8f40659306fe',
            '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052');
        UPDATE modes SET name = 'Multi Premises' WHERE id = '37d3b340-2c83-468a-9f42-fdd429f7e95d';
        UPDATE modes SET name = 'Single Premises' WHERE id = 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86';
        """)


def downgrade():
    op.drop_column("modes", "is_public")

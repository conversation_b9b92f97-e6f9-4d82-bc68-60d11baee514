"""NAICS submission action types

Revision ID: ab0d45942ac5
Revises: 79c4221h8f9d
Create Date: 2023-05-11 12:09:25.257958+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ab0d45942ac5"
down_revision = "79c4221h8f9d"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_ENTITY_MAPPING_STARTED';")


def downgrade():
    ...

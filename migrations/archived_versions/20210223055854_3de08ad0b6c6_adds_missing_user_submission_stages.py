"""Adds missing user_submission_stages

Revision ID: 3de08ad0b6c6
Revises: b06880d3a22b
Create Date: 2021-02-23 05:58:54.801298-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3de08ad0b6c6"
down_revision = "b06880d3a22b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""insert into user_submission_stage(id, submission_id, user_id, stage) select uuid_generate_v4() 
    as id, submissions_reports.submission_id as submission_id, report_permissions.grantee_user_id as user_id, 
    'JUST_IN'::submissionstage from report_permissions join submissions_reports on 
    report_permissions.report_id = submissions_reports.report_id where (report_permissions.report_id, 
    report_permissions.grantee_user_id) not in (select submissions_reports.report_id, user_submission_stage.user_id 
    from user_submission_stage join submissions_reports on submissions_reports.submission_id = 
    user_submission_stage.submission_id)""")


def downgrade():
    pass

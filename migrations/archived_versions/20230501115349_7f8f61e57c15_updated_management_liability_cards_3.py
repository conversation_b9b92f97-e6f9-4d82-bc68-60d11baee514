"""update the mode column position for file cards in  management liability mode to be 0

Revision ID: 7f8f61e57c15
Revises: 5be067e5b9ef
Create Date: 2023-05-01 11:53:49.070936+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7f8f61e57c15"
down_revision = "5be067e5b9ef"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        UPDATE mode_columns set position=0 where id='fb9d82a1-fb92-452b-9466-c985ce26e18e';
    """)


def downgrade():
    pass

"""notebook threads migration 2

Revision ID: 9c491af5e8da
Revises: d212b734e618
Create Date: 2021-10-02 01:52:15.519602+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9c491af5e8da"
down_revision = "d212b734e618"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''COASTAL_STORM_SURGE_RISK'')]}' where dossier_component_paths='{location.coastal_storm_surge}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''COASTAL_STORM_SURGE_RISK'')]}' where dossier_component_paths='{location.coastal_storm_surge_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ASSAULT_GRADE'')]}' where dossier_component_paths='{location.crime.assault}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ASSAULT_GRADE'')]}' where dossier_component_paths='{location.crime.assault_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ASSAULT_GRADE'')]}' where dossier_component_paths='{location.crime.assault_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''BURGLARY_GRADE'')]}' where dossier_component_paths='{location.crime.burglary}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''BURGLARY_GRADE'')]}' where dossier_component_paths='{location.crime.burglary_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''BURGLARY_GRADE'')]}' where dossier_component_paths='{location.crime.burglary_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''DRUG_ALCOHOL_RELATED_DEATHS_GRADE'')]}' where dossier_component_paths='{location.crime.drug_alcohol_deaths_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''DRUG_ALCOHOL_RELATED_DEATHS_GRADE'')]}' where dossier_component_paths='{location.crime.drug_alcohol_deaths_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''VEHICLE_THEFT_GRADE'')]}' where dossier_component_paths='{location.crime.motor_vehicle_theft_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''VEHICLE_THEFT_GRADE'')]}' where dossier_component_paths='{location.crime.motor_vehicle_theft_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''MURDER_GRADE'')]}' where dossier_component_paths='{location.crime.murder}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''MURDER_GRADE'')]}' where dossier_component_paths='{location.crime.murder_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''MURDER_GRADE'')]}' where dossier_component_paths='{location.crime.murder_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''OVERALL_CRIME_GRADE'')]}' where dossier_component_paths='{location.crime.overall}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''OVERALL_CRIME_GRADE'')]}' where dossier_component_paths='{location.crime.overall_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''OVERALL_CRIME_GRADE'')]}' where dossier_component_paths='{location.crime.overall_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''RAPE_GRADE'')]}' where dossier_component_paths='{location.crime.rape}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''RAPE_GRADE'')]}' where dossier_component_paths='{location.crime.rape_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''RAPE_GRADE'')]}' where dossier_component_paths='{location.crime.rape_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ROBBERY_GRADE'')]}' where dossier_component_paths='{location.crime.robbery}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ROBBERY_GRADE'')]}' where dossier_component_paths='{location.crime.robbery_score}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ROBBERY_GRADE'')]}' where dossier_component_paths='{location.crime.robbery_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''DISTANCE_TO_POI''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''DISTANCE_TO_COAST'')]}' where dossier_component_paths='{location.distance_to_coast}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''DISTANCE_TO_POI''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''DISTANCE_TO_TOXIC_FACILITY'')]}' where dossier_component_paths='{location.distance_to_toxic_facility}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''EARTHQUAKE_RISK'')]}' where dossier_component_paths='{location.earthquake}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''FIRE_PROTECTION_GRADE'')]}' where dossier_component_paths='{location.fire_protection}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''FIRE_PROTECTION_CLASS'')]}' where dossier_component_paths='{location.fire_protection_class}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''FIRE_PROTECTION_GRADE'')]}' where dossier_component_paths='{location.fire_protection_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''FLOOD_RISK'')]}' where dossier_component_paths='{location.flood_risk}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''HAIL_RISK'')]}' where dossier_component_paths='{location.hail}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''HAIL_RISK'')]}' where dossier_component_paths='{location.hail_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''LIGHTNING_RISK'')]}' where dossier_component_paths='{location.lightning}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''LIGHTNING_RISK'')]}' where dossier_component_paths='{location.lightning_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''SINKHOLE_RISK'')]}' where dossier_component_paths='{location.sinkhole}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''SNOW_LOAD_RISK'')]}' where dossier_component_paths='{location.snow_load}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''SNOW_LOAD_RISK'')]}' where dossier_component_paths='{location.snow_load_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''TORNADO_RISK'')]}' where dossier_component_paths='{location.tornado}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''TORNADO_RISK'')]}' where dossier_component_paths='{location.tornado_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''WILDFIRE_RISK'')]}' where dossier_component_paths='{location.wildfire}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''WILDFIRE_RISK'')]}' where dossier_component_paths='{location.wildfire_text}';
    update notebook_thread set dossier_component_paths='{multilabelFacts[?(@.group.id==''ENTERTAINMENT_TYPES'')]}' where dossier_component_paths='{restaurant_operation.entertainment_types}';
    """)


def downgrade():
    pass

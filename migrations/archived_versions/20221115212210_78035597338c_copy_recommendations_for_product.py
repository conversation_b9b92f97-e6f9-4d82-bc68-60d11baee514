"""Copy recommendations for product

Revision ID: 78035597338c
Revises: 1d6aba321aef
Create Date: 2022-11-15 21:22:10.019005+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "78035597338c"
down_revision = "1d6aba321aef"
branch_labels = None
depends_on = None

rule_definition = (
    '{"conditions":{"any":[{"any":[{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Dave'
    ' Canu"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Ann'
    <PERSON> <PERSON>"
    ' <PERSON>"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Brittany'
    ' Davies"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Julie'
    ' Rubel"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Mavy'
    ' Vera"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Kristen'
    ' D. Vorrier"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Tim'
    ' Pedersen"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Alicia'
    ' Greenberg"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Buddy'
    ' Taylor"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Greg'
    ' Petty"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Amanda'
    ' Barton"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Tanis'
    ' Smith"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Keith'
    ' Richardson"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Makenzie'
    ' Pfeiffer"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Chrissy'
    ' Fisher"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Matt'
    ' Domitrovich"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Daniel'
    ' Bernardo"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Evan'
    ' Simpson"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Charles'
    ' Cochran"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Steve'
    ' Beard"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Christy'
    ' Marvin"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Mark'
    ' Paul"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Brad'
    ' Verret"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Kevin'
    ' Pollard"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Taylor'
    ' Mogged"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"John'
    ' Grogan"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Miranda'
    ' Reeder"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Courtney'
    ' Murphree"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Kenneth'
    ' Kelly"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Nicole'
    ' Rickett"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Chase'
    ' Muder"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Scott'
    ' Cottingham"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Eli'
    ' Fiedler"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Dan'
    ' Marroccoli"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Robin'
    ' Sheridan"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Ashley'
    ' Ward"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Kim'
    ' Taylor"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Diane'
    ' Iervilino"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Gary'
    ' Schuldt"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Ryan'
    ' Jennings"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Bill'
    ' Rous"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Christina'
    ' Jaeger"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Lou'
    ' Yeager"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Carol'
    ' Mollo"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Brianna'
    ' Wilmoth"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Sue'
    ' E. Williams"}]}}},{"all":[{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Sue'
    ' Williams"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Jeff'
    ' Dunn"}]}}}]},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Lisa'
    ' Adams"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Stephanie'
    ' Sealock"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Shannon'
    ' Campbell"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Chris'
    ' Stuhlweissenburg"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Sean'
    ' Clark"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Annalie'
    ' Ricketts"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Ashby'
    ' Moore"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Caprice'
    ' Allen"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Rob'
    ' Harman"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"Elizabeth'
    ' Hermosillo"}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"broker_name","operator":"equal_to_case_insensitive","value":"david'
    " canu"
    ' jr."}]}}}]},{"any":[{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["111110'
    ' Soybean Farming","111120 Oilseed (except Soybean) Farming","111130 Dry Pea and Bean Farming","111140 Wheat'
    ' Farming","111150 Corn Farming","111160 Rice Farming","111191 Oilseed and Grain Combination Farming","111199 All'
    ' Other Grain Farming","111211 Potato Farming","111219 Other Vegetable (except Potato) and Melon Farming","111310'
    ' Orange Groves","111320 Citrus (except Orange) Groves","111331 Apple Orchards","111332 Grape Vineyards","111333'
    ' Strawberry Farming","111334 Berry (except Strawberry) Farming","111335 Tree Nut Farming","111336 Fruit and Tree'
    ' Nut Combination Farming","111339 Other Noncitrus Fruit Farming","111411 Mushroom Production","111419 Other Food'
    ' Crops Grown Under Cover","111421 Nursery and Tree Production","111422 Floriculture Production","111910 Tobacco'
    ' Farming","111920 Cotton Farming","111930 Sugarcane Farming","111991 Sugar Beet Farming","111940 Hay'
    ' Farming","111992 Peanut Farming","111998 All Other Miscellaneous Crop Farming","115111 Cotton'
    ' Ginning"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["236210'
    ' Industrial Building Construction","236220 Commercial and Institutional Building Construction","237110 Water and'
    ' Sewer Line and Related Structures Construction","237120 Oil and Gas Pipeline and Related Structures'
    ' Construction","237130 Power and Communication Line and Related Structures Construction","237210 Land'
    ' Subdivision","237310 Highway, Street, and Bridge Construction","237990 Other Heavy and Civil Engineering'
    ' Construction","238110 Poured Concrete Foundation and Structure Contractors","238120 Structural Steel and Precast'
    ' Concrete Contractors","238130 Framing Contractors","238140 Masonry Contractors","238150 Glass and Glazing'
    ' Contractors","238160 Roofing Contractors","238170 Siding Contractors","238190 Other Foundation, Structure, and'
    ' Building Exterior Contractors","238210 Electrical Contractors and Other Wiring Installation Contractors","238220'
    ' Plumbing, Heating, and Air-Conditioning Contractors","238290 Other Building Equipment Contractors","238310'
    ' Drywall and Insulation Contractors","238320 Painting and Wall Covering Contractors","238330 Flooring'
    ' Contractors","238340 Tile and Terrazzo Contractors","238350 Finish Carpentry Contractors","238390 Other Building'
    ' Finishing Contractors","238910 Site Preparation Contractors","238990 All Other Specialty Trade'
    ' Contractors"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["322110'
    ' Pulp Mills","322120 Paper Mills","322130 Paperboard Mills","322211 Corrugated and Solid Fiber Box'
    ' Manufacturing","322212 Folding Paperboard Box Manufacturing","322219 Other Paperboard Container'
    ' Manufacturing","322220 Paper Bag and Coated and Treated Paper Manufacturing","322230 Stationery Product'
    ' Manufacturing","322291 Sanitary Paper Product Manufacturing","322299 All Other Converted Paper Product'
    ' Manufacturing","326111 Plastics Bag and Pouch Manufacturing","326112 Plastics Packaging Film and Sheet (including'
    ' Laminated) Manufacturing","326113 Unlaminated Plastics Film and Sheet (except Packaging) Manufacturing","326121'
    ' Unlaminated Plastics Profile Shape Manufacturing","326122 Plastics Pipe and Pipe Fitting Manufacturing","326130'
    ' Laminated Plastics Plate, Sheet (except Packaging), and Shape Manufacturing","326140 Polystyrene Foam Product'
    ' Manufacturing","326150 Urethane and Other Foam Product (except Polystyrene) Manufacturing","326160 Plastics'
    ' Bottle Manufacturing","326199 All Other Plastics Product Manufacturing","326191 Plastics Plumbing Fixture'
    ' Manufacturing","326211 Tire Manufacturing (except Retreading)","326212 Tire Retreading","326220 Rubber and'
    ' Plastics Hoses and Belting Manufacturing","326291 Rubber Product Manufacturing for Mechanical Use","326299 All'
    ' Other Rubber Product Manufacturing","327110 Pottery, Ceramics, and Plumbing Fixture Manufacturing","327120 Clay'
    ' Building Material and Refractories Manufacturing","327211 Flat Glass Manufacturing","327212 Other Pressed and'
    ' Blown Glass and Glassware Manufacturing","327213 Glass Container Manufacturing","327215 Glass Product'
    ' Manufacturing Made of Purchased Glass","327310 Cement Manufacturing","327320 Ready-Mix Concrete'
    ' Manufacturing","327331 Concrete Block and Brick Manufacturing","327332 Concrete Pipe Manufacturing","327390 Other'
    ' Concrete Product Manufacturing","327410 Lime Manufacturing","327420 Gypsum Product Manufacturing","327910'
    ' Abrasive Product Manufacturing","327991 Cut Stone and Stone Product Manufacturing","327992 Ground or Treated'
    ' Mineral and Earth Manufacturing","327993 Mineral Wool Manufacturing","327999 All Other Miscellaneous Nonmetallic'
    " Mineral Product"
    ' Manufacturing"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["423110'
    ' Automobile and Other Motor Vehicle Merchant Wholesalers","423120 Motor Vehicle Supplies and New Parts Merchant'
    ' Wholesalers","423130 Tire and Tube Merchant Wholesalers","423140 Motor Vehicle Parts (Used) Merchant'
    ' Wholesalers","423210 Furniture Merchant Wholesalers","423220 Home Furnishing Merchant Wholesalers","423310'
    ' Lumber, Plywood, Millwork, and Wood Panel Merchant Wholesalers","423320 Brick, Stone, and Related Construction'
    ' Material Merchant Wholesalers","423330 Roofing, Siding, and Insulation Material Merchant Wholesalers","423390'
    ' Other Construction Material Merchant Wholesalers","423410 Photographic Equipment and Supplies Merchant'
    ' Wholesalers","423420 Office Equipment Merchant Wholesalers","423430 Computer and Computer Peripheral Equipment'
    ' and Software Merchant Wholesalers","423450 Medical, Dental, and Hospital Equipment and Supplies Merchant'
    ' Wholesalers","423440 Other Commercial Equipment Merchant Wholesalers","423460 Ophthalmic Goods Merchant'
    ' Wholesalers","423490 Other Professional Equipment and Supplies Merchant Wholesalers","423510 Metal Service'
    ' Centers and Other Metal Merchant Wholesalers","423520 Coal and Other Mineral and Ore Merchant'
    ' Wholesalers","423610 Electrical Apparatus and Equipment, Wiring Supplies, and Related Equipment Merchant'
    ' Wholesalers","423620 Household Appliances, Electric Housewares, and Consumer Electronics Merchant'
    ' Wholesalers","423690 Other Electronic Parts and Equipment Merchant Wholesalers","423710 Hardware Merchant'
    ' Wholesalers","423720 Plumbing and Heating Equipment and Supplies (Hydronics) Merchant Wholesalers","423730 Warm'
    ' Air Heating and Air-Conditioning Equipment and Supplies Merchant Wholesalers","423740 Refrigeration Equipment and'
    ' Supplies Merchant Wholesalers","423810 Construction and Mining (except Oil Well) Machinery and Equipment Merchant'
    ' Wholesalers","423820 Farm and Garden Machinery and Equipment Merchant Wholesalers","423830 Industrial Machinery'
    ' and Equipment Merchant Wholesalers","423840 Industrial Supplies Merchant Wholesalers","423850 Service'
    ' Establishment Equipment and Supplies Merchant Wholesalers","423860 Transportation Equipment and Supplies (except'
    ' Motor Vehicle) Merchant Wholesalers","423910 Sporting and Recreational Goods and Supplies Merchant'
    ' Wholesalers","423920 Toy and Hobby Goods and Supplies Merchant Wholesalers","423930 Recyclable Material Merchant'
    ' Wholesalers","423940 Jewelry, Watch, Precious Stone, and Precious Metal Merchant Wholesalers","423990 Other'
    " Miscellaneous Durable Goods Merchant"
    ' Wholesalers"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["444110'
    ' Home Centers","444120 Paint and Wallpaper Retailers","444140 Hardware Retailers","444180 Other Building Material'
    ' Dealers","444230 Outdoor Power Equipment Retailers","444240 Nursery, Garden Center, and Farm Supply'
    ' Retailers","449110 Furniture Retailers","449121 Floor Covering Retailers","449122 Window Treatment'
    ' Retailers","449129 All Other Home Furnishings Retailers","449210 Electronics and Appliance Retailers","457110'
    ' Gasoline Stations with Convenience Stores","457120 Other Gasoline Stations","457210 Fuel'
    ' Dealers"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["484110'
    ' General Freight Trucking, Local","484121 General Freight Trucking, Long-Distance, Truckload","484122 General'
    ' Freight Trucking, Long-Distance, Less Than Truckload","484210 Used Household and Office Goods Moving","484220'
    ' Specialized Freight (except Used Goods) Trucking, Local","484230 Specialized Freight (except Used Goods)'
    " Trucking,"
    ' Long-Distance"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["531110'
    ' Lessors of Residential Buildings and Dwellings","531120 Lessors of Nonresidential Buildings (except'
    ' Miniwarehouses)","531130 Lessors of Miniwarehouses and Self-Storage Units","531190 Lessors of Other Real Estate'
    ' Property","531210 Offices of Real Estate Agents and Brokers","531311 Residential Property Managers","531312'
    ' Nonresidential Property Managers","531320 Offices of Real Estate Appraisers","531390 Other Activities Related to'
    ' Real Estate","532411 Commercial Air, Rail, and Water Transportation Equipment Rental and Leasing","532412'
    ' Construction, Mining, and Forestry Machinery and Equipment Rental and Leasing","532420 Office Machinery and'
    ' Equipment Rental and Leasing","532490 Other Commercial and Industrial Machinery and Equipment Rental and'
    ' Leasing"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["711211'
    ' Sports Teams and Clubs","711212 Racetracks","711219 Other Spectator Sports","713110 Amusement and Theme'
    ' Parks","713120 Amusement Arcades","713210 Casinos (except Casino Hotels)","713290 Other Gambling'
    ' Industries","713910 Golf Courses and Country Clubs","713920 Skiing Facilities","713930 Marinas","713940 Fitness'
    ' and Recreational Sports Centers","713990 All Other Amusement and Recreation Industries","713950 Bowling'
    ' Centers"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["811111'
    ' General Automotive Repair","811114 Specialized Automotive Repair","811121 Automotive Body, Paint, and Interior'
    ' Repair and Maintenance","811122 Automotive Glass Replacement Shops","811191 Automotive Oil Change and Lubrication'
    ' Shops","811192 Car Washes","811198 All Other Automotive Repair and Maintenance","811210 Electronic and Precision'
    ' Equipment Repair and Maintenance","811310 Commercial and Industrial Machinery and Equipment (except Automotive'
    ' and Electronic) Repair and Maintenance","811411 Home and Garden Equipment Repair and Maintenance","811412'
    ' Appliance Repair and Maintenance","811420 Reupholstery and Furniture Repair","811430 Footwear and Leather Goods'
    ' Repair","811490 Other Personal and Household Goods Repair and'
    ' Maintenance"]}]}}}]},{"any":[{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["21'
    ' Mining, Quarrying, and Oil and Gas Extraction","211120 Crude Petroleum Extraction","211130 Natural Gas'
    ' Extraction","212114 Surface Coal Mining","212115 Underground Coal Mining","212210 Iron Ore Mining","212220 Gold'
    ' Ore and Silver Ore Mining","212230 Copper, Nickel, Lead, and Zinc Mining","212290 Other Metal Ore Mining","212311'
    ' Dimension Stone Mining and Quarrying","212312 Crushed and Broken Limestone Mining and Quarrying","212313 Crushed'
    ' and Broken Granite Mining and Quarrying","212319 Other Crushed and Broken Stone Mining and Quarrying","212321'
    ' Construction Sand and Gravel Mining","212322 Industrial Sand Mining","212323 Kaolin, Clay, and Ceramic and'
    ' Refractory Minerals Mining","212390 Other Nonmetallic Mineral Mining and Quarrying","213111 Drilling Oil and Gas'
    ' Wells","213112 Support Activities for Oil and Gas Operations","213113 Support Activities for Coal Mining","213114'
    ' Support Activities for Metal Mining","213115 Support Activities for Nonmetallic Minerals (except Fuels)'
    ' Mining"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["22'
    ' Utilities","221111 Hydroelectric Power Generation","221112 Fossil Fuel Electric Power Generation","221113 Nuclear'
    ' Electric Power Generation","221114 Solar Electric Power Generation","221115 Wind Electric Power'
    ' Generation","221116 Geothermal Electric Power Generation","221117 Biomass Electric Power Generation","221118'
    ' Other Electric Power Generation","221121 Electric Bulk Power Transmission and Control","221122 Electric Power'
    ' Distribution","221210 Natural Gas Distribution","221310 Water Supply and Irrigation Systems","221320 Sewage'
    ' Treatment Facilities","221330 Steam and Air-Conditioning'
    ' Supply"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["311111'
    ' Dog and Cat Food Manufacturing","311119 Other Animal Food Manufacturing","311211 Flour Milling","311212 Rice'
    ' Milling","311213 Malt Manufacturing","311221 Wet Corn Milling and Starch Manufacturing","311224 Soybean and Other'
    ' Oilseed Processing","311225 Fats and Oils Refining and Blending","311230 Breakfast Cereal Manufacturing","311313'
    ' Beet Sugar Manufacturing","311314 Cane Sugar Manufacturing","311340 Nonchocolate Confectionery'
    ' Manufacturing","311351 Chocolate and Confectionery Manufacturing from Cacao Beans","311352 Confectionery'
    ' Manufacturing from Purchased Chocolate","311411 Frozen Fruit, Juice, and Vegetable Manufacturing","311412 Frozen'
    ' Specialty Food Manufacturing","311421 Fruit and Vegetable Canning","311422 Specialty Canning","311423 Dried and'
    ' Dehydrated Food Manufacturing","311511 Fluid Milk Manufacturing","311512 Creamery Butter Manufacturing","311513'
    ' Cheese Manufacturing","311514 Dry, Condensed, and Evaporated Dairy Product Manufacturing","311520 Ice Cream and'
    ' Frozen Dessert Manufacturing","311611 Animal (except Poultry) Slaughtering","311612 Meat Processed from'
    ' Carcasses","311613 Rendering and Meat Byproduct Processing","311615 Poultry Processing","311710 Seafood Product'
    ' Preparation and Packaging","311811 Retail Bakeries","311812 Commercial Bakeries","311813 Frozen Cakes, Pies, and'
    ' Other Pastries Manufacturing","311821 Cookie and Cracker Manufacturing","311824 Dry Pasta, Dough, and Flour Mixes'
    ' Manufacturing from Purchased Flour","311830 Tortilla Manufacturing","311911 Roasted Nuts and Peanut Butter'
    ' Manufacturing","311919 Other Snack Food Manufacturing","311920 Coffee and Tea Manufacturing","311930 Flavoring'
    ' Syrup and Concentrate Manufacturing","311941 Mayonnaise, Dressing, and Other Prepared Sauce'
    ' Manufacturing","311942 Spice and Extract Manufacturing","311999 All Other Miscellaneous Food'
    ' Manufacturing","311991 Perishable Prepared Food Manufacturing","312111 Soft Drink Manufacturing","312112 Bottled'
    ' Water Manufacturing","312113 Ice Manufacturing","312120 Breweries","312130 Wineries","312140'
    ' Distilleries","312230 Tobacco Manufacturing","313210 Broadwoven Fabric Mills","313110 Fiber, Yarn, and Thread'
    ' Mills","313220 Narrow Fabric Mills and Schiffli Machine Embroidery","313230 Nonwoven Fabric Mills","313240 Knit'
    ' Fabric Mills","313310 Textile and Fabric Finishing Mills","313320 Fabric Coating Mills","314110 Carpet and Rug'
    ' Mills","314120 Curtain and Linen Mills","314910 Textile Bag and Canvas Mills","314994 Rope, Cordage, Twine, Tire'
    ' Cord, and Tire Fabric Mills","315120 Apparel Knitting Mills","314999 All Other Miscellaneous Textile Product'
    ' Mills","315210 Cut and Sew Apparel Contractors","315250 Cut and Sew Apparel Manufacturing (except'
    ' Contractors)","315990 Apparel Accessories and Other Apparel Manufacturing","316110 Leather and Hide Tanning and'
    ' Finishing","316210 Footwear Manufacturing","316990 Other Leather and Allied Product'
    ' Manufacturing"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["491110'
    ' Postal Service","492110 Couriers and Express Delivery Services","492210 Local Messengers and Local'
    ' Delivery","493110 General Warehousing and Storage","493120 Refrigerated Warehousing and Storage","493130 Farm'
    ' Product Warehousing and Storage","493190 Other Warehousing and'
    ' Storage"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["51'
    ' Information","512110 Motion Picture and Video Production","512120 Motion Picture and Video Distribution","512131'
    ' Motion Picture Theaters (except Drive-Ins)","512132 Drive-In Motion Picture Theaters","512191 Teleproduction and'
    ' Other Postproduction Services","512199 Other Motion Picture and Video Industries","512230 Music'
    ' Publishers","512240 Sound Recording Studios","512250 Record Production and Distribution","512290 Other Sound'
    ' Recording Industries","513110 Newspaper Publishers","513120 Periodical Publishers","513130 Book'
    ' Publishers","513140 Directory and Mailing List Publishers","513191 Greeting Card Publishers","513199 All Other'
    ' Publishers","513210 Software Publishers","516110 Radio Broadcasting Stations","516120 Television Broadcasting'
    ' Stations","516210 Media Streaming Distribution Services, Social Networks, and Other Media Networks and Content'
    ' Providers","517111 Wired Telecommunications Carriers","517112 Wireless Telecommunications Carriers (except'
    ' Satellite)","517121 Telecommunications Resellers","517122 Agents for Wireless Telecommunications'
    ' Services","517410 Satellite Telecommunications","517810 All Other Telecommunications","518210 Computing'
    ' Infrastructure Providers, Data Processing, Web Hosting, and Related Services","519210 Libraries and'
    ' Archives","519290 Web Search Portals and All Other Information'
    ' Services"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["52'
    ' Finance and Insurance","521110 Monetary Authorities-Central Bank","522110 Commercial Banking","522130 Credit'
    ' Unions","522180 Savings Institutions and Other Depository Credit Intermediation","522210 Credit Card'
    ' Issuing","522220 Sales Financing","522291 Consumer Lending","522292 Real Estate Credit","522299 International,'
    ' Secondary Market, and All Other Nondepository Credit Intermediation","522310 Mortgage and Nonmortgage Loan'
    ' Brokers","522320 Financial Transactions Processing, Reserve, and Clearinghouse Activities","522390 Other'
    ' Activities Related to Credit Intermediation","523150 Investment Banking and Securities Intermediation","523160'
    ' Commodity Contracts Intermediation","523210 Securities and Commodity Exchanges","523910 Miscellaneous'
    ' Intermediation","523940 Portfolio Management and Investment Advice","523991 Trust, Fiduciary, and Custody'
    ' Activities","523999 Miscellaneous Financial Investment Activities","524113 Direct Life Insurance'
    ' Carriers","524114 Direct Health and Medical Insurance Carriers","524126 Direct Property and Casualty Insurance'
    ' Carriers","524127 Direct Title Insurance Carriers","524128 Other Direct Insurance (except Life, Health, and'
    ' Medical) Carriers","524130 Reinsurance Carriers","524210 Insurance Agencies and Brokerages","524291 Claims'
    ' Adjusting","524292 Pharmacy Benefit Management and Other Third Party Administration of Insurance and Pension'
    ' Funds","524298 All Other Insurance Related Activities","525110 Pension Funds","525120 Health and Welfare'
    ' Funds","525190 Other Insurance Funds","525910 Open-End Investment Funds","525920 Trusts, Estates, and Agency'
    ' Accounts","525990 Other Financial'
    ' Vehicles"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["54'
    ' Professional, Scientific, and Technical Services","541110 Offices of Lawyers","541120 Offices of'
    ' Notaries","541191 Title Abstract and Settlement Offices","541199 All Other Legal Services","541211 Offices of'
    ' Certified Public Accountants","541213 Tax Preparation Services","541214 Payroll Services","541219 Other'
    ' Accounting Services","541310 Architectural Services","541320 Landscape Architectural Services","541330'
    ' Engineering Services","541340 Drafting Services","541350 Building Inspection Services","541360 Geophysical'
    ' Surveying and Mapping Services","541370 Surveying and Mapping (except Geophysical) Services","541380 Testing'
    ' Laboratories and Services","541410 Interior Design Services","541420 Industrial Design Services","541430 Graphic'
    ' Design Services","541490 Other Specialized Design Services","541511 Custom Computer Programming Services","541512'
    ' Computer Systems Design Services","541513 Computer Facilities Management Services","541519 Other Computer Related'
    ' Services","541611 Administrative Management and General Management Consulting Services","541612 Human Resources'
    ' Consulting Services","541613 Marketing Consulting Services","541614 Process, Physical Distribution, and Logistics'
    ' Consulting Services","541618 Other Management Consulting Services","541620 Environmental Consulting'
    ' Services","541690 Other Scientific and Technical Consulting Services","541713 Research and Development in'
    ' Nanotechnology","541714 Research and Development in Biotechnology (except Nanobiotechnology)","541715 Research'
    " and Development in the Physical, Engineering, and Life Sciences (except Nanotechnology and"
    ' Biotechnology)","541720 Research and Development in the Social Sciences and Humanities","541810 Advertising'
    ' Agencies","541820 Public Relations Agencies","541830 Media Buying Agencies","541840 Media'
    ' Representatives","541850 Indoor and Outdoor Display Advertising","541860 Direct Mail Advertising","541870'
    ' Advertising Material Distribution Services","541890 Other Services Related to Advertising","541910 Marketing'
    ' Research and Public Opinion Polling","541921 Photography Studios, Portrait","541922 Commercial'
    ' Photography","541930 Translation and Interpretation Services","541940 Veterinary Services","541990 All Other'
    " Professional, Scientific, and Technical"
    ' Services"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["55'
    ' Management of Companies and Enterprises","551111 Offices of Bank Holding Companies","551112 Offices of Other'
    ' Holding Companies","551114 Corporate, Subsidiary, and Regional Managing'
    ' Offices"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["56'
    ' Administrative and Support and Waste Management and Remediation Services","561110 Office Administrative'
    ' Services","561210 Facilities Support Services","561311 Employment Placement Agencies","561312 Executive Search'
    ' Services","561320 Temporary Help Services","561330 Professional Employer Organizations","561410 Document'
    ' Preparation Services","561421 Telephone Answering Services","561422 Telemarketing Bureaus and Other Contact'
    ' Centers","561431 Private Mail Centers","561439 Other Business Service Centers (including Copy Shops)","561440'
    ' Collection Agencies","561450 Credit Bureaus","561491 Repossession Services","561492 Court Reporting and Stenotype'
    ' Services","561499 All Other Business Support Services","561510 Travel Agencies","561520 Tour Operators","561591'
    ' Convention and Visitors Bureaus","561599 All Other Travel Arrangement and Reservation Services","561611'
    ' Investigation and Personal Background Check Services","561612 Security Guards and Patrol Services","561613'
    ' Armored Car Services","561621 Security Systems Services (except Locksmiths)","561622 Locksmiths","561710'
    ' Exterminating and Pest Control Services","561720 Janitorial Services","561730 Landscaping Services","561740'
    ' Carpet and Upholstery Cleaning Services","561790 Other Services to Buildings and Dwellings","561910 Packaging and'
    ' Labeling Services","561920 Convention and Trade Show Organizers","561990 All Other Support Services","562111'
    ' Solid Waste Collection","562112 Hazardous Waste Collection","562119 Other Waste Collection","562211 Hazardous'
    ' Waste Treatment and Disposal","562212 Solid Waste Landfill","562213 Solid Waste Combustors and'
    ' Incinerators","562219 Other Nonhazardous Waste Treatment and Disposal","562910 Remediation Services","562920'
    ' Materials Recovery Facilities","562991 Septic Tank and Related Services","562998 All Other Miscellaneous Waste'
    " Management"
    ' Services"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["61'
    ' Educational Services","611110 Elementary and Secondary Schools","611210 Junior Colleges","611310 Colleges,'
    ' Universities, and Professional Schools","611410 Business and Secretarial Schools","611420 Computer'
    ' Training","611430 Professional and Management Development Training","611511 Cosmetology and Barber'
    ' Schools","611512 Flight Training","611513 Apprenticeship Training","611519 Other Technical and Trade'
    ' Schools","611610 Fine Arts Schools","611620 Sports and Recreation Instruction","611630 Language Schools","611691'
    ' Exam Preparation and Tutoring","611692 Automobile Driving Schools","611699 All Other Miscellaneous Schools and'
    ' Instruction","611710 Educational Support'
    ' Services"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["62'
    ' Health Care and Social Assistance","621111 Offices of Physicians (except Mental Health Specialists)","621112'
    ' Offices of Physicians, Mental Health Specialists","621210 Offices of Dentists","621310 Offices of'
    ' Chiropractors","621320 Offices of Optometrists","621330 Offices of Mental Health Practitioners (except'
    ' Physicians)","621340 Offices of Physical, Occupational and Speech Therapists, and Audiologists","621391 Offices'
    ' of Podiatrists","621399 Offices of All Other Miscellaneous Health Practitioners","621410 Family Planning'
    ' Centers","621420 Outpatient Mental Health and Substance Abuse Centers","621491 HMO Medical Centers","621492'
    ' Kidney Dialysis Centers","621493 Freestanding Ambulatory Surgical and Emergency Centers","621498 All Other'
    ' Outpatient Care Centers","621512 Diagnostic Imaging Centers","621511 Medical Laboratories","621610 Home Health'
    ' Care Services","621910 Ambulance Services","621991 Blood and Organ Banks","621999 All Other Miscellaneous'
    ' Ambulatory Health Care Services","622110 General Medical and Surgical Hospitals","622210 Psychiatric and'
    ' Substance Abuse Hospitals","622310 Specialty (except Psychiatric and Substance Abuse) Hospitals","623110 Nursing'
    ' Care Facilities (Skilled Nursing Facilities)","623210 Residential Intellectual and Developmental Disability'
    ' Facilities","623220 Residential Mental Health and Substance Abuse Facilities","623311 Continuing Care Retirement'
    ' Communities","623312 Assisted Living Facilities for the Elderly","623990 Other Residential Care'
    ' Facilities","624110 Child and Youth Services","624120 Services for the Elderly and Persons with'
    ' Disabilities","624190 Other Individual and Family Services","624210 Community Food Services","624221 Temporary'
    ' Shelters","624229 Other Community Housing Services","624230 Emergency and Other Relief Services","624310'
    ' Vocational Rehabilitation Services","624410 Child Care'
    ' Services"]}]}}},{"name":"dossiers","operator":"include_one_or_more_with","value":{"conditions":{"all":[{"name":"naics_codes","operator":"shares_at_least_one_element_with","value":["92'
    ' Public Administration","921110 Executive Offices","921120 Legislative Bodies","921130 Public Finance'
    ' Activities","921140 Executive and Legislative Offices, Combined","921150 American Indian and Alaska Native Tribal'
    ' Governments","921190 Other General Government Support","922120 Police Protection","922110 Courts","922130 Legal'
    ' Counsel and Prosecution","922140 Correctional Institutions","922150 Parole Offices and Probation Offices","922160'
    ' Fire Protection","922190 Other Justice, Public Order, and Safety Activities","923110 Administration of Education'
    ' Programs","923120 Administration of Public Health Programs","923130 Administration of Human Resource Programs'
    ' (except Education, Public Health, and Veterans Affairs Programs)","923140 Administration of Veterans'
    ' Affairs","924110 Administration of Air and Water Resource and Solid Waste Management Programs","924120'
    ' Administration of Conservation Programs","925110 Administration of Housing Programs","925120 Administration of'
    ' Urban Planning and Community and Rural Development","926110 Administration of General Economic Programs","926120'
    ' Regulation and Administration of Transportation Programs","926130 Regulation and Administration of'
    ' Communications, Electric, Gas, and Other Utilities","926140 Regulation of Agricultural Marketing and'
    ' Commodities","926150 Regulation, Licensing, and Inspection of Miscellaneous Commercial Sectors","927110 Space'
    ' Research and Technology","928110 National Security","928120 International'
    ' Affairs"]}]}}}]}]},"actions":[{"name":"append_recommendation","params":{"action":"PREFERRED"}},{"name":"increment_expected_value","params":{"by":"80000"}}],"scaleExpectedValue":0.1}'
)


def upgrade():
    conn = op.get_bind()
    conn.execute("SET statement_timeout TO '600 s';")

    for i in range(0, 3):
        conn.execute(f"""
            insert into recommendation_rule
            (id, created_at, description, definition, owner_organization_id, is_active, is_immutable)
            values (uuid_generate_v4(), now(),'REWARD BROKER AND OUT OF APPETITE (copy #{i+1})', '{rule_definition}', 10, false, false);
        """)


def downgrade():
    pass

"""Adding entity_mapped_data

Revision ID: 4212e1d78421
Revises: 93b7e1440f65
Create Date: 2023-01-23 23:13:51.836030+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4212e1d78421"
down_revision = "93b7e1440f65"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "processed_files", sa.Column("entity_mapped_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True)
    )


def downgrade():
    op.drop_column("processed_files", "entity_mapped_data")

"""Model fo Verification checks

Revision ID: 5f8b73522923
Revises: 48fc7c5aaf35
Create Date: 2023-06-15 08:47:30.614105+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5f8b73522923"
down_revision = "48fc7c5aaf35"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "verification_checks",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("status", sa.Enum("SUCCESS", "ERROR", "SKIPPED", name="resultstatus"), nullable=False),
        sa.Column("error_message", sa.String(), nullable=True),
        sa.Column("manual", sa.Boolean(), nullable=False),
        sa.Column("force_manual", sa.Boolean(), nullable=False),
        sa.Column("force_verify", sa.Boolean(), nullable=False),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("run_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_verification_checks_status"), "verification_checks", ["status"], unique=False)
    op.create_index(
        op.f("ix_verification_checks_submission_id"), "verification_checks", ["submission_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_verification_checks_submission_id"), table_name="verification_checks")
    op.drop_index(op.f("ix_verification_checks_status"), table_name="verification_checks")
    op.drop_table("verification_checks")

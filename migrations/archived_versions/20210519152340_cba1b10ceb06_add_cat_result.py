"""Add CAT_RESULT to filetype

Revision ID: cba1b10ceb06
Revises: 441ff14d4adf
Create Date: 2021-05-19 15:23:40.458547+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cba1b10ceb06"
down_revision = "441ff14d4adf"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'CAT_RESULT';""")


def downgrade():
    pass

"""Add constraints to metric config

Revision ID: d322b1df4a28
Revises: ce43d7b72e25
Create Date: 2021-06-28 12:00:18.422415+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d322b1df4a28"
down_revision = "ce43d7b72e25"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            DELETE FROM metric_config R1 USING metric_config R2 WHERE R1.created_at < R2.created_at 
            AND R1.display_name = R2.display_name AND R1.report_id = R2.report_id AND
            ((R1.submission_business_id is null AND R2.submission_business_id is null) OR (R1.submission_business_id = R2.submission_business_id));
        """)
    op.create_index(
        "metric_config_name_report_business_idx",
        "metric_config",
        ["display_name", "report_id", "submission_business_id"],
        unique=True,
        postgresql_where=sa.text("submission_business_id IS NOT NULL"),
    )
    op.create_index(
        "metric_config_name_report_idx",
        "metric_config",
        ["display_name", "report_id"],
        unique=True,
        postgresql_where=sa.text("submission_business_id IS NULL"),
    )


def downgrade():
    op.drop_index("metric_config_name_report_idx", table_name="metric_config")
    op.drop_index("metric_config_name_report_business_idx", table_name="metric_config")

"""capitalize card names

Revision ID: be98a101063e
Revises: 1cf7012aef54
Create Date: 2023-02-01 16:39:54.539511+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "be98a101063e"
down_revision = "1cf7012aef54"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards SET title = 'BASIC Scores' WHERE title = 'BASIC scores';
UPDATE mode_cards SET title = 'Breakdown of Operations' WHERE title = 'Breakdown of operations';
UPDATE mode_cards SET title = 'Breakdown of Work' WHERE title = 'Breakdown of work';
UPDATE mode_cards SET title = 'Crash Report Summary' WHERE title = 'Crash report summary';
UPDATE mode_cards SET title = 'Crime Score' WHERE title = 'Crime score';
UPDATE mode_cards SET title = 'Demolition Methods Used' WHERE title = 'Demolition methods used';
UPDATE mode_cards SET title = 'Description of Operations' WHERE title = 'Description of operations';
UPDATE mode_cards SET title = 'Fleet Operations' WHERE title = 'Fleet operations';
UPDATE mode_cards SET title = 'GC Highlights' WHERE title = 'GC highlights';
UPDATE mode_cards SET title = 'GC & Subcontractor Work Breakdown' WHERE title = 'GC & Subcontractor work breakdown';
UPDATE mode_cards SET title = 'Geotech File' WHERE title = 'Geotech file';
UPDATE mode_cards SET title = 'Geotech Snippet' WHERE title = 'Geotech snippet';
UPDATE mode_cards SET title = 'Hazardous Work Performed' WHERE title = 'Hazardous work performed';
UPDATE mode_cards SET title = 'Interior vs Exterior Work' WHERE title = 'Interior vs exterior work';
UPDATE mode_cards SET title = 'Location of Work' WHERE title = 'Location of work';
UPDATE mode_cards SET title = 'Other Named Insured' WHERE title = 'Other named insured';
UPDATE mode_cards SET title = 'Out of Service Rates' WHERE title = 'Out of service rates';
UPDATE mode_cards SET title = 'Permit Locations' WHERE title = 'Permit locations';
UPDATE mode_cards SET title = 'Pitch of Roofs Worked On' WHERE title = 'Pitch of roofs worked on';
UPDATE mode_cards SET title = 'Potential Exposures' WHERE title = 'Potential exposures';
UPDATE mode_cards SET title = 'Prior Insurance' WHERE title = 'Prior insurance';
UPDATE mode_cards SET title = 'Project Description' WHERE title = 'Project description';
UPDATE mode_cards SET title = 'Project Location' WHERE title = 'Project location';
UPDATE mode_cards SET title = 'Roofing Methods Used' WHERE title = 'Roofing methods used';
UPDATE mode_cards SET title = 'Rules Triggered' WHERE title = 'Rules triggered';
UPDATE mode_cards SET title = 'Vehicles in FMCSA Summary' WHERE title = 'Vehicles in FMCSA summary';
UPDATE mode_cards SET title = 'Vehicles in Submission Summary' WHERE title = 'Vehicles in submission summary';


UPDATE mode_rows SET title = 'Catastrophic Risks' WHERE title = 'Catastrophic risks';
UPDATE mode_rows SET title = 'Contractor Experience' WHERE title = 'Contractor experience';
UPDATE mode_rows SET title = 'Crime Score' WHERE title = 'Crime score';
UPDATE mode_rows SET title = 'Enforcement Cases' WHERE title = 'Enforcement cases';
UPDATE mode_rows SET title = 'Fleet Operations' WHERE title = 'Fleet operations';
UPDATE mode_rows SET title = 'Geotech Report' WHERE title = 'Geotech report';
UPDATE mode_rows SET title = 'Important Highlights' WHERE title = 'Important highlights';
UPDATE mode_rows SET title = 'Potential Exposures' WHERE title = 'Potential exposures';
UPDATE mode_rows SET title = 'Prior Insurance and Loss Runs' WHERE title = 'Prior insurance and loss runs';
UPDATE mode_rows SET title = 'Project Description' WHERE title = 'Project description';
UPDATE mode_rows SET title = 'Project Information' WHERE title = 'Project information';
UPDATE mode_rows SET title = 'Project Location' WHERE title = 'Project location';
UPDATE mode_rows SET title = 'Submission Information' WHERE title = 'Submission information';
UPDATE mode_rows SET title = 'Vehicles in Submission' WHERE title = 'Vehicles in submission';
UPDATE mode_rows SET title = 'Work Performed' WHERE title = 'Work performed';
    """)


def downgrade():
    pass

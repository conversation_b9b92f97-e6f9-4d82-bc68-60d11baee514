"""Create Org Metric Configs for QBE
Revision ID: b1c239a0773b
Revises: 5ffc8346ef2c
Create Date: 2021-08-31 18:07:02.379593+00:00
"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b1c239a0773b"
down_revision = "5ffc8346ef2c"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    create extension if not exists "uuid-ossp";
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Websites', 5, 200);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Flood', 5, 190);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime in Premises Risk', 5, 180);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Overall', 5, 170);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Assault', 5, 160);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Burglary', 5, 150);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Drug and Alcohol Deaths', 5, 140);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Larceny', 5, 130);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Motor Vehicle Theft', 5, 120);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Murder', 5, 110);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Rape', 5, 100);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Crime Score: Robbery', 5, 90);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Has Security Guards', 5, 80);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Employees', 5, 70);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'IS_SUBMITTED_DATA', 5, 60);
    INSERT into org_metric_config (id, metric_name, organization_id, weight) values (uuid_generate_v4(), 'Hail', 5, 50);
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
    DELETE from org_metric_config where metric_name='Websites' and organization_id=5 and weight=200;
    DELETE from org_metric_config where metric_name='Flood' and organization_id=5 and weight=190;
    DELETE from org_metric_config where metric_name='Crime in Premises Risk' and organization_id=5 and weight=180;
    DELETE from org_metric_config where metric_name='Crime Score: Overall' and organization_id=5 and weight=170;
    DELETE from org_metric_config where metric_name='Crime Score: Assault' and organization_id=5 and weight=160;
    DELETE from org_metric_config where metric_name='Crime Score: Burglary' and organization_id=5 and weight=150;
    DELETE from org_metric_config where metric_name='Crime Score: Drug and Alcohol Deaths' and organization_id=5 and weight=140;
    DELETE from org_metric_config where metric_name='Crime Score: Larceny' and organization_id=5 and weight=130;
    DELETE from org_metric_config where metric_name='Crime Score: Motor Vehicle Theft' and organization_id=5 and weight=120;
    DELETE from org_metric_config where metric_name='Crime Score: Murder' and organization_id=5 and weight=110;
    DELETE from org_metric_config where metric_name='Crime Score: Rape' and organization_id=5 and weight=100;
    DELETE from org_metric_config where metric_name='Crime Score: Robbery' and organization_id=5 and weight=90;
    DELETE from org_metric_config where metric_name='Has Security Guards' and organization_id=5 and weight=80;
    DELETE from org_metric_config where metric_name='Employees' and organization_id=5 and weight=70;
    DELETE from org_metric_config where metric_name='IS_SUBMITTED_DATA' and organization_id=5 and weight=60;
    DELETE from org_metric_config where metric_name='Hail' and organization_id=5 and weight=50;
    """)

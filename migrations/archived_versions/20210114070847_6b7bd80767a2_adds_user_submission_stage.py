"""Adds user_submission_stage

Revision ID: 6b7bd80767a2
Revises: 46dd7582ec2b
Create Date: 2021-01-14 07:08:47.126374-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6b7bd80767a2"
down_revision = "46dd7582ec2b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user_submission_stage",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("stage", postgresql.ENUM(name="submissionstage", create_type=False), nullable=False),
        sa.Column("dependent_on", postgresql.ARRAY(postgresql.INTEGER), nullable=True),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_user_submission_stage_submission_id"), "user_submission_stage", ["submission_id"], unique=False
    )
    op.create_index(op.f("ix_user_submission_stage_user_id"), "user_submission_stage", ["user_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_user_submission_stage_user_id"), table_name="user_submission_stage")
    op.drop_index(op.f("ix_user_submission_stage_submission_id"), table_name="user_submission_stage")
    op.drop_table("user_submission_stage")

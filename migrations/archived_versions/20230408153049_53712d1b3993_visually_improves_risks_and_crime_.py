"""Visually improves risks and crime scores cards

Revision ID: 53712d1b3993
Revises: d3448e9b8d7b
Create Date: 2023-04-08 15:30:49.947109+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "53712d1b3993"
down_revision = "d3448e9b8d7b"
branch_labels = None
depends_on = None

crime_score_props_with_processor = '{"facts": {"group": "CRIME_CARD"}, "processors": ["orderByCrimeScore"]}'
location_facts_risks_props_with_processor = (
    '{"facts": {"parentType": "PREMISES", "group": "LOCATION_RISKS"}, "processors": ["orderByRawValue"]}'
)
catastrophic_risks_with_new_processor = (
    '{"facts": {"parentType": "PREMISES", "group": "CATASTROPHIC_RISKS"}, "processors": ["orderByRawValue"],'
    ' "businessSelect": "contractorProject"}'
)


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
    UPDATE mode_cards SET props = '{crime_score_props_with_processor}' WHERE id = 'f21d265b-6db1-4a33-9f68-9b8654ba6933';
    UPDATE mode_cards SET props = '{location_facts_risks_props_with_processor}' WHERE id = 'dbf4c522-3b4f-4ccd-8dfd-7f0eb5c95061';
    UPDATE mode_cards SET props = '{catastrophic_risks_with_new_processor}' WHERE id = '60c0db7a-a527-4960-a4a9-eb1ec60a9dd0';
    """)


def downgrade():
    ...

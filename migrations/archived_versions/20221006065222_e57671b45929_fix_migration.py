"""Fix migration

Revision ID: e57671b45929
Revises: 9da6cdbe8ec3
Create Date: 2022-10-06 06:52:22.406904+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e57671b45929"
down_revision = "9da6cdbe8ec3"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
                    update submissions s
                    set agency_id = aid
                    from (
                             select brokerages.id as aid, submission_id
                             from submissions
                                      inner join users us on submissions.owner_id = us.id
                                      inner join user_fields_review ufr on submissions.id = ufr.submission_id
                                      inner join brokerages on lower(brokerages.name) = lower(trim(field_value)) and brokerages.organization_id = us.organization_id
                             where field_key = 'agent/agency') b
                    where b.submission_id = s.id;
                    """)


def downgrade():
    pass

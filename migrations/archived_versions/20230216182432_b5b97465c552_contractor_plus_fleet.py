"""contractor plus fleet

Revision ID: b5b97465c552
Revises: e7cfb2703188
Create Date: 2023-02-16 18:24:32.216828+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b5b97465c552"
down_revision = "e7cfb2703188"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
INSERT INTO modes (id, name) VALUES ('12f0d278-28a9-49db-9ec5-6a5ead9f3d46', 'Contractor - Practice mode + Fleet');
INSERT INTO mode_permissions (id, mode_id, organization_id, is_shared_across_organization)
VALUES('dfd8693c-969a-42d5-b070-6c71f7e5db2b', '12f0d278-28a9-49db-9ec5-6a5ead9f3d46', 3, true);

INSERT INTO mode_rows (id, position, elevation, is_collapsible, is_default_open, title, mode_id)
VALUES ('a8d155d5-905e-4c22-b498-40608f555241',10,1,false,null,null,'12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('e04e80eb-3dbb-455e-86a8-5ccafbcc47f6',20,null,null,null,'Recommendation','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('c170f74b-32ec-4316-966b-db1a278a5e4d',30,null,null,null,'Important Highlights','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('57e47934-ab6f-43ec-8f8b-c11f8c01d1ee',40,null,true,true,'Submission Information','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('63f76d3b-ffbd-4304-935a-882106fb036b',50,null,null,null,'General Contractor','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('69083410-d77f-4295-8eef-6605484b8e64',60,null,null,null,'Violations','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('4d51805b-f6ba-4db9-b60b-008d77c9fff4',70,null,null,null,'News','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('28c8f5dc-61a9-42a3-b058-e861a3a40b36',80,null,null,null,'Legal Filings','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('21dea465-14fe-47f6-9847-cd4b47831e27',90,null,null,null,'Work Performed','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('77b20248-2562-4781-b4a9-192505414056',100,null,null,null,'Potential Exposures','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('b7e89b02-75a0-4f6e-9b66-0e2603ebbdfe',110,null,null,null,'Images','12f0d278-28a9-49db-9ec5-6a5ead9f3d46'),
('3d4d4ea6-e860-497d-8daa-17a55710d920',120,null,null,null,'Loss Runs','12f0d278-28a9-49db-9ec5-6a5ead9f3d46');


INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES ('d8258797-5bfa-49bb-980e-8744644658ec', 'a8d155d5-905e-4c22-b498-40608f555241',0, 12, null),
       ('bceb6eae-fae9-468c-bc34-d01cdf54689d','e04e80eb-3dbb-455e-86a8-5ccafbcc47f6', 0, 12, null),
       ('bafe3bad-e037-4a90-b1ab-8607e19cb772','c170f74b-32ec-4316-966b-db1a278a5e4d', 0, 12, null),
       ('4b386db1-2174-4198-8520-82a45237a80e','57e47934-ab6f-43ec-8f8b-c11f8c01d1ee', 0, 12, null),
       ('b0ec659a-995f-41e2-8e97-e987421a1334','63f76d3b-ffbd-4304-935a-882106fb036b', 0, 12, null),
       ('e9578a7f-08db-43e7-90b5-79012856106e','69083410-d77f-4295-8eef-6605484b8e64', 0, 12, null),
       ('42668fc2-fb4a-440e-bf7e-ee287804e806','4d51805b-f6ba-4db9-b60b-008d77c9fff4', 0, 12, 'News'),
       ('723f2335-7d1c-40cd-a578-367d052cb43b','28c8f5dc-61a9-42a3-b058-e861a3a40b36', 0, 12, 'Legal Fillings'),
       ('00e31ce3-75b3-47a2-b8bd-e5ed4a6eca7d','21dea465-14fe-47f6-9847-cd4b47831e27', 0, 12, null),
       ('59665401-0e6c-4853-a042-aaf221412690','77b20248-2562-4781-b4a9-192505414056', 0, 12, null),
       ('ca55d31a-75b3-4a4e-b6ad-2b6eb9b3638f','b7e89b02-75a0-4f6e-9b66-0e2603ebbdfe', 0, 12, null),
       ('0a3710c6-ee91-4fec-83cb-a041e0eae738','3d4d4ea6-e860-497d-8daa-17a55710d920', 0, 12, 'Loss Runs');

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('fbd7943d-7a1a-41e8-a621-9716ab5dd8b5','d8258797-5bfa-49bb-980e-8744644658ec',null,0,'CONTRACTOR_HEADER','contractor-header',null),
('5aaa3355-ff24-4df3-9977-c0c668d64f5d','bceb6eae-fae9-468c-bc34-d01cdf54689d','Rules Triggered',10,'RECOMMENDATIONS_CARD','recommendations-rules',null),
('f5621019-d81c-4def-9b2e-8cd55e746cb1','bafe3bad-e037-4a90-b1ab-8607e19cb772','GC Highlights',10,'IMPORTANT_HIGHLIGHTS_CARD','gc-highlights','{
  "columns": 4,
  "highlights": [
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "Legal filings",
      "cardId": "f9f94a42-f555-47b8-946c-9be1b6b743da",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "LEGAL_FILING"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to legal filings"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "Osha violations",
      "cardId": "974e754f-553c-4da8-9356-e83a9d80e78b",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "OSHA_VIOLATION",
          "businessSelect": "generalContractorAndDuplicates"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to Osha violations"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "EPA Inspections ",
      "cardId": "dd5fa1a1-5d4f-45fb-ac68-5b99d3f62c1d",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "EPA_INSPECTION",
          "businessSelect": "generalContractorAndDuplicates"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to EPA Inspections"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "High-risk exposures",
      "cardId": "fc9c391b-b490-4589-bae5-5fb15b48bd09",
      "source": {
        "mapper": "numberOfPositiveFacts",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "FACT",
          "factSubtypes": [
            "PROJECT_USE_OF_EIFS",
            "PROJECT_SCAFFOLDING",
            "PROJECT_MOLD_REMOVAL",
            "PROJECT_ROOF_WORK",
            "PROJECT_CRANE_WORK",
            "PROJECT_DEMOLITION_WORK",
            "PROJECT_BLASTING_WORK",
            "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
            "PROJECT_EXCAVATION_WORK",
            "PROJECT_BELOW_GRADE",
            "PROJECT_DEPTH_OF_WORK",
            "PROJECT_HEIGHT_IN_FT",
            "PROJECT_HEIGHT_IN_STORIES",
            "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT",
            "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
          ],
          "businessSelect": "generalContractor"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to High-risk exposures"
    }
  ]
}
'),

('a4095855-df95-4e0b-931e-fb36e18838a9','4b386db1-2174-4198-8520-82a45237a80e','Email',10,'SUBMISSION_INFO_EMAIL','submission-info-email','{}'),
('1bb82c8c-0513-4605-8b68-4a0013a3e6c0','4b386db1-2174-4198-8520-82a45237a80e','Support',20,'SUBMISSION_INFO_SUPPORT','submission-support',null),
('1032410c-e04d-490b-8d74-007a30310088','4b386db1-2174-4198-8520-82a45237a80e','Date',30,'SUBMISSION_INFO_DATE','submission-date',null),
('be9e722e-69c1-4948-bb75-2d31d1c51961','4b386db1-2174-4198-8520-82a45237a80e','Assigned Underwriters',40,'SUBMISSION_INFO_ASSIGNEE','submission-assignee',null),
('086850b7-677d-4e7d-861d-ae72030734c6','4b386db1-2174-4198-8520-82a45237a80e','Description of Operations',50,'SUBMISSION_INFO_DESCRIPTION_OF_OPERATIONS','description-of-operations',null),
('966786b3-9c79-491c-aa59-2ca9e19d7b94','4b386db1-2174-4198-8520-82a45237a80e','NAICS Codes',60,'SUBMISSION_INFO_NAICS','naics-codes',null),
('48a44956-d267-4ed4-917b-7fe5358ea5ac','4b386db1-2174-4198-8520-82a45237a80e','Coverage',70,'SUBMISSION_INFO_COVERAGE','coverage',null),
('e63878f2-f0b0-4074-a940-5ff8b172279f','4b386db1-2174-4198-8520-82a45237a80e','Broker / Agent & Brokerage / Agency',80,'SUBMISSION_INFO_BROKER_AGENT','broker-agent',null),
('9b2237d0-f808-4cc7-8c0e-e6b9e826b4c9','4b386db1-2174-4198-8520-82a45237a80e','ID',90,'SUBMISSION_INFO_ID','id',null),
('ab73b481-fa02-47b1-96c0-e8a0f5213d2d','4b386db1-2174-4198-8520-82a45237a80e','Notes',100,'SUBMISSION_INFO_NOTES','notes',null),
('7f933a04-fd36-4180-b91c-1ed43c8e4eb3','4b386db1-2174-4198-8520-82a45237a80e','Other Named Insured',110,'SUBMISSION_BUSINESSES','gc-other-named-insured','{"types": ["OTHER_INSURED"]}'),
('fdecf0e8-82a8-47df-9626-b0c6b4463b63','4b386db1-2174-4198-8520-82a45237a80e','Bundled Submissions',120,'SUBMISSION_INFO_BUNDLE','submission-bundle',null),

('66438968-8214-45ad-8d3a-8711ceb91293','b0ec659a-995f-41e2-8e97-e987421a1334','General Contractor',10,'FACTS','general-contractor','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "YEARS_IN_BUSINESS"}, {"id": "KNOWN_PERSONNEL"}, {"id": "BBB_RATING"}]}}}'),
('ac89a32c-d632-4f95-83b7-145fc4f2c1f9','b0ec659a-995f-41e2-8e97-e987421a1334','Financials',20,'FACTS','gc-financials','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "TOTAL_SALES"}, {"id": "PAYROLL"}]}}}'),
('9bad610c-0661-4b53-9db7-35e3c5843d56','b0ec659a-995f-41e2-8e97-e987421a1334','Work Locations',30,'MAP_CARD','permit-locations','{"height": 416, "parentType": "BUSINESS", "documentType": "PERMIT", "markerSelect": "positionFromPremises", "businessSelect": "generalContractor"}'),
('5abf824a-446f-4d99-855c-0558fdfd95ff','b0ec659a-995f-41e2-8e97-e987421a1334',null,40,'TABLE','gc-permits','{"type": "PERMIT"}'),
('bbec3a66-4ea8-4129-96c8-0841c5226ea2','b0ec659a-995f-41e2-8e97-e987421a1334','Licenses',60,'DOCUMENTS','gc-licenses','{"height": 500, "documentType": "LICENSE", "businessEntityType": "GENERAL_CONTRACTOR"}'),
('eb49d961-8e64-47c5-aae9-ca30310d7f7e','b0ec659a-995f-41e2-8e97-e987421a1334','Other insurance coverage',70,'FACT_BLOCKS','gc-other-insurance-coverage','{"height": 500, "factSubtypeIds": ["INSURANCE_INFORMATION"], "businessEntityType": "GENERAL_CONTRACTOR"}'),

('118cd60d-12c0-4fd8-8779-8ef01cff6efa','e9578a7f-08db-43e7-90b5-79012856106e',null,0,'OSHA_VIOLATION','osha-violations','{"parentType": "BUSINESS", "businessSelect": "generalContractorAndDuplicates"}'),
('b4537251-2665-4224-96e0-3a750e18f095','e9578a7f-08db-43e7-90b5-79012856106e',null,10,'EPA_INSPECTION','epa-inspections','{"parentType": "BUSINESS", "businessSelect": "generalContractorAndDuplicates"}'),


('451d370b-a8e1-4ca5-8110-23519c750631','42668fc2-fb4a-440e-bf7e-ee287804e806','News',0,'NEWS_CARD','news-card','{"height": 50}'),

('634fff2e-def0-449a-9e4d-5c4b3657e96d','723f2335-7d1c-40cd-a578-367d052cb43b','Legal Filings',0,'LEGAL_FILINGS_CARD','legal-filings-card','{"height": 50}'),

('e621079c-1cec-409c-922b-6e9061476abb','00e31ce3-75b3-47a2-b8bd-e5ed4a6eca7d','Breakdown of Work',0,'FACTS','breakdown-of-work','{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        { "id": "PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL" },
        { "id": "PROJECT_PERCENTAGE_OF_WORK_PUBLIC" },
        { "id": "PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL" },
        { "id": "PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL" }
      ]
    }
  }
}
'),
('cc8ac5fc-f75d-406c-bb6f-0baeec599391','00e31ce3-75b3-47a2-b8bd-e5ed4a6eca7d','Breakdown of Operations',1,'FACTS','breakdown-of-operations','{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        { "id": "PROJECT_NEW_CONSTRUCTION" },
        { "id": "PROJECT_REMODELING_OR_REPAIR" }
      ]
    }
  }
}
'),
('cd195b93-f0fa-4171-b555-046e06eeda4b','00e31ce3-75b3-47a2-b8bd-e5ed4a6eca7d','GC & Subcontractor Work Breakdown',2,'FACTS','gc-and-subcontractor-work-breakdown','{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        { "id": "PROJECT_SUBCONTRACTORS_USED" },
        { "id": "PROJECT_PERCENTAGE_OF_WORK_AS_GC" },
        { "id": "PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR" }
      ]
    }
  }
}
'),
('6aafe327-eab9-4c80-b0f3-7f520af19219','00e31ce3-75b3-47a2-b8bd-e5ed4a6eca7d','Interior vs Exterior Work',3,'FACTS','interior-vs-exterior-work','{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        { "id": "PROJECT_PERCENTAGE_OF_EXTERIOR_WORK" },
        { "id": "PROJECT_PERCENTAGE_OF_INTERIOR_WORK" }
      ]
    }
  }
}
'),

('6ad4689c-97a3-423a-b2bf-9b9f667ed196','59665401-0e6c-4853-a042-aaf221412690','Hazardous Work Performed',10,'FACTS','hazardous-work-performed','{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"]
        },
        { "id": "PROJECT_SCAFFOLDING", "parentSubtypeIds": ["SCAFFOLDING"] },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"]
        },
        { "id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"] },
        { "id": "PROJECT_CRANE_WORK", "parentSubtypeIds": ["CRANES"] },
        { "id": "PROJECT_DEMOLITION_WORK", "parentSubtypeIds": ["DEMOLITION"] },
        { "parentSubtypeIds": ["BLASTING_OPERATIONS"] },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"]
        },
        { "id": "PROJECT_EXCAVATION_WORK" },
        { "id": "PROJECT_BELOW_GRADE" },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"]
        },
        { "id": "PROJECT_HEIGHT_IN_FT", "hiddenWhenNegative": true },
        { "id": "PROJECT_HEIGHT_IN_STORIES", "hiddenWhenNegative": true },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT",
          "hiddenWhenNegative": true
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES",
          "hiddenWhenNegative": true
        }
      ]
    },
    "ELECTRICAL_CONTRACTOR": {
      "factSubtypes": [
        { "id": "PROJECT_ELECTRICAL_WORK" },
        { "id": "PROJECT_TRAFFIC_LIGHTING_SIGNALS_WORK" },
        { "id": "PROJECT_AIRPORT_WORK" },
        { "id": "PROJECT_BELOW_GRADE" },
        { "id": "PROJECT_DEPTH_OF_WORK" },
        { "id": "PROJECT_HEIGHT_IN_FT" },
        { "id": "PROJECT_HEIGHT_IN_STORIES" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES" }
      ]
    },
    "SITE_PREPARATION_PRACTICE": {
      "factSubtypes": [
        {
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_EXCAVATION_WORK", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_GRADING_WORK" },
        { "id": "PROJECT_PILE_DRIVING_WORK" },
        { "id": "PROJECT_PAVING_WORK" },
        { "id": "PROJECT_DRILLING_WORK" },
        {
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_BELOW_GRADE", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"],
          "hiddenWhenNegative": true
        }
      ]
    },
    "HVAC_AND_PLUMBING_PRACTICE": {
      "factSubtypes": [
        {
          "id": "PROJECT_HVAC_WORK",
          "parentSubtypeIds": ["HVAC_INSTALLATION_AND_SERVICE"]
        },
        { "id": "PROJECT_PLUMBING_WORK", "parentSubtypeIds": ["PLUMBING"] },
        { "id": "PROJECT_BOILER_WORK" },
        { "id": "PROJECT_ELECTRICAL_WORK" },
        { "id": "PROJECT_GAS_LINE_WORK" },
        { "id": "PROJECT_SPRINKLER_SYSTEM_WORK" },
        { "id": "PROJECT_SEWER_WORK" },
        { "id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"] },
        { "id": "PROJECT_SCAFFOLDING", "parentSubtypeIds": ["SCAFFOLDING"] },
        { "id": "PROJECT_EXCAVATION_WORK" },
        { "id": "PROJECT_HEIGHT_IN_FT" },
        { "id": "PROJECT_HEIGHT_IN_STORIES" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES" }
      ]
    },
    "ROOFING_AND_SIDING_PRACTICE": {
      "factSubtypes": [
        { "id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"] },
        { "id": "PROJECT_SIDING_WORK" },
        { "id": "PROJECT_INSULATION_WORK" },
        { "id": "PROJECT_WATER_PROOFING_WORK" },
        { "id": "PROJECT_HEIGHT_IN_FT", "hiddenWhenNegative": true },
        { "id": "PROJECT_HEIGHT_IN_STORIES", "hiddenWhenNegative": true },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT",
          "hiddenWhenNegative": true
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES",
          "hiddenWhenNegative": true
        }
      ]
    }
  }
}
'),
('fc0f2c91-364f-4176-8759-928db7978a49','59665401-0e6c-4853-a042-aaf221412690','Other',20,'FACTS','other','{
  "collapsible": true,
  "generalContractor": {
    "DEFAULT": { "factSubtypes": [] },
    "ELECTRICAL_CONTRACTOR": {
      "factSubtypes": [
        { "id": "PROJECT_ALARM_SYSTEMS_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_SOLAR_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_TOWER_ANTENNAS_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_FIBER_OPTICS_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_EXCAVATION_WORK", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_ROOF_WORK",
          "parentSubtypeIds": ["ROOFING_SERVICE"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"],
          "hiddenWhenNegative": true
        }
      ]
    },
    "SITE_PREPARATION_PRACTICE": {
      "factSubtypes": [
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_HEIGHT_IN_FT", "hiddenWhenNegative": true },
        { "id": "PROJECT_HEIGHT_IN_STORIES", "hiddenWhenNegative": true },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT",
          "hiddenWhenNegative": true
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES",
          "hiddenWhenNegative": true
        }
      ]
    },
    "HVAC_AND_PLUMBING_PRACTICE": {
      "factSubtypes": [
        { "id": "PROJECT_SHEET_METAL_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_WELDING_WORK", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_BELOW_GRADE", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"],
          "hiddenWhenNegative": true
        }
      ]
    },
    "ROOFING_AND_SIDING_PRACTICE": {
      "factSubtypes": [
        { "id": "PROJECT_RAIN_GUTTER_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_SHEET_METAL_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_CARPENTRY_WORK", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_EXCAVATION_WORK", "hiddenWhenNegative": true },
        { "id": "PROJECT_BELOW_GRADE", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"],
          "hiddenWhenNegative": true
        }
      ]
    }
  }
}
'),

('5d986096-5617-4b6b-9e6e-2eb1a65a286c','ca55d31a-75b3-4a4e-b6ad-2b6eb9b3638f','Images',10,'IMAGES_CARD','images','{"parentType": "BUSINESS", "businessSelect": "generalContractor"}'),

('a4ec612d-cfc8-4bbc-a8d4-4c8351fa65b7','0a3710c6-ee91-4fec-83cb-a041e0eae738','Loss run files',0,'LOSS_FILES_CARD','loss-files-card',null),
('00425978-a728-4fba-b2d8-6b10772ae8c2','0a3710c6-ee91-4fec-83cb-a041e0eae738','Summary of losses',10,'LOSS_SUMMARIES_CARD','loss-summaries-card',null),
('d80bae4f-0c2c-40f2-b29a-b8444aca1e2e','0a3710c6-ee91-4fec-83cb-a041e0eae738','',20,'LOSS_RUNS_CARD','loss-runs-card', '{"height": 50}');

-- FLEET
-- header
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('84eafe69-8dfa-489e-b764-8940cb9183dd','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',130,null,null,null,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('9023ec07-ee43-4169-8a51-1d916fc03085','84eafe69-8dfa-489e-b764-8940cb9183dd',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('b21a0e17-c551-4221-91b4-f43a640438cb','9023ec07-ee43-4169-8a51-1d916fc03085',null,0,'FLEET_HEADER','fleet_header',null);

-- Vehicles in submission
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('7adf4030-f7d5-49bc-994c-5bc38ab457eb','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',140,null,null,null,'Vehicles in Submission');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('e44d17e5-4fad-4f16-87ae-3cb337305be8','7adf4030-f7d5-49bc-994c-5bc38ab457eb',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('e10956bd-74ca-4d19-80e0-e4a5c9b38c0b','e44d17e5-4fad-4f16-87ae-3cb337305be8','Summary',10,'VEHICLES_IN_SUBMISSION_SUMMARY','vehicles-in-submission-summary',null);
---
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('971718e9-1c67-4a3b-bbc0-8715507ab43c','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',145,null,false,true,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('29537500-9eab-4abb-be35-d6e532d9c35d','971718e9-1c67-4a3b-bbc0-8715507ab43c',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('bda01ad2-8b24-4bd7-8e9b-6d9b35692eea','29537500-9eab-4abb-be35-d6e532d9c35d',null,10,'VEHICLES_IN_SUBMISSION_TABLE','vehicles-table-Submission',null);


-- Drivers in submission
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('86dee5c4-bcb1-4851-9e8d-b1f287c0f530','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',150,null,null,null,'Drivers in Submission');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('4f51a60c-64c5-4dc0-bb25-93e11b6034d3','86dee5c4-bcb1-4851-9e8d-b1f287c0f530',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('c180a3c6-fae4-4532-85f3-ea1880711841','4f51a60c-64c5-4dc0-bb25-93e11b6034d3','Summary',10,'DRIVERS_IN_SUBMISSION_SUMMARY','drivers-in-submission-summary',null);
---
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('561bc316-a697-4b96-b494-1faa054954e9','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',155,null,false,true,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('917aeb5d-c463-44f4-a34e-238c9b49d9bd','561bc316-a697-4b96-b494-1faa054954e9',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('4c40303b-f4b5-4b24-8e2a-965da1ad19e2','917aeb5d-c463-44f4-a34e-238c9b49d9bd','',10,'DRIVERS_IN_SUBMISSION_TABLE','drivers-table-submission',null);


-- Fleet operations
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('351db445-6b34-48b6-a065-97ed23e4a21f','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',160,null,null,null,'Fleet Operations');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('767fdb7c-e4d9-4b4e-88be-eae6f2365f86','351db445-6b34-48b6-a065-97ed23e4a21f',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('c7d79db4-1931-4ee6-8979-aab51d281d31','767fdb7c-e4d9-4b4e-88be-eae6f2365f86','Fleet Operations',10,'FACTS','fleet-operations','{"facts": {"parentType": "SUBMISSION", "factSubtypeIds": ["CARGO_CARRIED", "FLEET_OPERATION_STATES", "CARRIER_OPERATION_STATUS", "CARRIER_OPERATION", "OPERATION_CLASSIFICATION", "TRANSPORTATION_SAFETY_RATING", "TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS", "NUMBER_OF_VEHICLES", "NUMBER_OF_REGISTERED_DRIVERS", "TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION", "TRANSPORTATION_HAS_CONSUMER_COMPLAINTS", "IS_NEW_ENTRANT", "TRANSPORTATION_RISKY_NEW_ENTRANT"]}, "hiddenWhenAllFactsNegative": true}'),
('ae6026fa-70b7-422f-a762-bb909ef8f2dd','767fdb7c-e4d9-4b4e-88be-eae6f2365f86','Out of Service Rates',20,'OUT_OF_SERVICE_CARD','fleet-operations-oosr', null),
('e8495bea-c51f-4037-96c3-a6abe68184d5','767fdb7c-e4d9-4b4e-88be-eae6f2365f86','BASIC Scores',30,'FACTS','fleet-operations-basic-scores','{"facts": {"parentType": "SUBMISSION", "factSubtypeIds": ["TRANSPORTATION_BASIC_BENCHMARK_UNSAFE_DRIVING", "TRANSPORTATION_BASIC_BENCHMARK_HOS_COMPLIANCE", "TRANSPORTATION_BASIC_BENCHMARK_VEHICLE_MAINTENANCE", "TRANSPORTATION_BASIC_BENCHMARK_DRUGS_ALCOHOL", "TRANSPORTATION_BASIC_BENCHMARK_DRIVER_FITNESS"]}, "hiddenWhenAllFactsNegative": true}');

-- crashes
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('037c566f-d95e-43c9-b760-29ff80e82a3e','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',170,null,null,null,'Crashes');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('fc61f915-1832-446c-bc00-b7fef373cf13','037c566f-d95e-43c9-b760-29ff80e82a3e',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('458fdca0-f5ae-4dee-b5b6-938a7aced3e8','fc61f915-1832-446c-bc00-b7fef373cf13','Filter By',0,'ENTITY_FILTER', 'crashes-entity-filter','{
  "entityFilterKey": "crashes"
}');

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('f5b3df55-b155-48e8-9e10-dde9f28d99a3','fc61f915-1832-446c-bc00-b7fef373cf13','Crash Report Summary',10,'CRASH_REPORT_SUMMARY','crash-report-summary','{"entityFilterKey": "crashes"}'),
('7e0ab0e0-9751-47d0-b996-636c44899be9','fc61f915-1832-446c-bc00-b7fef373cf13',null,20,'TABLE','crash-list', '{"type": "CRASHES", "entityFilterKey": "crashes"}'),
('336f6e0c-f2e6-4a79-8594-6840694acced','fc61f915-1832-446c-bc00-b7fef373cf13',null,30,'CRASH_MAP','crash-map','{"entityFilterKey": "crashes"}');

-- enforcement cases
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('5ced9fe3-7ad7-4a01-beb5-efcd30f02a77','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',180,null,null,null,'Enforcement Cases');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('483af329-ae4f-4f84-a12d-6aeb5288c8ae','5ced9fe3-7ad7-4a01-beb5-efcd30f02a77',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('ce33f00d-7d19-438c-b625-bfead56ffb16','483af329-ae4f-4f84-a12d-6aeb5288c8ae','Filter By',0,'ENTITY_FILTER', 'enforcement-cases-entity-filter','{
  "entityFilterKey": "enforcementCases"
}');

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('c74bb5c2-ff7a-4278-843d-52567fac9622','483af329-ae4f-4f84-a12d-6aeb5288c8ae',null,10,'TABLE','crash-enforcement-cases','{"type": "ENFORCEMENT_CASES", "entityFilterKey": "enforcementCases"}');

-- Vehicles in fmcsa

INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('8022bc34-8151-4f04-98d2-84fda9cf2383','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',190,null,null,null,'Vehicles in FMCSA');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('7e0a8803-5de7-499d-b9a3-1342461c3956','8022bc34-8151-4f04-98d2-84fda9cf2383',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('079e050f-dbdc-4b50-94e0-8fe765ad13de','7e0a8803-5de7-499d-b9a3-1342461c3956','Summary',10,'VEHICLES_IN_FMCSA_SUMMARY','vehicles-in-fmcsa-summary',null);
---
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('df4f8418-9004-4082-b549-3a92977e28ba','12f0d278-28a9-49db-9ec5-6a5ead9f3d46',195,null,false,true,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('b9892b05-b73c-4f12-9a5b-ff7b201d263d','df4f8418-9004-4082-b549-3a92977e28ba',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('6fae9917-ae8e-48c2-87d5-b08d1651018f','b9892b05-b73c-4f12-9a5b-ff7b201d263d','',10,'VEHICLES_IN_FMCSA_TABLE','vehicles-table-fmcsa',null);


------------------------------------------------


INSERT INTO modes (id, name) VALUES ('0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa', 'Contractor - Project mode + Fleet');
INSERT INTO mode_permissions (id, mode_id, organization_id, is_shared_across_organization)
VALUES('a805cfa0-21ff-40b5-80e3-460ddd33a563', '0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa', 3, true);

INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('33deef36-8eaf-40b4-b4e8-b2f2834ea195','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',10,1,false,null,null),
('1827dbe9-3a34-4e44-acde-320a15c8af90','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',20,null,null,null,'Recommendation'),
('1668de71-fbf9-4892-acac-e7ca3e03bfb4','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',30,null,null,null,'Important Highlights'),
('6a5516f7-68e2-4a10-bcd2-a49f706fe064','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',40,null,true,true,'Submission Information'),
('27fc2aaa-1aaf-4a44-b7bd-b1fef4dcc371','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',45,null,null,null,'Loss Runs'),
('fc8a33da-4bd5-4851-9820-ee95362a5a34','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',50,null,null,null,'General Contractor'),
('2ad0ea86-8b60-47af-8760-8b5f4bb92c45','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',60,null,null,null,'Violations'),
('4785f835-e2b6-4c1f-8c72-a50a8cfa192a','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',70,null,null,null,'News'),
('252e8982-1c69-46d0-bbce-e051aa4eb73e','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',80,null,null,null,'Legal Filings'),
('7466da94-323d-4717-8b34-484946fbfe23','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',90,null,null,null,'Contractor Experience'),
('32e11d7f-1b06-436d-ab12-aabd178e4195','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',100,null,null,null,'Work Performed'),
('2e162c5e-d2b3-43c5-a249-aa9d6c3b391e','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',110,null,null,null,'Images'),
('1c23442a-b7e7-40bf-845f-9361d84d37a5','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',120,null,null,null,null),
('c1f2b097-82b1-4616-aed9-75114f00cd9b','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',130,null,null,null,'Project Description'),
('63e408bb-9087-4811-a8e6-21fe282da849','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',140,null,null,null,'Project Location'),
('ce69d78c-13b3-4bc8-b918-8f51d0fb27fd','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',150,null,null,null,'Catastrophic Risks'),
('57ca149d-1bb2-4d12-899d-df7d6d4b4788','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',160,null,null,null,'Project Information'),
('9e693f9d-1b2c-4b56-828f-30ce061aeb38','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',170,null,null,null,'Geotech Report'),
('15adadba-fe76-4a2a-9f7b-d5b9d63c7e2f','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',180,null,null,null,'Crime Score'),
('20efd68e-5d38-465d-b692-50a32e325a68','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',190,null,null,null,'Images');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('a86fc80d-8d93-406b-b900-8b6cbebfb6e6','33deef36-8eaf-40b4-b4e8-b2f2834ea195',0,12,null),
('42b882e3-83ae-4007-b0d1-d2c047c766ad','1827dbe9-3a34-4e44-acde-320a15c8af90',0,12,null),
('cd11978a-0ca5-4ef5-b897-0e2bb6575cc3','1668de71-fbf9-4892-acac-e7ca3e03bfb4',0,12,null),
('dcbc533c-f4f0-48c9-aec3-62a2102891c7','6a5516f7-68e2-4a10-bcd2-a49f706fe064',0,12,null),
('dead5a8f-c44b-42f0-978e-d3fcc8050e8d','27fc2aaa-1aaf-4a44-b7bd-b1fef4dcc371',0,12,'Loss Runs'),
('81fa4ebc-14ca-4464-ad5b-828d8eda976a','fc8a33da-4bd5-4851-9820-ee95362a5a34',0,12,null),
('90a1b1c4-b8c7-4831-a8a3-d1cc2a4b0c89','2ad0ea86-8b60-47af-8760-8b5f4bb92c45',0,12,null),
('23e7a40a-4d8b-4d7f-a7ba-9d8eb4021d4d','4785f835-e2b6-4c1f-8c72-a50a8cfa192a',0,12,'News'),
('b3fec88e-8735-4222-b334-a73e845f8433','252e8982-1c69-46d0-bbce-e051aa4eb73e',0,12,'Legal Filings'),
('9ab4acc6-c5bb-4612-8bd2-c1c173b71b99','7466da94-323d-4717-8b34-484946fbfe23',0,12,null),
('7d09de5c-1985-491a-b912-5d719b545fc0','32e11d7f-1b06-436d-ab12-aabd178e4195',0,12,null),
('7307b39a-c269-41e1-93f4-91e6ca87a105','2e162c5e-d2b3-43c5-a249-aa9d6c3b391e',0,12,null),
('3a01769c-c552-4edb-b92c-7f59cfe1b9dd','1c23442a-b7e7-40bf-845f-9361d84d37a5',0,12,null),
('caac5ce4-500d-4532-ae22-96cf2c5ff433','c1f2b097-82b1-4616-aed9-75114f00cd9b',0,12,null),
('13339e35-ca2b-4620-887e-f2eb57dd8645','63e408bb-9087-4811-a8e6-21fe282da849',0,12,null),
('f2de6acc-2141-411c-9b8b-f20c8099b239','ce69d78c-13b3-4bc8-b918-8f51d0fb27fd',0,12,null),
('3c115e3c-3e5a-4298-b6af-64f3a7f71927','57ca149d-1bb2-4d12-899d-df7d6d4b4788',0,12,null),
('afd3ec0b-e46a-45c6-86df-d1bf56739f91','9e693f9d-1b2c-4b56-828f-30ce061aeb38',0,12,null),

('0323c88f-7323-441f-902a-5c64960aa1a0','15adadba-fe76-4a2a-9f7b-d5b9d63c7e2f',0,5,null),
('22a30030-c328-4f36-8a45-1d89cf50fdaf','15adadba-fe76-4a2a-9f7b-d5b9d63c7e2f',6,6,null),

('f857bd1c-611b-464b-a87b-5aed631b2b16','20efd68e-5d38-465d-b692-50a32e325a68',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('91139522-4a1a-41d9-a880-9e5127c120fa','a86fc80d-8d93-406b-b900-8b6cbebfb6e6',null,0,'CONTRACTOR_HEADER','contractor-header',null),
('02021222-d79e-4c93-8347-914cea1d13f3','42b882e3-83ae-4007-b0d1-d2c047c766ad','Rules Triggered',10,'RECOMMENDATIONS_CARD','recommendations-rules',null),
('a529e1fc-b3c4-4837-8ebf-bebb5b6dfd37','cd11978a-0ca5-4ef5-b897-0e2bb6575cc3','GC Highlights',10,'IMPORTANT_HIGHLIGHTS_CARD','gc-highlights', '{"columns": 4, "highlights": [{"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "Legal filings", "cardId": "0b596b51-6f73-41ef-b55a-961c05f30d0d", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "LEGAL_FILING"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to legal filings"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "Osha violations", "cardId": "11b3e3d4-3124-41b2-9e19-7bf98ce8aced", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION", "businessSelect": "generalContractorAndDuplicates"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to Osha violations"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "EPA Inspections", "cardId": "82ba32a1-052f-4621-9ae8-143ac7915bb0", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "EPA_INSPECTION", "businessSelect": "generalContractorAndDuplicates"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to EPA Inspections"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "High-risk exposures", "cardId": "91da8d98-fdbb-4816-93e7-18ccb4951054", "source": {"mapper": "numberOfPositiveFacts", "source": {"parentType": "BUSINESS", "sourceType": "FACT", "factSubtypes": ["PROJECT_USE_OF_EIFS", "PROJECT_SCAFFOLDING", "PROJECT_MOLD_REMOVAL", "PROJECT_ROOF_WORK", "PROJECT_CRANE_WORK", "PROJECT_DEMOLITION_WORK", "PROJECT_BLASTING_WORK", "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL", "PROJECT_EXCAVATION_WORK", "PROJECT_BELOW_GRADE", "PROJECT_DEPTH_OF_WORK", "PROJECT_HEIGHT_IN_FT", "PROJECT_HEIGHT_IN_STORIES", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"], "businessSelect": "contractorProject"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to High-risk exposures"}]}'),

('ea13c06f-abc3-441c-a0eb-1e64d1f8dec9','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Email',10,'SUBMISSION_INFO_EMAIL','submission-info-email','{}'),
('698877e2-9845-4349-ac9f-9d59ec358a62','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Support',20,'SUBMISSION_INFO_SUPPORT','submission-support',null),
('8c253e64-059c-495f-a3b6-b3dc9d05caf9','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Date',30,'SUBMISSION_INFO_DATE','submission-date',null),
('3023be08-7319-433f-b17d-f6a5ee2a6dfa','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Assigned Underwriters',40,'SUBMISSION_INFO_ASSIGNEE','submission-assignee',null),
('cae8a7a1-7c00-4fba-980f-7c4f619e2f7e','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Description of Operations',50,'SUBMISSION_INFO_DESCRIPTION_OF_OPERATIONS','description-of-operations',null),
('7ec03833-8a2a-498f-941b-6719a0d7394b','dcbc533c-f4f0-48c9-aec3-62a2102891c7','NAICS Codes',60,'SUBMISSION_INFO_NAICS','naics-codes',null),
('a0b34a0b-fc50-4e52-859d-9633271e5a2c','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Coverage',70,'SUBMISSION_INFO_COVERAGE','coverage',null),
('314fd307-5d3e-4e0b-a56c-273ce21efba5','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Broker / Agent & Brokerage / Agency',80,'SUBMISSION_INFO_BROKER_AGENT','broker-agent',null),
('884fba83-eef8-4180-bcd0-0fad5cfab34e','dcbc533c-f4f0-48c9-aec3-62a2102891c7','ID',90,'SUBMISSION_INFO_ID','id',null),
('3b90c334-f9e8-4342-bdcc-28dfa085201b','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Other Named Insured',100,'SUBMISSION_BUSINESSES','gc-other-named-insured','{"types": ["OTHER_INSURED"]}'),
('de9616e1-a3dd-47ce-8646-87b6dc9eb396','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Bundled Submissions',110,'SUBMISSION_INFO_BUNDLE','submission-bundle',null),
('52f97ede-7225-4d21-8a90-11f28c7cf4a1','dcbc533c-f4f0-48c9-aec3-62a2102891c7','Notes',120,'SUBMISSION_INFO_NOTES','notes',null),


('420512ea-2694-4b3a-9d37-950b28d4a424','dead5a8f-c44b-42f0-978e-d3fcc8050e8d',null,0,'LOSS_FILES_CARD','loss-files-card',null),
('9d905201-60a6-45ea-9edb-d3e385e44f98','dead5a8f-c44b-42f0-978e-d3fcc8050e8d',null,10,'LOSS_SUMMARIES_CARD','loss-summaries-card',null),
('82bdb5f3-fd51-40c0-b24a-f1e5b2c19b2d','dead5a8f-c44b-42f0-978e-d3fcc8050e8d','',20,'LOSS_RUNS_CARD','loss-runs-card',null),

('dfaec1f1-daf8-42d3-b3ad-88d633bc01f2','81fa4ebc-14ca-4464-ad5b-828d8eda976a','General Contractor',10,'FACTS','general-contractor','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "YEARS_IN_BUSINESS"}, {"id": "KNOWN_PERSONNEL"}, {"id": "BBB_RATING"}]}}}'),
('78b1ebad-2491-484a-9fa7-356b0df239f4','81fa4ebc-14ca-4464-ad5b-828d8eda976a','Financials',20,'FACTS','gc-financials','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "TOTAL_SALES"}, {"id": "PAYROLL"}]}}}'),
('dc33871e-520f-4a83-9e3d-8d0263141f3f','81fa4ebc-14ca-4464-ad5b-828d8eda976a','Permit Locations',30,'MAP_CARD','permit-locations','{"height": 416, "parentType": "BUSINESS", "documentType": "PERMIT", "markerSelect": "positionFromPremises", "businessSelect": "generalContractor"}'),
('54315579-0f70-48c2-9928-fd6000d98213','81fa4ebc-14ca-4464-ad5b-828d8eda976a',null,40,'TABLE','gc-permits','{"type": "PERMIT"}'),
('e83ce940-5f21-465e-b5b7-42b8d420b412','81fa4ebc-14ca-4464-ad5b-828d8eda976a','Complaints',80,'DEFAULT','gc-complaints','{"height": 50}'),

('72425328-3e87-43dc-a3c2-4400dfa961ea','90a1b1c4-b8c7-4831-a8a3-d1cc2a4b0c89',null,0,'OSHA_VIOLATION','osha-violations','{"parentType": "BUSINESS", "businessSelect": "generalContractorAndDuplicates"}'),
('fe8daea7-8d81-4e07-a97f-ad123e579bd2','90a1b1c4-b8c7-4831-a8a3-d1cc2a4b0c89',null,10,'EPA_INSPECTION','epa-inspections','{"parentType": "BUSINESS", "businessSelect": "generalContractorAndDuplicates"}'),

('59ef21a3-c63f-4b91-bef6-10ade9940f42','23e7a40a-4d8b-4d7f-a7ba-9d8eb4021d4d','News',0,'NEWS_CARD','news-card', '{"height": 50}'),
('22b625a0-af3f-4506-95cd-f47e48e141c3','b3fec88e-8735-4222-b334-a73e845f8433','Legal Filings',0,'LEGAL_FILINGS_CARD','legal-filings-card', '{"height": 50}'),

('2d2ae574-3874-44a2-aa68-a3786300cb0b','9ab4acc6-c5bb-4612-8bd2-c1c173b71b99','Breakdown of Work',10,'FACTS','ce-breakdown-of-work', '{"businessSelect": "generalContractorAndDuplicates", "generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_USE_OF_EIFS"}, {"id": "PROJECT_SCAFFOLDING"}, {"id": "PROJECT_MOLD_REMOVAL"}, {"id": "PROJECT_ROOF_WORK"}, {"id": "PROJECT_CRANE_WORK"}, {"id": "PROJECT_DEMOLITION_WORK"}, {"id": "PROJECT_BLASTING_WORK"}, {"id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL"}, {"id": "PROJECT_EXCAVATION_WORK"}, {"id": "PROJECT_BELOW_GRADE"}, {"id": "PROJECT_DEPTH_OF_WORK", "parentSubtypeIds": ["PROJECT_BELOW_GRADE"], "hiddenWhenNegative": true}]}}}'),
('67bac76c-fae9-4632-9341-5fe420d77314','9ab4acc6-c5bb-4612-8bd2-c1c173b71b99','Other',20,'FACTS','ce-other', '{"collapsible": true, "businessSelect": "generalContractorAndDuplicates", "generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_RAIN_GUTTER_WORK"}, {"id": "PROJECT_SHEET_METAL_WORK"}, {"id": "PROJECT_CARPENTRY_WORK"}]}}}'),

('3ce2fb11-7df0-46ce-88a1-ea6a47db6007','7d09de5c-1985-491a-b912-5d719b545fc0','Breakdown of Work',10,'FACTS','breakdown-of-work','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_PERCENTAGE_OF_WORK_RESIDENTIAL"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_PUBLIC"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_COMMERCIAL"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_INDUSTRIAL"}]}}}'),
('036a2be6-756f-442e-8179-70f0a5d6686e','7d09de5c-1985-491a-b912-5d719b545fc0','Breakdown of Operations',20,'FACTS','breakdown-of-operations','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_NEW_CONSTRUCTION"}, {"id": "PROJECT_REMODELING_OR_REPAIR"}]}}}'),
('a0761dd7-82fa-42e8-b55a-30572f8b6ecf','7d09de5c-1985-491a-b912-5d719b545fc0','GC & Subcontractor Work Breakdown',30,'FACTS','gc-and-subcontractor-work-breakdown','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_SUBCONTRACTORS_USED"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_AS_GC"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_AS_SUBCONTRACTOR"}]}}}'),
('acab1bab-34ba-4958-90bc-944c2cf29032','7d09de5c-1985-491a-b912-5d719b545fc0','Interior vs Exterior Work',40,'FACTS','interior-vs-exterior-work','{"generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_PERCENTAGE_OF_EXTERIOR_WORK"}, {"id": "PROJECT_PERCENTAGE_OF_INTERIOR_WORK"}]}}}'),
('1ee55b59-c150-45ac-b2c2-39a5dc483046','7d09de5c-1985-491a-b912-5d719b545fc0','Roofing Methods Used',50,'FACTS','roofing-methods-used','{"generalContractor": {"DEFAULT": {"factSubtypes": []}, "ROOFING_AND_SIDING_PRACTICE": {"factSubtypes": [{"id": "PROJECT_ASPHALT_SHINGLE"}, {"id": "PROJECT_WOOD_SHAKE_SHINGLE"}, {"id": "PROJECT_SLATE"}, {"id": "PROJECT_TILE"}, {"id": "PROJECT_METAL"}, {"id": "PROJECT_POLYURETHANE_FOAM"}, {"id": "PROJECT_HOT_TAR"}, {"id": "PROJECT_TORCH_DOWN"}, {"id": "PROJECT_HOT_AIR_WELDING"}, {"id": "PROJECT_MODIFIED_BITUMEN_HOT_OR_COLD"}, {"id": "PROJECT_EPDM_HOT_OR_COLD"}, {"id": "PROJECT_OTHER_ROOFING_METHODS"}]}}, "hiddenWhenAllFactsNegative": true}'),
('1d8b1e9c-4dd6-431f-a1f4-32e1c045bd23','7d09de5c-1985-491a-b912-5d719b545fc0','Pitch of Roofs Worked On',60,'FACTS','pitch-of-roofs-worked-on','{"generalContractor": {"DEFAULT": {"factSubtypes": []}, "ROOFING_AND_SIDING_PRACTICE": {"factSubtypes": [{"id": "PROJECT_PERCENTAGE_OF_WORK_ON_PITCHED_ROOFS"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_ON_FLAT_ROOFS"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_ON_OTHER_ROOFS"}]}}, "hiddenWhenAllFactsNegative": true}'),
('48c6622e-3cbf-4c89-8e27-89344e69b54e','7d09de5c-1985-491a-b912-5d719b545fc0','Demolition Methods Used',70,'FACTS','demolition-methods-used','{"generalContractor": {"DEFAULT": {"factSubtypes": []}, "SITE_PREPARATION_PRACTICE": {"factSubtypes": [{"id": "PROJECT_PERCENTAGE_OF_HAND_DEMOLITION"}, {"id": "PROJECT_PERCENTAGE_OF_PULL_PUSH_DOWN"}, {"id": "PROJECT_PERCENTAGE_OF_MECHANICAL_DEMOLITION"}, {"id": "PROJECT_PERCENTAGE_OF_IMPLOSION_EXPLOSIVES"}, {"id": "PROJECT_PERCENTAGE_OF_HYDRODEMOLITION"}, {"id": "PROJECT_PERCENTAGE_OF_WRECKING_BALL"}, {"id": "PROJECT_PERCENTAGE_OF_OTHER_DEMOLITION_METHODS"}]}}, "hiddenWhenAllFactsNegative": true}'),
('63e8b34a-6dc5-4ecc-8185-dc4e0ef8c490','7d09de5c-1985-491a-b912-5d719b545fc0','Location of Work',80,'FACTS','location-of-work','{"generalContractor": {"DEFAULT": {"factSubtypes": []}, "SITE_PREPARATION_PRACTICE": {"factSubtypes": [{"id": "PROJECT_PERCENTAGE_OF_WORK_URBAN"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_SUBURBAN"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_RURAL"}]}}, "hiddenWhenAllFactsNegative": true}'),

('555b0ca6-b517-4282-9ec6-f4aa68a8de86','7307b39a-c269-41e1-93f4-91e6ca87a105','Images',10,'IMAGES_CARD','images', '{"parentType": "BUSINESS", "businessSelect": "generalContractor"}'),
('d6f35899-745e-48df-bd6d-0c02a193b83c','3a01769c-c552-4edb-b92c-7f59cfe1b9dd',null,10,'PROJECT_DESCRIPTION_HEADER','project-description-header',null),
('d3cd2dba-ef67-4218-b25b-ab0b75dc7a26','caac5ce4-500d-4532-ae22-96cf2c5ff433','Project Description',10,'PROJECT_DESCRIPTION','project-description','{"height": 50}'),

('ea2a3813-b700-4687-bc7b-c816424ea340','13339e35-ca2b-4620-887e-f2eb57dd8645','Project Location',10,'MAP_CARD','project-location','{"height": 423, "source": "SUBMISSION", "parentType": "BUSINESS", "markerSelect": "positionFromPremises", "businessSelect": "contractorProject"}'),
('99860188-0258-4a36-aa4e-70814a95a2aa','13339e35-ca2b-4620-887e-f2eb57dd8645','Address',20,'SUBMISSION_BUSINESSES_DATA_CARD','project-location-address','{"path": "$.entity_data.entity.premises[*].premises.formatted_address", "businessSelect": "contractorProject"}'),

('15b06f74-b127-4338-9122-9e4067e3e61d','f2de6acc-2141-411c-9b8b-f20c8099b239','Risks',10,'FACTS','catastrophic-risks','{"facts": {"parentType": "PREMISES", "factSubtypeIds": ["CATASTROPHIC_FLOOD_RISK", "COASTAL_STORM_SURGE_RISK", "ELEVATION", "DISTANCE_TO_COAST", "FLOOD_RISK", "HAIL_RISK", "LIGHTNING_RISK", "DISTANCE_TO_TOXIC_FACILITY", "EARTHQUAKE_RISK", "WILDFIRE_RISK", "SNOW_LOAD_RISK", "FLOOD_ZONE", "SINKHOLE_RISK", "TORNADO_RISK"]}, "processors": ["orderByImportance"], "businessSelect": "contractorProject"}'),

('7dd135fd-cbd3-4c88-845c-bfb7e69ad8c1','3c115e3c-3e5a-4298-b6af-64f3a7f71927','Costs',10,'FACTS','costs','{"businessSelect": "contractorProject", "generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_ESTIMATED_CONSTRUCTION_COST"}]}}}'),
('3d7e3b41-c0ed-46ee-9249-a5c6a3bdffa1','3c115e3c-3e5a-4298-b6af-64f3a7f71927','Contractor',20,'FACTS','contractor','{"businessSelect": "contractorProject", "generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_SUBCONTRACTORS_USED"}, {"id": "PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS"}, {"id": "PROJECT_SUBCONTRACTORS_COST"}]}}}'),
('66176c6d-3688-44af-b6ce-13163113ce6b','3c115e3c-3e5a-4298-b6af-64f3a7f71927','Safety',30,'FACTS','safety','{"businessSelect": "contractorProject", "generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_SAFETY_PROGRAM"}, {"id": "PROJECT_QUALITY_CONTROL_PROGRAM"}, {"id": "PROJECT_SITE_INSPECTION_PROGRAM"}]}}}'),
('5a27383d-1136-4d24-9618-d734565ff308','3c115e3c-3e5a-4298-b6af-64f3a7f71927','Potential Exposures',40,'FACTS','potential-exposures','{"businessSelect": "contractorProject", "generalContractor": {"DEFAULT": {"factSubtypes": [{"id": "PROJECT_USE_OF_EIFS", "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"]}, {"id": "PROJECT_SCAFFOLDING", "parentSubtypeIds": ["SCAFFOLDING"]}, {"id": "PROJECT_MOLD_REMOVAL", "parentSubtypeIds": ["MOLD_ABATEMENT"]}, {"id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"]}, {"id": "PROJECT_CRANE_WORK", "parentSubtypeIds": ["CRANES"]}, {"id": "PROJECT_DEMOLITION_WORK", "parentSubtypeIds": ["DEMOLITION"]}, {"id": "PROJECT_BLASTING_WORK", "parentSubtypeIds": ["BLASTING_OPERATIONS"]}, {"id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL", "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"]}, {"id": "PROJECT_EXCAVATION_WORK"}, {"id": "PROJECT_BELOW_GRADE"}, {"id": "PROJECT_DEPTH_OF_WORK", "parentSubtypeIds": ["PROJECT_BELOW_GRADE"]}, {"id": "PROJECT_HEIGHT_IN_FT"}, {"id": "PROJECT_HEIGHT_IN_STORIES"}, {"id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"}, {"id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"}]}}}'),

('98552910-7c0d-4670-82ac-434ae8bc1754','afd3ec0b-e46a-45c6-86df-d1bf56739f91',null,10,'FILE_WITH_COMMENT','geotech-report-file','{"fileType": "Geotech Report"}'),

('aac39efa-d3c8-41ad-9845-7b483d0ea41d','0323c88f-7323-441f-902a-5c64960aa1a0','Crime Score',10,'FACTS','overall-crime-score', '{"facts": {"parentType": "PREMISES", "factSubtypeIds": ["OVERALL_CRIME_GRADE"]}, "businessSelect": "contractorProject"}'),
('1de8221c-9265-448a-9a1c-9003099b8a18','22a30030-c328-4f36-8a45-1d89cf50fdaf',null,20,'FACTS','crime-score','{"facts": {"parentType": "PREMISES", "factSubtypeIds": ["ASSAULT_GRADE", "LARCENY_GRADE", "MURDER_GRADE", "ROBBERY_GRADE", "BURGLARY_GRADE", "VEHICLE_THEFT_GRADE", "RAPE_GRADE", "DRUG_ALCOHOL_RELATED_DEATHS_GRADE"]}, "businessSelect": "contractorProject"}'),

('4fc91150-3ca2-4a42-a645-2692d04635a3','f857bd1c-611b-464b-a87b-5aed631b2b16','Project Images',10,'IMAGES_CARD','project-images','{"parentType": "BUSINESS", "businessSelect": "contractorProject"}');


-- FLEET
-- header
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('5e8548d6-e18f-40c7-b55f-6bd7ce3af57f','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',200,null,null,null,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('ad3706bf-d2a6-43be-bef7-d77542227133','5e8548d6-e18f-40c7-b55f-6bd7ce3af57f',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('e7a961d1-b9b7-419f-a862-21bc6509980b','ad3706bf-d2a6-43be-bef7-d77542227133',null,0,'FLEET_HEADER','fleet_header',null);

-- Vehicles in submission
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('811ebb9e-9085-4bc0-869d-f41c9db15ea6','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',210,null,null,null,'Vehicles in Submission');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('636500ca-23b2-44d9-9841-96f50511d43a','811ebb9e-9085-4bc0-869d-f41c9db15ea6',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('2294e590-2511-40b5-8c28-126a76884c01','636500ca-23b2-44d9-9841-96f50511d43a','Summary',10,'VEHICLES_IN_SUBMISSION_SUMMARY','vehicles-in-submission-summary',null);
---
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('10b5bc1e-6c25-46fd-9b12-4f631a9eb133','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',215,null,false,true,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('4908430b-cacc-4ac3-8828-b3db21d15be9','10b5bc1e-6c25-46fd-9b12-4f631a9eb133',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('de193b6c-2840-4596-8e8e-8e3bc8d6617e','4908430b-cacc-4ac3-8828-b3db21d15be9',null,10,'VEHICLES_IN_SUBMISSION_TABLE','vehicles-table-Submission',null);


-- Drivers in submission
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('c719686c-d403-4018-95be-d626eb64e997','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',220,null,null,null,'Drivers in Submission');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('76e0bc74-b9ab-4ca6-8f18-eaba43387921','c719686c-d403-4018-95be-d626eb64e997',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('8f0d19a7-58e2-4f99-b239-dc20771ff65f','76e0bc74-b9ab-4ca6-8f18-eaba43387921','Summary',10,'DRIVERS_IN_SUBMISSION_SUMMARY','drivers-in-submission-summary',null);
---
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('60d903d3-38af-41c0-a043-daeaa9a90994','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',225,null,false,true,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('6a76b8ec-de32-453d-b3a6-a2309f688ec8','60d903d3-38af-41c0-a043-daeaa9a90994',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('676b54cf-9c33-4f34-8e37-870bfb31a165','6a76b8ec-de32-453d-b3a6-a2309f688ec8','',10,'DRIVERS_IN_SUBMISSION_TABLE','drivers-table-submission',null);


-- Fleet operations
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('2009bf78-7adf-4c71-8319-27b64e0a0d3f','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',230,null,null,null,'Fleet Operations');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('79fb516b-5e0c-401b-aeb3-5160afbc92f0','2009bf78-7adf-4c71-8319-27b64e0a0d3f',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('b95f05ae-53f3-4fcf-90b8-db559051b4bb','79fb516b-5e0c-401b-aeb3-5160afbc92f0','Fleet Operations',10,'FACTS','fleet-operations','{"facts": {"parentType": "SUBMISSION", "factSubtypeIds": ["CARGO_CARRIED", "FLEET_OPERATION_STATES", "CARRIER_OPERATION_STATUS", "CARRIER_OPERATION", "OPERATION_CLASSIFICATION", "TRANSPORTATION_SAFETY_RATING", "TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS", "NUMBER_OF_VEHICLES", "NUMBER_OF_REGISTERED_DRIVERS", "TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION", "TRANSPORTATION_HAS_CONSUMER_COMPLAINTS", "IS_NEW_ENTRANT", "TRANSPORTATION_RISKY_NEW_ENTRANT"]}, "hiddenWhenAllFactsNegative": true}'),
('5d24a810-3f79-4d98-8d58-86c99e357d28','79fb516b-5e0c-401b-aeb3-5160afbc92f0','Out of Service Rates',20,'OUT_OF_SERVICE_CARD','fleet-operations-oosr', null),
('5d17616d-dcb1-4c33-9bd3-33afb7df1c14','79fb516b-5e0c-401b-aeb3-5160afbc92f0','BASIC Scores',30,'FACTS','fleet-operations-basic-scores','{"facts": {"parentType": "SUBMISSION", "factSubtypeIds": ["TRANSPORTATION_BASIC_BENCHMARK_UNSAFE_DRIVING", "TRANSPORTATION_BASIC_BENCHMARK_HOS_COMPLIANCE", "TRANSPORTATION_BASIC_BENCHMARK_VEHICLE_MAINTENANCE", "TRANSPORTATION_BASIC_BENCHMARK_DRUGS_ALCOHOL", "TRANSPORTATION_BASIC_BENCHMARK_DRIVER_FITNESS"]}, "hiddenWhenAllFactsNegative": true}');

-- crashes
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('78ae73b4-27b9-4d0e-94fb-e0b45e5e9aee','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',240,null,null,null,'Crashes');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('fa1a58b9-a93e-44bf-85b0-b9d5a76af994','78ae73b4-27b9-4d0e-94fb-e0b45e5e9aee',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('3b8bf77f-333b-4ec3-a5d2-885b280884ff','fa1a58b9-a93e-44bf-85b0-b9d5a76af994','Filter By',0,'ENTITY_FILTER', 'crashes-entity-filter','{
  "entityFilterKey": "crashes"
}');


INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('3b3ca3e3-fdc3-4b0b-9b18-3919c385fa09','fa1a58b9-a93e-44bf-85b0-b9d5a76af994','Crash Report Summary',10,'CRASH_REPORT_SUMMARY','crash-report-summary', '{"entityFilterKey": "crashes"}'),
('a97ffd8c-7d86-4279-8477-50067c5ed37e','fa1a58b9-a93e-44bf-85b0-b9d5a76af994',null,20,'TABLE','crash-list', '{"type": "CRASHES", "entityFilterKey": "crashes"}'),
('09e508b8-88e8-464a-ba4a-28cb4998acc1','fa1a58b9-a93e-44bf-85b0-b9d5a76af994',null,30,'CRASH_MAP','crash-map','{"entityFilterKey": "crashes"}');


-- enforcement cases


INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('28266ba8-2c47-49bc-8881-79f48718bd6a','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',250,null,null,null,'Enforcement Cases');



INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('ebf62a2f-8d38-4a5e-9d98-ddbdbc4750cf','28266ba8-2c47-49bc-8881-79f48718bd6a',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('7591743d-4e0d-4cb8-b2a6-fe755aa5ff28','ebf62a2f-8d38-4a5e-9d98-ddbdbc4750cf','Filter By',0,'ENTITY_FILTER', 'enforcement-cases-entity-filter','{
  "entityFilterKey": "enforcementCases"
}');

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('0660ec54-d4dc-4158-9215-aaf714a60e46','ebf62a2f-8d38-4a5e-9d98-ddbdbc4750cf',null,10,'TABLE','crash-enforcement-cases','{"type": "ENFORCEMENT_CASES", "entityFilterKey": "enforcementCases"}');


-- Vehicles in fmcsa

INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('63e6e0e1-95bd-4caa-8e3d-a5d4f9edaa79','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',260,null,null,null,'Vehicles in FMCSA');

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('d4cc8d35-e1b6-4228-b0b2-a8f2a3b3db07','63e6e0e1-95bd-4caa-8e3d-a5d4f9edaa79',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('be92102d-75e3-47b6-9f9f-f335d5c7f3d4','d4cc8d35-e1b6-4228-b0b2-a8f2a3b3db07','Summary',10,'VEHICLES_IN_FMCSA_SUMMARY','vehicles-in-fmcsa-summary',null);
---
INSERT INTO mode_rows (id, mode_id, position, elevation, is_collapsible, is_default_open, title)
VALUES
('1b6b2698-264d-4d9f-99e2-69311d40dde5','0b102ca4-24c4-44b0-b5f0-5f4a9b64e6aa',265,null,false,true,null);

INSERT INTO mode_columns(id, row_id, position, width, section_title)
VALUES
('e1c398c6-51bb-4fb1-b123-9531ba80d932','1b6b2698-264d-4d9f-99e2-69311d40dde5',0,12,null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props)
VALUES
('179ed978-463b-47c0-ae38-b3db6c880ff6','e1c398c6-51bb-4fb1-b123-9531ba80d932','',10,'VEHICLES_IN_FMCSA_TABLE','vehicles-table-fmcsa',null);



UPDATE mode_cards SET card_id = 'project-images' where title = 'Project Images';


    """)


def downgrade():
    pass

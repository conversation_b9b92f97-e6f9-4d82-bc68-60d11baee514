"""Add north star mutual

Revision ID: 48fc7c5aaf35
Revises: ed5dcd346d18
Create Date: 2023-06-15 07:40:05.014081+00:00

"""
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

from copilot.models.sensible_calls import DAYS_OF_MONTH

# revision identifiers, used by Alembic.
revision = "48fc7c5aaf35"
down_revision = "ed5dcd346d18"
branch_labels = None
depends_on = None

sensible_calls_init_query = sa.text("""
    INSERT INTO sensible_calls (
        id,
        organization_id, 
        year,
        month,
        day,
        calls_made
    )
    VALUES (
        :id,
        :organization_id, 
        :year, 
        :month, 
        :day, 
        :calls_made
    ) ON CONFLICT DO NOTHING;
    """)


def upgrade():
    op.execute("""
        insert into organization values
        (40, 'North Star Mutual', null, null, INTERVAL '90 day', 'northstarmutual.com', null, null, true)
    """)
    op.execute("""
        INSERT INTO settings (
            id, created_at, updated_at, organization_id, is_map_enabled_by_default, support_email, email_domains, default_tier, max_tier_for_auto_processing
        )
        values (
            uuid_generate_v4(), now(), null, 40, false, '<EMAIL>', '{northstarmutual.com, kalepa.co, kalepa.com}', 0, 0
        )
    """)
    op.execute("""
        insert into users values
        (default, '<EMAIL>', null, 'auth0|648abeb53589d123d094e00d', 40, 'manager', '<EMAIL>', null, null, now(), null, false, null, false, null, true)
    """)
    conn = op.get_bind()
    for organization_id in [40]:
        for year in range(2023, 2028):
            for month in range(1, 13):
                for day in range(1, DAYS_OF_MONTH[month] + 1):
                    query_params = {
                        "id": uuid4(),
                        "organization_id": organization_id,
                        "year": year,
                        "month": month,
                        "day": day,
                        "calls_made": 0,
                    }
                    conn.execute(statement=sensible_calls_init_query, **query_params)
    conn.execute("UPDATE settings SET loss_runs_enabled = true WHERE organization_id = 40;")


def downgrade():
    pass

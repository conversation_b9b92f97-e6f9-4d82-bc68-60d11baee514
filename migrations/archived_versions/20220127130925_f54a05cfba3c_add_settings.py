"""Add settings

Revision ID: f54a05cfba3c
Revises: 30a98384b2d3
Create Date: 2022-01-27 13:09:25.871490+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f54a05cfba3c"
down_revision = "30a98384b2d3"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "settings",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.Column("is_map_enabled_by_default", sa.<PERSON>(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_settings_organization_id"), "settings", ["organization_id"], unique=False)

    conn = op.get_bind()
    conn.execute("""
        INSERT INTO settings (id, created_at, updated_at, organization_id, is_map_enabled_by_default)
        SELECT uuid_generate_v4(), now(), null, id, false
        FROM organization
    """)
    conn.execute("update settings set is_map_enabled_by_default = true where organization_id = 5;")


def downgrade():
    op.drop_index(op.f("ix_settings_organization_id"), table_name="settings")
    op.drop_table("settings")

"""mode-changes-and-single-premise

Revision ID: 5867e2c7e53f
Revises: 7fd4dec27c68
Create Date: 2023-03-05 12:01:06.346844+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5867e2c7e53f"
down_revision = "7fd4dec27c68"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mode_elements",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("mode_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("position", sa.SmallInteger(), nullable=False),
        sa.Column("section_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("row_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.CheckConstraint("row_id IS NOT NULL OR section_id IS NOT NULL"),
        sa.CheckConstraint("row_id IS NULL OR section_id IS NULL"),
        sa.ForeignKeyConstraint(
            ["mode_id"],
            ["modes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["row_id"],
            ["mode_rows.id"],
        ),
        sa.ForeignKeyConstraint(
            ["section_id"],
            ["mode_sections.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("mode_id", "position"),
        sa.UniqueConstraint("mode_id", "section_id", "row_id"),
    )
    op.create_index(op.f("ix_mode_elements_mode_id"), "mode_elements", ["mode_id"], unique=False)
    op.create_index(op.f("ix_mode_elements_row_id"), "mode_elements", ["row_id"], unique=False)
    op.create_index(op.f("ix_mode_elements_section_id"), "mode_elements", ["section_id"], unique=False)
    op.drop_index("ix_mode_mode_sections_mode_id", table_name="mode_mode_sections")
    op.drop_index("ix_mode_mode_sections_section_id", table_name="mode_mode_sections")
    op.drop_table("mode_mode_sections")
    op.create_unique_constraint(None, "mode_section_elements", ["section_id", "position"])
    op.create_unique_constraint(None, "mode_section_elements", ["section_id", "child_section_id", "child_row_id"])
    op.drop_column("mode_section_elements", "props")
    op.add_column("mode_sections", sa.Column("name", sa.String(), nullable=True))
    op.add_column(
        "modes", sa.Column("props", postgresql.JSONB(none_as_null=True, astext_type=sa.Text()), nullable=True)
    )
    conn = op.get_bind()
    conn.execute("""
    INSERT INTO modes (id, name, props) VALUES ('bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 'Single Premise', '{"context":{"type":"parents-select","parentType":"PREMISES"}}');


INSERT INTO mode_sections (id, name, props) VALUES
('3270b427-c037-450e-9c52-96c743f6d5fe', 'Premises and Named Insured Important Highlights', '{"strategy":{"type":"merge-rows-columns"}}'),
('55aebe98-8488-4e4a-8ba1-6f008a648823', 'Property Highlights only if property converage', '{"strategy":{"type":"conditional","condition":{"type":"report","jsonPath":"$.submissions[0].coverages[*].coverage.name","contains":"property"}}}'),
('6e5a6f8b-5d23-48b7-b2a8-45d9fc0a5397', 'News and Legal Filings', NULL);


INSERT INTO mode_rows (id, position, title, is_collapsible, is_default_open) VALUES
('1d7b3cd0-202d-4c9a-9f1f-87d3ba03ef28', 100, 'Important Highlights', NULL, NULL),
('bdecf327-8c3f-45af-8231-0bade8187463', 100, 'Important Highlights', NULL, NULL),
('fa533c0e-4c6d-42f7-992d-6bfe1dc99c30', 400, 'News', NULL, NULL),
('fbe14804-eff5-414c-bdd2-c56aaacf5603', 500, 'Legal Filings', NULL, NULL),
('21b8aa66-edf0-4b05-b4ce-f3963bca2c0f', 20, 'Recommendation', NULL, NULL),
('7a39afb7-fb70-4460-af64-11d29cde844a', 40, 'Submission Information', TRUE, TRUE),
('fa6f1865-7777-455e-92c0-fa0a4e83502a', 50, 'Named Insureds & Premises', NULL, NULL),
('e357c13b-c054-4796-8ba2-7e5fc15b30ab', 60, 'Loss Runs', NULL, NULL),
('86e78214-e253-460a-890e-7974407022bf', 100, NULL, FALSE, NULL),
('8dd78b11-7673-4f71-bf33-f5b0c5aab32c', 100, 'Permits', NULL, NULL),
('1647f338-99e7-41d8-a5c0-847cd1a9bf8a', 100, 'Location', NULL, NULL),
('d9d4d5a3-31cf-4c08-947c-c03f0499ba7f', 100, 'Violations', NULL, NULL),
('287e71a2-e24a-48d5-b774-0da2315779aa', 100, 'Businesses at this Location', NULL, NULL);


INSERT INTO mode_columns (row_id, id, width, position) VALUES
('1d7b3cd0-202d-4c9a-9f1f-87d3ba03ef28', 'dd395f56-110f-4d6b-a649-4b230977baae', 12, 0),
('bdecf327-8c3f-45af-8231-0bade8187463', 'e8a3b705-2747-4dfb-8839-94f8290146ea', 12, 0),
('fa533c0e-4c6d-42f7-992d-6bfe1dc99c30', '012ff404-adb9-43dd-818f-7b05fbd54f06', 12, 0),
('fbe14804-eff5-414c-bdd2-c56aaacf5603', '2bb147ce-0396-46fc-962d-b81b90f722e5', 12, 0),
('21b8aa66-edf0-4b05-b4ce-f3963bca2c0f', '2a7b9cca-9c5c-4a48-8850-e78bee11e0bf', 12, 0),
('7a39afb7-fb70-4460-af64-11d29cde844a', '142783ef-97bd-4320-9dec-b40ec87223ef', 12, 0),
('fa6f1865-7777-455e-92c0-fa0a4e83502a', '62078a3e-0fd6-4576-8f0c-d79026c21bf7', 12, 0),
('e357c13b-c054-4796-8ba2-7e5fc15b30ab', '9c01ee14-d0aa-47a4-8fe1-2dd629406ca3', 12, 0),
('86e78214-e253-460a-890e-7974407022bf', 'ea59308b-9e20-4525-936e-c303f5ac3f57', 12, 0),
('8dd78b11-7673-4f71-bf33-f5b0c5aab32c', 'c029f1b4-036a-470b-9b4e-8e144c49c4c6', 12, 0),
('1647f338-99e7-41d8-a5c0-847cd1a9bf8a', '69e32348-8fd0-4e07-8705-6b5fe6e8f368', 12, 0),
('d9d4d5a3-31cf-4c08-947c-c03f0499ba7f', '41c63ae7-9e3f-482c-a2e4-5d91a3bbfcdf', 12, 0),
('287e71a2-e24a-48d5-b774-0da2315779aa', '92a1c21e-7c41-48a3-9452-c4edc0b5fb04', 12, 0);


INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
('dd395f56-110f-4d6b-a649-4b230977baae', '11349090-5456-4e30-bbac-b8ecfa5e5b5f', 10, 'named-insured-imp-highlights', 'IMPORTANT_HIGHLIGHTS_CARD', 'Named Insured Highlights', '{"columns":4,"highlights":[{"cardId":"legal-filings-card","icons":[{"color":"error","condition":{"min":1,"type":"isInRange"},"name":"warning"}],"label":"Legal filings","noValuesLabel":"None found","redirectLinkLabel":"Go to legal filings","source":{"mapper":"numberOfItems","source":{"documentType":"LEGAL_FILING","parentType":"BUSINESS","sourceType":"DOCUMENT"}}},{"cardId":"osha-violations","icons":[{"color":"error","condition":{"min":1,"type":"isInRange"},"name":"warning"}],"label":"Osha violations","noValuesLabel":"None found","redirectLinkLabel":"Go to Osha violations","source":{"mapper":"numberOfItems","source":{"documentType":"OSHA_VIOLATION","parentType":"BUSINESS","sourceType":"DOCUMENT"}}},{"cardId":"news-card","icons":[{"color":"error","condition":{"min":1,"type":"isInRange"},"name":"warning"}],"label":"News","noValuesLabel":"None found","redirectLinkLabel":"Go to News","source":{"mapper":"numberOfItems","source":{"documentType":"NEWS","parentType":"BUSINESS","sourceType":"DOCUMENT"}}},{"cardId":"epa-inspections","icons":[{"color":"error","condition":{"min":1,"type":"isInRange"},"name":"warning"}],"label":"EPA Inspections","noValuesLabel":"None found","redirectLinkLabel":"Go to EPA Inspections","source":{"mapper":"numberOfItems","source":{"documentType":"EPA_INSPECTION","parentType":"BUSINESS","sourceType":"DOCUMENT"}},"conditions":[{"condition":{"type":"isInRange","min":1},"mapper":"numberOfItems","source":{"documentType":"EPA_INSPECTION","parentType":"BUSINESS","sourceType":"DOCUMENT"}}]}]}'),
('e8a3b705-2747-4dfb-8839-94f8290146ea', 'e8815088-c35a-4a39-8c97-b231481c8eda', 10, 'premises-imp-highlights', 'IMPORTANT_HIGHLIGHTS_CARD', 'Property Highlights', '{"columns":4,"highlights":[{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Catastrophic Flood Risk","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["CATASTROPHIC_FLOOD_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["CATASTROPHIC_FLOOD_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Coastal Storm Surge Risk","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["COASTAL_STORM_SURGE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["COASTAL_STORM_SURGE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Flood","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["FLOOD_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["FLOOD_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Earthquake","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["EARTHQUAKE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["EARTHQUAKE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Hail","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["HAIL_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["HAIL_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Lightning","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["LIGHTNING_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["LIGHTNING_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Sinkhole","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["SINKHOLE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["SINKHOLE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Snow Load","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["SNOW_LOAD_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["SNOW_LOAD_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Tornado","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["TORNADO_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["TORNADO_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Wildfire","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["WILDFIRE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["WILDFIRE_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-risks","icons":[{"color":"error","condition":{"min":"D","type":"isGradeInRange"},"name":"warning"}],"label":"High Prostitution Risk","noValuesLabel":"None found","redirectLinkLabel":"Go to Risks","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["PROSTITUTION_RISK"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"grade"},"conditions":[{"condition":{"type":"isGradeInRange","min":"D"},"mapper":"jsonPathValue","mapperConfig":{"path":"observation"},"source":{"factSubtypes":["PROSTITUTION_RISK"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-distance","icons":[{"color":"error","condition":{"max":0.19,"type":"isInRange"},"name":"warning"}],"label":"Close Distance to Coast","noValuesLabel":"None found","redirectLinkLabel":"Go to Location","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation.at_least_miles"},"source":{"factSubtypes":["DISTANCE_TO_COAST"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"distance"},"conditions":[{"condition":{"type":"isInRange","max":0.19},"mapper":"jsonPathValue","mapperConfig":{"path":"observation.at_least_miles"},"source":{"factSubtypes":["DISTANCE_TO_COAST"],"parentType":"PREMISES","sourceType":"FACT"}}]},{"cardId":"location-facts-distance","icons":[{"color":"error","condition":{"max":0.19,"type":"isInRange"},"name":"warning"}],"label":"Close Distance to Toxic Facility","noValuesLabel":"None found","redirectLinkLabel":"Go to Location","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"observation.at_least_miles"},"source":{"factSubtypes":["DISTANCE_TO_TOXIC_FACILITY"],"parentType":"PREMISES","sourceType":"FACT"}},"valueFormatter":{"type":"distance"},"conditions":[{"condition":{"type":"isInRange","max":0.19},"mapper":"jsonPathValue","mapperConfig":{"path":"observation.at_least_miles"},"source":{"factSubtypes":["DISTANCE_TO_TOXIC_FACILITY"],"parentType":"PREMISES","sourceType":"FACT"}}]}]}'),
('012ff404-adb9-43dd-818f-7b05fbd54f06', 'eda9f03d-c9cf-4365-9a1d-bd32b448a688', 0, 'news-card', 'NEWS_CARD', 'News', '{"height":50}'),
('2bb147ce-0396-46fc-962d-b81b90f722e5', '9181e341-b64f-4cc7-952d-7e0e1fcc575c', 0, 'legal-filings-card', 'LEGAL_FILINGS_CARD', 'Legal Filings', '{"height":50}'),
('2a7b9cca-9c5c-4a48-8850-e78bee11e0bf', 'acabc52a-b438-4119-945e-628890451c8a', 10, 'recommendations-rules', 'RECOMMENDATIONS_CARD', 'Rules Triggered', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '46550956-849a-4cd5-872a-d3465050ba02', 10, 'submission-info-email', 'SUBMISSION_INFO_EMAIL', 'Email', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', 'cbe12265-0fe3-4242-9334-282dcb7e7255', 20, 'submission-support', 'SUBMISSION_INFO_SUPPORT', 'Support', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '39e7e0e3-b7b2-4e5d-9467-2b310bb26697', 30, 'submission-date', 'SUBMISSION_INFO_DATE', 'Date', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '4dd25e00-d6a5-4f3a-8b30-4edbd0019316', 40, 'submission-assignee', 'SUBMISSION_INFO_ASSIGNEE', 'Assigned Underwriters', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '64f6b12c-113a-4465-8083-667771ed3008', 50, 'description-of-operations', 'SUBMISSION_INFO_DESCRIPTION_OF_OPERATIONS', 'Description of Operations', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '1bfee50c-7bd5-4fc8-8dd8-ba13b1c8e3c2', 60, 'naics-codes', 'SUBMISSION_INFO_NAICS', 'NAICS Codes', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', 'a6ae1db6-acd1-4998-ad86-ac45965ad196', 70, 'coverage', 'SUBMISSION_INFO_COVERAGE', 'Coverage', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '70147085-cd3e-4000-94dd-22782b6e6512', 80, 'broker-agent', 'SUBMISSION_INFO_BROKER_AGENT', 'Broker / Agent & Brokerage / Agency', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '181e57f7-58b9-4afe-906b-4b4ab2bac04b', 90, 'id', 'SUBMISSION_INFO_ID', 'ID', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '23bf1f1a-525b-4500-90b6-4411d34c0373', 110, 'gc-other-named-insured', 'SUBMISSION_BUSINESSES', 'Other Named Insured', '{"types":["OTHER_INSURED"]}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '04fe7e1b-9a2c-4736-8e91-48d57d60baa1', 120, 'submission-bundle', 'SUBMISSION_INFO_BUNDLE', 'Bundled Submissions', '{}'),
('142783ef-97bd-4320-9dec-b40ec87223ef', '53948186-9642-4e4e-9c64-564025ba6d19', 130, 'notes', 'SUBMISSION_INFO_NOTES', 'Notes', '{}'),
('62078a3e-0fd6-4576-8f0c-d79026c21bf7', '05a10d13-59e2-4175-aceb-f6468f978816', 10, 'premises-and-locations', 'MAP_CARD', 'Premises and Locations', '{"height":416,"parentType":"BUSINESS"}'),
('62078a3e-0fd6-4576-8f0c-d79026c21bf7', '4a01d95e-f1d5-4304-85f5-a7ded8f9c616', 20, 'first-named-insured', 'SUBMISSION_BUSINESSES', 'First named insured', '{}'),
('9c01ee14-d0aa-47a4-8fe1-2dd629406ca3', '28bd9e68-3e00-4799-a756-968f399328f2', 20, 'loss-details', 'LOSS_RUNS_CARD', NULL, '{"height":50}'),
('9c01ee14-d0aa-47a4-8fe1-2dd629406ca3', 'd73012b8-0698-4acb-a998-922bea20a99f', 0, 'loss-files-card', 'LOSS_FILES_CARD', 'Loss Run Files', '{}'),
('9c01ee14-d0aa-47a4-8fe1-2dd629406ca3', 'a68be5cf-b461-4fc8-8c31-361707f5fa44', 10, 'loss-summaries', 'LOSS_SUMMARIES_CARD', 'Summary of losses', '{}'),
('ea59308b-9e20-4525-936e-c303f5ac3f57', '22552403-6a4e-4ca7-9c26-a4fe87686616', 100, 'entity-details', 'ENTITY_DETAILS', NULL, '{}'),
('c029f1b4-036a-470b-9b4e-8e144c49c4c6', '0e65eeae-d2cd-4fbb-af5f-988acc22dda6', 10, 'permits', 'TABLE', NULL, '{"type":"PERMIT"}'),
('69e32348-8fd0-4e07-8705-6b5fe6e8f368', '689ac7de-78a1-4d1c-8bd9-82a4d9a685a5', 100, 'location-facts-distance', 'FACTS', 'Location', '{"facts":{"parentType":"PREMISES","factSubtypeIds":["DISTANCE_TO_FIRE_STATION","DISTANCE_TO_POLICE_STATION","DISTANCE_TO_URGENT_CARE","DISTANCE_TO_HOSPITAL","DISTANCE_TO_FIRE_HYDRANT","DISTANCE_TO_COAST","DISTANCE_TO_TOXIC_FACILITY"]}}'),
('69e32348-8fd0-4e07-8705-6b5fe6e8f368', 'dbf4c522-3b4f-4ccd-8dfd-7f0eb5c95061', 200, 'location-facts-risks', 'FACTS', 'Risks', '{"facts":{"parentType":"PREMISES","factSubtypeIds":["CATASTROPHIC_FLOOD_RISK","COASTAL_STORM_SURGE_RISK","FLOOD_RISK","EARTHQUAKE_RISK","HAIL_RISK","LIGHTNING_RISK","SINKHOLE_RISK","SNOW_LOAD_RISK","TORNADO_RISK","WILDFIRE_RISK","PROSTITUTION_RISK"]}}'),
('41c63ae7-9e3f-482c-a2e4-5d91a3bbfcdf', '44947a60-bcb6-4b3c-9dfe-0979afa92490', 100, 'osha-violations', 'OSHA_VIOLATION', NULL, '{"parentType":"PREMISES"}'),
('41c63ae7-9e3f-482c-a2e4-5d91a3bbfcdf', '1e2c5ccc-85e0-45dc-9fe6-abcbf1747276', 200, 'epa-inspections', 'EPA_INSPECTION', NULL, '{"parentType":"PREMISES"}'),
('92a1c21e-7c41-48a3-9452-c4edc0b5fb04', '8d11287f-b25d-4e4f-a4b1-f8eae24517af', 100, 'businesses-at-this-location', 'BUSINESSES_AT_THIS_LOCATION', 'Businesses at this Location', '{}');


INSERT INTO mode_elements (id, mode_id, position, row_id, section_id) VALUES
('5fe04d38-4421-46f0-bb74-6e53b1262381', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 100, '21b8aa66-edf0-4b05-b4ce-f3963bca2c0f', NULL),
('27af9c75-78a8-4eb0-8f6b-ca4dee11c84b', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 200, NULL, '3270b427-c037-450e-9c52-96c743f6d5fe'),
('f0755010-955a-4376-871c-6e331649c8a2', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 300, '7a39afb7-fb70-4460-af64-11d29cde844a', NULL),
('8e9ade09-30c3-49b6-a3f7-331e86ef9b3c', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 400, 'fa6f1865-7777-455e-92c0-fa0a4e83502a', NULL),
('e2a518dd-491c-4e14-aaa3-9b4cbefa5e1f', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 500, 'e357c13b-c054-4796-8ba2-7e5fc15b30ab', NULL),
('9e0f0b7c-30ed-4644-a050-c3c7d9ab4c08', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 600, '86e78214-e253-460a-890e-7974407022bf', NULL),
('9fedbb04-e368-4a0a-a74f-0108f2638865', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 700, '8dd78b11-7673-4f71-bf33-f5b0c5aab32c', NULL),
('644ce4ba-0407-49b3-b207-11e1c11e00b6', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 800, '1647f338-99e7-41d8-a5c0-847cd1a9bf8a', NULL),
('00c0431a-8293-43d8-ac0e-bb8838b27e50', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 900, '617578e0-792b-41fb-b8f1-faf4124c8dd6', NULL),
('47e2ae88-0721-4e8e-8967-4016043fa7be', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 1000, 'd9d4d5a3-31cf-4c08-947c-c03f0499ba7f', NULL),
('cbf73088-b00c-4125-95f6-fdf27cfc6846', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 1100, NULL, '6e5a6f8b-5d23-48b7-b2a8-45d9fc0a5397'),
('d8641258-d1b0-4378-b0e7-d4b9ed06e143', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 1200, '196c2600-f32f-411f-bdbb-27801da5e571', NULL),
('e37f07a2-7322-47cf-9739-e603ae2eda38', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 1300, '287e71a2-e24a-48d5-b774-0da2315779aa', NULL);


INSERT INTO mode_section_elements (id, section_id, position, child_row_id, child_section_id) VALUES
('80e2a229-5c7d-4acd-a6f2-a5bf0c4f59e7', '3270b427-c037-450e-9c52-96c743f6d5fe', 100, NULL, '55aebe98-8488-4e4a-8ba1-6f008a648823'),
('5990af81-3f69-416d-980e-b83d54b571e7', '3270b427-c037-450e-9c52-96c743f6d5fe', 200, '1d7b3cd0-202d-4c9a-9f1f-87d3ba03ef28', NULL),
('cb226d30-e0e4-4f6d-b81b-adf83e03dac6', '55aebe98-8488-4e4a-8ba1-6f008a648823', 100, 'bdecf327-8c3f-45af-8231-0bade8187463', NULL),
('7d0088f1-12d6-42bd-b6b0-0d94a12590e8', '6e5a6f8b-5d23-48b7-b2a8-45d9fc0a5397', 100, 'fa533c0e-4c6d-42f7-992d-6bfe1dc99c30', NULL),
('bb924cd9-d38f-4722-a09a-3a6c2b7197d2', '6e5a6f8b-5d23-48b7-b2a8-45d9fc0a5397', 200, 'fbe14804-eff5-414c-bdd2-c56aaacf5603', NULL);
    """)


def downgrade():
    op.drop_column("modes", "props")
    op.drop_column("mode_sections", "name")
    op.add_column(
        "mode_section_elements",
        sa.Column("props", postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    )
    op.drop_constraint(None, "mode_section_elements", type_="unique")
    op.drop_constraint(None, "mode_section_elements", type_="unique")

    op.create_table(
        "mode_mode_sections",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("mode_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column("section_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column("position", sa.SMALLINT(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["mode_id"], ["modes.id"], name="mode_mode_sections_mode_id_fkey"),
        sa.ForeignKeyConstraint(["section_id"], ["mode_sections.id"], name="mode_mode_sections_section_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="mode_mode_sections_pkey"),
    )
    op.create_index("ix_mode_mode_sections_section_id", "mode_mode_sections", ["section_id"], unique=False)
    op.create_index("ix_mode_mode_sections_mode_id", "mode_mode_sections", ["mode_id"], unique=False)
    op.drop_index(op.f("ix_mode_elements_section_id"), table_name="mode_elements")
    op.drop_index(op.f("ix_mode_elements_row_id"), table_name="mode_elements")
    op.drop_index(op.f("ix_mode_elements_mode_id"), table_name="mode_elements")
    op.drop_table("mode_elements")

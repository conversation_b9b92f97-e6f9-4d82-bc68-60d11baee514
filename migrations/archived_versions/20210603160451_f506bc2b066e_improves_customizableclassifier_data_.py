"""Improves CustomizableClassifier data model

Revision ID: f506bc2b066e
Revises: 4876a9b6d797
Create Date: 2021-06-03 16:04:51.379857+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f506bc2b066e"
down_revision = "4876a9b6d797"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "customizable_classifier_labels",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("customizable_classifier_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("business_name", sa.String(), nullable=True),
        sa.Column("business_address", sa.String(), nullable=True),
        sa.Column("business_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("ground_truth_class", sa.Enum("NO", "YES", name="binaryclassificationclasstype"), nullable=False),
        sa.Column("is_excluded", sa.Boolean(), nullable=True),
        sa.Column("comment", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["customizable_classifier_id"],
            ["customizable_classifiers.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_customizable_classifier_labels_customizable_classifier_id"),
        "customizable_classifier_labels",
        ["customizable_classifier_id"],
        unique=False,
    )
    op.create_table(
        "test_run_classification_task",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("test_run_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("label_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("predicted_class", sa.Enum("NO", "YES", name="binaryclassificationclasstype"), nullable=True),
        sa.Column("predicted_probability", sa.Float(), nullable=True),
        sa.Column("evidence_details", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(
            ["label_id"],
            ["customizable_classifier_labels.id"],
        ),
        sa.ForeignKeyConstraint(
            ["test_run_id"],
            ["customizable_classifier_test_runs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_test_run_classification_task_label_id"), "test_run_classification_task", ["label_id"], unique=False
    )
    op.create_index(
        op.f("ix_test_run_classification_task_test_run_id"),
        "test_run_classification_task",
        ["test_run_id"],
        unique=False,
    )
    op.drop_table("submission_snapshot")
    op.add_column("customizable_classifier_test_runs", sa.Column("total_f1_score", sa.Float(), nullable=True))
    op.add_column("customizable_classifier_test_runs", sa.Column("total_precision", sa.Float(), nullable=True))
    op.add_column("customizable_classifier_test_runs", sa.Column("total_recall", sa.Float(), nullable=True))
    op.add_column("customizable_classifier_test_runs", sa.Column("total_support", sa.Integer(), nullable=True))
    op.alter_column(
        "customizable_classifier_test_runs",
        "customizable_classifier_id",
        existing_type=postgresql.UUID(),
        nullable=False,
    )
    op.drop_column("customizable_classifier_test_runs", "positive_business_ids")
    op.drop_column("customizable_classifier_test_runs", "negative_business_ids")
    op.add_column("customizable_classifiers", sa.Column("description", sa.String(), nullable=True))
    op.add_column("customizable_classifiers", sa.Column("is_applicable_to", sa.ARRAY(sa.String()), nullable=True))
    op.add_column("customizable_classifiers", sa.Column("is_applicable_to_all_industries", sa.Boolean(), nullable=True))
    op.add_column("customizable_classifiers", sa.Column("should_analyze", sa.ARRAY(sa.String()), nullable=True))


def downgrade():
    op.drop_column("customizable_classifiers", "should_analyze")
    op.drop_column("customizable_classifiers", "is_applicable_to_all_industries")
    op.drop_column("customizable_classifiers", "is_applicable_to")
    op.drop_column("customizable_classifiers", "description")
    op.add_column(
        "customizable_classifier_test_runs",
        sa.Column("negative_business_ids", postgresql.ARRAY(postgresql.UUID()), autoincrement=False, nullable=True),
    )
    op.add_column(
        "customizable_classifier_test_runs",
        sa.Column("positive_business_ids", postgresql.ARRAY(postgresql.UUID()), autoincrement=False, nullable=True),
    )
    op.alter_column(
        "customizable_classifier_test_runs",
        "customizable_classifier_id",
        existing_type=postgresql.UUID(),
        nullable=True,
    )
    op.drop_column("customizable_classifier_test_runs", "total_support")
    op.drop_column("customizable_classifier_test_runs", "total_recall")
    op.drop_column("customizable_classifier_test_runs", "total_precision")
    op.drop_column("customizable_classifier_test_runs", "total_f1_score")
    op.create_table(
        "submission_snapshot",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("submission_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column("snapshot", postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["submission_id"], ["submissions.id"], name="submission_snapshot_submission_id_fkey", ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id", name="submission_snapshot_pkey"),
    )
    op.drop_index(op.f("ix_test_run_classification_task_test_run_id"), table_name="test_run_classification_task")
    op.drop_index(op.f("ix_test_run_classification_task_label_id"), table_name="test_run_classification_task")
    op.drop_table("test_run_classification_task")
    op.drop_index(
        op.f("ix_customizable_classifier_labels_customizable_classifier_id"),
        table_name="customizable_classifier_labels",
    )
    op.drop_table("customizable_classifier_labels")

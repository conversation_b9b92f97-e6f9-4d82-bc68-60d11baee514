"""new coverages for new client

Revision ID: cd85af4e20a5
Revises: df8d214842a0
Create Date: 2023-06-20 12:20:37.827877+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cd85af4e20a5"
down_revision = "df8d214842a0"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
VALUES
    ('a38bbcea-aa7f-43f2-a39b-4b788b03e16b', 'crime', 'Crime', 40, '{}'),
    ('f4fd613c-3070-4e97-a970-37df08d2a16a', 'liquorLiability', 'Liquor Liability', 40, '{PRIMARY}'),
    ('b499d6fe-9fe7-4a92-855c-4aef1fbdc315', 'employmentPracticesLiability', 'Employment Practices Liability', 40, '{}');
        """)


def downgrade():
    pass

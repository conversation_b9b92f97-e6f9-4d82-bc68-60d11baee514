"""Configures cascading deletes for MetricConfigs and MetricPreferences

Revision ID: f239ec8eeb8f
Revises: 7aef1882624b
Create Date: 2021-01-27 16:23:32.260225+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "f239ec8eeb8f"
down_revision = "7aef1882624b"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("metric_preference_metric_config_id_fkey", "metric_preference", type_="foreignkey")
    op.create_foreign_key(None, "metric_preference", "metric_config", ["metric_config_id"], ["id"], ondelete="CASCADE")


def downgrade():
    op.drop_constraint(None, "metric_preference", type_="foreignkey")
    op.create_foreign_key(
        "metric_preference_metric_config_id_fkey", "metric_preference", "metric_config", ["metric_config_id"], ["id"]
    )

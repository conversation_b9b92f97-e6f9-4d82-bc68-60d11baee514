"""Configures additional metrics

Revision ID: 5618fa5b5cf1
Revises: eef36e6c6069
Create Date: 2020-09-10 01:38:30.974918+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5618fa5b5cf1"
down_revision = "eef36e6c6069"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT
    INTO
    summary_preference(id, display_name, group_display_name, is_default)
    VALUES
    ('e60a4bb7-7129-49b5-bf55-854ca12997b0', 'Cooking Types', 'Equipment Insights', false),
    ('61dca638-6b8e-48a1-93da-d75108ef1ce2', 'Has Adult Entertainment', 'Operations Insights', false),
    ('bddafdca-c0fc-45ce-bb1f-2de2bf77347a', '<PERSON><PERSON>', 'Operations Insights', false),
    ('c0342856-1aad-41e1-a81a-44d70245f5d3', 'Entertainment Types', 'Operations Insights', false),
    ('efb07c13-8da8-4ca1-82ed-6cb6b2f0d1ed', 'Has Bottle Service', 'Operations Insights', false),
    ('46bd924a-adcf-4494-a097-2a1a50c09c9b', 'Has Bouncers', 'Operations Insights', false),
    ('fc61ac54-486b-42a4-9bf0-ceb52cbe43ac', 'Has BYOB', 'Operations Insights', false),
    ('7980a6bb-4c24-42a4-8d4b-76fd383c92f2', 'Has Catering', 'Operations Insights', false),
    ('50224bf2-66ce-4f9a-b909-fd84f5780d9e', 'Has Dancing', 'Operations Insights', false),
    ('21efb2be-e0b0-4f5d-b86c-9044afb42a6f', 'Has Delivery', 'Operations Insights', false),
    ('5d2f77bd-343b-4ec2-a3a1-492c89036b2c', 'Has Outdoor Seating', 'Operations Insights', false),
    ('324f0115-343f-4653-beec-01af75d7eda1', 'Has Waiter Service', 'Operations Insights', false),
    ('204f7ba7-649b-42bc-a6a7-9b5f6c7c5535', 'Performance Types', 'Operations Insights', false),
    ('108dc57f-4cbf-4c0c-b50d-dfdc00bd120a', 'Has Infestation', 'Operations Insights', false),
    ('83395704-bf08-4f71-92aa-809d7bceda80', 'Has Roof Access', 'Operations Insights', false),
    ('9f63b563-8fb4-4d2d-92eb-c5c097be6d29', 'Has Security Guards', 'Operations Insights', false),
    ('ddf94910-9069-4fe6-abd5-65d592aa01fb', 'Parking Types', 'Operations Insights', false),
    ('4ffa8374-4994-4598-8ba3-8d206021bfd0', 'Has Balconies', 'Operations Insights', false),
    ('15030b96-e2eb-42df-b463-756a667a286b', 'Hotel Amenities', 'Operations Insights', false),
    ('807382d7-e07b-458c-b0d0-28387022daf8', 'Has Swimming Pool', 'Operations Insights', false),
    ('29fc2576-f296-4c4a-b44b-862ec40cbb4a', 'Building Construction Type', 'Property Insights', false),
    ('a8604c00-4a91-4e98-89c8-7197c451c634', 'Hotel Average Rating', 'Operations Insights', false),
    ('175de953-f4a9-4501-820a-e021f0bd2c97', 'Hotel: Number of Rooms', 'Operations Insights', false),
    ('34a36c09-31e1-41a1-9f13-9dacd4f85432', 'Hotel: Maximum Price', 'Operations Insights', false),
    ('61671400-825a-4863-8696-39e730335c6a', 'Hotel: Minimum Price', 'Operations Insights', false),
    ('bdd8329d-7fab-4683-b16e-964a246a8735', 'Hotel Class', 'Operations Insights', false),
    ('f0d922d8-a5ee-442e-8c01-37002a490b1c', 'Building Stories', 'Property Insights', false),
    ('d2cb14dd-8749-49fc-8274-5002f9ac5b69', 'Coastal Storm Surge', 'Catastrophe Risks', false),
    ('2d6edf68-8548-41a0-8ff1-acca989b8e54', 'Crime Score: Assault', 'Crime Risks', false),
    ('96713485-9bf6-4800-b1dc-d17a6b3500e8', 'Crime Score: Burglary', 'Crime Risks', false),
    ('fdd46d06-7830-4405-a17f-ce97632551da', 'Crime Score: Drug and Alcohol Deaths', 'Crime Risks', false),
    ('0c40a901-b3ef-45d9-ab60-4601f6c4832c', 'Crime Score: Larceny', 'Crime Risks', false),
    ('728c94aa-b3e3-4af9-ae40-de499e458669', 'Crime Score: Motor Vehicle Theft', 'Crime Risks', false),
    ('456d7b5c-a851-4ae8-b153-5e54c9c09689', 'Crime Score: Murder', 'Crime Risks', false),
    ('da7d96ac-634d-4a6c-8a4b-320fc2ade3e6', 'Crime Score: Rape', 'Crime Risks', false),
    ('e7458adb-96e4-40e7-a117-b4769947e87b', 'Crime Score: Robbery', 'Crime Risks', false),
    ('6482ca65-2461-4ddf-aecd-a95294cdd37c', 'Fire Protection Class', 'Fire Risk', false),
    ('5b1c59a8-56f1-4189-8489-4e267d3f920b', 'Flood ', 'Catastrophe Risks', false),
    ('978d901f-4beb-407d-ab28-2ce81e3ccfce', 'Hail', 'Catastrophe Risks', false),
    ('50cd0f73-b84d-4394-b74a-900c781f7943', 'Lightning', 'Catastrophe Risks', false),
    ('f79915eb-5fa4-405c-9749-0bd8c7be4a25', 'Sinkhole', 'Catastrophe Risks', false),
    ('1d2f196d-ad61-4b65-a150-8fc887b5b50a', 'Snow Load', 'Catastrophe Risks', false),
    ('5ec2f30f-67d5-48ae-aed0-d9f570ba1e74', 'Tornado', 'Catastrophe Risks', false),
    ('7e54c4e5-e7ac-4cfe-9259-c02bbb28e5b5', 'Wildfire', 'Catastrophe Risks', false),
    ('70021902-b7ff-452e-baac-dc952282469b', 'Prostitution Risk', 'Crime Risks', false),
    ('c5e896d7-ddf2-446d-8bf0-ba6173c8738b', 'Crime in Premises Risk', 'Crime Risks', false),
    ('e2ce77d8-93f1-4f2d-9796-1cfbcadb5d9b', 'Distance to Hydrant', 'Fire Risk', false),
    ('4a871e00-801a-4f03-9da8-5dea12f98272', 'Distance to Toxic Facility', 'Other Risks', false),
    ('f7c5b5a9-e9ab-42ef-a060-06ce3fcd9125', 'Distance to Urgent Care', 'Other Risks', false)
    ON CONFLICT DO NOTHING;
    """)

    conn.execute("UPDATE metric SET name = 'Crime Score: Overall' where name = 'Crime'")
    conn.execute("UPDATE metric SET name = 'Building Area' where name = 'Building Size'")
    conn.execute("UPDATE metric SET name = 'Distance to Hydrant' where name = 'Distance to Fire Hydrant'")
    conn.execute("UPDATE summary_preference SET display_name = 'Building Area' where display_name = 'Building Size'")
    conn.execute("UPDATE summary_preference SET display_name = 'Crime Score: Overall' where display_name = 'Crime'")
    conn.execute(
        "UPDATE summary_preference SET display_name = 'Distance to Hydrant' where display_name = 'Distance to Fire"
        " Hydrant'"
    )


def downgrade():
    pass

"""Migrates SSO user

Revision ID: b668232cafc6
Revises: c8d547ff28b9
Create Date: 2021-08-20 17:40:03.850683+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "b668232cafc6"
down_revision = "c8d547ff28b9"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("update users set external_id = null where id = 187 and email = '<PERSON><PERSON>@us.qbe.com';")

    conn.execute("""
    update users 
    set external_id = 'samlp|QBE|<EMAIL>' 
    where id = 71 and email = '<EMAIL>';
    """)


def downgrade():
    conn = op.get_bind()

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|David.<PERSON>@us.qbe.com'
    where id = 187 and email = '<PERSON><PERSON>@us.qbe.com';
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|5f5b8b6c5dd9ff006f75181a'
    where id = 71 and email = '<EMAIL>';
    """)

"""Add unique constraints to settings

Revision ID: e6b4905a5fe4
Revises: 646b41311f3f
Create Date: 2022-04-26 13:07:03.759242+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e6b4905a5fe4"
down_revision = "0c5db736d98a"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("ix_settings_organization_id", table_name="settings")
    op.create_index(op.f("ix_settings_organization_id"), "settings", ["organization_id"], unique=True)
    op.drop_index("ix_settings_user_id", table_name="settings")
    op.create_index(op.f("ix_settings_user_id"), "settings", ["user_id"], unique=True)


def downgrade():
    op.drop_index(op.f("ix_settings_user_id"), table_name="settings")
    op.create_index("ix_settings_user_id", "settings", ["user_id"], unique=False)
    op.drop_index(op.f("ix_settings_organization_id"), table_name="settings")
    op.create_index("ix_settings_organization_id", "settings", ["organization_id"], unique=False)

"""Adds temporary coverage for nationwide demo

Revision ID: 5efdd16160a7
Revises: 809f7d6ef1dc
Create Date: 2023-05-04 21:18:03.225104+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5efdd16160a7"
down_revision = "809f7d6ef1dc"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # create the mode
    conn.execute("""  
    INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)  
    VALUES  
        ('739a8ed5-0ca5-481a-b83a-29e3ca5ecfcf', 'fiduciaryLiabilityTemp', 'Fiduciary Liability', 6, '{PRIMARY,EXCESS}');
    """)


def downgrade():
    ...

"""Updating user 1698 to manager

Revision ID: 0347867d3f6c
Revises: 70da03f5fec5
Create Date: 2023-06-08 12:13:04.746756+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0347867d3f6c"
down_revision = "70da03f5fec5"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("UPDATE users SET role = 'manager' WHERE id=1698;")


def downgrade():
    pass

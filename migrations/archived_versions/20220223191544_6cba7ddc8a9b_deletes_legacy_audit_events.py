"""Deletes legacy audit events

Revision ID: 6cba7ddc8a9b
Revises: 016002b74d48
Create Date: 2022-02-23 19:15:44.455907+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "6cba7ddc8a9b"
down_revision = "016002b74d48"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    DELETE FROM audit_trails WHERE user_external_id IN (SELECT external_id FROM users WHERE organization_id in (5, 6));
    """)


def downgrade():
    pass

"""coverage_type

Revision ID: c9463ca65c6f
Revises: 60163ccb072b
Create Date: 2022-09-07 09:12:51.346286+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c9463ca65c6f"
down_revision = "60163ccb072b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submissions", sa.Column("coverage_type", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submissions", "coverage_type")
    # ### end Alembic commands ###

"""empty message

Revision ID: 14a98f59995c
Revises: 90ce74549021
Create Date: 2023-05-16 08:59:16.680741+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "14a98f59995c"
down_revision = "90ce74549021"
branch_labels = None
depends_on = None

CONSTRUCTION_PHRASES = """
[    
    ["abatement"],
    ["below ground"],
    ["builders"],
    ["commercial", "construction"],
    ["concrete", "construct"],
    ["concrete", "wall"],
    ["construction", "cost"],
    ["construction company"],
    ["construction submission"],
    ["crane", "operator"],
    ["general", "contractor"],
    ["geotech"],
    ["insulate"],
    ["new", "commercial", "construction"],
    ["new construction"],
    ["new residential"],
    ["owners interest"],
    ["painting", "service"],
    ["plubming"],
    ["pool management"],
    ["project", "specific"],
    ["project", "cost"],
    ["project submission"],
    ["remodeling"],
    ["residential", "construction"],
    ["restoration"],
    ["rolling", "ocip"],
    ["roofing"],
    ["subcosts"],
    ["scaffolding"],
    ["site plan"],
    ["subcost"],
    ["tower crane"],
    ["wrap up"]
]
"""


def upgrade():
    op.create_table(
        "routing_rule",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False, server_default=sa.text("uuid_generate_v4()")),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("tag", sa.String(), nullable=False, index=True),
        sa.Column("order", sa.Integer(), nullable=False),
        sa.Column("contained_words", postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column("not_contained_words", postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column("contained_phrases", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("skip_if_previous_rule_was_hit", sa.Boolean(), nullable=False),
        sa.Column("tier", sa.Integer(), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=True, index=True),
        sa.ForeignKeyConstraint(["organization_id"], ["organization.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column("settings", sa.Column("default_tier", sa.Integer(), nullable=True))
    op.add_column("settings", sa.Column("max_tier_for_auto_processing", sa.Integer(), nullable=True))

    op.add_column("reports_v2", sa.Column("routing_tags", postgresql.ARRAY(sa.String()), nullable=True))
    op.add_column("reports_v2", sa.Column("tier", sa.Integer(), nullable=True))

    with op.get_context().autocommit_block():
        # nationwide
        op.execute("update settings set default_tier=4 where organization_id=6")
        op.execute("update settings set max_tier_for_auto_processing=2 where organization_id=6")
        op.execute("""
        insert into routing_rule (tag, "order", contained_words, not_contained_words, contained_phrases, skip_if_previous_rule_was_hit, tier, organization_id) values 
        ('nw_lisa.kincaid', 1, '{"Kincaid", "lisa.kincaid"}', null, null, false, 0, 6),
        ('nw_amy.finnegan', 1, '{"amy.finnegan"}', null, null, false, 0, 6),
        ('nw_wadet7', 1, '{"Wade", "wadet7"}', null, null, false, 0, 6),
        ('nw_westh9', 1, '{"Harper", "westh9"}', null, null, false, 0, 6),
        ('nw_alicia.villanueva', 1, '{"Villanueva", "alicia.villanueva", "VILLA12"}', null, null, false, 0, 6),
        ('nw_kelly.collins', 1, '{"Collins", "kelly.collins", "collik28"}', null, null, false, 0, 6),
        ('nw_mclelm1', 1, '{"McLelland", "mclelm1"}', null, null, false, 0, 6),
        ('nw_klebbat', 1, '{"Klebba", "klebbat"}', null, null, false, 0, 6),
        ('nw_HOLLAA3', 1, '{"Holland", "HOLLAA3"}', null, null, false, 0, 6),
        ('nw_stefanie.johnson', 1, '{"stefanie.johnson", "stefanie"}', null, null, false, 0, 6),
        ('nw_ramsek3', 1, '{"Ramsey", "ramsek3"}', null, null, false, 0, 6),
        
        ('nw_c.green', 1, '{"c.green", "c.green"}', null, null, false, 1, 6),
        ('nw_toyosir', 1, '{"robert.toyosima", "toyosir", "toyosima"}', null, null, false, 1, 6),
        ('nw_bowera8', 1, '{"Bowery", "bowera8"}', null, null, false, 1, 6),
        ('nw_kimberly.kercheval-lorenz', 1, '{"Kercheval-Lorenz", "kercheval"}', null, null, false, 1, 6),
        ('nw_calesj1', 1, '{"Cales", "calesj1"}', null, null, false, 1, 6),
        ('nw_sandy.larson', 1, '{"Larson", "sandy.larson", "larsos3"}', null, null, false, 1, 6),
        ('nw_carmoj1', 1, '{"Carmona", "carmoj1"}', null, null, false, 1, 6),
        ('nw_LADAJ', 1, '{"Lada-Foster", "LADAJ"}', null, null, false, 1, 6),
        ('nw_dauphd1', 1, '{"Dauphin", "dauphd1"}', null, null, false, 1, 6),
        ('nw_adamc25', 1, '{"Adams", "adamc25"}', null, null, false, 1, 6),
        ('nw_colene.johnston', 1, '{"colene.johnston", "colene.johnston", "johnc13"}', null, null, false, 1, 6),
        ('nw_mohamed.khalid', 1, '{"mohamed.khalid", "KHALIM8"}', null, null, false, 1, 6),
        ('nw_britany.mercer', 1, '{"Mercer", "britany.mercer"}', null, null, false, 1, 6),
        
        ('nw_heerg', 1, '{"Heer", "heerg"}', null, null, false, 2, 6),
        ('nw_MILLAB1', 1, '{"Millan", "MILLAB1"}', null, null, false, 2, 6),
        ('nw_fryek3', 1, '{"Frye", "fryek3"}', null, null, false, 2, 6),
        ('nw_kelly.adams', 1, '{"kelly.adams", "kelly.adams"}', null, null, false, 2, 6),
        ('nw_jessica.oconnor', 1, '{"O''Connor", "jessica.oconnor"}', null, null, false, 2, 6),
        ('nw_kevin.bott', 1, '{"kevin.bott"}', null, null, false, 2, 6),
        ('nw_ashley.moffatt', 1, '{"Moffatt", "ashley.moffatt"}', null, null, false, 2, 6),
        ('nw_doherj2', 1, '{"Doherty", "doherj2"}', null, null, false, 2, 6),
        ('nw_Dunawm3', 1, '{"Dunaway", "Dunawm3"}', null, null, false, 2, 6),
        ('nw_kevin.kline', 1, '{"Kline", "kevin.kline"}', null, null, false, 2, 6),
        
        ('nw_noah.smith', 1, '{"Noah", "noah.smith"}', null, null, false, 3, 6),
        ('nw_braden.bond', 1, '{"Bond", "braden.bond", "braden"}', null, null, false, 3, 6),
        ('nw_jessica.heggarty', 1, '{"Heggarty", "jessica.heggarty"}', null, null, false, 3, 6),
        ('nw_shawp2', 1, '{"Patti", "shawp2"}', null, null, false, 3, 6),
        ('nw_richmas', 1, '{"Richman", "richmas"}', null, null, false, 3, 6),
        ('nw_sally.reynoldson', 1, '{"Reynoldson", "sally.reynoldson"}', null, null, false, 3, 6),        
        ('not_es', 1, '{"serrae5", "j.jolly", "Johnse12", "peltier", "hardria"}', null, null, false, null, 6),
        ('unassigned_construction', 2, '{"contractor", "subcontract", "subcontractor", "OCIP", "ROCIP", "geotech", "geotechnical", "GC", "project specific", "owner’s interest", "owners interest"}', null, null, true, null, 6),
        ('unassigned_nonconstruction', 3, null, null, null, true, null, 6)
        
        """)
        # arch
        op.execute("update settings set default_tier=4 where organization_id=10")
        op.execute("update settings set max_tier_for_auto_processing=2 where organization_id=10")
        op.execute("""
        insert into routing_rule (tag, "order", contained_words, not_contained_words, contained_phrases, skip_if_previous_rule_was_hit, tier, organization_id) values 
        ('arch_ejaffe', 1, '{"jaffe", "<EMAIL>"}', null, null, false, 0, 10),
        ('arch_dhaverson', 1, '{"haverson", "<EMAIL>"}', null, null, false, 0, 10),
        ('arch_aheaton', 1, '{"heaton", "cleapor", "<EMAIL>"}', null, null, false, 0, 10),
        ('arch_hbazzani', 1, '{"bazzani", "<EMAIL>"}', null, null, false, 0, 10),
        
        ('arch_lgarcia', 1, '{"lazaro", "<EMAIL>"}', null, null, false, 1, 10),
        ('arch_jcrist', 1, '{"<EMAIL>", "jcrist"}', null, null, false, 1, 10),
        ('arch_dchan', 1, '{"<EMAIL>", "dick", "dchan"}', null, null, false, 1, 10),
        ('arch_jbelloni', 1, '{"<EMAIL>", "belloni"}', null, null, false, 1, 10),
        ('arch_tfeshovets', 1, '{"<EMAIL>", "feshovets", "taras"}', null, null, false, 1, 10),
        ('arch_evillaflor', 1, '{"<EMAIL>", "villaflor"}', null, null, false, 1, 10),
        ('arch_elogan', 1, '{"<EMAIL>", "logan"}', null, null, false, 1, 10),
        ('arch_josh_testing', 1, '{"<EMAIL>"}', null, null, false, 1, 10),
        
        
        ('arch_tfreeman', 1, '{"<EMAIL>", "freeman"}', null, null, false, 2, 10),
        ('arch_fnichols', 1, '{"<EMAIL>", "fawn"}', null, null, false, 2, 10),
        ('arch_bdodson', 1, '{"<EMAIL>", "dodson"}', null, null, false, 2, 10),
        ('arch_pgoodwin', 1, '{"<EMAIL>", "goodwin"}', null, null, false, 2, 10),
        ('arch_rscott', 1, '{"<EMAIL>", "scott"}', null, null, false, 2, 10),
        ('arch_bboddy', 1, '{"<EMAIL>", "boddy"}', null, null, false, 2, 10),
        ('arch_clamprecht', 1, '{"<EMAIL>", "lamprecht", "chi yon"}', null, null, false, 2, 10),
        ('arch_jmautner', 1, '{"<EMAIL>", "mautner"}', null, null, false, 2, 10),
        
        ('arch_jaedack', 1, '{"<EMAIL>", "aedack"}', null, null, false, 3, 10),
        ('arch_dmosier', 1, '{"<EMAIL>", "mosier"}', null, null, false, 3, 10),
        ('arch_ekrakora', 1, '{"<EMAIL>", "krakora"}', null, null, false, 3, 10),
        ('arch_tmurrell', 1, '{"<EMAIL>", "murrell"}', null, null, false, 3, 10),
        ('arch_nhoffman', 1, '{"<EMAIL>", "hoffman"}', null, null, false, 3, 10),
        ('arch_apalmer', 1, '{"<EMAIL>", "palmer"}', null, null, false, 3, 10),
        ('arch_nnelson', 1, '{"<EMAIL>", "nelson"}', null, null, false, 3, 10),
        ('arch_rgates', 1, '{"<EMAIL>", "gates"}', null, null, false, 3, 10),
        ('arch_jhammond', 1, '{"<EMAIL>", "hammond"}', null, null, false, 3, 10)
        """)
        # guideone
        op.execute("update settings set default_tier=0 where organization_id=33")
        op.execute("update settings set max_tier_for_auto_processing=0 where organization_id=33")
        # munichre
        op.execute("update settings set default_tier=4 where organization_id=36")
        op.execute("update settings set max_tier_for_auto_processing=0 where organization_id=36")
        op.execute("""
                insert into routing_rule (tag, "order", contained_words, not_contained_words, contained_phrases, skip_if_previous_rule_was_hit, tier, organization_id) values 
                ('mr_jporter', 1, '{"jporter", "<EMAIL>", "Jennifer"}', null, null, false, 0, 36),
                ('mr_slaughlin', 1, '{"slaughlin", "<EMAIL>", "Stephanie"}', null, null, false, 0, 36),
                ('mr_mthompson', 1, '{"mthompson", "<EMAIL>", "Mark"}', null, null, false, 0, 36),
                ('mr_mgauthier', 1, '{"mgauthier", "<EMAIL>", "Matthew"}', null, null, false, 0, 36),
                ('mr_wbaker', 1, '{"wbaker", "<EMAIL>", "William Baker"}', null, null, false, 0, 36),
                ('mr_rdougherty', 1, '{"rdougherty", "<EMAIL>", "Rachael"}', null, null, false, 0, 36)
                """)
        # paragon
        op.execute("update settings set default_tier=0 where organization_id=37")
        op.execute("update settings set max_tier_for_auto_processing=0 where organization_id=37")
        # kalepa test
        op.execute("update settings set default_tier=0 where organization_id=3")
        op.execute("update settings set max_tier_for_auto_processing=0 where organization_id=3")
        # KIS
        op.execute("update settings set default_tier=0 where organization_id=7")
        op.execute("update settings set max_tier_for_auto_processing=0 where organization_id=7")
        # kalepa demo
        op.execute("update settings set default_tier=0 where organization_id=9")
        op.execute("update settings set max_tier_for_auto_processing=0 where organization_id=9")
        # cross org
        op.execute(f"""
        insert into routing_rule (tag, "order", contained_words, not_contained_words, contained_phrases, skip_if_previous_rule_was_hit, tier, organization_id) values
        ('construction', 1, null, null, '{CONSTRUCTION_PHRASES}', false, null, null)
        """)


def downgrade():
    pass

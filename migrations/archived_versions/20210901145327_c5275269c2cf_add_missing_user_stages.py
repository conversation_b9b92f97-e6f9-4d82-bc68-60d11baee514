"""Add missing user stages

Revision ID: c5275269c2cf
Revises: e4e33ff992f1
Create Date: 2021-09-01 14:53:27.339938+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c5275269c2cf"
down_revision = "e4e33ff992f1"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""insert into user_submission_stage(id, submission_id, user_id, stage) select uuid_generate_v4() 
    as id, submissions_reports.submission_id as submission_id, report_permissions.grantee_user_id as user_id, 
    'JUST_IN'::submissionstage from report_permissions join submissions_reports on 
    report_permissions.report_id = submissions_reports.report_id where (report_permissions.report_id, 
    report_permissions.grantee_user_id) not in (select submissions_reports.report_id, user_submission_stage.user_id 
    from user_submission_stage join submissions_reports on submissions_reports.submission_id = 
    user_submission_stage.submission_id)""")


def downgrade():
    pass

"""Add CAT Request Form File Type

Revision ID: ce43d7b72e25
Revises: 6e6ada4cfa7e
Create Date: 2021-06-24 19:02:33.144932+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ce43d7b72e25"
down_revision = "6e6ada4cfa7e"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'CAT_REQUEST_FORM';""")


def downgrade():
    pass

"""Parent Child File Relationship

Revision ID: c293e093758d
Revises: affbb81083d1
Create Date: 2023-03-10 13:25:51.005861+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c293e093758d"
down_revision = "affbb81083d1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("""
            ALTER TABLE files ADD COLUMN IF NOT EXISTS parent_file_id UUID;
        """)
    op.create_foreign_key(None, "files", "files", ["parent_file_id"], ["id"], ondelete="CASCADE")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "files", type_="foreignkey")
    op.drop_column("files", "parent_file_id")
    # ### end Alembic commands ###

"""Adds employement practices to management liability mode

Revision ID: adb05a215a04
Revises: 618f7682e5c7
Create Date: 2023-04-15 16:04:09.555647+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "adb05a215a04"
down_revision = "618f7682e5c7"
branch_labels = None
depends_on = None

management_liability_mode_id = "1b7d5832-9ac9-44a2-b88a-7e6d09f8b052"
employment_practices_general_card_props = '{"facts": {"group": "EMPLOYMENT_PRACTICES_CARD", "parentType": "BUSINESS"}}'
mode_row_id = "66cadaa7-ab4d-4be3-87a3-bf58ead355db"
mode_column_id = "c8a3604d-3c6d-4f23-b006-67e5a1e08f1c"


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
        INSERT INTO mode_rows (id, mode_id, position, title, is_collapsible, is_default_open) VALUES
        ('{mode_row_id}', '{management_liability_mode_id}', 100, 'Employment Practices', true, true);
        INSERT INTO mode_columns (row_id, id, width, position) VALUES
        ('{mode_row_id}', '{mode_column_id}', 12, 0);
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('{mode_column_id}', '748c9336-76bf-48ea-bc61-2cebf0c7f34e', 0, 'employment-practices-general', 'FACTS', 'General', '{employment_practices_general_card_props}');
        INSERT INTO mode_elements (id, mode_id, position, row_id, section_id) VALUES
        ('48b7321b-bdf3-4360-b597-aa4d06f784eb', '{management_liability_mode_id}', 150, '{mode_row_id}', NULL);
    """)


def downgrade():
    ...

"""Extend the recommendation enum

Revision ID: 26f59594ccd7
Revises: 6f27332469b7
Create Date: 2022-08-26 23:10:17.306725+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "26f59594ccd7"
down_revision = "6f27332469b7"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE recommendationtype ADD VALUE IF NOT EXISTS 'FLAG_FOR_REVIEW';""")
        op.execute("""ALTER TYPE recommendationtype ADD VALUE IF NOT EXISTS 'RED_FLAG';""")
        op.execute("""ALTER TYPE recommendationtype ADD VALUE IF NOT EXISTS 'PREFERRED';""")


def downgrade():
    pass

"""add coverages unique constraint

Revision ID: 89a00606a406
Revises: d1a5122d0fe8
Create Date: 2023-01-05 12:44:53.614621+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "89a00606a406"
down_revision = "d1a5122d0fe8"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
DELETE FROM submission_coverages t1
USING       submission_coverages t2
WHERE t1.CTID < t2.CTID
AND t1.submission_id = t2.submission_id
AND t1.coverage_id = t2.coverage_id
AND COALESCE(t1.coverage_type, 'null') = COALESCE(t2.coverage_type, 'null');

DELETE FROM submission_deductibles t1
USING       submission_deductibles t2
WHERE t1.CTID < t2.CTID
AND t1.submission_id = t2.submission_id
AND t1.coverage_id = t2.coverage_id
AND COALESCE(t1.coverage_type, 'null') = COALESCE(t2.coverage_type, 'null');

ALTER TABLE submission_coverages DROP CONSTRAINT IF EXISTS uq_submission_coverages_coverage_id_coverage_type_submission_id;
DROP INDEX IF EXISTS uq_submission_coverages_coverage_id_coverage_type_submission_id;

CREATE UNIQUE INDEX IF NOT EXISTS uq_submission_coverages_coverage_id_type_submission_id
ON submission_coverages (submission_id, coverage_id, coverage_type) WHERE coverage_type IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS uq_submission_coverages_coverage_id_type_submission_id_nulls
ON submission_coverages (submission_id, coverage_id) WHERE coverage_type IS NULL;

CREATE UNIQUE INDEX IF NOT EXISTS uq_submission_deductibles_coverage_id_type_submission_id
ON submission_deductibles (submission_id, coverage_id, coverage_type) WHERE coverage_type IS NOT NULL;

CREATE UNIQUE INDEX IF NOT EXISTS uq_submission_deductibles_coverage_id_type_submission_id_nulls
ON submission_deductibles (submission_id, coverage_id) WHERE coverage_type IS NULL;
    """)


def downgrade():
    pass

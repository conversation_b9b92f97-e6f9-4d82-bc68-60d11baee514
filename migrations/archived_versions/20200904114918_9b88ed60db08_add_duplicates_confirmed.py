"""empty message

Revision ID: 9b88ed60db08
Revises: 99f35068e0ea
Create Date: 2020-09-04 11:49:18.724511

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9b88ed60db08"
down_revision = "99f35068e0ea"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submission_businesses", sa.Column("org_duplicates_confirmed", sa.<PERSON>(), nullable=True))
    op.alter_column("submissions", "owner_id", existing_type=sa.INTEGER(), nullable=False)
    op.create_foreign_key(None, "submissions", "users", ["owner_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submission_businesses", "org_duplicates_confirmed")
    op.drop_constraint(None, "submissions", type_="foreignkey")
    op.alter_column("submissions", "owner_id", existing_type=sa.INTEGER(), nullable=True)
    # ### end Alembic commands ###

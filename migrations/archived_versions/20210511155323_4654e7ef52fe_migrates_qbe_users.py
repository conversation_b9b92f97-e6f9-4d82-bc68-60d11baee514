"""Migrates QBE users

Revision ID: 4654e7ef52fe
Revises: 8767943cdcd9
Create Date: 2021-05-11 15:53:23.843902+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "4654e7ef52fe"
down_revision = "8767943cdcd9"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<PERSON><PERSON><EMAIL>' and id = 141;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 65;
    """)

    conn.execute("delete from report_aliases where external_id = 'auth0|5f6e02880077930068de2cd3'")

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>' and id = 143;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<PERSON>.<PERSON>@us.qbe.com'
    where email = '<EMAIL>' and id = 74;
    """)

    conn.execute("delete from report_aliases where external_id = 'auth0|5f6e029a1213ba006fa2272b'")

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>' and id = 144;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 75;
    """)


def downgrade():
    pass

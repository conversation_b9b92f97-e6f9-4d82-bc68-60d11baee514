"""Fix Sensible Calls Table

Revision ID: f2ac08e79153
Revises: da127d3061c1
Create Date: 2023-02-27 17:43:55.216749+00:00

"""
# revision identifiers, used by Alembic.
revision = "f2ac08e79153"
down_revision = "da127d3061c1"
branch_labels = None
depends_on = None

from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

from copilot.models.sensible_calls import DAYS_OF_MONTH, organization_id_for_migration

sensible_calls_init_query = sa.text("""
    INSERT INTO sensible_calls (
        id,
        organization_id, 
        year,
        month,
        day,
        calls_made
    )
    VALUES (
        :id,
        :organization_id, 
        :year, 
        :month, 
        :day, 
        :calls_made
    ) ON CONFLICT DO NOTHING;
    """)


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()

    for organization_id in organization_id_for_migration:
        for year in range(2022, 2024):
            for month in range(1, 13):
                for day in range(DAYS_OF_MONTH[month], DAYS_OF_MONTH[month] + 1):
                    query_params = {
                        "id": uuid4(),
                        "organization_id": organization_id,
                        "year": year,
                        "month": month,
                        "day": day,
                        "calls_made": 0,
                    }
                    conn.execute(statement=sensible_calls_init_query, **query_params)
        for year in range(2024, 2026):
            for month in range(1, 13):
                for day in range(1, DAYS_OF_MONTH[month] + 1):
                    query_params = {
                        "id": uuid4(),
                        "organization_id": organization_id,
                        "year": year,
                        "month": month,
                        "day": day,
                        "calls_made": 0,
                    }
                    conn.execute(statement=sensible_calls_init_query, **query_params)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

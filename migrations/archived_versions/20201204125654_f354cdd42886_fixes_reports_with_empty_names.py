"""Fixes reports with empty names

Revision ID: f354cdd42886
Revises: 71566a6c2a7e
Create Date: 2020-12-04 12:56:54.784872-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f354cdd42886"
down_revision = "71566a6c2a7e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("update reports_v2 set name = 'My Report' where reports_v2.name = '';")


def downgrade():
    pass

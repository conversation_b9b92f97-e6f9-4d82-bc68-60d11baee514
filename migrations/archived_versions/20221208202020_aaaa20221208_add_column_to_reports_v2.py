"""Add column email_body to reports_v2 table

Revision ID: aaaa20221208
Revises: a10e1ae2f64a
Create Date: 2022-12-08 20:20:20.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "aaaa20221208"
down_revision = "a10e1ae2f64a"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("reports_v2", sa.Column("email_body", sa.Text(), nullable=True))


def downgrade():
    op.drop_column("reports_v2", "email_body")

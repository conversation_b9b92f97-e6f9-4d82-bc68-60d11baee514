"""Add mode previews

Revision ID: f32263869a5c
Revises: abd0896c4a80
Create Date: 2023-02-08 13:17:06.299012+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f32263869a5c"
down_revision = "6e2a228981f9"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "mode_card_previews",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("mode_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("card_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("title", sa.String(), nullable=True),
        sa.Column("position", sa.Small<PERSON>nteger(), nullable=False),
        sa.Column("props", postgresql.JSONB(none_as_null=True, astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(
            ["card_id"],
            ["mode_cards.id"],
        ),
        sa.ForeignKeyConstraint(
            ["mode_id"],
            ["modes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_mode_card_previews_card_id"), "mode_card_previews", ["card_id"], unique=False)
    op.create_index(op.f("ix_mode_card_previews_mode_id"), "mode_card_previews", ["mode_id"], unique=False)

    conn = op.get_bind()

    conn.execute("""
    insert into mode_card_previews (id, mode_id, card_id, title, position) values
    ('1392e678-78df-43ac-9a87-a0f62fa049cc', '2f622131-068c-4ca4-9066-99d7bb8436b8', '6afbd1b5-f7cd-42ef-aedf-7531edaf433a', null, 10),
    ('41ef9a78-4528-4d6e-99f5-479594cc9ae9', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 'e9ef7318-b076-42e1-a5e7-d692dc83ae19', null, 10),
    ('548d2a39-20f8-4ebb-b374-f4206f50cf22', 'a265715d-4411-4411-9fb0-e745695d8aa8', '00df525b-eaf7-4e47-abdf-cd6f263f8d03', null, 10),
    ('52f1365d-74b1-4f74-adb0-4e4c6e60518b', 'a72774ed-b6c9-4845-a9dc-310fba8d4879', '3963d269-d954-43ea-ac71-bc34f684562e', null, 10),
    ('8d79909c-7278-46b0-ab81-707db17cdb60', '2f622131-068c-4ca4-9066-99d7bb8436b8', '1f5d1ea1-e781-4661-bf9f-f420e7e6aca8', null, 20),
    ('d5cfb6ad-6f21-4fd1-a7e5-333efc006ac1', '0da04e4f-b332-4fa3-9f89-08caa046fc24', '873ccc46-2c35-4690-84e8-2ad2be7551d1', null, 20),
    ('0b627b16-23bf-4f6d-9806-31838542d0c8', 'a265715d-4411-4411-9fb0-e745695d8aa8', '53a8d4ba-8869-43d9-9a91-f56aaa457c14', null, 20),
    ('c1aa2b6f-6fd0-456b-9c3b-f854c911be5c', 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 'afe0344f-e32b-4c22-884e-fa2e9bdfe62b', null, 20),
    ('37ba4140-3084-4d89-a824-4ce5195d80ac', '2f622131-068c-4ca4-9066-99d7bb8436b8', 'e72996d0-8fe2-4fc3-8df5-af412614ae92', null, 30),
    ('76efb05d-156e-440d-b6ee-5dc42277c431', '0da04e4f-b332-4fa3-9f89-08caa046fc24', '5f89206c-e764-4ea1-aac8-03b8eca2120d', null, 30),
    ('ed1b70d1-05c4-423e-8eab-0d3d5a5d641c', 'a265715d-4411-4411-9fb0-e745695d8aa8', '13bd3924-fb04-4f6c-923e-b6ff91f6ce34', null, 30),
    ('48b4a242-372d-46e9-bb99-ecf528f74020', 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 'aa8184a5-df9e-460c-a955-df15ee25bc04', null, 30);
    """)


def downgrade():
    op.drop_index(op.f("ix_mode_card_previews_mode_id"), table_name="mode_card_previews")
    op.drop_index(op.f("ix_mode_card_previews_card_id"), table_name="mode_card_previews")
    op.drop_table("mode_card_previews")

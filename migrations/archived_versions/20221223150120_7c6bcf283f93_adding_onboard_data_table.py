"""Adding onboard data table

Revision ID: 7c6bcf283f93
Revises: 1584d62dcc88
Create Date: 2022-12-23 15:01:20.648829+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
from sqlalchemy.engine.reflection import Inspector
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7c6bcf283f93"
down_revision = "1584d62dcc88"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    tables = inspector.get_table_names()
    if "onboarded_files" not in tables:
        op.create_table(
            "onboarded_files",
            sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
            sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
            sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("onboarded_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
            sa.PrimaryKeyConstraint("id"),
        )
        op.create_index(op.f("ix_onboarded_files_submission_id"), "onboarded_files", ["submission_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_onboarded_files_submission_id"), table_name="onboarded_files")
    op.drop_table("onboarded_files")

"""adding Merged and Unknown file types

Revision ID: de58e5b7bbf3
Revises: 5c67b9ef4cbe
Create Date: 2023-01-26 15:29:33.531749+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e1r4ty5f6d42"
down_revision = "t65f7g8jk80j"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'MERGED';""")
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'UNKNOWN';""")


def downgrade():
    pass

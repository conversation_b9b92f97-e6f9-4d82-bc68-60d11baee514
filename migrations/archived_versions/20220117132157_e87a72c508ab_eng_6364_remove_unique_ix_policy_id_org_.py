"""eng_6364_remove_unique_ix_policy_id_org_id

Revision ID: e87a72c508ab
Revises: 5aef92b0c6e4
Create Date: 2022-01-17 13:21:57.269478+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e87a72c508ab"
down_revision = "5aef92b0c6e4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("_external_id_organization_id_uc", "policy", type_="unique")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint("_external_id_organization_id_uc", "policy", ["external_id", "organization_id"])
    # ### end Alembic commands ###

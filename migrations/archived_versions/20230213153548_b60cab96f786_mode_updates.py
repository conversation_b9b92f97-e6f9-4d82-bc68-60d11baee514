"""mode-updates

Revision ID: b60cab96f786
Revises: c62f4919d96e
Create Date: 2023-02-13 15:35:48.526026+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b60cab96f786"
down_revision = "c62f4919d96e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
            update mode_cards set title='Assigned Underwriters' where card_id='submission-assignee';
            update mode_rows set is_collapsible=TRUE where title = 'Submission Information';
        """)


def downgrade():
    pass

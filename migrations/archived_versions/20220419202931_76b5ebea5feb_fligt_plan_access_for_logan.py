"""fligt plan access for Logan

Revision ID: 76b5ebea5feb
Revises: e39bacd13ef9
Create Date: 2022-04-19 20:29:31.817432+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "76b5ebea5feb"
down_revision = "e39bacd13ef9"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        update users set role = 'manager' where email in ('<EMAIL>', '<EMAIL>')
    """)


def downgrade():
    pass

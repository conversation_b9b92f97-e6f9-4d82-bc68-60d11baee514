"""Adds is_immutable to RecomendationRule

Revision ID: 1abf4965a66a
Revises: b005a042111b
Create Date: 2021-03-10 17:07:12.835745-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1abf4965a66a"
down_revision = "7da2fa7da168"
branch_labels = None
depends_on = None

RULE_DESCRIPTION = (
    "If number of businesses in high hail risk greater than threshold of total number of businesses, refer"
)


def upgrade():
    op.add_column("recommendation_rule", sa.Column("is_immutable", sa.<PERSON><PERSON>an(), nullable=True))
    conn = op.get_bind()
    conn.execute(f"""UPDATE recommendation_rule SET is_immutable = TRUE
                     where description = '{RULE_DESCRIPTION}'""")
    conn = op.get_bind()
    conn.execute(f"""UPDATE recommendation_rule SET is_immutable = FALSE
                     WHERE is_immutable IS NULL""")


def downgrade():
    op.drop_column("recommendation_rule", "is_immutable")

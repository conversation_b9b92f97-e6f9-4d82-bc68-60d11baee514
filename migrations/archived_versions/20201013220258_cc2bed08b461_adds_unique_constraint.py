"""Adds unique constraint

Revision ID: cc2bed08b461
Revises: 30c0aaf769c3
Create Date: 2020-10-13 22:02:58.156058+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cc2bed08b461"
down_revision = "30c0aaf769c3"
branch_labels = None
depends_on = None


def upgrade():
    op.create_unique_constraint(None, "metric_preference", ["report_id", "metric_config_id"])


def downgrade():
    op.drop_constraint(None, "metric_preference", type_="unique")

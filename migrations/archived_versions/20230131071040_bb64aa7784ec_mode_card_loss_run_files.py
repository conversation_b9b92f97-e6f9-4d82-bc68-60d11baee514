"""Mode Card - Loss Runs

Revision ID: bb64aa7784ec
Revises: 0bc98822f64e
Create Date: 2023-01-31 07:10:40.219332+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bb64aa7784ec"
down_revision = "0bc98822f64e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        insert into mode_rows (id, created_at, mode_id, position, title) values ('e16415c9-5af1-43d1-9834-b1e130811e6d', current_timestamp, '0da04e4f-b332-4fa3-9f89-08caa046fc24', 169, 'Loss Run Files');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('8bc2dece-5faa-42b2-88ac-40959a778e97', current_timestamp, '2f622131-068c-4ca4-9066-99d7bb8436b8', 169, 'Loss Run Files');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('125407fb-4083-44f8-b775-1c1e6ccf026f', current_timestamp, 'a265715d-4411-4411-9fb0-e745695d8aa8', 169, 'Loss Run Files');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('c7750861-256d-4a63-aafc-7b9d37e77556', current_timestamp, 'e16415c9-5af1-43d1-9834-b1e130811e6d', 0, 12, 'Loss Run Files');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('cee61882-cd17-463f-91c2-0799df889e2d', current_timestamp, '8bc2dece-5faa-42b2-88ac-40959a778e97', 0, 12, 'Loss Run Files');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('511f6802-2236-4db5-b379-0c7dc627eeeb', current_timestamp, '125407fb-4083-44f8-b775-1c1e6ccf026f', 0, 12, 'Loss Run Files');
        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('ccc11ef7-0467-4995-9481-39c2063f1319', current_timestamp, 'c7750861-256d-4a63-aafc-7b9d37e77556', '', 0, 'LOSS_FILES_CARD', 'loss-files-card');
        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('c0dc089c-ae2f-4063-aa9c-8ed8d9d5f28a', current_timestamp, 'cee61882-cd17-463f-91c2-0799df889e2d', '', 0, 'LOSS_FILES_CARD', 'loss-files-card');
        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('f719bce7-54ab-4182-a8ba-97e91adda520', current_timestamp, '511f6802-2236-4db5-b379-0c7dc627eeeb', '', 0, 'LOSS_FILES_CARD', 'loss-files-card');
    """)


def downgrade():
    pass

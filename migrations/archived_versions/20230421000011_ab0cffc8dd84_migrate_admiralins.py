"""Migrate <EMAIL> to Kalepa org

Revision ID: ab0cffc8dd84
Revises: 6e2ae5db8d51
Create Date: 2023-04-21 00:00:11.106121+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ab0cffc8dd84"
down_revision = "6e2ae5db8d51"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        ALTER TABLE users DISABLE TRIGGER cross_check_users_trigger;
        UPDATE users
        SET organization_id = (SELECT id FROM organization WHERE name = 'Kalepa'), role = 'manager'
        WHERE email = '<EMAIL>';
        ALTER TABLE users ENABLE TRIGGER cross_check_users_trigger;
    """)


def downgrade():
    pass

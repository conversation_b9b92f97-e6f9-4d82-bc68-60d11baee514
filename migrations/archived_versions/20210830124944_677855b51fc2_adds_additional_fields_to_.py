"""Adds additional fields to SubmissionBusinessFieldValue

Revision ID: 677855b51fc2
Revises: 69edc1ef26fb
Create Date: 2021-08-30 12:49:44.016140+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "677855b51fc2"
down_revision = "69edc1ef26fb"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "submission_business_field_values", sa.Column("fact_parent_id", postgresql.UUID(as_uuid=True), nullable=True)
    )
    op.add_column(
        "submission_business_field_values",
        sa.Column("fact_parent_type", sa.Enum("BUSINESS", "PREMISES", name="parenttype"), nullable=True),
    )
    op.add_column(
        "submission_business_field_values", sa.Column("fact_subtype_id", postgresql.UUID(as_uuid=True), nullable=True)
    )


def downgrade():
    op.drop_column("submission_business_field_values", "fact_subtype_id")
    op.drop_column("submission_business_field_values", "fact_parent_type")
    op.drop_column("submission_business_field_values", "fact_parent_id")

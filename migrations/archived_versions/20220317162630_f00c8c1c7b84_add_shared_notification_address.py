"""Add shared_notification_address

Revision ID: f00c8c1c7b84
Revises: 782218aa9f1d
Create Date: 2022-03-17 16:26:30.313676+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f00c8c1c7b84"
down_revision = "782218aa9f1d"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("organization", sa.Column("shared_notification_address", sa.String(), nullable=True))
    conn = op.get_bind()
    conn.execute("""
        update organization set shared_notification_address='<EMAIL>' where id = 6;
    """)


def downgrade():
    op.drop_column("organization", "shared_notification_address")

"""Adds client_application.client_id unique constraint

Revision ID: 2310c360860a
Revises: 11f41db9c025
Create Date: 2022-01-13 22:58:30.799575+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "2310c360860a"
down_revision = "11f41db9c025"
branch_labels = None
depends_on = None


def upgrade():
    op.create_unique_constraint(None, "client_application", ["client_id"])
    op.create_foreign_key(None, "client_application", "organization", ["organization_id"], ["id"])


def downgrade():
    op.drop_constraint(None, "client_application", type_="foreignkey")
    op.drop_constraint(None, "client_application", type_="unique")

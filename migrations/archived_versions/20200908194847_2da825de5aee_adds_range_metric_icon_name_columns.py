"""Adds range metric icon name columns

Revision ID: 2da825de5aee
Revises: 905d7ce24a29
Create Date: 2020-09-08 19:48:47.019899+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2da825de5aee"
down_revision = "905d7ce24a29"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("range_summary", sa.Column("inside_range_icon_name", sa.String(length=30), nullable=True))
    op.add_column("range_summary", sa.Column("outside_range_icon_name", sa.String(length=30), nullable=True))


def downgrade():
    op.drop_column("range_summary", "outside_range_icon_name")
    op.drop_column("range_summary", "inside_range_icon_name")

"""add business_id list to mean metric

Revision ID: 9fb5280a9945
Revises: a6c6bab31ede
Create Date: 2020-10-12 16:06:43.070522+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9fb5280a9945"
down_revision = "a6c6bab31ede"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mean", sa.Column("business_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True))


def downgrade():
    op.drop_column("mean", "business_ids")

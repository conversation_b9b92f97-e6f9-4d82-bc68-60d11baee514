"""Add distribution channel employed setting

Revision ID: fffb30ce754b
Revises: 084e35b3d96b
Create Date: 2023-02-02 10:01:21.880592+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

from copilot.models.types import DistributionChannelEmployedType

# revision identifiers, used by Alembic.
revision = "fffb30ce754b"
down_revision = "084e35b3d96b"
branch_labels = None
depends_on = None


def upgrade():
    distribution_channel_employed_types = tuple([e.name for e in DistributionChannelEmployedType])
    op.execute(f"CREATE TYPE distributionchannelemployedtype AS ENUM {distribution_channel_employed_types};")
    op.add_column(
        "settings",
        sa.Column(
            "distribution_channel_employed",
            sa.Enum(*distribution_channel_employed_types, name="distributionchannelemployedtype"),
            nullable=True,
        ),
    )
    conn = op.get_bind()

    conn.execute("""
        update settings set distribution_channel_employed='BROKERAGE_AGENCY_BROKER_AGENT' where organization_id is not null;
        update settings set distribution_channel_employed='BROKERAGE_BROKER' where organization_id = 6 or organization_id = 10;
    """)


def downgrade():
    op.drop_column("settings", "distribution_channel_employed")
    op.execute("DROP TYPE IF EXISTS distributionchannelemployedtype;")

"""Update project location cards

Revision ID: 7bf0654421de
Revises: acfb30ce754b
Create Date: 2023-02-03 09:06:00.925032+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7bf0654421de"
down_revision = "acfb30ce754b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update mode_cards
    set props='{"businessSelect": "contractorProject", "path": "$.entity_data.entity.premises[*].premises.formatted_address"}'
    where id='449ef4fd-6000-492b-b4a0-d582c0901ffb';
    """)


def downgrade():
    pass

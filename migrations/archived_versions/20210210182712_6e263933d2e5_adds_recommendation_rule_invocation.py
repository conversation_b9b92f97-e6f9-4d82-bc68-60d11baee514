"""Adds recommendation_rule_invocation

Revision ID: 6e263933d2e5
Revises: c83a9112ea08
Create Date: 2021-02-10 18:27:12.269002+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6e263933d2e5"
down_revision = "c83a9112ea08"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "recommendation_rule_invocation",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("rule_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"],
            ["reports_v2.id"],
        ),
        sa.ForeignKeyConstraint(
            ["rule_id"],
            ["recommendation_rule.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_recommendation_rule_invocation_created_at"),
        "recommendation_rule_invocation",
        ["created_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_recommendation_rule_invocation_report_id"),
        "recommendation_rule_invocation",
        ["report_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_recommendation_rule_invocation_rule_id"), "recommendation_rule_invocation", ["rule_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_recommendation_rule_invocation_rule_id"), table_name="recommendation_rule_invocation")
    op.drop_index(op.f("ix_recommendation_rule_invocation_report_id"), table_name="recommendation_rule_invocation")
    op.drop_index(op.f("ix_recommendation_rule_invocation_created_at"), table_name="recommendation_rule_invocation")
    op.drop_table("recommendation_rule_invocation")

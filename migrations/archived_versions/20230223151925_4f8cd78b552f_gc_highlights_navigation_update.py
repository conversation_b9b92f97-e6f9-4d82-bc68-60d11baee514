"""gc highlights navigation update

Revision ID: 4f8cd78b552f
Revises: ac72139051a4
Create Date: 2023-02-23 15:19:25.865154+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4f8cd78b552f"
down_revision = "ac72139051a4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
UPDATE mode_cards
SET
   props = '{
  "columns": 4,
  "highlights": [
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "Legal filings",
      "cardId": "634fff2e-def0-449a-9e4d-5c4b3657e96d",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "LEGAL_FILING"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to legal filings"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "Osha violations",
      "cardId": "118cd60d-12c0-4fd8-8779-8ef01cff6efa",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "OSHA_VIOLATION",
          "businessSelect": "generalContractorAndDuplicates"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to Osha violations"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "EPA Inspections ",
      "cardId": "b4537251-2665-4224-96e0-3a750e18f095",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "EPA_INSPECTION",
          "businessSelect": "generalContractorAndDuplicates"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to EPA Inspections"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "High-risk exposures",
      "cardId": "6ad4689c-97a3-423a-b2bf-9b9f667ed196",
      "source": {
        "mapper": "numberOfPositiveFacts",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "FACT",
          "factSubtypes": [
            "PROJECT_USE_OF_EIFS",
            "PROJECT_SCAFFOLDING",
            "PROJECT_MOLD_REMOVAL",
            "PROJECT_ROOF_WORK",
            "PROJECT_CRANE_WORK",
            "PROJECT_DEMOLITION_WORK",
            "PROJECT_BLASTING_WORK",
            "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
            "PROJECT_EXCAVATION_WORK",
            "PROJECT_BELOW_GRADE",
            "PROJECT_DEPTH_OF_WORK",
            "PROJECT_HEIGHT_IN_FT",
            "PROJECT_HEIGHT_IN_STORIES",
            "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT",
            "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
          ],
          "businessSelect": "generalContractor"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to High-risk exposures"
    }
  ]
}
'
WHERE id = 'f5621019-d81c-4def-9b2e-8cd55e746cb1';



UPDATE mode_cards
SET
   props = '{
  "columns": 4,
  "highlights": [
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "Legal filings",
      "cardId": "22b625a0-af3f-4506-95cd-f47e48e141c3",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "LEGAL_FILING"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to legal filings"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "Osha violations",
      "cardId": "72425328-3e87-43dc-a3c2-4400dfa961ea",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "OSHA_VIOLATION",
          "businessSelect": "generalContractorAndDuplicates"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to Osha violations"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "EPA Inspections",
      "cardId": "fe8daea7-8d81-4e07-a97f-ad123e579bd2",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "EPA_INSPECTION",
          "businessSelect": "generalContractorAndDuplicates"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to EPA Inspections"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "High-risk exposures",
      "cardId": "5a27383d-1136-4d24-9618-d734565ff308",
      "source": {
        "mapper": "numberOfPositiveFacts",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "FACT",
          "factSubtypes": [
            "PROJECT_USE_OF_EIFS",
            "PROJECT_SCAFFOLDING",
            "PROJECT_MOLD_REMOVAL",
            "PROJECT_ROOF_WORK",
            "PROJECT_CRANE_WORK",
            "PROJECT_DEMOLITION_WORK",
            "PROJECT_BLASTING_WORK",
            "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
            "PROJECT_EXCAVATION_WORK",
            "PROJECT_BELOW_GRADE",
            "PROJECT_DEPTH_OF_WORK",
            "PROJECT_HEIGHT_IN_FT",
            "PROJECT_HEIGHT_IN_STORIES",
            "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT",
            "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
          ],
          "businessSelect": "contractorProject"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to High-risk exposures"
    }
  ]
}'
WHERE id = 'a529e1fc-b3c4-4837-8ebf-bebb5b6dfd37';
    """)


def downgrade():
    pass

"""adds business_id to insight_feedback

Revision ID: f08453a9616b
Revises: e8ba7d0f3f85
Create Date: 2020-10-08 09:44:05.115472+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f08453a9616b"
down_revision = "e8ba7d0f3f85"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("feedback", sa.Column("business_id", postgresql.UUID(), nullable=True))
    conn = op.get_bind()


def downgrade():
    op.drop_column("feedback", "business_id")

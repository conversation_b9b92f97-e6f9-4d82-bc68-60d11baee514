"""Improves loss run data model

Revision ID: ed9dc42eb1b5
Revises: 138c956c3233
Create Date: 2022-01-28 16:55:14.180728+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed9dc42eb1b5"
down_revision = "138c956c3233"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("loss", sa.Column("organization_id", sa.Integer(), nullable=True))
    op.add_column("loss", sa.Column("policy_id", sa.String(), nullable=True))
    op.alter_column("loss", "claim_number", existing_type=sa.VARCHAR(length=128), nullable=False)
    op.alter_column("loss", "submission_id", existing_type=postgresql.UUID(), nullable=True)
    op.create_index(op.f("ix_loss_claim_number"), "loss", ["claim_number"], unique=False)
    op.create_index(op.f("ix_loss_organization_id"), "loss", ["organization_id"], unique=False)
    op.create_index(op.f("ix_loss_policy_id"), "loss", ["policy_id"], unique=False)
    op.create_index(
        op.f("ix_loss_claim_number_policy_id_organization_id"),
        "loss",
        ["claim_number", "policy_id", "organization_id"],
        unique=True,
    )
    op.create_foreign_key(None, "loss", "organization", ["organization_id"], ["id"])


def downgrade():
    op.drop_constraint(None, "loss", type_="foreignkey")
    op.drop_constraint(None, "loss", type_="unique")
    op.drop_index(op.f("ix_loss_policy_id"), table_name="loss")
    op.drop_index(op.f("ix_loss_organization_id"), table_name="loss")
    op.drop_index(op.f("ix_loss_claim_number"), table_name="loss")
    op.drop_index(op.f("ix_loss_claim_number_policy_id_organization_id"), table_name="loss")
    op.alter_column("loss", "submission_id", existing_type=postgresql.UUID(), nullable=False)
    op.alter_column("loss", "claim_number", existing_type=sa.VARCHAR(length=128), nullable=True)
    op.drop_column("loss", "policy_id")
    op.drop_column("loss", "organization_id")

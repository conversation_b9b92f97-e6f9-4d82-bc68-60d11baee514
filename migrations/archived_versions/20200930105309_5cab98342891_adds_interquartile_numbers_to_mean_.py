"""adds interquartile_numbers to mean metric

Revision ID: 5cab98342891
Revises: 67664144751b
Create Date: 2020-09-30 10:53:09.768769+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5cab98342891"
down_revision = "67664144751b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mean", sa.Column("interquartile_numbers", postgresql.ARRAY(sa.Integer()), nullable=True))


def downgrade():
    op.drop_column("mean", "interquartile_numbers")

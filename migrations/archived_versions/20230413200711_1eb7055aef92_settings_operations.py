"""settings for operations

Revision ID: 1eb7055aef92
Revises: b0918baeaab7
Create Date: 2023-04-13 20:07:11.067340+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1eb7055aef92"
down_revision = "b0918baeaab7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("settings", sa.Column("is_operations", sa.<PERSON>(), nullable=True))
    op.execute("update settings set is_operations = true where organization_id = 3;")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("settings", "is_operations")
    # ### end Alembic commands ###

"""Removes LOSS_SUMMARIES_CARD cards

Revision ID: 3bd58f2b4a26
Revises: 5be067e5b9ef
Create Date: 2023-05-02 13:35:16.392543+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3bd58f2b4a26"
down_revision = "5be067e5b9ef"
branch_labels = None
depends_on = None

summary_card_type = "LOSS_SUMMARIES_CARD"


def upgrade():
    conn = op.get_bind()
    conn.execute(f"DELETE FROM mode_cards WHERE type = '{summary_card_type}'")


def downgrade():
    ...

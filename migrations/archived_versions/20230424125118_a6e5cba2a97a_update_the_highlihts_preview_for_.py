"""Update the highlihts preview for premises

Revision ID: a6e5cba2a97a
Revises: b5f0247191c0
Create Date: 2023-04-24 12:51:18.041545+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a6e5cba2a97a"
down_revision = "9a5926520ef4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
update mode_card_previews
set props = '{"showPremisesSummary":[],"highlights":[{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"noValuesLabel":"None found","redirectLinkLabel":"Go to Legal Filings"},{"icons":[{"name":"warning","color":"error"}],"label":"OSHA Violations","cardId":"osha-violations","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"OSHA_VIOLATION"}},"noValuesLabel":"None found","redirectLinkLabel":"Go to OSHA Violations"}]}'
where id='3d65d00d-aa03-4b7d-ad26-84fa0c56e446';
    """)


def downgrade():
    pass

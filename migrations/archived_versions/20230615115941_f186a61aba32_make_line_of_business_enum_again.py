"""make line of business enum again

Revision ID: f186a61aba32
Revises: 5f8b73522923
Create Date: 2023-06-15 11:59:41.246398+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f186a61aba32"
down_revision = "cd85af4e20a5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'PROPERTY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'UMBRELLA';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'MULTIPLE';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'OTHER';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'UNKNOWN';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'BOILER_MACHINERY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'BUSINESS_AUTO';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'BUSINESS_OWNERS';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'INLAND_MARINE';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'CRIME';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'CYBER_PRIVACY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'FIDUCIARY_LIABILITY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'FOREIGN_LIABILITY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'GARAGE_DEALERS';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'GENERAL_LIABILITY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'LIQUOR_LIABILITY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'PACKAGE';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'TERRORISM';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'TRUCKERS';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'WORKERS_COMP';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'PROFESSIONAL_LIABILITY';""")
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE IF NOT EXISTS 'PRODUCT_LIABILITY';""")

    op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
    op.execute("""
            UPDATE loss SET line_of_business='PROPERTY' WHERE line_of_business IN ('Property');
    """)
    op.execute("""
            UPDATE lob_carrier SET line_of_business='PROPERTY' WHERE line_of_business IN ('Property');
    """)

    op.execute("""
            UPDATE loss SET line_of_business='UMBRELLA' WHERE line_of_business IN ('Umbrella');
    """)
    op.execute("""
            UPDATE lob_carrier SET line_of_business='UMBRELLA' WHERE line_of_business IN ('Umbrella');
    """)

    op.execute("""
            UPDATE loss SET line_of_business='MULTIPLE' WHERE line_of_business IN ('Multiple');
    """)

    op.execute("""
            UPDATE loss SET line_of_business='OTHER' WHERE line_of_business IN ('Other');
    """)

    op.execute("""
            UPDATE loss SET line_of_business='BUSINESS_AUTO' WHERE line_of_business IN ('FLEET', 'businessAuto', 'Business Auto');
    """)
    op.execute("""
            UPDATE lob_carrier SET line_of_business='BUSINESS_AUTO' WHERE line_of_business = 'Business Auto';
    """)

    op.execute("""
            UPDATE loss SET line_of_business='BUSINESS_OWNERS' WHERE line_of_business = 'BOP';
    """)

    op.execute("""
            UPDATE loss SET line_of_business='GENERAL_LIABILITY' WHERE line_of_business IN ('generalLiability', 'General Liability', 'GL');
    """)
    op.execute("""
            UPDATE lob_carrier SET line_of_business='GENERAL_LIABILITY' WHERE line_of_business IN ('generalLiability', 'General Liability', 'GL');
    """)

    op.execute("""
            UPDATE loss SET line_of_business='WORKERS_COMP' WHERE line_of_business IN ('WC', 'Workers Comp');
    """)
    op.execute("""
            UPDATE lob_carrier SET line_of_business='WORKERS_COMP' WHERE line_of_business IN ('WC', 'Workers Comp');
    """)

    op.execute("""
            UPDATE loss SET line_of_business='UNKNOWN' WHERE line_of_business = 'Unknown';
    """)

    op.execute("""
            UPDATE loss SET line_of_business='PROFESSIONAL_LIABILITY' WHERE line_of_business = 'Professional Liability';
    """)
    op.execute("""
            UPDATE lob_carrier SET line_of_business='PROFESSIONAL_LIABILITY' WHERE line_of_business = 'Professional Liability';
    """)

    op.execute("""
            UPDATE loss SET line_of_business='PRODUCT_LIABILITY' WHERE line_of_business = 'Product Liability';
    """)
    op.execute("""
            UPDATE lob_carrier SET line_of_business='PRODUCT_LIABILITY' WHERE line_of_business = 'Product Liability';
    """)

    op.execute(
        "ALTER TABLE loss ALTER COLUMN line_of_business TYPE lineofbusinesstype USING"
        " line_of_business::lineofbusinesstype;"
    )

    op.execute(
        "ALTER TABLE lob_carrier ALTER COLUMN line_of_business TYPE lineofbusinesstype USING"
        " line_of_business::lineofbusinesstype;"
    )

    # default lob carrier mappings to reflect old logic
    op.execute(f"""
            insert into lob_carrier
            (id, lob_raw, line_of_business)
            values 
                (uuid_generate_v4(), 'property', 'PROPERTY'),
                (uuid_generate_v4(), 'gl', 'GENERAL_LIABILITY'),
                (uuid_generate_v4(), 'generalliability', 'GENERAL_LIABILITY'),
                (uuid_generate_v4(), 'general liability', 'GENERAL_LIABILITY'),
                (uuid_generate_v4(), 'wc', 'WORKERS_COMP'),
                (uuid_generate_v4(), 'workers comp', 'WORKERS_COMP'),
                (uuid_generate_v4(), 'workers_comp', 'WORKERS_COMP'),
                (uuid_generate_v4(), 'umbrella', 'UMBRELLA'),
                (uuid_generate_v4(), 'fleet', 'BUSINESS_AUTO'),
                (uuid_generate_v4(), 'business_auto', 'BUSINESS_AUTO'),
                (uuid_generate_v4(), 'businessauto', 'BUSINESS_AUTO'),
                (uuid_generate_v4(), 'business auto', 'BUSINESS_AUTO'),
                (uuid_generate_v4(), 'multiple', 'MULTIPLE'),
                (uuid_generate_v4(), 'other', 'OTHER'),
                (uuid_generate_v4(), 'unknown', 'UNKNOWN'),
                (uuid_generate_v4(), 'boiler_machinery', 'BOILER_MACHINERY'),
                (uuid_generate_v4(), 'business_owners', 'BUSINESS_OWNERS'),
                (uuid_generate_v4(), 'bop', 'BUSINESS_OWNERS'),
                (uuid_generate_v4(), 'commercial_inland_marine' ,'INLAND_MARINE'),
                (uuid_generate_v4(), 'inland_marine' ,'INLAND_MARINE'),
                (uuid_generate_v4(), 'crime', 'CRIME'),
                (uuid_generate_v4(), 'cyber_privacy', 'CYBER_PRIVACY'),
                (uuid_generate_v4(), 'fiduciary_liability', 'FIDUCIARY_LIABILITY'),
                (uuid_generate_v4(), 'foreign_liability', 'FOREIGN_LIABILITY'),
                (uuid_generate_v4(), 'garage_dealers', 'GARAGE_DEALERS'),
                (uuid_generate_v4(), 'liquor_liability', 'LIQUOR_LIABILITY'),
                (uuid_generate_v4(), 'package', 'PACKAGE'),
                (uuid_generate_v4(), 'terrorism', 'TERRORISM'),
                (uuid_generate_v4(), 'truckers', 'TRUCKERS'),
                (uuid_generate_v4(), 'professional_liability', 'PROFESSIONAL_LIABILITY'),
                (uuid_generate_v4(), 'product_liability', 'PRODUCT_LIABILITY');
        """)

    # use new normalization for lob_raw
    op.execute(r"""
        UPDATE lob_carrier 
        SET lob_raw=regexp_replace(
            regexp_replace(
                regexp_replace(
                    lower(lob_raw), '\s*([^\w\d\s]+)\s*', '\1', 'g'),
                '\s+', ' ', 'g'),
            '\s+$', '') WHERE lob_raw IS NOT NULL
        """)

    op.execute(r"""
        UPDATE loss
        SET original_line_of_business=regexp_replace(
            regexp_replace(
                regexp_replace(
                    lower(lob_raw), '\s*([^\w\d\s]+)\s*', '\1', 'g'),
                '\s+', ' ', 'g'),
            '\s+$', '') WHERE lob_raw IS NOT NULL
        """)


def downgrade():
    op.execute("ALTER TABLE loss ALTER COLUMN line_of_business TYPE varchar;")
    op.execute("ALTER TABLE lob_carrier ALTER COLUMN line_of_business TYPE varchar;")

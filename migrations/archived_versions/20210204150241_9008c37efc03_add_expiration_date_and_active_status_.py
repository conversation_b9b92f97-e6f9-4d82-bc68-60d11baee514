"""Add expiration date and active status to recommendation

Revision ID: 9008c37efc03
Revises: 0252ee623ebb
Create Date: 2021-02-04 15:02:41.506389+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9008c37efc03"
down_revision = "0252ee623ebb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("recommendation_rule", sa.Column("expiration_date", sa.DateTime(), nullable=True))
    op.add_column("recommendation_rule", sa.Column("is_active", sa.<PERSON>(), nullable=True))
    op.execute("UPDATE recommendation_rule SET is_active = true")
    op.alter_column("recommendation_rule", "is_active", nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("recommendation_rule", "is_active")
    op.drop_column("recommendation_rule", "expiration_date")
    # ### end Alembic commands ###

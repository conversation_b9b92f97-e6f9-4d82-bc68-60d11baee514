"""Add fleet & fleet + construction modes

Revision ID: d6dd6745cc08
Revises: 62e6654ec03d
Create Date: 2023-01-17 13:43:27.510470+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d6dd6745cc08"
down_revision = "62e6654ec03d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # set up correct order of sections
    conn.execute("""
    update mode_rows
    set position = 10
    where id in ('2a902880-b76d-4b61-9ae2-5cff6618a531', 'be27c54e-3033-4eb9-a351-167d81700bfa');
    
    update mode_rows
    set position = 20
    where id in ('ff801abf-0e85-471c-aab7-a8f2c04a83c2', '17dd5321-d5e7-4c6a-8aff-396eb5c861cf');
    
    update mode_rows
    set position = 50
    where id in ('f537fc68-ff88-4929-aa0d-ab8d4edd5a18', 'dd114578-3e7a-4821-b118-21ade1d2d097');
    
    update mode_rows
    set position = 60
    where id in ('fe161684-dd44-4ed0-8659-8eb77c19693f', '9553f8cf-d005-4096-97d8-50bc2e1232bf');
    
    update mode_rows
    set position = 80
    where id in ('02d5a75d-5429-4bd3-ae5f-29be4673739e', '2faed8f4-2a0a-4883-aa31-923c6621bd35');
    
    update mode_rows
    set position = 130
    where id='07402248-0997-4441-beb7-8e9b4b072297';
    """)

    # add the recommendations section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('e1e6afb6-8888-4c55-b4ec-a63ffbdbdc09', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 30, 'Recommendation');
    
    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('dd521d9a-6fe5-4f09-a799-ead223ae994e', 'e1e6afb6-8888-4c55-b4ec-a63ffbdbdc09', 0, 12, null);
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '873ccc46-2c35-4690-84e8-2ad2be7551d1',
        'dd521d9a-6fe5-4f09-a799-ead223ae994e',
        'Rules', 
        10,
        'DEFAULT',
        'recommendations-rules',
        '{"height": 50}'
    );
    
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('cf8fee73-349c-47a7-a622-615766135b48', '2f622131-068c-4ca4-9066-99d7bb8436b8', 30, 'Recommendation');
    
    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('3ceb11fe-0741-4722-a3c5-16113a92108b', 'cf8fee73-349c-47a7-a622-615766135b48', 0, 12, null);
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '1f5d1ea1-e781-4661-bf9f-f420e7e6aca8',
        '3ceb11fe-0741-4722-a3c5-16113a92108b',
        'Rules', 
        10,
        'DEFAULT',
        'recommendations-rules',
        '{"height": 50}'
    );
    """)

    # add the important highlights section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('9e88f413-c20e-4f9d-b116-acd9920ff0be', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 40, 'Important highlights');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('a77bc00e-6fcf-4b9c-af64-316af5458762', '9e88f413-c20e-4f9d-b116-acd9920ff0be', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '59d67a6c-b911-4790-b598-da276d2ff290',
        'a77bc00e-6fcf-4b9c-af64-316af5458762',
        'GC highlights', 
        10,
        'DEFAULT',
        'gc-highlights',
        '{"height": 50}'
    );

    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('6984e90e-c73a-44d0-8be4-250e853af384', '2f622131-068c-4ca4-9066-99d7bb8436b8', 40, 'Important highlights');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('75aaf334-9fd1-4961-a801-a9a659598f10', '6984e90e-c73a-44d0-8be4-250e853af384', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '052cab3f-8d48-45b0-8397-4bc0b66e583f',
        '75aaf334-9fd1-4961-a801-a9a659598f10',
        'GC highlights',
        10,
        'DEFAULT',
        'gc-highlights',
        '{"height": 50}'
    );
    """)

    # fill in general contractor cards
    conn.execute("""
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '2b48b297-9f55-4f76-b144-a80b6db4d700',
        '44ab64d9-4327-476a-a156-a10f08195696',
        'General Contractor', 
        10,
        'DEFAULT',
        'general-contractor',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '3786a024-2929-4793-8448-dbf10ee2bb49',
        '4055ad70-5a24-4ef5-82dc-9aa346896c71',
        'General Contractor', 
        10,
        'DEFAULT',
        'general-contractor',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '2cdf26c8-247f-4007-8839-940c1bdce261',
        '44ab64d9-4327-476a-a156-a10f08195696',
        'Financials', 
        20,
        'DEFAULT',
        'gc-financials',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '2eb7e12b-694b-4ca1-a2f7-d9d080052106',
        '4055ad70-5a24-4ef5-82dc-9aa346896c71',
        'Financials', 
        20,
        'DEFAULT',
        'gc-financials',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '10499924-b32b-464c-a5c8-3defcf866e8d',
        '44ab64d9-4327-476a-a156-a10f08195696',
        'Permits', 
        40,
        'DEFAULT',
        'gc-permits',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'a18a11e4-e8f1-47d6-b73b-514b01775fa5',
        '4055ad70-5a24-4ef5-82dc-9aa346896c71',
        'Permits', 
        40,
        'DEFAULT',
        'gc-permits',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'f6427a97-6f7a-4c78-aa51-e90e0be6f2c7',
        '44ab64d9-4327-476a-a156-a10f08195696',
        'Other named insured', 
        50,
        'DEFAULT',
        'gc-other-named-insured',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'e7ad716f-ace9-402e-87c2-ae3ca2d8275d',
        '4055ad70-5a24-4ef5-82dc-9aa346896c71',
        'Other named insured', 
        50,
        'DEFAULT',
        'gc-other-named-insured',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '9d66ae57-85f0-4aab-9bf5-0d2c85b2fca2',
        '44ab64d9-4327-476a-a156-a10f08195696',
        'News', 
        60,
        'DEFAULT',
        'gc-news',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'c80e0951-03f2-486b-a82b-48df84946243',
        '4055ad70-5a24-4ef5-82dc-9aa346896c71',
        'News', 
        60,
        'DEFAULT',
        'gc-news',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'f9f94a42-f555-47b8-946c-9be1b6b743da',
        '44ab64d9-4327-476a-a156-a10f08195696',
        'Legal filings', 
        70,
        'DEFAULT',
        'gc-legal-filings',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '0b596b51-6f73-41ef-b55a-961c05f30d0d',
        '4055ad70-5a24-4ef5-82dc-9aa346896c71',
        'Legal filings', 
        70,
        'DEFAULT',
        'gc-legal-filings',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'dfee9595-a6f8-43a8-990c-649ddede36d1',
        '44ab64d9-4327-476a-a156-a10f08195696',
        'Complaints', 
        80,
        'DEFAULT',
        'gc-complaints',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'ae340a13-bfb4-456b-bdd5-c33c876feade',
        '4055ad70-5a24-4ef5-82dc-9aa346896c71',
        'Complaints', 
        80,
        'DEFAULT',
        'gc-complaints',
        '{"height": 50}'
    );
    
    """)

    # add contractor experience section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('648ed96a-0a72-4a91-ab33-5562cc985676', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 70, 'Contractor experience');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('70513e35-9058-4f6e-84b8-ee3f7cde9abc', '648ed96a-0a72-4a91-ab33-5562cc985676', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'a60790f1-1676-4c27-8cad-808081150b08',
        '70513e35-9058-4f6e-84b8-ee3f7cde9abc',
        'Breakdown of work', 
        10,
        'DEFAULT',
        'ce-breakdown-of-work',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '6569afc9-d094-4f17-b333-e54495ebb0d2',
        '70513e35-9058-4f6e-84b8-ee3f7cde9abc',
        'Other', 
        20,
        'DEFAULT',
        'ce-other',
        '{"height": 50}'
    );

    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('33d547be-d0dd-4892-8299-cfa22505d77b', '2f622131-068c-4ca4-9066-99d7bb8436b8', 70, 'Contractor experience');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('be322fe2-8495-4202-ae96-10ebc661520a', '33d547be-d0dd-4892-8299-cfa22505d77b', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '105f4e08-677a-494d-bd1c-4d25616b49a0',
        'be322fe2-8495-4202-ae96-10ebc661520a',
        'Breakdown of work',
        10,
        'DEFAULT',
        'ce-breakdown-of-work',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '3a81b372-e88a-497b-a994-6e280f6304f1',
        'be322fe2-8495-4202-ae96-10ebc661520a',
        'Other', 
        20,
        'DEFAULT',
        'ce-other',
        '{"height": 50}'
    );
    """)

    # add the images section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('9ecf8869-bb28-4b83-a9f8-753eeac7af7f', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 90, 'Images');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('7ed2bd1e-c564-4d97-8f9c-968d19f0508c', '9ecf8869-bb28-4b83-a9f8-753eeac7af7f', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'cd27fed1-6793-4e39-8e94-f14959f85273',
        '7ed2bd1e-c564-4d97-8f9c-968d19f0508c',
        'Images', 
        10,
        'DEFAULT',
        'images',
        '{"height": 50}'
    );

    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('eb436e71-7ab1-42fa-a3bc-494bbd43e1a4', '2f622131-068c-4ca4-9066-99d7bb8436b8', 90, 'Images');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('cf3840bd-20c1-41ca-9b0d-31d46de78ce9', 'eb436e71-7ab1-42fa-a3bc-494bbd43e1a4', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '047f00cb-9d13-4e43-b10b-e45c1f04c55b',
        'cf3840bd-20c1-41ca-9b0d-31d46de78ce9',
        'Images', 
        10,
        'DEFAULT',
        'images',
        '{"height": 50}'
    );
    """)

    # add the project description section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('64cf6479-40c1-47f5-9980-857222788b9f', '2f622131-068c-4ca4-9066-99d7bb8436b8', 100, 'Project description');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('b1df3be1-a533-46ac-9250-bb76e43b8e78', '64cf6479-40c1-47f5-9980-857222788b9f', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '55be9fb2-1f8b-456f-944d-65158faafb74',
        'b1df3be1-a533-46ac-9250-bb76e43b8e78',
        'Project description', 
        10,
        'DEFAULT',
        'project-description',
        '{"height": 50}'
    );
    """)

    # add the project location section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('1b2dab12-6ae4-41ed-bfa2-b22bd1e9c461', '2f622131-068c-4ca4-9066-99d7bb8436b8', 110, 'Project location');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('8546ec5c-1c9c-418a-82e7-b9a6a7d2748c', '1b2dab12-6ae4-41ed-bfa2-b22bd1e9c461', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '5eda0384-3162-427e-b4f9-af4c540daadb',
        '8546ec5c-1c9c-418a-82e7-b9a6a7d2748c',
        'Project location', 
        10,
        'DEFAULT',
        'project-location',
        '{"height": 50}'
    );
    
    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '449ef4fd-6000-492b-b4a0-d582c0901ffb',
        '8546ec5c-1c9c-418a-82e7-b9a6a7d2748c',
        'Address', 
        20,
        'DEFAULT',
        'project-location-address',
        '{"height": 50}'
    );
    """)

    # add the catastrophic risks section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('93a2a303-6baf-42b6-b128-f8bfc12260c1', '2f622131-068c-4ca4-9066-99d7bb8436b8', 120, 'Catastrophic risks');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('ce1e177b-fc06-4996-9b1b-bb697e542654', '93a2a303-6baf-42b6-b128-f8bfc12260c1', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '6e64374c-f86f-44d2-a4de-f418cf89c11e',
        'ce1e177b-fc06-4996-9b1b-bb697e542654',
        'Risks', 
        10,
        'DEFAULT',
        'catastrophic-risks',
        '{"height": 50}'
    );
    """)

    # add the geotech report section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('e889e9a9-4ab7-475f-820d-1989c4bed992', '2f622131-068c-4ca4-9066-99d7bb8436b8', 140, 'Geotech report');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('2bb4ba9f-760d-4acb-a25b-d85f2aefbaa3', 'e889e9a9-4ab7-475f-820d-1989c4bed992', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'b11cafe2-ddd3-4b36-81ca-5055d40c3c1a',
        '2bb4ba9f-760d-4acb-a25b-d85f2aefbaa3',
        'Geotech file', 
        10,
        'DEFAULT',
        'geotech-report-file',
        '{"height": 50}'
    );

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'c656e43a-459d-4ed9-9731-caf34cc9e9aa',
        '2bb4ba9f-760d-4acb-a25b-d85f2aefbaa3',
        'Geotech snippet', 
        20,
        'DEFAULT',
        'geotech-report-file-snippet',
        '{"height": 50}'
    );
    """)

    # add the crime score section
    conn.execute("""
    INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('617578e0-792b-41fb-b8f1-faf4124c8dd6', '2f622131-068c-4ca4-9066-99d7bb8436b8', 160, 'Crime score');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('5318ac83-1300-46b6-9fe9-25824b5ecdfe', '617578e0-792b-41fb-b8f1-faf4124c8dd6', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        'e5f53f63-012c-4bba-9bdb-9587fb8715d0',
        '5318ac83-1300-46b6-9fe9-25824b5ecdfe',
        'Crime score', 
        10,
        'DEFAULT',
        'crime-score',
        '{"height": 50}'
    );
    """)

    # remove left over sections
    conn.execute("""
    delete from mode_cards
    using mode_columns
    where mode_cards.column_id = mode_columns.id
    and mode_columns.row_id in ('ef3259ab-db69-4fc6-b5fd-e77283406614', '9c9b70f1-fe7b-48f3-83fa-9f16a7aa63bc');
    
    delete from mode_columns
    where row_id in ('ef3259ab-db69-4fc6-b5fd-e77283406614', '9c9b70f1-fe7b-48f3-83fa-9f16a7aa63bc');
    
    delete from mode_rows
    where id in ('ef3259ab-db69-4fc6-b5fd-e77283406614', '9c9b70f1-fe7b-48f3-83fa-9f16a7aa63bc');
    """)


def downgrade():
    pass

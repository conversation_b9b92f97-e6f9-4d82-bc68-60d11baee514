"""Add fleet operations

Revision ID: a8b2170aa29e
Revises: 3d1a690bfca5
Create Date: 2023-02-16 10:15:56.068121+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a8b2170aa29e"
down_revision = "3d1a690bfca5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mode_cards", sa.Column("tooltip", sa.String(), nullable=True))

    conn = op.get_bind()
    conn.execute("""
    update mode_cards
    set type='FACTS', props='{"facts":{"parentType":"SUBMISSION","factSubtypeIds":["CARGO_CARRIED","FLEET_OPERATION_STATES","CARRIER_OPERATION_STATUS","CARRIER_OPERATION","OPERATION_CLASSIFICATION","TRANSPORTATION_SAFETY_RATING","TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS","NUMBER_OF_VEHICLES","NUMBER_OF_REGISTERED_DRIVERS","TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION","TRANSPORTATION_HAS_CONSUMER_COMPLAINTS","IS_NEW_ENTRANT","TRANSPORTATION_RISKY_NEW_ENTRANT"]},"hiddenWhenAllFactsNegative":true}'
    where id='2406045d-18ae-4f98-af8d-2f3a514c0658';
    
    update mode_cards
    set type='OUT_OF_SERVICE_CARD', tooltip='Rates based on FMCSA inspections'
    where id='ad816f12-559f-4c12-93c9-281d88b5bbb4';
    
    update mode_cards
    set type='FACTS', props='{"facts":{"parentType":"SUBMISSION","factSubtypeIds":["TRANSPORTATION_BASIC_BENCHMARK_UNSAFE_DRIVING","TRANSPORTATION_BASIC_BENCHMARK_HOS_COMPLIANCE","TRANSPORTATION_BASIC_BENCHMARK_VEHICLE_MAINTENANCE","TRANSPORTATION_BASIC_BENCHMARK_DRUGS_ALCOHOL","TRANSPORTATION_BASIC_BENCHMARK_DRIVER_FITNESS"]},"hiddenWhenAllFactsNegative":true}', tooltip='These are raw BASIC scores from FMCSA, which represent a relative severity of FMCSA violations. 0 is the best score possible.'
    where id='eab31101-4c02-477c-87aa-e5a60fb00c89';
    """)


def downgrade():
    op.drop_column("mode_cards", "tooltip")

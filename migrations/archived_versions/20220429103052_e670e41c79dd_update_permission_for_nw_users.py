"""Update user names

Revision ID: e670e41c79dd
Revises: e6b4905a5fe4
Create Date: 2022-04-29 10:30:52.916467+00:00

"""
from alembic import op

revision = "e670e41c79dd"
down_revision = "e6b4905a5fe4"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
                update report_permissions set permission_type = 'OWNER' 
                where report_permissions.id in (
                select rp.id from reports_v2
                inner join report_permissions rp on reports_v2.id = rp.report_id
                inner join users u on rp.grantee_user_id = u.id
                where organization_id = 6 and is_org_permission = false and u.id != 154 and permission_type = 'EDITOR' and rp.created_at > now() - interval '5 days');
        """)
        op.execute("update users set name='<PERSON><PERSON><PERSON><PERSON>, <PERSON> (<PERSON><PERSON><PERSON>)' where email='<EMAIL>';")


def downgrade():
    pass

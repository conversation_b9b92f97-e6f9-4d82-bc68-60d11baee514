"""Change coverage handler config

Revision ID: 6a4d0a6de94c
Revises: 7f77d535d52d
Create Date: 2023-03-15 09:51:49.017392+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6a4d0a6de94c"
down_revision = "7f77d535d52d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()

    conn.execute("""
    update sync_configuration set configuration = jsonb_set(configuration, '{handlers,2,config,removal_strategy}', to_jsonb('ONE_OF'::text)) where organization_id=6;
    """)
    conn.execute("""
    update sync_configuration set configuration = jsonb_set(configuration, '{handlers,2,config,removal_strategy}', to_jsonb('ONE_OF'::text)) where organization_id=10;
    """)

    conn.execute("""
        update sync_configuration set configuration = jsonb_set(configuration, '{handlers,2,config,coverages}', to_jsonb('{"umbrella", "generalLiability"}'::text[])) where
    organization_id = 6;""")

    conn.execute("""
        update sync_configuration set configuration = jsonb_set(configuration, '{handlers,2,config,coverages}', to_jsonb('{"umbrella", "generalLiability", "businessAuto"}'::text[])) where
    organization_id = 10;""")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

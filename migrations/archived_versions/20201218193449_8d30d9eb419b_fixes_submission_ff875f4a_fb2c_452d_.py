"""Fixes submission ff875f4a-fb2c-452d-ad1f-dce41b2114bb

Revision ID: 8d30d9eb419b
Revises: 5bf8c7941349
Create Date: 2020-12-18 12:34:49.851313-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8d30d9eb419b"
down_revision = "5bf8c7941349"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""update submission_snapshot set snapshot = to_jsonb(snapshot::text)::jsonb 
    where submission_id = 'ff875f4a-fb2c-452d-ad1f-dce41b2114bb';""")


def downgrade():
    pass

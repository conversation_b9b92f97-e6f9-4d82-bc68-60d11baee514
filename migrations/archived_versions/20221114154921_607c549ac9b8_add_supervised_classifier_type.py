"""Add supervised classifier type

Revision ID: 607c549ac9b8
Revises: 974f985770cf
Create Date: 2022-11-14 15:49:21.194623+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "607c549ac9b8"
down_revision = "974f985770cf"
branch_labels = None
depends_on = None

ARNS = (
    "INFER_NAICS_TWO_DIGIT",
    "INFER_CONTRACTOR_SERVICES",
    "INFER_DANGEROUS_MATERIALS",
    "INFER_HAS_ENTERTAINMENT_BLUE",
    "INFER_HAS_PROSTITUTION_BLUE",
    "INFER_HAS_DELIVERY_BLUE",
    "INFER_NAICS_FOOD_ACCOMMODATION72",
    "INFER_NAICS_CONSTRUCTION23",
    "INFER_NAICS_FLEET4849",
)


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE customizableclassifiertype ADD VALUE IF NOT EXISTS 'SUPERVISED';")
        op.execute(f"CREATE TYPE supervisedclassifierarn AS ENUM {ARNS};")
    op.add_column(
        "customizable_classifiers",
        sa.Column(
            "supervised_classifier_arn",
            sa.Enum(
                *ARNS,
                name="supervisedclassifierarn",
            ),
            nullable=True,
        ),
    )


def downgrade():
    pass

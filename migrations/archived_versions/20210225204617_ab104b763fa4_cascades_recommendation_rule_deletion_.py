"""Cascades recommendation rule deletion to invocation

Revision ID: ab104b763fa4
Revises: 9969a79434b5
Create Date: 2021-02-25 20:46:17.526299+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ab104b763fa4"
down_revision = "9969a79434b5"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint(
        "recommendation_rule_invocation_rule_id_fkey", "recommendation_rule_invocation", type_="foreignkey"
    )
    op.create_foreign_key(
        None, "recommendation_rule_invocation", "recommendation_rule", ["rule_id"], ["id"], ondelete="CASCADE"
    )


def downgrade():
    op.drop_constraint(None, "recommendation_rule_invocation", type_="foreignkey")
    op.create_foreign_key(
        "recommendation_rule_invocation_rule_id_fkey",
        "recommendation_rule_invocation",
        "recommendation_rule",
        ["rule_id"],
        ["id"],
    )

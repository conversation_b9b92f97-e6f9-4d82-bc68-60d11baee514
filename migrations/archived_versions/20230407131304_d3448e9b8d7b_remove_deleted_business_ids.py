"""remove deleted business ids

Revision ID: d3448e9b8d7b
Revises: 3ejn77723h22
Create Date: 2023-04-07 13:13:04.674836+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d3448e9b8d7b"
down_revision = "05e51b61758d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE submission_businesses SET is_user_confirmed = null WHERE business_id = '9617f6df-ff45-447e-a166-1d091fee1eaf';
        """)
    conn.execute("""
        UPDATE submission_businesses SET business_id = null WHERE business_id = '9617f6df-ff45-447e-a166-1d091fee1eaf';
        """)


def downgrade():
    pass

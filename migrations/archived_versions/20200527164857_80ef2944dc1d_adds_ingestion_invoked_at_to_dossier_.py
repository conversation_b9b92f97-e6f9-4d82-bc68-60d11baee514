"""adds ingestion_invoked_at to dossier table

Revision ID: 80ef2944dc1d
Revises: 8b5c5f83ac4b
Create Date: 2020-05-27 16:48:57.474042

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "80ef2944dc1d"
down_revision = "8b5c5f83ac4b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""ALTER TABLE dossiers ADD COLUMN if not exists ingestion_invoked_at timestamp;""")
    conn.execute("""ALTER TABLE dossiers ALTER COLUMN insights_invoked_at DROP NOT NULL;""")


def downgrade():
    conn = op.get_bind()
    conn.execute("""ALTER TABLE dossiers DROP COLUMN ingestion_invoked_at""")

"""Add recommendations card

Revision ID: 671bf32f3224
Revises: 71d8d2df730f
Create Date: 2023-01-19 16:32:04.190132+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "671bf32f3224"
down_revision = "71d8d2df730f"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards
        set title='Rules triggered', type='RECOMMENDATIONS_CARD', props=null
        where title='Rules';
        
        INSERT INTO mode_rows(id, mode_id, position, title) VALUES
        ('de04ba9c-2714-48ab-b5df-e3178f5ec071', 'a265715d-4411-4411-9fb0-e745695d8aa8', 50, 'Recommendation');
        
        INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
        ('22d61e93-dbf5-403a-91bd-2b3a5fe233d7', 'de04ba9c-2714-48ab-b5df-e3178f5ec071', 0, 12, null);
        
        INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
        (
            '53a8d4ba-8869-43d9-9a91-f56aaa457c14',
            '22d61e93-dbf5-403a-91bd-2b3a5fe233d7',
            'Rules triggered', 
            10,
            'RECOMMENDATIONS_CARD',
            'recommendations-rules',
            null
        );
    """)


def downgrade():
    pass

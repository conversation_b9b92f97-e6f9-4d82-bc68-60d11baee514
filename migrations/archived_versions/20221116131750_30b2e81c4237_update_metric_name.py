"""update metric name

Revision ID: 30b2e81c4237
Revises: 6dcc87dcb8af
Create Date: 2022-11-16 13:17:50.876529+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "30b2e81c4237"
down_revision = "6dcc87dcb8af"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("SET statement_timeout TO '600 s';")  # 10 mins
    conn.execute("""
        UPDATE metric SET name = 'Production Year (Submission)' WHERE name = 'Submission Year of the vehicle production';
        UPDATE metric_preference SET display_name = 'Production Year (Submission)' WHERE display_name = 'Submission Year of the vehicle production';

        UPDATE metric SET name = 'Submission Make' WHERE name = 'Make (Submission)';
        UPDATE metric_preference SET display_name = 'Submission Make' WHERE display_name = 'Make (Submission)';
        """)


def downgrade():
    pass

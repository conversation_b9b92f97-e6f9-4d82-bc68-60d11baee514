"""add munich re sync config

Revision ID: 17b26a71c039
Revises: d37d8506c9b3
Create Date: 2023-04-27 06:43:29.000127+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "17b26a71c039"
down_revision = "d37d8506c9b3"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE sync_configuration set configuration = jsonb_set(configuration, '{handlers,1,config,allow_backwards_status_update}', to_jsonb(TRUE)) where organization_id=6;
        """)

    conn.execute("""
        UPDATE sync_configuration set configuration = jsonb_set(configuration, '{handlers,1,config,allow_backwards_status_update}', to_jsonb(TRUE)) where organization_id=10;
        """)

    munich_re_config = """
        {
                "handlers": [{
                        "config": {},
                        "handler_type": "POLICY_HANDLER"
                }, {
                        "config": {
                                "allow_backwards_status_update": false
                        },
                        "handler_type": "SUBMISSION_HANDLER"
                }, {
                        "config": {
                                "coverages": ["umbrella", "generalLiability", "property"],
                                "removal_strategy": "ONE_OF",
                                "remove_non_matching_coverages": true
                        },
                        "handler_type": "COVERAGE_HANDLER"
                }, {
                        "config": {
                                "default_primary_naics": {},
                                "remove_previous_assignees": true
                        },
                        "handler_type": "UNDERWRITER_HANDLER"
                }],
                "max_attempts": 0,
                "submission_matcher_config": {
                        "shell_owner_email": "<EMAIL>",
                        "fuzzy_matching_level": 2,
                        "duplicate_submissions": true,
                        "original_max_days_old": 6,
                        "original_min_days_old": 2,
                        "name_match_max_days_old": 3,
                        "create_shell_submissions": false,
                        "shell_submissions_days_passed": 7
                }
        }"""

    conn.execute(
        "INSERT INTO sync_configuration (id, updated_at, organization_id, configuration) values (uuid_generate_v4(),"
        f" null, 36, '{munich_re_config}');"
    )


def downgrade():
    pass

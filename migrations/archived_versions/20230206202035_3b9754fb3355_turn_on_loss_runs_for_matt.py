"""Turn on Loss Runs for Matt

Revision ID: 3b9754fb3355
Revises: 4cceabf2305e
Create Date: 2023-02-06 20:20:35.056777+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3b9754fb3355"
down_revision = "4cceabf2305e"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""
        INSERT INTO settings (id, created_at, updated_at, user_id, loss_runs_enabled)
        SELECT uuid_generate_v4(), now(), null, id, true
        FROM users
        WHERE email='<EMAIL>' ON CONFLICT DO NOTHING;
    """)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

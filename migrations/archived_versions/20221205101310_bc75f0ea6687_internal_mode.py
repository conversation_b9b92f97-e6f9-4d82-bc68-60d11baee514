"""internal_mode

Revision ID: bc75f0ea6687
Revises: 1657dea7ecf4
Create Date: 2022-12-05 10:13:10.444224+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "bc75f0ea6687"
down_revision = "1657dea7ecf4"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE cardtype ADD VALUE IF NOT EXISTS 'OSHA_VIOLATION'""")

    conn = op.get_bind()

    # Inserting with hardcoded ids because as of now these tables does not have default values
    # organisation_id 3 -> Kalepa internal organisation (same in all envs)
    conn.execute("""
        INSERT INTO modes(id, name) VALUES 
            ('a265715d-4411-4411-9fb0-e745695d8aa8', 'Internal Mode');
        INSERT INTO mode_rows(id, mode_id, position) VALUES 
            ('a2d3b981-4bb3-4354-9762-43eef847b908', 'a265715d-4411-4411-9fb0-e745695d8aa8', 0);
        INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES 
            ('81345711-684f-48b0-8807-a68472a444a5', 'a2d3b981-4bb3-4354-9762-43eef847b908', 0, 12, '');
        INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
            ('d3822ce7-f371-44f7-ac86-df1081d06c7a', '81345711-684f-48b0-8807-a68472a444a5', 'Osha Violations', 0, 'OSHA_VIOLATION', 'osha-violations');
        INSERT INTO mode_permissions(id, mode_id, organization_id, is_shared_across_organization) VALUES
            ('c41529c5-16c0-4dcf-bc38-04625aefdf47', 'a265715d-4411-4411-9fb0-e745695d8aa8', 3, TRUE); 
    """)


def downgrade():
    conn = op.get_bind()

    # Currently modes does not have cascading deletes
    conn.execute("""
            DELETE FROM mode_permissions WHERE id = 'c41529c5-16c0-4dcf-bc38-04625aefdf47';
            DELETE FROM mode_cards WHERE id = 'd3822ce7-f371-44f7-ac86-df1081d06c7a';
            DELETE FROM mode_columns WHERE id = '81345711-684f-48b0-8807-a68472a444a5';
            DELETE FROM mode_rows WHERE id = 'a2d3b981-4bb3-4354-9762-43eef847b908';
            DELETE FROM modes WHERE id = 'a265715d-4411-4411-9fb0-e745695d8aa8';
        """)

"""Add column email to brokers table

Revision ID: aaaa20230105
Revises: 89a00606a406
Create Date: 2023-01-05 13:22:00.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "aaaa20230105"
down_revision = "89a00606a406"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("brokers", sa.Column("email", sa.String(), nullable=True, index=True))


def downgrade():
    op.drop_column("brokers", "email")

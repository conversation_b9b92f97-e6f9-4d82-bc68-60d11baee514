"""update the mode column width for file cards in  management liability mode to be 12

Revision ID: 2615bdb44222
Revises: 17b26a71c039
Create Date: 2023-04-27 07:48:28.070936+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2615bdb44222"
down_revision = "17b26a71c039"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        UPDATE mode_columns set width=12 where id='fb9d82a1-fb92-452b-9466-c985ce26e18e';
        UPDATE mode_columns set width=12 where id='5d69e91d-3d20-49f8-92ff-4a134fbc7a2f';
    """)


def downgrade():
    pass

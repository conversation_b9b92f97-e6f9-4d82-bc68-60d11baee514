"""Fixes metric group name

Revision ID: 259e679c1f80
Revises: 4af94e500a6d
Create Date: 2020-09-28 00:18:06.057982+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "259e679c1f80"
down_revision = "4af94e500a6d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update summary_preference
    set group_display_name = 'Fire Risks' 
    where group_display_name = 'Fire Risk';
    """)


def downgrade():
    pass

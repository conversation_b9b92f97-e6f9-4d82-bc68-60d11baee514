"""add parent metric to metric_config

Revision ID: 34a7ef46c555
Revises: b6afda01aa2f
Create Date: 2021-10-22 15:42:12.451603+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "34a7ef46c555"
down_revision = "b6afda01aa2f"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric_config", sa.Column("parent_metric", sa.String(), nullable=True))
    conn = op.get_bind()
    conn.execute("""
    update metric_config set parent_metric='Cooking Types' where display_name='Solid Fuel Cooking';
    update metric_config set parent_metric='Cooking Types' where display_name='Grilling';
    update metric_config set parent_metric='Cooking Types' where display_name='Open Broiling';
    update metric_config set parent_metric='Cooking Types' where display_name='Smokehouse';
    update metric_config set parent_metric='Cooking Types' where display_name='Roasting';
    update metric_config set parent_metric='Cooking Types' where display_name='Barbecue';
    update metric_config set parent_metric='Cooking Types' where display_name='Deep Fryers';
    update metric_config set parent_metric='Cooking Types' where display_name='Table Side Cooking';
    update metric_config set parent_metric='Entertainment Types' where display_name='Arcades';
    update metric_config set parent_metric='Entertainment Types' where display_name='Axe Throwing';
    update metric_config set parent_metric='Entertainment Types' where display_name='Archery';
    update metric_config set parent_metric='Entertainment Types' where display_name='Bowling';
    update metric_config set parent_metric='Entertainment Types' where display_name='Darts';
    update metric_config set parent_metric='Entertainment Types' where display_name='Karaoke';
    update metric_config set parent_metric='Entertainment Types' where display_name='TV';
    update metric_config set parent_metric='Entertainment Types' where display_name='Jukebox';
    update metric_config set parent_metric='Entertainment Types' where display_name='Hookah';
    update metric_config set parent_metric='Entertainment Types' where display_name='Pool Table';
    update metric_config set parent_metric='Entertainment Types' where display_name='Foosball';
    update metric_config set parent_metric='Parking Types' where display_name='Street';
    update metric_config set parent_metric='Parking Types' where display_name='Private Lot';
    update metric_config set parent_metric='Parking Types' where display_name='Valet';
    update metric_config set parent_metric='Performance Types' where display_name='Comedy';
    update metric_config set parent_metric='Performance Types' where display_name='DJ';
    update metric_config set parent_metric='Performance Types' where display_name='Live Music';
    """)


def downgrade():
    op.drop_column("metric_config", "parent_metric")

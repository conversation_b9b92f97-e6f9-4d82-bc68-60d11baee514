"""Extend metrics to support structure IDs

Revision ID: 663f4b0e566c
Revises: 596bf2a42eb7
Create Date: 2021-04-08 23:58:35.348854+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "663f4b0e566c"
down_revision = "596bf2a42eb7"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "category_summary_structure",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("category_summary_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("structure_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["category_summary_id"], ["category_summary.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_category_summary_structure_category_summary_id"),
        "category_summary_structure",
        ["category_summary_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_category_summary_structure_structure_id"), "category_summary_structure", ["structure_id"], unique=False
    )
    op.add_column("mean", sa.Column("structure_ids", postgresql.ARRAY(sa.String()), nullable=True))


def downgrade():
    op.drop_column("mean", "structure_ids")
    op.drop_index(op.f("ix_category_summary_structure_structure_id"), table_name="category_summary_structure")
    op.drop_index(op.f("ix_category_summary_structure_category_summary_id"), table_name="category_summary_structure")
    op.drop_table("category_summary_structure")

"""Update permit feedback labels

Revision ID: 0b958702f64f
Revises: 2e1cca351933
Create Date: 2021-05-10 16:38:51.136767+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0b958702f64f"
down_revision = "2e1cca351933"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    UPDATE feedback SET value='"Electrical"' WHERE attribute_name ILIKE 'permit%' AND value='"electrical"';
    UPDATE feedback SET value='"Other"' WHERE attribute_name ILIKE 'permit%' AND value='"foundational"';
    UPDATE feedback SET value='"Other"' WHERE attribute_name ILIKE 'permit%' AND value='"other_construction_equipment"';
    UPDATE feedback SET value='"Plumbing"' WHERE attribute_name ILIKE 'permit%' AND value='"plumbing"';
    UPDATE feedback SET value='"Other"' WHERE attribute_name ILIKE 'permit%' AND value='"sprinkler"';
    """)


def downgrade():
    pass

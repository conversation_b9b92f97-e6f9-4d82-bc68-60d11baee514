"""Backfills report permissions

Revision ID: e302aef210e5
Revises: eff5605c4027
Create Date: 2020-09-03 12:03:35.788001

"""
from alembic import op

# revision identifiers, used by Alembic.

revision = "e302aef210e5"
down_revision = "eff5605c4027"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
create extension if not exists "uuid-ossp";

insert into report_permissions (id, grantee_user_id, report_id, permission_type)
select
    uuid_generate_v4() as id,
    owner_id as grantee_user_id,
    id as report_id,
    'OWNER' as permission_type
from reports_v2;    
    """)


def downgrade():
    pass

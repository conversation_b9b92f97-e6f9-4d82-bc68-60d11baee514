"""Removes depreciated rules

Revision ID: 7ab8485a36c9
Revises: 34a7ef46c555
Create Date: 2021-10-26 11:21:34.084930+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7ab8485a36c9"
down_revision = "34a7ef46c555"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""DELETE FROM recommendation_rule where description='Ranking rule: Subcontractor' or 
    description = 'Ranking rule: Average Size Of Jobs'""")


def downgrade():
    pass

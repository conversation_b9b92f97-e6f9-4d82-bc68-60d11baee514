"""More clearing settings

Revision ID: a02f040923b9
Revises: 33213eaf82c3
Create Date: 2023-05-17 13:30:56.004144+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a02f040923b9"
down_revision = "33213eaf82c3"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'NEEDS_CLEARING'")
    op.add_column("settings", sa.Column("is_cs_manager", sa.<PERSON>(), nullable=True))


def downgrade():
    pass

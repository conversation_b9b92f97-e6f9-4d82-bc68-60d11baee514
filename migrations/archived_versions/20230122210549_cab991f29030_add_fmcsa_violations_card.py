"""Add FMCSA violations card

Revision ID: cab991f29030
Revises: 1b19daa1e3b6
Create Date: 2023-01-22 21:05:49.091293+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cab991f29030"
down_revision = "1b19daa1e3b6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards
        set type='FMCSA_VIOLATIONS', props=null
        where id='9ebf8cdf-07b6-4cc4-a7b3-edc84c3a448b';
    """)


def downgrade():
    pass

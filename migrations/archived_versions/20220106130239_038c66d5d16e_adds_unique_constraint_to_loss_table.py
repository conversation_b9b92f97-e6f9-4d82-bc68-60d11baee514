"""Adds unique constraint to loss table

Revision ID: 038c66d5d16e
Revises: 6838814e852a
Create Date: 2022-01-06 13:02:39.016462+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "038c66d5d16e"
down_revision = "6838814e852a"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
        DELETE FROM loss L1 USING loss L2 WHERE L1.id < L2.id AND L1.claim_number = L2.claim_number 
        AND L1.submission_id = L2.submission_id;
        """)
    op.create_unique_constraint("loss_claim_number_submission_id", "loss", ["claim_number", "submission_id"])


def downgrade():
    op.drop_constraint("loss_claim_number_submission_id", "loss", type_="unique")

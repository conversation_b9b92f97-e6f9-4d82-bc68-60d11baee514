"""Submission Quality Audit tables

Revision ID: 8e7e39336522
Revises: 252323b8bb2f
Create Date: 2023-06-18 18:58:22.033518+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8e7e39336522"
down_revision = "252323b8bb2f"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "quality_audit_questions",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("question", sa.String(), nullable=False),
        sa.Column("answer", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_quality_audit_questions_submission_id"), "quality_audit_questions", ["submission_id"], unique=False
    )
    op.add_column("submissions", sa.Column("is_for_audit", sa.Boolean(), nullable=True))
    op.add_column("submissions", sa.Column("audited_at", sa.DateTime(), nullable=True))


def downgrade():
    op.drop_column("submissions", "audited_at")
    op.drop_column("submissions", "is_for_audit")
    op.drop_index(op.f("ix_quality_audit_questions_submission_id"), table_name="quality_audit_questions")
    op.drop_table("quality_audit_questions")

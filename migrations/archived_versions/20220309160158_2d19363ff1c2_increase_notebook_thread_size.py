"""Increase notebook_thread size

Revision ID: 2d19363ff1c2
Revises: 7968eef0bf39
Create Date: 2022-03-07 09:33:13.729954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2d19363ff1c2"
down_revision = "7968eef0bf39"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "notebook_thread",
        "dossier_component_paths",
        existing_type=postgresql.ARRAY(sa.String(length=256)),
        type_=postgresql.ARRAY(sa.String(length=512)),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "notebook_thread",
        "dossier_component_paths",
        existing_type=postgresql.ARRAY(sa.String(length=512)),
        type_=postgresql.ARRAY(sa.String(length=256)),
        existing_nullable=True,
    )

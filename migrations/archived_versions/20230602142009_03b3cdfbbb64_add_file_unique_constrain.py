"""Adding file unique constrain on submission, name, type and size

Revision ID: 03b3cdfbbb64
Revises: 1eb6d9a8ee7c
Create Date: 2023-06-02 14:20:09.752722+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "03b3cdfbbb64"
down_revision = "1eb6d9a8ee7c"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '3600 s';")
        op.execute("""DELETE
                FROM files USING (SELECT id,
                                         size,
                                         ROW_NUMBER()
                                         OVER (PARTITION BY submission_id, name, file_type, size ORDER BY created_at) AS Row
                                  FROM files) dups
                WHERE dups.id = files.id
                  AND dups.Row > 1
                  AND dups.size IS NOT NULL;
            """)
        op.execute("""
            CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS ix_files_submission_id_name_file_type_size_key ON files (submission_id, name, file_type, size);
            """)


def downgrade():
    pass

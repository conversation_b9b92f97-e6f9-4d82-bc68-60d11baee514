"""drop mode properties

Revision ID: 1584d62dcc88
Revises: 5cd32f664efc
Create Date: 2022-12-28 12:20:58.614282+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1584d62dcc88"
down_revision = "5cd32f664efc"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "mode_cards", sa.Column("props", postgresql.JSONB(none_as_null=True, astext_type=sa.Text()), nullable=True)
    )

    conn = op.get_bind()

    conn.execute("""
    DROP TABLE mode_card_properties;
    """)


def downgrade():
    op.drop_column("mode_cards", "props")

"""Adds report_v2_id to report

Revision ID: 36d1838e47c7
Revises: eef36e6c6069
Create Date: 2020-09-10 11:30:57.600432+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "36d1838e47c7"
down_revision = "5618fa5b5cf1"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("reports", sa.Column("report_v2_id", postgresql.UUID(as_uuid=True), nullable=True))


def downgrade():
    op.drop_column("reports", "report_v2_id")

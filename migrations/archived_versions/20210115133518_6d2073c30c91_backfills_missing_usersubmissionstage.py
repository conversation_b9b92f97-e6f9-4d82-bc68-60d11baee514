"""Backfills missing UserSubmissionStage

Revision ID: 6d2073c30c91
Revises: 6b7bd80767a2
Create Date: 2021-01-15 13:35:18.047580-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6d2073c30c91"
down_revision = "6b7bd80767a2"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        """insert into user_submission_stage(id, submission_id, user_id, stage) select uuid_generate_v4() as id, submissions.id as submission_id, report_permissions.grantee_user_id as user_id, submissions.stage from report_permissions JOIN submissions_reports ON submissions_reports.report_id = report_permissions.report_id JOIN submissions ON submissions.id = submissions_reports.submission_id;"""
    )


def downgrade():
    pass

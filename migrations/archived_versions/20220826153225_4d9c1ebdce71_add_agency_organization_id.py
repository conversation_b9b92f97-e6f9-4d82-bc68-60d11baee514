"""Add agency organization id

Revision ID: 4d9c1ebdce71
Revises: 8accffbff362
Create Date: 2022-08-26 15:32:25.418951+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4d9c1ebdce71"
down_revision = "8accffbff362"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index(op.f("uq_agency_name"), table_name="agencies")
    op.add_column("agencies", sa.Column("organization_id", sa.INTEGER(), nullable=True))
    op.create_foreign_key("agency_organization_id_fkey", "agencies", "organization", ["organization_id"], ["id"])
    op.create_index(op.f("ix_agency_organization_id"), "agencies", ["organization_id"], unique=False)
    op.create_index("uq_agency_name", "agencies", [sa.text("lower(name)"), "organization_id"], unique=True)


def downgrade():
    op.drop_index(op.f("ix_agency_organization_id"), table_name="agencies")
    op.drop_constraint("agency_organization_id_fkey", "agencies", type_="foreignkey")
    op.drop_column("agencies", "organization_id")

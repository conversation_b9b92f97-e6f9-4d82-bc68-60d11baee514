"""submissionactiontype stuck

Revision ID: aad5b314a729
Revises: ad1a690bfca5
Create Date: 2023-06-05 12:40:47.911286+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "aad5b314a729"
down_revision = "ad1a690bfca5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'STUCK_CHANGED';")


def downgrade():
    pass

"""add tenants feedback table

Revision ID: 4af3aca4f090
Revises: 01b514d1ecb4
Create Date: 2020-08-12 15:31:53.809126

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4af3aca4f090"
down_revision = "01b514d1ecb4"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "tenant_feedback",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("business_name", sa.VARCHAR(length=128), nullable=True),
        sa.Column("formatted_address", sa.VARCHAR(length=128), nullable=True),
        sa.Column("premise_id", postgresql.UUID(), nullable=True),
        sa.Column("business_id", postgresql.UUID(), nullable=True),
        sa.Column("open_date", sa.DateTime(), nullable=True),
        sa.Column("closed_date", sa.DateTime(), nullable=True),
        sa.Column("owner_name", sa.VARCHAR(length=128), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("is_trusted", sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade():
    op.drop_table("tenant_feedback")

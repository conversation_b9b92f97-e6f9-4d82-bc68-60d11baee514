"""adds fields to report_permission

Revision ID: e8ba7d0f3f85
Revises: 3f02430ebd9d
Create Date: 2020-10-07 09:34:07.799720+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e8ba7d0f3f85"
down_revision = "3f02430ebd9d"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("report_permissions", sa.Column("message", sa.String(), nullable=True))


def downgrade():
    op.drop_column("report_permissions", "message")

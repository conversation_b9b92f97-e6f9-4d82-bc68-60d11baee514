"""settings for Underwriter Dashboard

Revision ID: 2eb7055aef91
Revises: e78c4f136034
Create Date: 2023-01-03 10:07:11.067340+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2eb7055aef91"
down_revision = "e78c4f136034"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("settings", sa.Column("is_underwriter_dashboard_enabled", sa.<PERSON>(), nullable=True))
    op.execute("update settings set is_underwriter_dashboard_enabled = true where organization_id = 3;")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("settings", "is_underwriter_dashboard_enabled")
    # ### end Alembic commands ###

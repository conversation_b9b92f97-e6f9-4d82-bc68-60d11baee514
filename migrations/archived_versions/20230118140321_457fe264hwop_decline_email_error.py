"""add send_decline_email_error column

Revision ID: 457fe264hwop
Revises: 20399fad513b
Create Date: 2023-01-18 14:03:21.434308+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "457fe264hwop"
down_revision = "61b1bf950c58"
branch_labels = None
depends_on = None


def upgrade():
    error_variants = ["BROKER_EMAIL_NOT_SET", "OTHER_ERROR"]

    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(f"""CREATE TYPE declineemailerror AS ENUM {tuple(error_variants)};""")
    op.add_column(
        "submissions",
        sa.Column("send_decline_email_error", sa.Enum(*error_variants, name="declineemailerror"), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submissions", "send_decline_email_error")
    op.execute("""DROP TYPE IF EXISTS declineemailerror;""")
    # ### end Alembic commands ###

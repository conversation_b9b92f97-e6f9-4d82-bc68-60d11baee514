"""Store recommendations v2 for sorting

Revision ID: 62acfa1d662b
Revises: 133a90f87bb0
Create Date: 2023-01-13 11:03:26.200337+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "62acfa1d662b"
down_revision = "133a90f87bb0"
branch_labels = None
depends_on = None


def upgrade():
    v2_recommendation_action = postgresql.ENUM(
        "ACCEPT",
        "REFER",
        "DECLINE",
        "NO_ACTION",
        "PREFERRED",
        "FLAG_FOR_REVIEW",
        "RED_FLAG",
        name="recommendationactionenum",
    )
    v2_recommendation_action.create(op.get_bind())
    op.add_column(
        "submissions",
        sa.Column(
            "recommendation_v2_action",
            sa.Enum(
                "ACCEPT",
                "REFER",
                "DECLINE",
                "NO_ACTION",
                "PREFERRED",
                "FLAG_FOR_REVIEW",
                "RED_FLAG",
                name="recommendationactionenum",
            ),
            nullable=True,
        ),
    )
    op.add_column("submissions", sa.Column("recommendation_v2_priority", sa.DECIMAL(), nullable=True))
    op.create_index(
        op.f("ix_submissions_recommendation_v2_action"), "submissions", ["recommendation_v2_action"], unique=False
    )
    op.create_index(
        op.f("ix_submissions_recommendation_v2_priority"), "submissions", ["recommendation_v2_priority"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_submissions_recommendation_v2_priority"), table_name="submissions")
    op.drop_index(op.f("ix_submissions_recommendation_v2_action"), table_name="submissions")
    op.drop_column("submissions", "recommendation_v2_priority")
    op.drop_column("submissions", "recommendation_v2_action")

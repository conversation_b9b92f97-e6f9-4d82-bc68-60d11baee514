"""submission information cards

Revision ID: ea09339e2877
Revises: bb64aa7784ec
Create Date: 2023-02-01 07:54:00.771959+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ea09339e2877"
down_revision = "bb64aa7784ec"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""

delete from mode_cards where column_id in (
'ff801abf-0e85-471c-aab7-a8f2c04a83c2',
'320b65a9-b968-4951-a6c0-53a4c80ec6f6',
'fe2891df-9005-4cae-8b0b-b703bc7cf46a',
'b8bb0889-915d-4478-b088-bb8a5c808715');

delete from mode_columns where id in (
'ff801abf-0e85-471c-aab7-a8f2c04a83c2',
'320b65a9-b968-4951-a6c0-53a4c80ec6f6',
'fe2891df-9005-4cae-8b0b-b703bc7cf46a',
'b8bb0889-915d-4478-b088-bb8a5c808715');

delete from mode_rows where id in (
'ff801abf-0e85-471c-aab7-a8f2c04a83c2',
'17dd5321-d5e7-4c6a-8aff-396eb5c861cf',
'15f5834e-8ee7-4829-b678-7a069d78318c',
'877ad8f3-3cf6-4624-a5d7-199fef047fab');


-- Contractor - Project mode   2f622131-068c-4ca4-9066-99d7bb8436b8

insert into mode_rows (id, mode_id, position, is_collapsible, is_default_open, title)
values ('7c7ddb7a-b674-4693-8621-7a011528a8a3', '2f622131-068c-4ca4-9066-99d7bb8436b8', 20, false, true, 'Submission information');

INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
('efd70c1d-be56-43b4-8200-0d9b03088c21', '7c7ddb7a-b674-4693-8621-7a011528a8a3', 0, 12, null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '1193020b-5f49-4fbd-8c33-4b1218f74930',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'Support',
    10,
    'SUBMISSION_INFO_SUPPORT',
    'submission-support'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '65d1f90d-b868-4216-9a4d-081d97ec2da2',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'Date',
    20,
    'SUBMISSION_INFO_DATE',
    'submission-date'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'a9520839-d738-4f64-bd7e-b4e333e4b2c8',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'Assignee',
    30,
    'SUBMISSION_INFO_ASSIGNEE',
    'submission-assignee'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'e72996d0-8fe2-4fc3-8df5-af412614ae92',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'Description of operations',
    40,
    'SUBMISSION_INFO_DESCRIPTION_OF_OPERATIONS',
    'description-of-operations'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '6afbd1b5-f7cd-42ef-aedf-7531edaf433a',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'NAICS Codes',
    50,
    'SUBMISSION_INFO_NAICS',
    'naics-codes'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '2392037c-5369-44b0-855d-bfbd8c36a0a9',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'Coverage',
    60,
    'SUBMISSION_INFO_COVERAGE',
    'coverage'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '2a585069-2dca-4254-b0f2-8e9e3ee75739',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'Broker / Agent & Brokerage / Agency',
    70,
    'SUBMISSION_INFO_BROKER_AGENT',
    'broker-agent'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '39ec58e1-30bf-4485-9659-0ab8c6dcfaff',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'ID',
    80,
    'SUBMISSION_INFO_ID',
    'id'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'e59c2394-08b8-4c08-a57f-b4e9eea5329f',
    'efd70c1d-be56-43b4-8200-0d9b03088c21',
    'Notes',
    90,
    'SUBMISSION_INFO_NOTES',
    'notes'
);

-- Contractor Practice mode Contractor - 0da04e4f-b332-4fa3-9f89-08caa046fc24

insert into mode_rows (id, mode_id, position, is_collapsible, is_default_open, title)
values ('863fb927-a42d-4032-ace2-c7172a8448fa', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 20, false, true, 'Submission information');

INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
('a0125d26-55ea-46ae-afeb-4ebb055a158c', '863fb927-a42d-4032-ace2-c7172a8448fa', 0, 12, null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'ee9c7867-573b-465c-b610-ea2285dfedd8',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'Support',
    10,
    'SUBMISSION_INFO_SUPPORT',
    'submission-support'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '29a0dd55-44c9-4eee-a0f8-65514197c79e',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'Date',
    20,
    'SUBMISSION_INFO_DATE',
    'submission-date'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '966ffd9e-51bd-4738-a8ac-c49c08652eca',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'Assignee',
    30,
    'SUBMISSION_INFO_ASSIGNEE',
    'submission-assignee'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '5f89206c-e764-4ea1-aac8-03b8eca2120d',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'Description of operations',
    40,
    'SUBMISSION_INFO_DESCRIPTION_OF_OPERATIONS',
    'description-of-operations'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'e9ef7318-b076-42e1-a5e7-d692dc83ae19',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'NAICS Codes',
    50,
    'SUBMISSION_INFO_NAICS',
    'naics-codes'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'c20fcdf9-ffda-4771-864f-95e2a4c3bb76',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'Coverage',
    60,
    'SUBMISSION_INFO_COVERAGE',
    'coverage'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '81e7df55-41a9-407f-bb2b-19006f61fbce',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'Broker / Agent & Brokerage / Agency',
    70,
    'SUBMISSION_INFO_BROKER_AGENT',
    'broker-agent'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '4929fa30-ae83-43e4-90b8-9f0220632cc4',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'ID',
    80,
    'SUBMISSION_INFO_ID',
    'id'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '3d297c62-cb8d-4962-bdf2-ad4b05927b6d',
    'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    'Notes',
    90,
    'SUBMISSION_INFO_NOTES',
    'notes'
);


-- Internal mode - a265715d-4411-4411-9fb0-e745695d8aa8

insert into mode_rows (id, mode_id, position, is_collapsible, is_default_open, title)
values ('4e39f201-e485-432b-bad3-6895a507ce70', 'a265715d-4411-4411-9fb0-e745695d8aa8', 10, false, true, 'Submission information');

INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
('ac91fa0b-7b5a-4f81-a86b-75171299de40', '4e39f201-e485-432b-bad3-6895a507ce70', 0, 12, null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'b2eea563-aa9d-4de5-b83d-3c80d54582cf',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'Support',
    10,
    'SUBMISSION_INFO_SUPPORT',
    'submission-support'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '3311df53-0d52-4424-8ab1-244145878bea',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'Date',
    20,
    'SUBMISSION_INFO_DATE',
    'submission-date'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'ec728417-1db6-4278-9fea-18a776011f10',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'Assignee',
    30,
    'SUBMISSION_INFO_ASSIGNEE',
    'submission-assignee'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '13bd3924-fb04-4f6c-923e-b6ff91f6ce34',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'Description of operations',
    40,
    'SUBMISSION_INFO_DESCRIPTION_OF_OPERATIONS',
    'description-of-operations'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '00df525b-eaf7-4e47-abdf-cd6f263f8d03',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'NAICS Codes',
    50,
    'SUBMISSION_INFO_NAICS',
    'naics-codes'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'eea6fbf8-a801-467a-bbc7-5b2b22a07e79',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'Coverage',
    60,
    'SUBMISSION_INFO_COVERAGE',
    'coverage'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'f1c29658-9bbc-4f45-a969-384b8dddf5b6',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'Broker / Agent & Brokerage / Agency',
    70,
    'SUBMISSION_INFO_BROKER_AGENT',
    'broker-agent'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '5630bedf-fffb-4889-98c9-6b644698401e',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'ID',
    80,
    'SUBMISSION_INFO_ID',
    'id'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '0b31a1d3-b3bf-4b9e-8ba0-c55d89b6bd1e',
    'ac91fa0b-7b5a-4f81-a86b-75171299de40',
    'Notes',
    90,
    'SUBMISSION_INFO_NOTES',
    'notes'
);

-- Fleet - a72774ed-b6c9-4845-a9dc-310fba8d4879
insert into mode_rows (id, mode_id, position, is_collapsible, is_default_open, title)
values ('8465bea3-4edf-44e7-8b0b-bb8b45287e2e', 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 10, false, true, 'Submission information');

INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
('38cc1cfe-cf46-4b0d-ae34-86712f5d2403', '8465bea3-4edf-44e7-8b0b-bb8b45287e2e', 0, 12, null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '66cb0d27-2713-4179-bf5e-8f0608a54511',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'Support',
    10,
    'SUBMISSION_INFO_SUPPORT',
    'submission-support'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '3ef9a955-c503-48e0-92e3-4cdf797fe4a5',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'Date',
    20,
    'SUBMISSION_INFO_DATE',
    'submission-date'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '58efbc7b-c2d7-4db1-9ad7-047936c0332a',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'Assignee',
    30,
    'SUBMISSION_INFO_ASSIGNEE',
    'submission-assignee'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'aa8184a5-df9e-460c-a955-df15ee25bc04',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'Description of operations',
    40,
    'SUBMISSION_INFO_DESCRIPTION_OF_OPERATIONS',
    'description-of-operations'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '3963d269-d954-43ea-ac71-bc34f684562e',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'NAICS Codes',
    50,
    'SUBMISSION_INFO_NAICS',
    'naics-codes'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '6dc300bc-81ca-49f7-b98d-77fc7582db97',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'Coverage',
    60,
    'SUBMISSION_INFO_COVERAGE',
    'coverage'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    '00ddc07d-c375-42b2-bd3b-a98213029666',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'Broker / Agent & Brokerage / Agency',
    70,
    'SUBMISSION_INFO_BROKER_AGENT',
    'broker-agent'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'f40f24a5-0203-43e0-979b-f9f7106bf98c',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'ID',
    80,
    'SUBMISSION_INFO_ID',
    'id'
);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
(
    'abe55c68-0fd6-45ab-a77b-8840d93563ff',
    '38cc1cfe-cf46-4b0d-ae34-86712f5d2403',
    'Notes',
    90,
    'SUBMISSION_INFO_NOTES',
    'notes'
);
    """)


def downgrade():
    pass

"""Extends CustomizableClassifier with flag if it is submission or business classifier

Revision ID: a4169329fdda
Revises: 34fcc16685b5
Create Date: 2022-07-06 20:03:52.979590+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a4169329fdda"
down_revision = "34fcc16685b5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "customizable_classifiers", sa.Column("is_business_classifier", sa.<PERSON>(), nullable=True, index=True)
    )
    op.add_column(
        "customizable_classifiers", sa.Column("is_submission_classifier", sa.<PERSON>(), nullable=True, index=True)
    )
    conn = op.get_bind()
    conn.execute("""UPDATE customizable_classifiers SET is_business_classifier=true""")
    conn.execute("""UPDATE customizable_classifiers SET is_submission_classifier=false""")
    op.alter_column("customizable_classifiers", "is_business_classifier", existing_type=sa.BOOLEAN(), nullable=False)
    op.alter_column("customizable_classifiers", "is_submission_classifier", existing_type=sa.BOOLEAN(), nullable=False)


def downgrade():
    op.drop_column("customizable_classifiers", "is_submission_classifier")
    op.drop_column("customizable_classifiers", "is_business_classifier")

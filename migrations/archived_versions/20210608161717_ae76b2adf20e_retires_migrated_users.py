"""Retires migrated users

Revision ID: ae76b2adf20e
Revises: b8b79190a87b
Create Date: 2021-06-08 16:17:17.830471+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "ae76b2adf20e"
down_revision = "b8b79190a87b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # todo check for fks

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>' and id = 161;
    """)

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>' and id = 164;
    """)

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>' and id = 167;
    """)


def downgrade():
    conn = op.get_bind()

    conn.execute("""
    update users
    set email = '<EMAIL>', external_id = 'auth0|5f5b0c361e076c00797b7200', organization_id = 5
    where email = '<EMAIL>' and id = 161;
    """)

    conn.execute("""
    update users
    set email = '<EMAIL>', external_id = 'auth0|5f5aee52e61fe3006c3bfe39', organization_id = 5
    where email = '<EMAIL>' and id = 164;
    """)

    conn.execute("""
    update users
    set email = '<EMAIL>', external_id = 'auth0|5f6251b48d4d0c00768d8f55', organization_id = 5
    where email = '<EMAIL>' and id = 167;
    """)

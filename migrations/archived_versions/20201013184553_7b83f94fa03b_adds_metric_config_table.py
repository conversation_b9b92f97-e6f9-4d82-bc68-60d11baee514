"""Adds metric_config table

Revision ID: 7b83f94fa03b
Revises: fa2f455b4c5a
Create Date: 2020-10-13 18:45:53.662390+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7b83f94fa03b"
down_revision = "fa2f455b4c5a"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "metric_group",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("display_name", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "metric_config",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("display_name", sa.String(), nullable=False),
        sa.Column("icon_name", sa.String(), nullable=True),
        sa.Column(
            "metric_type",
            postgresql.ENUM(
                "MEAN",
                "RANGE_SUMMARY",
                "GRADE_SUMMARY",
                "CATEGORICAL_DATA_SUMMARY",
                "LIST_SUMMARY",
                "EMPLOYEES_SUMMARY",
                name="metrictype",
                create_type=False,
            ),
            nullable=False,
        ),
        sa.Column("metric_group_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(["metric_group_id"], ["metric_group.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_metric_config_metric_group_id"), "metric_config", ["metric_group_id"], unique=False)
    op.create_index(op.f("ix_metric_config_metric_type"), "metric_config", ["metric_type"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_metric_config_metric_type"), table_name="metric_config")
    op.drop_index(op.f("ix_metric_config_metric_group_id"), table_name="metric_config")
    op.drop_table("metric_config")
    op.drop_table("metric_group")

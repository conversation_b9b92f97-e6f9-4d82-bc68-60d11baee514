"""Adds TIV column to CAT MOD Request form

Revision ID: b7785044dd49
Revises: 28a731e9541e
Create Date: 2021-09-09 17:21:32.567281+00:00

"""
from datetime import datetime
from uuid import uuid4

from alembic import op
import sqlalchemy as sa

from copilot.models.types import FieldType, FormatType, ParentType

# revision identifiers, used by Alembic.
revision = "b7785044dd49"
down_revision = "28a731e9541e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    res = conn.execute(
        sa.text("""INSERT INTO form_field(id, form_definition_id, created_at, label, value_type, parent_type, 
        is_required, format, description) VALUES (:idx, :form_definition_id, :created_at, :label, :value_type, 
        :parent_type, :is_required, :format, :description) returning id"""),
        idx=uuid4(),
        form_definition_id="CAT MOD REQUEST",
        created_at=datetime.utcnow(),
        label="TIV",
        value_type=FieldType.NUMBER,
        parent_type=ParentType.BUSINESS,
        is_required=False,
        format=FormatType.DECIMAL,
        description="TIV",
    )
    form_field_id = res.fetchone()[0]
    conn.execute(
        sa.text("""
                INSERT INTO form_field_source(id, created_at, form_field_id, label, source_type, path, fact_subtype_id)
                VALUES (:idx, :created_at, :form_field_id, :label, :source_type, :path, :fact_subtype_id)"""),
        idx=uuid4(),
        created_at=datetime.utcnow(),
        form_field_id=form_field_id,
        source_type="FACTS",
        label="TIV",
        path="",
        fact_subtype_id="TIV",
    )
    conn.execute(
        sa.text("""
    INSERT INTO form_field_value (id, submission_id, submission_business_id, form_field_id, value_type, form_id)
    SELECT uuid_generate_v4() as id, submission_businesses.submission_id, 
    submission_businesses.id as submission_business_id, :form_field_id, 'NUMBER', 
    form_v2.id FROM submission_businesses JOIN form_v2 on submission_businesses.submission_id = form_v2.submission_id 
    and form_v2.form_definition_id = 'CAT MOD REQUEST'"""),
        form_field_id=form_field_id,
    )


def downgrade():
    pass

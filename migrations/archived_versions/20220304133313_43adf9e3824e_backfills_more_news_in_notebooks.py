"""Backfills more news in notebook

Revision ID: 43adf9e3824e
Revises: 17fed8a9489e
Create Date: 2022-03-04 13:33:13.729954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "43adf9e3824e"
down_revision = "17fed8a9489e"
branch_labels = None
depends_on = None

id_mapping = [
    ["75e4b363-678b-4a78-bcff-1f0d41627add", "fbb377d3-0e9d-73a5-e03d-7c8c515f9743"],
    ["5a5501c5-e2df-492b-8927-0d1759e371e0", "1f2e0aab-cad9-419c-f825-c3fc54cf3dbb"],
    ["bc680597-5100-4f19-a54c-421904e4427e", "02dd42bf-4a37-4a23-4780-436cbec5b306"],
    ["6e5d6b86-2841-48d2-a545-de1118e353d6", "ec9cd4c6-3532-c9ee-6cf7-9c1273c5d4ae"],
    ["09c04eca-cc84-4416-a492-8bec59928af0", "c51be942-d27e-1f7e-3eeb-00b4382a2349"],
    ["e01b91cb-af67-4f71-90d2-b092903c6478", "133db85f-08cb-8275-f002-0047899b714f"],
    ["53689936-1dba-4734-b1fd-13703a66d796", "730c6888-fd41-8020-4ecb-7f8de609cce2"],
    ["e98d183e-8640-4e84-ad81-bcf955acdf2d", "d210dd2b-ea02-18dd-dfe2-c1df02ab36d2"],
    ["a7f158c1-fd30-483a-be76-ebc30fed1c1a", "fe377ce3-d3e9-18b5-45a1-0186737a86e4"],
]

query = sa.text("""
    UPDATE public.notebook_thread
            set dossier_component_paths=ARRAY [E'news[?(@.id==\\'' || :new_id || E'\\')]']
            where dossier_component_paths[1] =  E'news[?(@.id==\\'' || :old_id || E'\\')]';
            """)


def upgrade():
    conn = op.get_bind()
    for p in id_mapping:
        query_params = {"new_id": p[1], "old_id": p[0]}
        conn.execute(statement=query, **query_params)


def downgrade():
    pass

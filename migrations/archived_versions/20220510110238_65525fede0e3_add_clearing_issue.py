"""add clearing issue
Revision ID: 65525fede0e3
Revises: a6c977f5947f
Create Date: 2022-05-10 11:02:38.492641+00:00
"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "65525fede0e3"
down_revision = "a6c977f5947f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionstage ADD VALUE 'CLEARING_ISSUE'")
    op.create_table(
        "submission_clearing_issue",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("suspected_report_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("is_resolved", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["suspected_report_id"], ["reports_v2.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_clearing_issue_submission_id"), "submission_clearing_issue", ["submission_id"], unique=False
    )
    op.create_index(
        op.f("ix_submission_clearing_issue_suspected_report_id"),
        "submission_clearing_issue",
        ["suspected_report_id"],
        unique=False,
    )
    op.add_column("settings", sa.Column("can_resolve_clearing_issues", sa.Boolean(), nullable=True))
    op.add_column("settings", sa.Column("is_clearing_enabled", sa.Boolean(), nullable=True))
    op.add_column("settings", sa.Column("should_be_notified_for_clearing_issues", sa.Boolean(), nullable=True))
    with op.get_context().autocommit_block():
        # <EMAIL>
        op.execute("""
            INSERT INTO settings (id, created_at, updated_at, user_id, can_resolve_clearing_issues)
            VALUES (uuid_generate_v4(), now(), null, 18, true)
        """)
        # kalepa ORG
        op.execute("""
            INSERT INTO settings (id, created_at, updated_at, organization_id, is_clarion_door_enabled, is_clearing_enabled)
            VALUES (uuid_generate_v4(), now(), null, 7, true, true)
        """)
        # Logan
        op.execute("""
            INSERT INTO settings (id, created_at, updated_at, user_id, should_be_notified_for_clearing_issues)
            VALUES (uuid_generate_v4(), now(), null, (select id from users where email = '<EMAIL>'), true)
        """)
        # Connie
        op.execute("""
            INSERT INTO settings (id, created_at, updated_at, user_id, should_be_notified_for_clearing_issues, can_resolve_clearing_issues)
            VALUES (uuid_generate_v4(), now(), null, (select id from users where email = '<EMAIL>'), true, true)
        """)


def downgrade():
    op.drop_column("settings", "should_be_notified_for_clearing_issues")
    op.drop_column("settings", "is_clearing_enabled")
    op.drop_column("settings", "can_resolve_clearing_issues")
    op.drop_index(op.f("ix_submission_clearing_issue_suspected_report_id"), table_name="submission_clearing_issue")
    op.drop_index(op.f("ix_submission_clearing_issue_submission_id"), table_name="submission_clearing_issue")
    op.drop_table("submission_clearing_issue")

"""Removing permissions for unverified submissions

Revision ID: a589e47b668a
Revises: 77e53f6adf2b
Create Date: 2023-02-08 20:27:20.685536+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a589e47b668a"
down_revision = "77e53f6adf2b"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        delete
        from report_permissions
        where report_permissions.id in (select report_permissions.id
                                        from report_permissions
                                                 join submissions_reports sr on report_permissions.report_id = sr.report_id
                                                 join submissions s on sr.submission_id = s.id
                                                 join users u on s.owner_id = u.id
                                        where s.is_verification_required = true
                                          and s.is_verified = false
                                          and report_permissions.grantee_user_id not in (154, 348)
                                          and u.organization_id in (6, 10));
    """)


def downgrade():
    pass

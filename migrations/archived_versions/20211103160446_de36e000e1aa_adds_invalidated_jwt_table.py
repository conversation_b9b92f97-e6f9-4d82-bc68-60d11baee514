"""Adds invalidated_jwt table

Revision ID: de36e000e1aa
Revises: 8c5699c4ef3a
Create Date: 2021-11-03 16:04:46.830027+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "de36e000e1aa"
down_revision = "8c5699c4ef3a"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table("invalidated_jwt", sa.Column("jwt", sa.String(), nullable=False), sa.PrimaryKeyConstraint("jwt"))


def downgrade():
    op.drop_table("invalidated_jwt")

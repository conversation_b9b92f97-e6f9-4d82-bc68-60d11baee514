"""changed user timezone from int to str

Revision ID: 0b8287c0d21c
Revises: a9a2f4e30af3
Create Date: 2021-08-10 07:33:57.014173+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0b8287c0d21c"
down_revision = "a9a2f4e30af3"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("users", "timezone")
    op.add_column("users", sa.Column("timezone", sa.String(), nullable=True))


def downgrade():
    op.drop_column("users", "timezone")

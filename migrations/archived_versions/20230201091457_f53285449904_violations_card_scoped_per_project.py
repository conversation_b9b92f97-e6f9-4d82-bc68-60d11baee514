"""violations card scoped per project

Revision ID: f53285449904
Revises: ea09339e2877
Create Date: 2023-02-01 09:14:57.677188+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f53285449904"
down_revision = "ea09339e2877"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards SET props = '{"parentType": "BUSINESS", "businessSelect": "generalContractorAndDuplicates"}' WHERE id IN (
'974e754f-553c-4da8-9356-e83a9d80e78b',
'11b3e3d4-3124-41b2-9e19-7bf98ce8aced',
'82ba32a1-052f-4621-9ae8-143ac7915bb0',
'dd5fa1a1-5d4f-45fb-ac68-5b99d3f62c1d');

UPDATE mode_cards SET props = '{"parentType": "BUSINESS"}' WHERE type in ('OSHA_VIOLATION', 'EPA_INSPECTION') AND props IS NULL;
    """)


def downgrade():
    pass

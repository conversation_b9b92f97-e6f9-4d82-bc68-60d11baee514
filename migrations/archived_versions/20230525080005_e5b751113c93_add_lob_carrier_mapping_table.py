"""add lob carrier mapping table

Revision ID: e5b751113c93
Revises: 182ab96f0a48
Create Date: 2023-05-25 08:00:05.862223+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e5b751113c93"
down_revision = "182ab96f0a48"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '3600 s';")
    op.create_table(
        "lob_carrier",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("lob_raw", sa.String(), nullable=False),
        sa.Column("carrier", sa.String(), nullable=True),
        sa.Column("line_of_business", sa.String(), nullable=False),
        sa.UniqueConstraint("lob_raw", "carrier"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_lob_raw_carrier", "lob_carrier", ["lob_raw", "carrier"], unique=True)
    op.create_index(op.f("ix_loss_carrier"), "loss", ["carrier"], unique=False)
    op.create_index(op.f("ix_loss_lob_raw"), "loss", ["lob_raw"], unique=False)
    op.execute("ALTER TABLE loss ALTER COLUMN line_of_business TYPE varchar;")


def downgrade():
    op.execute(
        "ALTER TABLE loss ALTER COLUMN line_of_business TYPE lineofbusinesstype USING"
        " line_of_business::lineofbusinesstype;"
    )
    op.drop_index(op.f("ix_loss_lob_raw"), table_name="loss")
    op.drop_index(op.f("ix_loss_carrier"), table_name="loss")
    op.drop_index("ix_lob_raw_carrier", table_name="lob_carrier")
    op.drop_table("lob_carrier")

"""Add modes schema

Revision ID: 6ec132e26160
Revises: 1e6aa3c13911
Create Date: 2022-10-03 20:01:06.352627+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6ec132e26160"
down_revision = "cef4ce47a8a4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "modes",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "mode_rows",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("mode_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("position", sa.SmallInteger(), nullable=False),
        sa.ForeignKeyConstraint(
            ["mode_id"],
            ["modes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("mode_id", "position", name="uq_mode_rows_mode_id_position"),
    )
    op.create_index(op.f("ix_mode_rows_mode_id"), "mode_rows", ["mode_id"], unique=False)
    op.create_table(
        "mode_columns",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("row_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("position", sa.SmallInteger(), nullable=False),
        sa.Column("width", sa.SmallInteger(), nullable=False),
        sa.Column("section_title", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["row_id"],
            ["mode_rows.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("row_id", "position", name="uq_mode_columns_row_id_position"),
    )
    op.create_index(op.f("ix_mode_columns_row_id"), "mode_columns", ["row_id"], unique=False)
    op.create_table(
        "mode_permissions",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("mode_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("owner_id", sa.Integer(), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=True),
        sa.Column("is_shared_across_organization", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["mode_id"],
            ["modes.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
        ),
        sa.ForeignKeyConstraint(
            ["owner_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "mode_id", "owner_id", "organization_id", name="uq_mode_permissions_mode_id_owner_id_organization_id"
        ),
    )
    op.create_index(op.f("ix_mode_permissions_mode_id"), "mode_permissions", ["mode_id"], unique=False)
    op.create_index(op.f("ix_mode_permissions_organization_id"), "mode_permissions", ["organization_id"], unique=False)
    op.create_index(op.f("ix_mode_permissions_owner_id"), "mode_permissions", ["owner_id"], unique=False)
    op.create_table(
        "mode_cards",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("column_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("position", sa.SmallInteger(), nullable=False),
        sa.Column("type", sa.Enum("DEFAULT", name="cardtype"), nullable=False),
        sa.Column("card_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["column_id"],
            ["mode_columns.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("column_id", "position", name="uq_mode_cards_column_id_position"),
    )
    op.create_index(op.f("ix_mode_cards_column_id"), "mode_cards", ["column_id"], unique=False)
    op.create_table(
        "mode_card_properties",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("card_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("type", sa.Enum("STRING", "NUMBER", "JSON", name="cardpropertytype"), nullable=False),
        sa.Column("value", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["card_id"],
            ["mode_cards.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("card_id", "name", name="uq_mode_card_properties_card_id_name"),
    )
    op.create_index(op.f("ix_mode_card_properties_card_id"), "mode_card_properties", ["card_id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_mode_card_properties_card_id"), table_name="mode_card_properties")
    op.drop_table("mode_card_properties")
    op.drop_index(op.f("ix_mode_cards_column_id"), table_name="mode_cards")
    op.drop_table("mode_cards")
    op.drop_index(op.f("ix_mode_permissions_owner_id"), table_name="mode_permissions")
    op.drop_index(op.f("ix_mode_permissions_organization_id"), table_name="mode_permissions")
    op.drop_index(op.f("ix_mode_permissions_mode_id"), table_name="mode_permissions")
    op.drop_table("mode_permissions")
    op.drop_index(op.f("ix_mode_columns_row_id"), table_name="mode_columns")
    op.drop_table("mode_columns")
    op.drop_index(op.f("ix_mode_rows_mode_id"), table_name="mode_rows")
    op.drop_table("mode_rows")
    op.drop_table("modes")
    # ### end Alembic commands ###

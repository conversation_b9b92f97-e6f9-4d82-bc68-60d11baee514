"""Add open report relationship

Revision ID: ce99ee7eb4f6
Revises: 91c6395fd4cc
Create Date: 2022-09-01 09:18:25.209752+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ce99ee7eb4f6"
down_revision = "91c6395fd4cc"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "user_open_reports",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"],
            ["reports_v2.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("user_id", "report_id"),
    )


def downgrade():
    op.drop_table("user_open_reports")

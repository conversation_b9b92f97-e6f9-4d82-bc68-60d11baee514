"""Remove submissions.policy_id column
20220623083804_91661ae5e17b_remove_submission_policy_id.py
Revision ID: 91661ae5e17b
Revises: 5ff469f3a7bf
Create Date: 2022-06-23 09:01:19.010006+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "91661ae5e17b"
down_revision = "5ff469f3a7bf"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("submissions", "policy_id")


def downgrade():
    op.add_column("submissions", sa.Column("policy_id", sa.String(), nullable=True))

"""single business mode

Revision ID: b8638e265291
Revises: 27e18d6c5178
Create Date: 2023-03-06 15:35:35.506427+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b8638e265291"
down_revision = "c56c1e57896d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT INTO modes (id, name, props) VALUES ('a4430e79-3077-4f9e-88b9-0477ce7eb13b', 'Single Business', '{"context":{"parentType":"BUSINESS","type":"parents-select"}}');
    
    INSERT INTO mode_sections (id, name, props) VALUES
    ('88da12ee-f66a-4c62-935c-a781d4f15b44', 'Fleet and Named Insured Important Highlights', '{"strategy":{"type":"merge-rows-columns"}}'),
    ('6a777e3f-c1fd-4d16-86da-73a21eb933e4', 'Fleet Important Highlights', '{"strategy":{"type":"conditional","condition":{"conditions":[{"type":"hasFleetFacts"},{"type":"report","jsonPath":"$.submissions[0].files[*].file_type","contains":"(Vehicles|Drivers)"}],"type":"or"}}}'),
    ('7c23fb6d-ca8a-4596-956f-118eb6d2bf8d', 'Premises location and crime', '{"context":{"parentType":"PREMISES","type":"parents-select"}}'),
    ('124c2a09-3b92-401d-bbe1-e232ab43504f', 'Premises facts', '{"context":{"parentType":"PREMISES","type":"parents-select"}}'),
    ('86f3f113-4255-4512-a27d-a1019828f4aa', 'Fleet with header', '{"strategy":{"type":"conditional","condition":{"conditions":[{"type":"hasFleetFacts"},{"type":"report","jsonPath":"$.submissions[0].files[*].file_type","contains":"(Vehicles|Drivers)"}],"type":"or"}}}'),
    ('f46c8539-35d1-456d-b561-3951ebf88bfb', 'Fleet', '{"strategy":{"type":"conditional","condition":{"conditions":[{"type":"hasFleetFacts"},{"type":"report","jsonPath":"$.submissions[0].files[*].file_type","contains":"(Vehicles|Drivers)"}],"type":"or"}},"context":{"parentType":"SUBMISSION","type":"parents-select"}}');
    
    INSERT INTO mode_rows (id, position, title) VALUES ('6c246235-46ef-403e-bd2c-a519c5aa0f50', 600, 'Images') ON CONFLICT DO NOTHING;
    INSERT INTO mode_rows (id, position, title) VALUES ('33c91db4-32b8-4fa8-8852-1131fbfd3e25', 300, 'Crime Score') ON CONFLICT DO NOTHING;

    
    INSERT INTO mode_rows (id, position, title, is_collapsible, is_default_open) VALUES
    ('5367b9d5-08d6-439e-a607-119668019aa8', 100, 'Important Highlights', NULL, NULL),
    ('0c21aa0d-c055-4c11-8a0b-83b511e0e121', 100, 'Premises', NULL, NULL),
    ('b7893774-bc58-40c8-8ede-40232b2323fe', 130, NULL, FALSE, FALSE),
    ('dc556da5-9e45-4bec-a71f-256396e3b910', 140, 'Vehicles in Submission', NULL, NULL),
    ('608f3b9f-dd8b-48ad-a1f2-7517e8d320c4', 150, 'Drivers in Submission', NULL, NULL),
    ('d512ba9b-1fb0-4bf7-915f-0ddd31cc714d', 160, 'Fleet Operations', NULL, NULL),
    ('17ee9a97-5f80-470f-8a1e-6e56ba086b23', 170, 'Crashes', NULL, NULL),
    ('359ef163-6160-41a1-bea2-e45fc9e59624', 180, 'Enforcement Cases', NULL, NULL),
    ('ca131177-e3fe-4d60-aed7-35928d7cd6c7', 190, 'Vehicles in FMCSA', NULL, NULL),
    ('aefdad38-63fe-4d95-9cb9-cf119e7562d0', 100, 'Business Operation', NULL, NULL);
    
    
    INSERT INTO mode_columns (row_id, id, width, position) VALUES
    ('5367b9d5-08d6-439e-a607-119668019aa8', '4245e925-cf92-40b2-9d8c-a710e9931f9a', 12, 0),
    ('0c21aa0d-c055-4c11-8a0b-83b511e0e121', '0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', 12, 0),
    ('b7893774-bc58-40c8-8ede-40232b2323fe', 'bbe806fb-a738-46a4-8907-3fb4733302fb', 12, 0),
    ('dc556da5-9e45-4bec-a71f-256396e3b910', 'e69e9a02-e83d-400a-8951-3a98561b3891', 12, 0),
    ('608f3b9f-dd8b-48ad-a1f2-7517e8d320c4', '75631231-0684-4992-834b-913751105e18', 12, 0),
    ('d512ba9b-1fb0-4bf7-915f-0ddd31cc714d', '37e07bc0-9ca1-4b9a-8ea2-7bf47ba1b3a6', 12, 0),
    ('17ee9a97-5f80-470f-8a1e-6e56ba086b23', '8a4b1403-a3a5-40dd-a53d-3bc3c25fb23a', 12, 0),
    ('359ef163-6160-41a1-bea2-e45fc9e59624', 'f17317a3-a634-48c9-8b72-21a9b0b942a9', 12, 0),
    ('ca131177-e3fe-4d60-aed7-35928d7cd6c7', '0da69e85-54cd-44b7-8166-df9c72a04599', 12, 0),
    ('aefdad38-63fe-4d95-9cb9-cf119e7562d0', '79ed7720-589b-409b-aa46-d13e9867dfa3', 12, 0);
    
    
    INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
    ('4245e925-cf92-40b2-9d8c-a710e9931f9a', '65e56147-3559-4ec7-81e0-43fb69271492', 100, 'imp-highlights-fleet', 'IMPORTANT_HIGHLIGHTS_CARD', 'Fleet Highlights', '{"columns":3,"highlights":[{"cardId":"submission-vehicles-table","label":"Vehicles (Submitted)","noValuesLabels":[{"condition":{"max":0,"name":"submissionFile","type":"isInRange","types":["Drivers","Vehicles"]},"label":"-"}],"redirectLinkLabel":"Go to vehicles (submitted)","source":{"mapper":"childrenDiscoveredIn","mapperConfig":{"discoveredIn":["SOV","SOV_PDF"]},"source":{"factSubtypes":["VEHICLES"],"parentType":"SUBMISSION","sourceType":"FACT"}}},{"cardId":"fmcsa-vehicles-table","conditions":[{"condition":{"min":1,"type":"isInRange"},"mapper":"numberOfItems","source":{"factSubtypes":["FMCSA_VIOLATION_COUNT"],"parentType":"SUBMISSION","sourceType":"FACT"}}],"label":"Vehicles from FMCSA not in submission","redirectLinkLabel":"Go to vehicles (FMCSA)","source":{"mapper":"childrenDiscoveredIn","mapperConfig":{"discoveredIn":["FMCSA_INSPECTION","FMCSA_CRASH"],"exclude":["SOV","SOV_PDF"]},"source":{"factSubtypes":["VEHICLES"],"parentType":"SUBMISSION","sourceType":"FACT"}}},{"cardId":"submission-drivers-table","label":"Drivers (Submitted)","noValuesLabels":[{"condition":{"max":0,"name":"submissionFile","type":"isInRange","types":["Drivers","Vehicles"]},"label":"-"}],"redirectLinkLabel":"Go to drivers table","source":{"mapper":"childrenDiscoveredIn","mapperConfig":{"discoveredIn":["SOV","SOV_PDF"]},"source":{"factSubtypes":["DRIVERS"],"parentType":"SUBMISSION","sourceType":"FACT"}}},{"cardId":"fmcsa-violations","conditions":[{"condition":{"min":1,"type":"isInRange"},"mapper":"numberOfItems","source":{"factSubtypes":["FMCSA_VIOLATION_COUNT"],"parentType":"SUBMISSION","sourceType":"FACT"}}],"icons":[{"color":"warning","condition":{"min":10,"type":"isInRange"},"name":"warning"}],"label":"FMCSA violations","redirectLinkLabel":"Go to violations","source":{"mapper":"jsonPathValue","mapperConfig":{"path":"$.observation.value"},"source":{"factSubtypes":["FMCSA_VIOLATION_COUNT"],"parentType":"SUBMISSION","sourceType":"FACT"}}},{"cardId":"crash-list","conditions":[{"condition":{"min":1,"type":"isInRange"},"mapper":"numberOfItems","source":{"factSubtypes":["FMCSA_VIOLATION_COUNT"],"parentType":"SUBMISSION","sourceType":"FACT"}}],"icons":[{"color":"error","condition":{"min":1,"type":"isInRange"},"name":"warning"}],"label":"Crashes","redirectLinkLabel":"Go to crash table","source":{"mapper":"numberOfItems","source":{"documentType":"FMCSA_CRASH","expand":["crash_details"],"parentType":"BUSINESS","sourceType":"DOCUMENT"}}},{"cardId":"crash-list","conditions":[{"condition":{"min":1,"type":"isInRange"},"mapper":"numberOfItems","source":{"documentType":"FMCSA_CRASH","expand":["crash_details"],"parentType":"BUSINESS","sourceType":"DOCUMENT"}}],"icons":[{"color":"error","condition":{"min":1,"type":"isInRange"},"name":"warning"}],"label":"Fatalities","redirectLinkLabel":"Go to crash table","source":{"mapper":"jsonPathValue","mapperConfig":{"aggregation":"sum","path":"$.number_of_fatalities"},"source":{"documentType":"FMCSA_CRASH","expand":["crash_details"],"parentType":"BUSINESS","sourceType":"DOCUMENT"}}}]}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', 'a7f40f12-89be-4abb-9ced-2bbe8323409b', 100, 'premises-BUILDING_EXPOSURE', 'FACTS', 'Building exposure', '{"facts":{"group":"BUILDING_EXPOSURE"}}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', 'baa2e986-3b18-499d-a11d-1c16c5d0e3a5', 200, 'premises-BUILDING_INTEGRITY_RISK', 'FACTS', 'Building integrity risk', '{"facts":{"group":"BUILDING_INTEGRITY_RISK"}}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', '0a31b57d-ed94-4e3c-93eb-a46fc4d2a1c2', 300, 'premises-ESTIMATED_SALE_VALUE', 'FACTS', 'Estimated sale value', '{"facts":{"group":"ESTIMATED_SALE_VALUE"}}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', 'b2d324bc-1e7b-4403-9d48-df3fb1ed9b47', 400, 'premises-FIRE_RISK', 'FACTS', 'Fire risk', '{"facts":{"group":"FIRE_RISK"}}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', '229a64e6-47c3-4818-ade9-2642dca460cc', 500, 'premises-LAND_USE', 'FACTS', 'Land use', '{"facts":{"group":"LAND_USE"}}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', '078b63f0-ea42-4cd3-98b9-6de88a426fe6', 600, 'premises-OWNERS', 'FACTS', 'Owners', '{"facts":{"group":"OWNERS"}}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', 'afdcf84f-2942-4017-8fd0-77754af15dbd', 700, 'premises-ROOF', 'FACTS', 'Roof', '{"facts":{"group":"ROOF"}}'),
    ('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', '88e34605-12e2-477d-a478-4d7b6625d918', 800, 'premises-OTHER_PROPERTY_FACTS', 'FACTS', 'Other', '{"facts":{"group":"OTHER_PROPERTY_FACTS"}}'),
    ('bbe806fb-a738-46a4-8907-3fb4733302fb', 'e9ebecd0-63a0-4034-b00f-2e12c2ed188d', 0, 'fleet_header', 'FLEET_HEADER', NULL, '{}'),
    ('e69e9a02-e83d-400a-8951-3a98561b3891', '22e62f04-d2a4-4465-80b4-38b1ee83e3c9', 10, 'vehicles-in-submission-summary', 'VEHICLES_IN_SUBMISSION_SUMMARY', 'Summary', '{}'),
    ('e69e9a02-e83d-400a-8951-3a98561b3891', 'f9379610-b1ef-49c4-a854-25b09fb9106f', 20, 'submission-vehicles-table', 'VEHICLES_IN_SUBMISSION_TABLE', NULL, '{}'),
    ('75631231-0684-4992-834b-913751105e18', '3de2dd0b-5ec2-45df-a8a4-32a8cb77c76e', 10, 'drivers-in-submission-summary', 'DRIVERS_IN_SUBMISSION_SUMMARY', 'Summary', '{}'),
    ('75631231-0684-4992-834b-913751105e18', 'c1f4dd08-380c-4dfd-8c78-115d259f5f12', 20, 'submission-drivers-table', 'DRIVERS_IN_SUBMISSION_TABLE', NULL, '{}'),
    ('37e07bc0-9ca1-4b9a-8ea2-7bf47ba1b3a6', '19d7de21-c7d0-4866-897e-8ba3326e8ff8', 10, 'fleet-operations', 'FACTS', 'Fleet Operations', '{"facts":{"factSubtypeIds":["CARGO_CARRIED","FLEET_OPERATION_STATES","CARRIER_OPERATION_STATUS","CARRIER_OPERATION","OPERATION_CLASSIFICATION","TRANSPORTATION_SAFETY_RATING","TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS","NUMBER_OF_VEHICLES","NUMBER_OF_REGISTERED_DRIVERS","TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION","TRANSPORTATION_HAS_CONSUMER_COMPLAINTS","IS_NEW_ENTRANT","TRANSPORTATION_RISKY_NEW_ENTRANT"],"parentType":"SUBMISSION"},"hiddenWhenAllFactsNegative":true}'),
    ('37e07bc0-9ca1-4b9a-8ea2-7bf47ba1b3a6', 'd5609df1-3087-45d6-86f6-a3cc702ec0d1', 20, 'fleet-operations-oosr', 'OUT_OF_SERVICE_CARD', 'Out of Service Rates', '{}'),
    ('37e07bc0-9ca1-4b9a-8ea2-7bf47ba1b3a6', 'b071ad27-f4de-454c-9554-abf67d19fddf', 30, 'fleet-operations-basic-scores', 'FACTS', 'BASIC Scores', '{"facts":{"factSubtypeIds":["TRANSPORTATION_BASIC_BENCHMARK_UNSAFE_DRIVING","TRANSPORTATION_BASIC_BENCHMARK_HOS_COMPLIANCE","TRANSPORTATION_BASIC_BENCHMARK_VEHICLE_MAINTENANCE","TRANSPORTATION_BASIC_BENCHMARK_DRUGS_ALCOHOL","TRANSPORTATION_BASIC_BENCHMARK_DRIVER_FITNESS"],"parentType":"SUBMISSION"},"hiddenWhenAllFactsNegative":true}'),
    ('8a4b1403-a3a5-40dd-a53d-3bc3c25fb23a', 'ed4b8856-e920-4830-a2b1-930a0bbe29d3', 0, 'crashes-entity-filter', 'ENTITY_FILTER', 'Filter By', '{"entityFilterKey":"crashes"}'),
    ('8a4b1403-a3a5-40dd-a53d-3bc3c25fb23a', 'f82502a7-1483-49a9-860d-ee6b92a2dbb6', 10, 'crash-report-summary', 'CRASH_REPORT_SUMMARY', 'Crash Report Summary', '{"entityFilterKey":"crashes"}'),
    ('8a4b1403-a3a5-40dd-a53d-3bc3c25fb23a', '10232808-f552-44a8-b3e9-88fe50ac0f0e', 20, 'crash-list', 'TABLE', NULL, '{"businessSelect":"all","entityFilterKey":"crashes","type":"CRASHES"}'),
    ('8a4b1403-a3a5-40dd-a53d-3bc3c25fb23a', 'cbd8f084-28a9-4c3b-9cb8-bc5db8835d14', 30, 'crash-map', 'CRASH_MAP', NULL, '{"entityFilterKey":"crashes"}'),
    ('f17317a3-a634-48c9-8b72-21a9b0b942a9', '47fdc3b1-bd67-474f-81f4-c3c3de66a860', 0, 'enforcement-cases-entity-filter', 'ENTITY_FILTER', 'Filter By', '{"entityFilterKey":"enforcementCases"}'),
    ('f17317a3-a634-48c9-8b72-21a9b0b942a9', '0563b6ba-ca10-4c17-9ea6-ee53751f4afe', 10, 'crash-enforcement-cases', 'TABLE', NULL, '{"entityFilterKey":"enforcementCases","type":"ENFORCEMENT_CASES"}'),
    ('0da69e85-54cd-44b7-8166-df9c72a04599', '3adb8ef5-f26c-4727-9069-c71694852c0b', 10, 'vehicles-in-fmcsa-summary', 'VEHICLES_IN_FMCSA_SUMMARY', 'Summary', '{}'),
    ('0da69e85-54cd-44b7-8166-df9c72a04599', '75b7f113-2f20-416d-a3bb-a132c11ef7d6', 20, 'fmcsa-vehicles-table', 'VEHICLES_IN_FMCSA_TABLE', NULL, '{}'),
    ('79ed7720-589b-409b-aa46-d13e9867dfa3', 'daefac74-ab04-44ea-9c74-e98651d690df', 20, 'business-operation', 'FACTS', 'Operations', '{"facts":{"group":"OPERATIONS_CARD","parentType":"BUSINESS"}}');
    
    
    INSERT INTO mode_elements (id, mode_id, position, row_id, section_id) VALUES
    ('dfa67c2d-43d3-4bec-a162-10a079d5165c', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 100, '21b8aa66-edf0-4b05-b4ce-f3963bca2c0f', NULL),
    ('123f4712-fb62-4773-98f6-87c0fa47aa38', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 200, NULL, '88da12ee-f66a-4c62-935c-a781d4f15b44'),
    ('e4dff0c8-3ec1-48ef-b0e2-2eeb0195f213', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 300, '7a39afb7-fb70-4460-af64-11d29cde844a', NULL),
    ('e9556ecd-3b28-4142-a47c-9fb137e697bd', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 400, 'fa6f1865-7777-455e-92c0-fa0a4e83502a', NULL),
    ('3a449935-5a6e-4534-9f72-d343a3f639ab', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 500, 'e357c13b-c054-4796-8ba2-7e5fc15b30ab', NULL),
    ('6710d6aa-0a7c-4b03-a674-db3be8692b64', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 600, '86e78214-e253-460a-890e-7974407022bf', NULL),
    ('95c356e1-f38f-432c-b2e4-01fee263755e', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 700, 'aefdad38-63fe-4d95-9cb9-cf119e7562d0', NULL),
    ('9d063a0b-ae5a-4310-ac12-574aa74e8d74', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 800, NULL, '6e5a6f8b-5d23-48b7-b2a8-45d9fc0a5397'),
    ('5270d298-e9f1-4f2b-a7cc-183ea63aeefb', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 900, 'd9d4d5a3-31cf-4c08-947c-c03f0499ba7f', NULL),
    ('539be3e7-1a2c-43dd-871c-17af1312ff23', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 1000, NULL, '7c23fb6d-ca8a-4596-956f-118eb6d2bf8d'),
    ('b754b4c0-2f26-46f5-a0ae-860274ad61e3', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 1100, '6c246235-46ef-403e-bd2c-a519c5aa0f50', NULL),
    ('4d9143e4-94f3-435d-b80f-e555024e5d64', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 1200, '287e71a2-e24a-48d5-b774-0da2315779aa', NULL),
    ('98dcf787-3b32-49a6-bc70-26daf58fd196', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 1300, NULL, '124c2a09-3b92-401d-bbe1-e232ab43504f'),
    ('4a0cd179-f8f3-4218-b196-d8224f5f8037', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 1400, '8dd78b11-7673-4f71-bf33-f5b0c5aab32c', NULL),
    ('01333d6c-6312-43fd-9b53-de09c0b0ca3b', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 1500, NULL, '86f3f113-4255-4512-a27d-a1019828f4aa');
    
    
    INSERT INTO mode_section_elements (id, section_id, position, child_row_id, child_section_id) VALUES
    ('60d36fb6-c9df-4d33-97ab-3044ad5baeeb', '88da12ee-f66a-4c62-935c-a781d4f15b44', 100, NULL, '6a777e3f-c1fd-4d16-86da-73a21eb933e4'),
    ('4b8a2862-34ee-441d-bc5a-7e4dd7ede87a', '88da12ee-f66a-4c62-935c-a781d4f15b44', 200, '1d7b3cd0-202d-4c9a-9f1f-87d3ba03ef28', NULL),
    ('aac434bb-3394-4df5-9e2b-8ce03a7c416c', '6a777e3f-c1fd-4d16-86da-73a21eb933e4', 100, '5367b9d5-08d6-439e-a607-119668019aa8', NULL),
    ('ace3cc7f-efc1-4771-af5b-e51b40b59a5f', '7c23fb6d-ca8a-4596-956f-118eb6d2bf8d', 100, '1647f338-99e7-41d8-a5c0-847cd1a9bf8a', NULL),
    ('1df16692-7b4c-439c-9a0c-c028fc04efa8', '7c23fb6d-ca8a-4596-956f-118eb6d2bf8d', 200, '33c91db4-32b8-4fa8-8852-1131fbfd3e25', NULL),
    ('2ab18a98-c783-47a4-a893-b3ea3ab41953', '124c2a09-3b92-401d-bbe1-e232ab43504f', 100, '0c21aa0d-c055-4c11-8a0b-83b511e0e121', NULL),
    ('e2056944-bc85-4413-aee3-1a2bb73dc083', '86f3f113-4255-4512-a27d-a1019828f4aa', 100, 'b7893774-bc58-40c8-8ede-40232b2323fe', NULL),
    ('79baf90c-394e-4aae-809b-5e482449d08f', '86f3f113-4255-4512-a27d-a1019828f4aa', 200, NULL, 'f46c8539-35d1-456d-b561-3951ebf88bfb'),
    ('bc41e08d-9666-4040-9206-06b2f4d1798b', 'f46c8539-35d1-456d-b561-3951ebf88bfb', 100, 'dc556da5-9e45-4bec-a71f-256396e3b910', NULL),
    ('aecad219-31cf-4785-801c-c71368a4b166', 'f46c8539-35d1-456d-b561-3951ebf88bfb', 200, '608f3b9f-dd8b-48ad-a1f2-7517e8d320c4', NULL),
    ('2016fe71-1352-4641-920f-8b8a581de25f', 'f46c8539-35d1-456d-b561-3951ebf88bfb', 300, 'd512ba9b-1fb0-4bf7-915f-0ddd31cc714d', NULL),
    ('cf1c751e-7d56-41e7-9da8-290bd3b3744f', 'f46c8539-35d1-456d-b561-3951ebf88bfb', 400, '17ee9a97-5f80-470f-8a1e-6e56ba086b23', NULL),
    ('8202d764-9dfc-48ad-88ac-43da9947fe94', 'f46c8539-35d1-456d-b561-3951ebf88bfb', 500, '359ef163-6160-41a1-bea2-e45fc9e59624', NULL),
    ('9d4dffa9-d4bd-43cd-9270-f0e4b1d2c01b', 'f46c8539-35d1-456d-b561-3951ebf88bfb', 600, 'ca131177-e3fe-4d60-aed7-35928d7cd6c7', NULL);
    """)


def downgrade():
    pass

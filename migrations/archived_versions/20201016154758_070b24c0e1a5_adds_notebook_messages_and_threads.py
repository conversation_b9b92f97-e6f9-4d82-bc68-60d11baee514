"""Adds notebook messages and threads

Revision ID: 070b24c0e1a5
Revises: cc2bed08b461
Create Date: 2020-10-16 15:47:58.926171+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "070b24c0e1a5"
down_revision = "cc2bed08b461"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "notebook_thread",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "relates_to",
            sa.<PERSON>um("SUBMISSION", "SUBMISSION_BUSINESS", "DOSSIER_COMPONENT", name="notebookthreadsubject"),
            nullable=True,
        ),
        sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("dossier_component_path", sa.String(length=256), nullable=True),
        sa.ForeignKeyConstraint(["submission_business_id"], ["submission_businesses.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_notebook_thread_submission_business_id"), "notebook_thread", ["submission_business_id"], unique=False
    )
    op.create_index(op.f("ix_notebook_thread_submission_id"), "notebook_thread", ["submission_id"], unique=False)
    op.create_table(
        "notebook_message",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("body", sa.String(length=2048), nullable=True),
        sa.Column("author_id", sa.Integer(), nullable=False),
        sa.Column("thread_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["author_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(["thread_id"], ["notebook_thread.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_notebook_message_author_id"), "notebook_message", ["author_id"], unique=False)
    op.create_index(op.f("ix_notebook_message_thread_id"), "notebook_message", ["thread_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_notebook_message_thread_id"), table_name="notebook_message")
    op.drop_index(op.f("ix_notebook_message_author_id"), table_name="notebook_message")
    op.drop_table("notebook_message")
    op.drop_index(op.f("ix_notebook_thread_submission_id"), table_name="notebook_thread")
    op.drop_index(op.f("ix_notebook_thread_submission_business_id"), table_name="notebook_thread")
    op.drop_table("notebook_thread")

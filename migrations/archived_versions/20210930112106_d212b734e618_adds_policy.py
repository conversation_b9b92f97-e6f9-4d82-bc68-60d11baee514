"""Adds policy

Revision ID: d212b734e618
Revises: 49cdb50178f6
Create Date: 2021-09-28 22:29:29.558457+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d212b734e618"
down_revision = "1913504723ee"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "policy",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("external_id", sa.String(), nullable=False),
        sa.Column("organization_id", sa.Integer(), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
        ),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("external_id", "organization_id", name="_external_id_organization_id_uc"),
    )
    op.create_index(op.f("ix_policy_external_id"), "policy", ["external_id"], unique=False)
    op.create_index(op.f("ix_policy_organization_id"), "policy", ["organization_id"], unique=False)
    op.create_index(op.f("ix_policy_submission_id"), "policy", ["submission_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_policy_submission_id"), table_name="policy")
    op.drop_index(op.f("ix_policy_organization_id"), table_name="policy")
    op.drop_index(op.f("ix_policy_external_id"), table_name="policy")
    op.drop_table("policy")

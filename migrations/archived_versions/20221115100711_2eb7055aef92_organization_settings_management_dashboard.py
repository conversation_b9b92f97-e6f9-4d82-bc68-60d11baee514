"""organization_settings for Management Dashboard

Revision ID: 2eb7055aef92
Revises: 6598ff7d0eaa
Create Date: 2022-11-15 10:07:11.067340+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2eb7055aef92"
down_revision = "6598ff7d0eaa"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("settings", sa.Column("is_management_dashboard_enabled", sa.<PERSON>(), nullable=True))
    op.execute("update settings set is_management_dashboard_enabled = true where organization_id = 3;")
    op.execute(
        "update settings set is_management_dashboard_enabled = true where user_id = (SELECT id FROM users WHERE email ="
        " '<EMAIL>');"
    )
    op.execute(
        "update settings set is_management_dashboard_enabled = true where user_id = (SELECT id FROM users WHERE email ="
        " 'mno<PERSON>@mutualofenumclaw.com');"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("settings", "is_management_dashboard_enabled")
    # ### end Alembic commands ###

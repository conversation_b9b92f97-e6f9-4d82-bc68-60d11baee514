"""Indexes submission field keys and values

Revision ID: 2edb6d56221b
Revises: 619ffd92f860
Create Date: 2021-04-15 18:32:16.098177+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "2edb6d56221b"
down_revision = "619ffd92f860"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(op.f("ix_user_fields_review_field_key"), "user_fields_review", ["field_key"], unique=False)
    op.create_index(op.f("ix_user_fields_review_field_value"), "user_fields_review", ["field_value"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_user_fields_review_field_value"), table_name="user_fields_review")
    op.drop_index(op.f("ix_user_fields_review_field_key"), table_name="user_fields_review")

"""adds unique constraints to permission table

Revision ID: b0f4a32da6a8
Revises: f29b35c378b3
Create Date: 2020-10-09 11:11:25.774112+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b0f4a32da6a8"
down_revision = "f29b35c378b3"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(
        "report_permisison_report_id_grantee_user_id_index",
        "report_permissions",
        ["grantee_user_id", "report_id"],
        unique=True,
    )


def downgrade():
    op.drop_index("report_permisison_report_id_grantee_user_id_index", table_name="report_permissions")

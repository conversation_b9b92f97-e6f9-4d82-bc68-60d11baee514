"""Add created_at index for Report

Revision ID: 225f2246ccce
Revises: 03b3cdfbbb64
Create Date: 2023-06-04 15:26:19.314575+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "225f2246ccce"
down_revision = "03b3cdfbbb64"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '900 s';")  # 15 min
        op.execute("CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_reports_v2_created_at ON reports_v2 (created_at);")


def downgrade():
    pass

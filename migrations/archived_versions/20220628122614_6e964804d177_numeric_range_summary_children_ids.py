"""Adds children_ids to numeric range summary

Revision ID: 6e964804d177
Revises: 4ed967f1a7ce
Create Date: 2022-06-28 12:26:14.501168+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6e964804d177"
down_revision = "4ed967f1a7ce"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "numeric_range_summary",
        sa.Column("children_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
    )


def downgrade():
    op.drop_column("numeric_range_summary", "children_ids")

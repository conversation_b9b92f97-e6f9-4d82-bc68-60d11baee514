"""add_classification_to_file

Revision ID: 1657dea7ecf4
Revises: a9424ca12e2f
Create Date: 2022-12-01 12:04:51.998794+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1657dea7ecf4"
down_revision = "a9424ca12e2f"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("files", sa.Column("classification", sa.String(), nullable=True))


def downgrade():
    op.drop_column("files", "classification")

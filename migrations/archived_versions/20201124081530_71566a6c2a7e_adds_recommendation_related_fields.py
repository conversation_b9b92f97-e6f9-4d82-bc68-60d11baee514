"""Adds recommendation related fields

Revision ID: 71566a6c2a7e
Revises: 55f5df646174
Create Date: 2020-11-24 08:15:30.786736-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "71566a6c2a7e"
down_revision = "55f5df646174"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "recommendation_explanation",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("text", sa.String(length=2048), nullable=True),
        sa.Column("icon_name", sa.String(length=128), nullable=True),
        sa.Column("additional_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_recommendation_explanation_submission_id"),
        "recommendation_explanation",
        ["submission_id"],
        unique=False,
    )
    recommendation_type = postgresql.ENUM("ACCEPT", "DECLINE", "REFER", name="recommendationtype")
    recommendation_type.create(op.get_bind())
    op.add_column("submissions", sa.Column("expected_value", sa.Float(), nullable=True))
    op.add_column(
        "submissions",
        sa.Column(
            "recommendation_action", sa.Enum("ACCEPT", "DECLINE", "REFER", name="recommendationtype"), nullable=True
        ),
    )


def downgrade():
    op.drop_column("submissions", "recommendation_action")
    op.drop_column("submissions", "expected_value")
    op.drop_index(op.f("ix_recommendation_explanation_submission_id"), table_name="recommendation_explanation")
    op.drop_table("recommendation_explanation")
    # ### end Alembic commands ###

"""Remove the ACCEPT recommendation

Revision ID: c56c1e57896d
Revises: 27e18d6c5178
Create Date: 2023-03-06 14:39:15.811633+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c56c1e57896d"
down_revision = "27e18d6c5178"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update submissions
    set recommendation_v2_action='PREFERRED'
    where recommendation_v2_action='ACCEPT';
    
    alter type recommendationactionenum rename to recommendationactionenum_old;

    create type recommendationactionenum as enum('REFER', 'DECLINE', 'NO_ACTION', 'PREFERRED', 'FLAG_FOR_REVIEW', 'RED_FLAG');
    
    alter table submissions alter column recommendation_v2_action type recommendationactionenum using recommendation_v2_action::text::recommendationactionenum;
    
    drop type recommendationactionenum_old;
    """)


def downgrade():
    pass

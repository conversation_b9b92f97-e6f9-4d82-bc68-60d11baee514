"""Adds metric_preferences table

Revision ID: 30c0aaf769c3
Revises: 7b83f94fa03b
Create Date: 2020-10-13 21:44:18.367719+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "30c0aaf769c3"
down_revision = "7b83f94fa03b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "metric_preference",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("metric_config_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("is_applicable", sa.<PERSON>(), nullable=False),
        sa.Column("is_enabled", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["metric_config_id"],
            ["metric_config.id"],
        ),
        sa.ForeignKeyConstraint(
            ["report_id"],
            ["reports_v2.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_metric_preference_is_applicable"), "metric_preference", ["is_applicable"], unique=False)
    op.create_index(op.f("ix_metric_preference_is_enabled"), "metric_preference", ["is_enabled"], unique=False)
    op.create_index(
        op.f("ix_metric_preference_metric_config_id"), "metric_preference", ["metric_config_id"], unique=False
    )
    op.create_index(op.f("ix_metric_preference_report_id"), "metric_preference", ["report_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_metric_preference_report_id"), table_name="metric_preference")
    op.drop_index(op.f("ix_metric_preference_metric_config_id"), table_name="metric_preference")
    op.drop_index(op.f("ix_metric_preference_is_enabled"), table_name="metric_preference")
    op.drop_index(op.f("ix_metric_preference_is_applicable"), table_name="metric_preference")
    op.drop_table("metric_preference")

"""Adds LOSS_RUN_SUMMARY filetype

Revision ID: 044c8b188116
Revises: bad5b314a729
Create Date: 2023-05-30 19:46:37.336795+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "044c8b188116"
down_revision = "bad5b314a729"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'LOSS_RUN_SUMMARY';""")


def downgrade():
    ...

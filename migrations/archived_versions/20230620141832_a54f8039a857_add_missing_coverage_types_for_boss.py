"""add missing coverage types for boss

Revision ID: a54f8039a857
Revises: f186a61aba32
Create Date: 2023-06-20 14:18:32.367278+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a54f8039a857"
down_revision = "f186a61aba32"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""UPDATE coverages SET coverage_types[0] = 'EXCESS' WHERE name = 'umbrella' AND organization_id = 6;""")
    op.execute(
        """UPDATE coverages SET coverage_types[0] = 'PRIMARY' WHERE name = 'generalLiability' AND organization_id = 6;"""
    )


def downgrade():
    op.execute("""UPDATE coverages SET coverage_types = '{}' WHERE name = 'umbrella' AND organization_id = 6;""")
    op.execute(
        """UPDATE coverages SET coverage_types = '{}' WHERE name = 'generalLiability' AND organization_id = 6;"""
    )

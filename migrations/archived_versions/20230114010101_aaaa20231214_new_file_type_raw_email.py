""" New file type RAW_EMAIL
Revision ID: aaaa20231214
Revises: 62acfa1d662b
Create Date: 2023-01-14 01:01:01.000000+00:00
"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "aaaa20231214"
down_revision = "62acfa1d662b"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'RAW_EMAIL';""")


def downgrade():
    pass

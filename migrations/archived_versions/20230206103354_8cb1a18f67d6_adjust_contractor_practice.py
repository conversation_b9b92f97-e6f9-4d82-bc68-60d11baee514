"""adjust_contractor_practice

Revision ID: 8cb1a18f67d6
Revises: b8e9273de9ba
Create Date: 2023-02-06 10:33:54.697045+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8cb1a18f67d6"
down_revision = "b8e9273de9ba"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        UPDATE mode_cards 
            SET title = 'Work Locations'
            WHERE id = '38571967-eb91-424b-b03a-b35d0d5e250d';
        UPDATE mode_rows
            SET position = 45
            WHERE id = '863fb927-a42d-4032-ace2-c7172a8448fa';
        DELETE FROM mode_cards WHERE id = 'dfee9595-a6f8-43a8-990c-649ddede36d1';
    """)


def downgrade():
    pass

"""Extends copilot worker execution type

Revision ID: 9826509318ef
Revises: 2bf5e272a3e5
Create Date: 2022-04-21 08:52:09.949369+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9826509318ef"
down_revision = "2bf5e272a3e5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE copilotworkerexecutiontype ADD VALUE IF NOT EXISTS 'RESOLVE_BUSINESSES';""")


def downgrade():
    pass

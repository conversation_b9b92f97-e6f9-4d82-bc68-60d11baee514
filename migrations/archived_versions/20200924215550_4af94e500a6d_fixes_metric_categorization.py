"""Fixes metric categorization

Revision ID: 4af94e500a6d
Revises: 5fe4067414c4
Create Date: 2020-09-24 21:55:50.413685+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4af94e500a6d"
down_revision = "5fe4067414c4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update summary_preference
    set group_display_name = 'Catastrophe Risks' 
    where group_display_name = 'Weather Risks';
    """)

    conn.execute("""
    update summary_preference
    set group_display_name = 'Fire Risks'
    where display_name = 'Wildfire'
    """)

    conn.execute("""
    update summary_preference
    set group_display_name = 'Property Insights'
    where group_display_name = 'Property Statistics'
    """)

    conn.execute("""
    update summary_preference
    set group_display_name = 'Other Risks'
    where group_display_name = 'Location Risks'
    """)


def downgrade():
    pass

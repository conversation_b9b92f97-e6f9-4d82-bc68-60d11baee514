"""Migrates user

Revision ID: 673b489488ee
Revises: 8c6e0daef2a5
Create Date: 2021-04-19 19:42:29.057435+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "673b489488ee"
down_revision = "8c6e0daef2a5"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("delete from report_aliases where external_id='auth0|5e54b25cb23943101a7cd75e';")

    conn.execute("""
    update users
    set external_id = null
    where id = 103 and email = '<EMAIL>';
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|CAPSPECIALTY|<EMAIL>'
    where id = 44 and email = '<EMAIL>';
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
    update users
    set external_id = 'samlp|CAPSPECIALTY|<EMAIL>'
    where id = 103 and email = '<EMAIL>';
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|5e54b25cb23943101a7cd75e'
    where id = 44 and email = '<EMAIL>';
    """)

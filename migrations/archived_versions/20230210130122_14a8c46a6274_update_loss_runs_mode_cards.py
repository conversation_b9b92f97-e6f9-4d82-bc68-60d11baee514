"""guideone coverages

Revision ID: 14a8c46a6274
Revises: 276b40b19591
Create Date: 2023-02-13 08:01:22.877156+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "14a8c46a6274"
down_revision = "276b40b19591"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards set position=20 where column_id='c473d0b0-8467-407e-a191-2e35654c456b' and card_id='loss-runs-card';
        update mode_cards set position=20 where column_id='d9e9a17c-694b-4dfc-a9b0-3c731dde56f3' and card_id='loss-runs-card';
        update mode_cards set position=20 where column_id='a9dccd0b-166d-42f2-a986-ca0aca8de7b3' and card_id='loss-runs-card';

        update mode_cards set column_id='c473d0b0-8467-407e-a191-2e35654c456b', position=10 where column_id='9ae185ba-0acc-4881-be0e-746247d51cd9' and card_id='loss-summaries-card';
        update mode_cards set column_id='d9e9a17c-694b-4dfc-a9b0-3c731dde56f3', position=10 where column_id='6ca9034c-bf0d-4da6-957f-c123866acf56' and card_id='loss-summaries-card';
        update mode_cards set column_id='a9dccd0b-166d-42f2-a986-ca0aca8de7b3', position=10 where column_id='6a2e6e58-18a8-4801-9fcf-61f4f8663ef0' and card_id='loss-summaries-card';

        update mode_cards set column_id='c473d0b0-8467-407e-a191-2e35654c456b', position=0 where column_id='c7750861-256d-4a63-aafc-7b9d37e77556' and card_id='loss-files-card';
        update mode_cards set column_id='d9e9a17c-694b-4dfc-a9b0-3c731dde56f3', position=0 where column_id='cee61882-cd17-463f-91c2-0799df889e2d' and card_id='loss-files-card';
        update mode_cards set column_id='a9dccd0b-166d-42f2-a986-ca0aca8de7b3', position=0 where column_id='511f6802-2236-4db5-b379-0c7dc627eeeb' and card_id='loss-files-card';

        delete from mode_columns where id='9ae185ba-0acc-4881-be0e-746247d51cd9';
        delete from mode_columns where id='6ca9034c-bf0d-4da6-957f-c123866acf56';
        delete from mode_columns where id='6a2e6e58-18a8-4801-9fcf-61f4f8663ef0';
        delete from mode_columns where id='c7750861-256d-4a63-aafc-7b9d37e77556';
        delete from mode_columns where id='cee61882-cd17-463f-91c2-0799df889e2d';
        delete from mode_columns where id='511f6802-2236-4db5-b379-0c7dc627eeeb';

        delete from mode_rows where id='ade3745c-b863-4978-9b93-64266cfe9e21';
        delete from mode_rows where id='a95e4de8-1964-4ae2-9a2c-3ee9aab8b5b0';
        delete from mode_rows where id='bcc8740d-4557-460a-a0fa-ea229bb53d38';
        delete from mode_rows where id='e16415c9-5af1-43d1-9834-b1e130811e6d';
        delete from mode_rows where id='8bc2dece-5faa-42b2-88ac-40959a778e97';
        delete from mode_rows where id='125407fb-4083-44f8-b775-1c1e6ccf026f';
    """)


def downgrade():
    pass

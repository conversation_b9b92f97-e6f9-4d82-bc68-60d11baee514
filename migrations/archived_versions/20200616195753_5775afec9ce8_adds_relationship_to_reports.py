"""Adds relationship to Reports

Revision ID: 5775afec9ce8
Revises: 822f48703e37
Create Date: 2020-06-16 19:57:53.280158

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5775afec9ce8"
down_revision = "822f48703e37"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric", sa.Column("report_id", sa.Integer(), nullable=False))
    op.create_index(op.f("ix_metric_report_id"), "metric", ["report_id"], unique=False)
    op.create_foreign_key(None, "metric", "reports", ["report_id"], ["id"], ondelete="CASCADE")


def downgrade():
    pass

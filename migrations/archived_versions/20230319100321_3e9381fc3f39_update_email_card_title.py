"""Update email card title

Revision ID: 3e9381fc3f39
Revises: 1226f4c2dee2
Create Date: 2023-03-19 10:03:21.412339+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3e9381fc3f39"
down_revision = "1226f4c2dee2"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
            update mode_cards set title='Submission Email' where title='Email';
        """)


def downgrade():
    pass

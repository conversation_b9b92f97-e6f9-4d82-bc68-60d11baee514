"""Adds additional fields to loss

Revision ID: 8f42cc55924e
Revises: 4a26752c54f1
Create Date: 2021-11-26 13:54:16.075368+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8f42cc55924e"
down_revision = "4a26752c54f1"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("loss", sa.Column("claim_status", sa.String(), nullable=True))
    op.add_column("loss", sa.Column("ligitation_status", sa.String(), nullable=True))
    op.add_column("loss", sa.Column("original_line_of_business", sa.String(), nullable=True))
    op.add_column("loss", sa.Column("net_of_deductible", sa.Float(), nullable=True))
    op.add_column("loss", sa.Column("policy_expiration_date", sa.Date(), nullable=True))
    op.add_column("loss", sa.Column("recoveries", sa.Float(), nullable=True))
    op.add_column("loss", sa.Column("total_paid", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("loss", "total_paid")
    op.drop_column("loss", "recoveries")
    op.drop_column("loss", "policy_expiration_date")
    op.drop_column("loss", "net_of_deductible")
    op.drop_column("loss", "ligitation_status")
    op.drop_column("loss", "claim_status")
    op.drop_column("loss", "original_line_of_business")

"""drop user stages

Revision ID: f482315079d3
Revises: 1b007a248c2e
Create Date: 2023-03-23 07:50:26.050894+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f482315079d3"
down_revision = "1b007a248c2e"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("ix_user_submission_stage_stage", table_name="user_submission_stage")
    op.drop_index("ix_user_submission_stage_submission_id", table_name="user_submission_stage")
    op.drop_index("ix_user_submission_stage_user_id", table_name="user_submission_stage")
    op.drop_index("user_submission_stage_user_submission_idx", table_name="user_submission_stage")
    op.drop_table("user_submission_stage")


def downgrade():
    op.create_table(
        "user_submission_stage",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("submission_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "stage",
            postgresql.ENUM(
                "JUST_IN",
                "ON_MY_PLATE",
                "WAITING_FOR_OTHERS",
                "QUOTED",
                "COMPLETED",
                "DECLINED",
                "QUOTED_LOST",
                "QUOTED_BOUND",
                "CLEARING_ISSUE",
                "BLOCKED",
                "EXPIRED",
                "INDICATED",
                name="submissionstage",
            ),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("dependent_on", postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["submission_id"], ["submissions.id"], name="user_submission_stage_submission_id_fkey", ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], name="user_submission_stage_user_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="user_submission_stage_pkey"),
    )
    op.create_index(
        "user_submission_stage_user_submission_idx", "user_submission_stage", ["user_id", "submission_id"], unique=False
    )
    op.create_index("ix_user_submission_stage_user_id", "user_submission_stage", ["user_id"], unique=False)
    op.create_index("ix_user_submission_stage_submission_id", "user_submission_stage", ["submission_id"], unique=False)
    op.create_index("ix_user_submission_stage_stage", "user_submission_stage", ["stage"], unique=False)
    # ### end Alembic commands ###

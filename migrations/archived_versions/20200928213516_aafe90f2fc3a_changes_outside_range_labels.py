"""Changes outside range labels

Revision ID: aafe90f2fc3a
Revises: 259e679c1f80
Create Date: 2020-09-28 21:35:16.117706+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "aafe90f2fc3a"
down_revision = "259e679c1f80"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update range_summary
    set outside_range_label = 'Outside Range or Unknown'
    where outside_range_label = 'Outside Range'
    """)


def downgrade():
    pass

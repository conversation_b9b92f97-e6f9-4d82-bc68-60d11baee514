"""Adds index to recommendation.created_at

Revision ID: 1c7cddd4d453
Revises: f64a2a800c33
Create Date: 2021-04-01 11:48:09.636831-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1c7cddd4d453"
down_revision = "f64a2a800c33"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(op.f("ix_recommendations_created_at"), "recommendations", ["created_at"], unique=False)
    op.drop_column("recommendations", "updated_at")


def downgrade():
    op.add_column(
        "recommendations",
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    )
    op.drop_index(op.f("ix_recommendations_created_at"), table_name="recommendations")

"""Fills organization_id and policy_id and makes organization_id not nullable

Revision ID: 7d8525540040
Revises: e3074baff9d6
Create Date: 2022-01-31 10:58:33.492085+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7d8525540040"
down_revision = "e3074baff9d6"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""update loss set organization_id = users.organization_id from submissions 
        JOIN users ON submissions.owner_id = users.id where submissions.id = loss.submission_id""")
        op.execute("""update loss set policy_id = policy.external_id from policy 
        where policy.submission_id = loss.submission_id""")
        op.execute("""update loss set submission_id = null where loss.policy_id is not null""")
    conn = op.get_bind()
    conn.execute("""ALTER TABLE loss ALTER COLUMN organization_id SET NOT NULL""")


def downgrade():
    pass

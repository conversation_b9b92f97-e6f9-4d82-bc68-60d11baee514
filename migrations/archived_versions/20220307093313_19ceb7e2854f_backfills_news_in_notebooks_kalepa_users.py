"""Backfills news in notebook for Kalepa users

Revision ID: 19ceb7e2854f
Revises: 43adf9e3824e
Create Date: 2022-03-07 09:33:13.729954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "19ceb7e2854f"
down_revision = "43adf9e3824e"
branch_labels = None
depends_on = None

id_mapping = [
    ["f67490be-2b1f-4288-8868-a8b0569900b3", "810bfe6e-532d-d39a-9e8b-1dd40504c01b"],
    ["f38cec44-530d-4f8d-b9ed-ebeb7662f39e", "1b83dad5-e3bd-4469-872a-488507db76b0"],
    ["f2e1edec-8eef-4a12-b2e3-abccc176dd2b", "04811f24-1313-eda9-21b2-ecaaf1d57f05"],
    ["f14f7708-56fa-407f-8f30-6a487679e944", "7f07cd81-cb9a-9839-7340-3c4492b4cc1c"],
    ["ecaf66a2-0e10-45c5-95b0-38be431ec5c2", "59d0422d-a39f-6227-13c3-adcddc708ab1"],
    ["ea8b9e4b-9da1-4206-8fc8-b2a13db28455", "720f4025-8f00-4e2f-b055-89924ef397a7"],
    ["e5c98bf9-b847-4f9b-8509-6c3bb26b8b03", "d1d233d3-fe7a-f46a-09d4-f5d3a9c998cc"],
    ["df4c1b7a-17e9-4437-b416-47df9d797fd2", "f5095dca-c635-a838-134b-7655ecd87c60"],
    ["dc1456fa-e39e-469b-b030-dfc07dd975f2", "d0da7c22-3ca1-b5e8-54f1-e83e3c86d8f5"],
    ["dad099d1-1b1c-4e06-9739-918ef10a0b96", "15517039-3beb-a4bf-cc62-eca94ca056fe"],
    ["cc7c7996-3c5e-4d4a-8424-eac5387d19fe", "cd2fc44f-5fd7-9536-6d78-9747a382869b"],
    ["cc529127-58e3-4238-b702-262ff17c33f6", "56b5226e-4daf-8e3d-90b7-51a2886cf320"],
    ["c7cd3df5-3612-4873-a741-27bbc7f1b127", "129f933f-13fa-f0d1-7b65-4761d1a19570"],
    ["c2137ccb-92e9-4b05-8114-4b1fae80c94d", "bd32c442-f006-f3a4-6477-ad7c35d18fbb"],
    ["c12efb8d-9068-4d6e-8dd8-e38d3ff2b280", "edd5b191-44cb-d2bc-7ea0-d23595f757d7"],
    ["bfa2a01b-fc4a-47a3-a327-0cc0bd5b8c9b", "945d5db2-a195-70a6-048f-401131f3904e"],
    ["bf9f4768-870c-49ec-978f-1615bab3185e", "0e2cd1b5-367a-4a08-1328-13f77acdfca5"],
    ["bc1f393e-3c4d-4649-98ad-b93017d2f2c6", "307b1e92-d341-46c8-6012-02935c5fba52"],
    ["b756d2a2-7e8f-4a6a-bd89-20d736d65818", "d547aeb7-bc1e-da85-a54a-567b1c3236a2"],
    ["b6eacf11-255e-4f5b-8d9f-989bd4a854c0", "dbeb712c-46a6-3c83-98b8-0ab286bb815b"],
    ["b0f2d796-4590-4c2a-a4c8-996eeae77c75", "86841104-4536-4a45-20c6-bf4387b5c971"],
    ["a2edb627-fa15-41b9-922c-d7ecf3976180", "3d69eb97-89c3-e1b9-4ac7-6d21ae35f734"],
    ["a1411726-a621-4c92-8fcc-d4562865b3b0", "64acc739-bdde-16d6-4dc9-6cc3a47e7404"],
    ["985178a9-c5f1-4cc1-925d-1dd1c46b430e", "fc9ac55f-68aa-6d87-929c-4ddc683ddaaf"],
    ["97cf8f34-0dd4-4584-93fb-1bdfb1811907", "ccb8b538-7636-26b5-1c35-b2a4910ce0d0"],
    ["96c8c72b-77d9-4b6e-9d31-9143d9577ea2", "5c3d30b4-3cc3-972f-f826-c1cf37e63f87"],
    ["96585af9-74bc-4c8e-a49b-852b505759bf", "4952982a-307c-d721-f2be-72155d2f0e43"],
    ["9449e2ad-58cd-4a5c-af84-b344bf0f6caf", "7578a5c1-160b-80ee-0bff-30401e7077da"],
    ["90b56955-1e60-40c6-b41b-53a7f41d9fb1", "2c910f7e-5322-3970-cfa7-c82cf4827e16"],
    ["8e2b32bf-e594-4cb1-a11b-a6f6cb184894", "5661eeb7-a06a-04b5-fbe5-7122cffbb4f3"],
    ["8951f160-a519-4093-8a37-03b0c0c5ab6c", "5f757073-a793-9123-8b10-30a3cf611549"],
    ["88780734-ed0b-4619-8288-80a2f19bef8b", "915b5b56-cb4b-b58e-d098-8ddae46fb32a"],
    ["862f1e71-ba5b-419c-9241-eed4a1bcf563", "81b0b1a1-1406-b5be-9e52-28ee2bb9180d"],
    ["7fe8347b-54b3-48fe-9cf8-b0b55894c231", "cc286439-4d55-9170-513e-0976b2d4004e"],
    ["78343d0b-77fa-452a-8cef-8dee46989715", "9a2aeb74-9e16-42c1-6cbd-dbd5817f1b94"],
    ["74613eb1-eccc-4238-8e1f-29b4a34e5262", "da467bb1-02ed-f37b-4d11-e64029715650"],
    ["742a6ea7-ec8b-441e-989b-10e8e3d203e0", "94cda9a3-bc81-6a86-6702-9f761deebc92"],
    ["73b87228-cfe0-4eb1-bbbb-714b079bac0d", "16edf798-65e1-9ee6-d2ad-3bc93e4aeb69"],
    ["72926b66-1280-496b-ba44-311db9f7742f", "ce982cda-c630-030f-c4b7-91ce909297ee"],
    ["6b9fbc98-e2d0-4a94-9121-df6a64eee513", "42c4aa23-de16-2188-5f5a-5fd3d8e68815"],
    ["69656ddd-8961-4c10-8a33-c416b4486e9e", "1f177719-628c-c64e-ecff-a754b4a232dd"],
    ["693ab29f-5edb-4801-b6c8-452fdc46cec7", "7247229f-14cc-76c4-1241-17955b12e648"],
    ["609c650e-23a1-4673-9e49-3f2338b12c86", "a3e8ea23-775e-1618-e862-925bc9dd589c"],
    ["601a5338-2e47-42f5-b05a-61e07896ee2b", "419a2a6c-c79d-2b05-7f43-ebd6a512a3de"],
    ["556aadfa-bb4c-4700-90f5-20bf49ec37c0", "8845a980-1ac0-6e31-d54b-22d393ef298c"],
    ["522a88e2-b22c-4a61-81d1-984ea4204b12", "b4c77e7f-4867-b16d-868c-e7d41176b59e"],
    ["3d7e17b9-fa54-4151-a3ba-4b002fd5fc96", "57050cbc-dc4f-856b-a7a1-3e597b0e28c6"],
    ["38d92283-59b8-41d2-b531-be35bfa1d60d", "ee62b522-0a9f-8f49-566a-6fbe3f7c2008"],
    ["35292d18-f460-4322-896f-4321c7fa1c6d", "b0c8725b-adc4-bd9b-75dc-38235d3505ab"],
    ["3416e564-249d-431c-bd66-3cc18764ea65", "03047e22-c650-ff0c-90ef-3bea2d93a776"],
    ["263228b3-6018-4364-8569-436bc01f36f4", "12df533d-7c13-a6c4-8f5f-77eb707e9413"],
    ["216e39ae-2fde-41a7-884c-2305c76c350d", "264aebce-cf45-d9cc-8e4d-030610291b30"],
    ["1f1733e1-f684-4fcb-b303-e03f87b48f85", "a71f9a27-1c83-b87d-7bf5-792b80fa6bfd"],
    ["1d5dd82e-5f07-4b76-8e00-bb3bd9c311fa", "3d95f649-392c-b02c-3061-12919b79c190"],
    ["1b3cc5a4-b0c6-4661-8c0e-2b7f78f69b88", "20ba3142-1d47-2825-8c38-8bede70376a9"],
    ["1489dec1-3c76-40b1-bdb7-38ab9d766a73", "c719258c-32a5-7b05-b4d3-af586a9a8a95"],
    ["11dcff4c-79a6-4b73-8710-9b639376f266", "a8e4f3ad-f669-269c-b7a2-d740cf144d0e"],
    ["1022011f-77d3-4e0a-b87d-a045bd2bd8a3", "0d7e293f-9ceb-16df-36a8-ccc117c367b9"],
    ["075e422c-893a-4838-958d-2412355bef55", "544fdbc2-c4b9-c0f9-2826-d2c85bddf87f"],
    ["016b05c2-62ce-442a-ab82-aa8432e6bc6c", "4545a8bf-633e-035f-83fe-ce161eb413ed"],
    ["00f05472-0546-402c-acce-6a625d8778aa", "bdb1e7c7-b3ed-0fd8-6c80-8c9a89bad3c5"],
    ["00e2fac7-ebae-46ab-9c2b-a92aff805f62", "cbc7ddda-1811-9acb-f0d6-59e8c9a2afbf"],
]

query = sa.text("""
    UPDATE public.notebook_thread
            set dossier_component_paths=ARRAY [E'news[?(@.id==\\'' || :new_id || E'\\')]']
            where dossier_component_paths[1] =  E'news[?(@.id==\\'' || :old_id || E'\\')]';
            """)


def upgrade():
    conn = op.get_bind()
    for p in id_mapping:
        query_params = {"new_id": p[1], "old_id": p[0]}
        conn.execute(statement=query, **query_params)


def downgrade():
    pass

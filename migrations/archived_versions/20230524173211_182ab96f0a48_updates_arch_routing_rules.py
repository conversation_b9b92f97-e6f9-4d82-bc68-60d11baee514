"""Updates arch routing rules

Revision ID: 182ab96f0a48
Revises: 94e5ff60c186
Create Date: 2023-05-24 17:32:11.965661+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "182ab96f0a48"
down_revision = "94e5ff60c186"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        update routing_rule set tier = 0 where tag = 'arch_aheaton';
        update routing_rule set tier = 0 where tag = 'arch_dhaverson';
        update routing_rule set tier = 0 where tag = 'arch_hbazzani';
        update routing_rule set tier = 0 where tag = 'arch_ejaffe';
        update routing_rule set tier = 1 where tag = 'arch_dchan';
        update routing_rule set tier = 1 where tag = 'arch_elogan';
        update routing_rule set tier = 1 where tag = 'arch_lgarcia';
        update routing_rule set tier = 1 where tag = 'arch_tfeshovets';
        update routing_rule set tier = 3 where tag = 'arch_bboddy';
        update routing_rule set tier = 1 where tag = 'arch_bdodson';
        update routing_rule set tier = 1 where tag = 'arch_clamprecht';
        update routing_rule set tier = 3 where tag = 'arch_evillaflor';
        update routing_rule set tier = 1 where tag = 'arch_fnichols';
        update routing_rule set tier = 2 where tag = 'arch_jbelloni';
        update routing_rule set tier = 2 where tag = 'arch_jcrist';
        update routing_rule set tier = 3 where tag = 'arch_jmautner';
        update routing_rule set tier = 3 where tag = 'arch_pgoodwin';
        update routing_rule set tier = 1 where tag = 'arch_rscott';
        update routing_rule set tier = 3 where tag = 'arch_tfreeman';
        """)


def downgrade():
    ...

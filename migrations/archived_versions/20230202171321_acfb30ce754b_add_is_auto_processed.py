"""Add distribution channel employed setting

Revision ID: acfb30ce754b
Revises: 96eda8326580
Create Date: 2023-02-02 17:13:21.880592+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

from copilot.models.types import DistributionChannelEmployedType

# revision identifiers, used by Alembic.
revision = "acfb30ce754b"
down_revision = "96eda8326580"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("is_auto_processed", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("submissions", "is_auto_processed")

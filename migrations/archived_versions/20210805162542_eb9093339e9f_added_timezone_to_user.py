"""Added timezone to user

Revision ID: eb9093339e9f
Revises: 9266cbe3a5ac
Create Date: 2021-08-05 16:25:42.699450+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "eb9093339e9f"
down_revision = "9266cbe3a5ac"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("users", sa.Column("timezone", sa.Integer(), nullable=True))


def downgrade():
    op.drop_column("users", "timezone")

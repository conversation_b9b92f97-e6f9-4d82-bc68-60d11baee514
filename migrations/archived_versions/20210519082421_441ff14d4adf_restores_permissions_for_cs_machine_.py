"""Restores permissions for CS machine user permissions

Revision ID: 441ff14d4adf
Revises: 711cf68ee157
Create Date: 2021-05-19 08:24:21.941166-04:00

"""
from alembic import op
import sqlalchemy as sa

from copilot.constants import CAPSPECIALITY_EDITORS

# revision identifiers, used by Alembic.
revision = "441ff14d4adf"
down_revision = "711cf68ee157"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    affected_reports = conn.execute("""select report_id from reports_v2 JOIN report_permissions ON 
    reports_v2.id=report_permissions.report_id where reports_v2.owner_id in (98, 85) group by report_id 
    having count(*) < 2""").fetchall()
    CS_EDITORS = conn.execute(
        f"""select id from users where email in ({', '.join([f"'{email}'" for email in CAPSPECIALITY_EDITORS])})"""
    )
    CS_EDITORS = [row[0] for row in CS_EDITORS]
    for row in affected_reports:
        report_id = row[0]
        for grantee_id in CS_EDITORS:
            conn.execute(
                f"""INSERT INTO report_permissions(id, created_at, grantee_user_id, report_id, 
            permission_type) VALUES (uuid_generate_v4(), now(), '{grantee_id}','{report_id}', 'EDITOR') ON CONFLICT DO NOTHING"""
            )


def downgrade():
    pass

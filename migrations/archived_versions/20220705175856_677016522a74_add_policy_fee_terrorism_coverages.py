"""Add terrorism and policy fee coverages

Revision ID: 677016522a74
Revises: 4c35a0d43b34
Create Date: 2022-07-05 17:03:14.501168+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "677016522a74"
down_revision = "4c35a0d43b34"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""INSERT INTO coverages VALUES (
           'e3118463-7b67-4e04-a300-228d0b8f24bc', now(), null, 'policyFee', 'Policy Fee', false, 7
       )""")
    op.execute("""INSERT INTO coverages VALUES (
               '5182fda1-e580-4c51-8da0-f4b3fffc5dc2', now(), null, 'terrorism', 'Terrorism', false, 7
           )""")


def downgrade():
    pass

"""Adds cascade ondelete to submission_history

Revision ID: e0b8a03e4be3
Revises: 12e004da54e2
Create Date: 2023-04-11 21:32:35.154555+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e0b8a03e4be3"
down_revision = "12e004da54e2"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("submission_history_submission_id_fkey", "submission_history", type_="foreignkey")
    op.create_foreign_key(None, "submission_history", "submissions", ["submission_id"], ["id"], ondelete="CASCADE")


def downgrade():
    ...

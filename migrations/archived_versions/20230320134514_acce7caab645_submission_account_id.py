"""submission account_id

Revision ID: acce7caab645
Revises: 3e9381fc3f39
Create Date: 2023-03-20 13:45:14.749655+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "acce7caab645"
down_revision = "3e9381fc3f39"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("account_id", sa.String(), nullable=True))
    op.create_index(
        "submissions_account_id_idx",
        "submissions",
        ["account_id"],
        unique=False,
        postgresql_where=sa.text("account_id IS NOT NULL"),
    )


def downgrade():
    op.drop_index(
        "submissions_account_id_idx", table_name="submissions", postgresql_where=sa.text("account_id IS NOT NULL")
    )
    op.drop_column("submissions", "account_id")

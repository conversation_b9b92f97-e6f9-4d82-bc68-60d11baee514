"""Add is verification required

Revision ID: a62f3e6153e9
Revises: cef4ce47a8a4
Create Date: 2022-10-03 14:21:23.456226+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a62f3e6153e9"
down_revision = "cef4ce47a8a4"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("is_verification_required", sa.<PERSON>(), nullable=True))
    op.add_column("submissions", sa.Column("is_verified", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("submissions", "is_verified")
    op.drop_column("submissions", "is_verification_required")

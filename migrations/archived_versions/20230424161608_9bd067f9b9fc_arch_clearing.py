"""arch clearing

Revision ID: 9bd067f9b9fc
Revises: a6e5cba2a97a
Create Date: 2023-04-24 16:16:08.229999+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9bd067f9b9fc"
down_revision = "a6e5cba2a97a"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE settings
SET can_resolve_clearing_issues = TRUE
WHERE user_id = 348;
        """)


def downgrade():
    pass

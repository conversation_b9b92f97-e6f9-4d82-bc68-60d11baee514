"""project cards parent id selector

Revision ID: 4bab671b2b76
Revises: fffb30ce754b
Create Date: 2023-02-02 08:10:07.602227+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4bab671b2b76"
down_revision = "fffb30ce754b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
-- Potential Exposures
UPDATE mode_cards SET props = '{
  "businessSelect": "contractorProject",
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"]
        },
        { "id": "PROJECT_SCAFFOLDING", "parentSubtypeIds": ["SCAFFOLDING"] },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"]
        },
        { "id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"] },
        { "id": "PROJECT_CRANE_WORK", "parentSubtypeIds": ["CRANES"] },
        { "id": "PROJECT_DEMOLITION_WORK", "parentSubtypeIds": ["DEMOLITION"] },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"]
        },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"]
        },
        { "id": "PROJECT_EXCAVATION_WORK" },
        { "id": "PROJECT_BELOW_GRADE" },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"]
        },
        { "id": "PROJECT_HEIGHT_IN_FT" },
        { "id": "PROJECT_HEIGHT_IN_STORIES" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES" }
      ]
    }
  }
}
'
WHERE id = '91da8d98-fdbb-4816-93e7-18ccb4951054';

-- Safety
UPDATE mode_cards SET props = '{
  "businessSelect": "contractorProject",
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        { "id": "PROJECT_SAFETY_PROGRAM" },
        { "id": "PROJECT_QUALITY_CONTROL_PROGRAM" },
        { "id": "PROJECT_SITE_INSPECTION_PROGRAM" }
      ]
    }
  }
}'
WHERE id = 'aac7825b-ccbd-4539-bbda-b93bdca02f80';

-- Costs
UPDATE mode_cards SET props = '{
  "businessSelect": "contractorProject",
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [{ "id": "PROJECT_ESTIMATED_CONSTRUCTION_COST" }]
    }
  }
}
'
WHERE id = 'e03570bd-903e-45be-a1c8-7f85deecdfd3';

-- Contractor
UPDATE mode_cards SET props = '{
  "businessSelect": "contractorProject",
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        { "id": "PROJECT_SUBCONTRACTORS_USED" },
        { "id": "PROJECT_PERCENTAGE_OF_WORK_SUBCONTRACTED_TO_OTHERS" },
        { "id": "PROJECT_SUBCONTRACTORS_COST" }
      ]
    }
  }
}
'
WHERE id = '92d60cf4-fce6-485e-9e98-7c2412a97604';

-- Crime Score
UPDATE mode_cards SET props = '{
  "businessSelect": "contractorProject",
  "facts": {
    "parentType": "PREMISES",
    "factSubtypeIds": ["OVERALL_CRIME_GRADE"]
  }
}
'
WHERE id = '6f3fa64d-b578-4760-99e9-ec089a46e6d9';

UPDATE mode_cards SET props = '{
  "businessSelect": "contractorProject",
  "facts": {
    "parentType": "PREMISES",
    "factSubtypeIds": [
      "ASSAULT_GRADE",
      "LARCENY_GRADE",
      "MURDER_GRADE",
      "ROBBERY_GRADE",
      "BURGLARY_GRADE",
      "VEHICLE_THEFT_GRADE",
      "RAPE_GRADE",
      "DRUG_ALCOHOL_RELATED_DEATHS_GRADE"
    ]
  }
}
'
WHERE id = '2a49671f-a1a0-41dc-90ae-66b6a54d6e6d';
    """)


def downgrade():
    pass

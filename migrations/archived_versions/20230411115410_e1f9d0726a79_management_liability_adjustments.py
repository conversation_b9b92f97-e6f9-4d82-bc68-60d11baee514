"""management liability mode adjustments

Revision ID: e1f9d0726a79
Revises: 1bab671b2b76
Create Date: 2023-04-11 11:54:10.781364+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e1f9d0726a79"
down_revision = "1bab671b2b76"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # create the mode
    conn.execute("""
        UPDATE modes
        SET props = NULL
        WHERE id = '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052';
        
        UPDATE mode_sections
        SET props =
                '{
                  "context": {
                    "type": "parents-select",
                    "parentType": "PREMISES"
                  },
                  "strategy": {
                    "type": "conditional",
                    "condition": {
                      "conditions": [
                        {
                          "type": "report",
                          "jsonPath": "$.submissions[0].coverages[*].coverage.name",
                          "containsOtherValues": "fiduciaryLiability"
                        }
                      ],
                      "type": "or"
                    }
                  }
                }'
        WHERE id = '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9';
        
        DELETE
        FROM mode_elements
        WHERE id = '44f1b1c7-34ec-4a7d-8bb3-3c69a6ef2fc9';
        
        DO
        $do$
        BEGIN
            IF EXISTS (select * from mode_sections where id='f079a313-6e05-492d-9e3f-e0e6bdf371ce') THEN
            INSERT INTO mode_elements (id, mode_id, position, row_id, section_id)
            VALUES ('78b8adea-e959-4704-b284-573f65fb5ed8', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 200,
                    null, 'f079a313-6e05-492d-9e3f-e0e6bdf371ce')
            ON CONFLICT DO NOTHING;
            END IF;
        END
        $do$
    """)


def downgrade():
    pass

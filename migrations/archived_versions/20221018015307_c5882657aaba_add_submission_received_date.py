"""Add submission received_date

Revision ID: c5882657aaba
Revises: 5204fc7a6c93
Create Date: 2022-10-18 01:53:07.465810+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c5882657aaba"
down_revision = "5204fc7a6c93"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("received_date", sa.DateTime(), nullable=True))


def downgrade():
    op.drop_column("submissions", "received_date")

"""Adds foreign key constraint to recommendation_rule.owner_organization_id

Revision ID: 559b8f7dc889
Revises: 77060cffb8dd
Create Date: 2020-11-23 23:21:47.848874+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "559b8f7dc889"
down_revision = "77060cffb8dd"
branch_labels = None
depends_on = None


def upgrade():
    op.create_foreign_key(None, "recommendation_rule", "organization", ["owner_organization_id"], ["id"])


def downgrade():
    op.drop_constraint(None, "recommendation_rule", type_="foreignkey")

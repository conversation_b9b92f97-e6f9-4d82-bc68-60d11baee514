"""New clearing logic

Revision ID: 0457a92ee4fa
Revises: 4d8a92201209
Create Date: 2023-04-17 12:50:50.150110+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0457a92ee4fa"
down_revision = "4d8a92201209"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("reports_v2", sa.<PERSON>umn("is_copy", sa.<PERSON>(), nullable=True))
    op.add_column("settings", sa.Column("clear_oldest_by_default", sa.<PERSON>(), nullable=True))
    op.execute("""update settings set clear_oldest_by_default = true;""")


def downgrade():
    op.drop_column("settings", "clear_oldest_by_default")
    op.drop_column("reports_v2", "is_copy")

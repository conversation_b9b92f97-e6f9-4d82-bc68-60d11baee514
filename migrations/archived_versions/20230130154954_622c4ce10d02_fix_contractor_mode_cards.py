"""fix_contractor_mode_cards

Revision ID: 622c4ce10d02
Revises: d7e02a720d34
Create Date: 2023-01-30 15:49:54.935987+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "622c4ce10d02"
down_revision = "d7e02a720d34"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE mode_cards SET
             props = '{"generalContractor":{"DEFAULT":{"factSubtypes":[{"id":"TOTAL_SALES"},{"id":"PAYROLL"}]}}}'
            WHERE id = '2cdf26c8-247f-4007-8839-940c1bdce261';
    """)


def downgrade():
    pass

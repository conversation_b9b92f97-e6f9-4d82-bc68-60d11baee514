"""Adds column for Auth0 SAML connector IDs

Revision ID: cad174f99056
Revises: d386ce2375b9
Create Date: 2020-11-20 21:57:29.914726+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cad174f99056"
down_revision = "d386ce2375b9"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("users", sa.Column("external_id_saml", sa.String(), nullable=True))
    op.create_index(op.f("ix_users_external_id_saml"), "users", ["external_id_saml"], unique=True)


def downgrade():
    op.drop_index(op.f("ix_users_external_id_saml"), table_name="users")
    op.drop_column("users", "external_id_saml")

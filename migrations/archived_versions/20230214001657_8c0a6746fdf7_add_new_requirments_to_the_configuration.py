"""Add new requirments to the configuration

Revision ID: 8c0a6746fdf7
Revises: f13b4b5a57e0
Create Date: 2023-02-14 00:16:57.709202+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8c0a6746fdf7"
down_revision = "f13b4b5a57e0"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update mode_cards
    set props='{"columns":3,"highlights":[{"source":{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["VEHICLES"]},"mapper":"childrenDiscoveredIn","mapperConfig":{"discoveredIn":["SOV","SOV_PDF"]}},"label":"Vehicles (Submitted)","redirectLinkLabel":"Go to vehicles (submitted)","cardId":"a34cd882-741e-4c13-b7a1-e40a65583aa0","noValuesLabels":[{"label":"-","condition":{"name":"submissionFile","types":["Drivers","Vehicles"],"type":"isInRange","max":0}}]},{"source":{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["VEHICLES"]},"mapper":"childrenDiscoveredIn","mapperConfig":{"discoveredIn":["FMCSA_INSPECTION","FMCSA_CRASH"],"exclude":["SOV","SOV_PDF"]}},"conditions":[{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["FMCSA_VIOLATION_COUNT"]},"mapper":"numberOfItems","condition":{"type":"isInRange","min":1}}],"label":"Vehicles from FMCSA not in submission","redirectLinkLabel":"Go to vehicles (FMCSA)","cardId":"9c84fa32-21c5-438b-b8ad-dafe7a7e550f"},{"source":{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["DRIVERS"]},"mapper":"childrenDiscoveredIn","mapperConfig":{"discoveredIn":["SOV","SOV_PDF"]}},"label":"Drivers (Submitted)","redirectLinkLabel":"Go to drivers table","cardId":"","noValuesLabels":[{"label":"-","condition":{"name":"submissionFile","types":["Drivers","Vehicles"],"type":"isInRange","max":0}}]},{"source":{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["FMCSA_VIOLATION_COUNT"]},"mapper":"jsonPathValue","mapperConfig":{"path":"$.observation.value"}},"conditions":[{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["FMCSA_VIOLATION_COUNT"]},"mapper":"numberOfItems","condition":{"type":"isInRange","min":1}}],"label":"FMCSA violations","redirectLinkLabel":"Go to violations","cardId":"9ebf8cdf-07b6-4cc4-a7b3-edc84c3a448b","icons":[{"name":"warning","color":"warning","condition":{"type":"isInRange","min":10}}]},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"FMCSA_CRASH","expand":["crash_details"]},"mapper":"numberOfItems"},"conditions":[{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["FMCSA_VIOLATION_COUNT"]},"mapper":"numberOfItems","condition":{"type":"isInRange","min":1}}],"label":"Crashes","redirectLinkLabel":"Go to crash table","cardId":"c72fc960-1e6f-490b-b628-7aa8e1421ceb","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}]},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"FMCSA_CRASH","expand":["crash_details"]},"mapper":"jsonPathValue","mapperConfig":{"path":"$.number_of_fatalities","aggregation":"sum"}},"conditions":[{"source":{"sourceType":"FACT","parentType":"SUBMISSION","factSubtypes":["FMCSA_VIOLATION_COUNT"]},"mapper":"numberOfItems","condition":{"type":"isInRange","min":1}}],"label":"Fatalities","redirectLinkLabel":"Go to crash table","cardId":"c72fc960-1e6f-490b-b628-7aa8e1421ceb","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}]}]}'
    where id='ae450c67-53cb-40bf-8c4c-0ed192e74f46';
    """)


def downgrade():
    pass

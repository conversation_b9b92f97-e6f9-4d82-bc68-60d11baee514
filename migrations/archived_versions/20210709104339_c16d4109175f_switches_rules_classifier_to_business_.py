"""Switches rules classifier to business rule engine

Revision ID: c16d4109175f
Revises: e4fbfaede52c
Create Date: 2021-07-09 10:43:39.194871+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c16d4109175f"
down_revision = "e4fbfaede52c"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE customizableclassifiertype ADD VALUE IF NOT EXISTS 'BUSINESS_RULES';""")
    op.drop_index("ix_rules_classifier_rule_customizable_classifier_id", table_name="rules_classifier_rule")
    op.drop_table("rules_classifier_rule")
    op.add_column(
        "customizable_classifiers", sa.Column("definition", postgresql.JSONB(astext_type=sa.Text()), nullable=True)
    )


def downgrade():
    op.drop_column("customizable_classifiers", "definition")
    op.create_table(
        "rules_classifier_rule",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("customizable_classifier_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("fact_subtype_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "points_towards",
            postgresql.ENUM("NO", "YES", name="binaryclassificationclasstype"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("weight", postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
        sa.Column("explanation", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["customizable_classifier_id"],
            ["customizable_classifiers.id"],
            name="rules_classifier_rule_customizable_classifier_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="rules_classifier_rule_pkey"),
    )
    op.create_index(
        "ix_rules_classifier_rule_customizable_classifier_id",
        "rules_classifier_rule",
        ["customizable_classifier_id"],
        unique=False,
    )

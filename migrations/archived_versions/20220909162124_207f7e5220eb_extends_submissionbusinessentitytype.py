"""Extends submissionbusinessentitytype

Revision ID: 207f7e5220eb
Revises: 645c42712b31
Create Date: 2022-09-09 16:21:24.512849+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "207f7e5220eb"
down_revision = "645c42712b31"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE submissionbusinessentitytype ADD VALUE IF NOT EXISTS 'DUPLICATE';""")


def downgrade():
    pass

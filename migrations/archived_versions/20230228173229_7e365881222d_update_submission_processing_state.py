"""Update submission processing_state

Revision ID: 7e365881222d
Revises: d88b9ce56245
Create Date: 2023-02-28 17:32:29.723040+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "7e365881222d"
down_revision = "d88b9ce56245"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'INCOMPLETE';""")


def downgrade():
    pass

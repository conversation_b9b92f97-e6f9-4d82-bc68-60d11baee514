"""Add snapshot_id to SubmissionBusiness

Revision ID: 153698ef995c
Revises: 4c63ef28586b
Create Date: 2020-10-07 18:11:45.880122+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "153698ef995c"
down_revision = "4c63ef28586b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submission_businesses", sa.Column("snapshot_id", postgresql.UUID(as_uuid=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submission_businesses", "snapshot_id")
    # ### end Alembic commands ###

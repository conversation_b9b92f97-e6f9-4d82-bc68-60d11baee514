"""Sets organization ID for user

Revision ID: fa2f455b4c5a
Revises: 1a109d68b51e
Create Date: 2020-10-12 18:49:11.390600+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fa2f455b4c5a"
down_revision = "1a109d68b51e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update users
    set organization_id = (select id from organization where name = 'QBE')
    where email='<EMAIL>';
    """)


def downgrade():
    pass

"""adds updated at to submission_fields

Revision ID: 4c63ef28586b
Revises: d9cc7b198456
Create Date: 2020-10-08 11:24:13.552679+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4c63ef28586b"
down_revision = "d9cc7b198456"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("user_fields_review", sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True))


def downgrade():
    op.drop_column("user_fields_review", "updated_at")

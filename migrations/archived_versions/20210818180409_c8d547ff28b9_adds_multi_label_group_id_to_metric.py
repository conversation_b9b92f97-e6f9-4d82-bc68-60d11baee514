"""Adds multi-label group ID to metric

Revision ID: c8d547ff28b9
Revises: c3dc56ca8a50
Create Date: 2021-08-18 18:04:09.510002+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c8d547ff28b9"
down_revision = "c3dc56ca8a50"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "categorical_data_summary", sa.Column("multi_label_grouping_id", sa.String(length=256), nullable=True)
    )


def downgrade():
    op.drop_column("categorical_data_summary", "multi_label_grouping_id")

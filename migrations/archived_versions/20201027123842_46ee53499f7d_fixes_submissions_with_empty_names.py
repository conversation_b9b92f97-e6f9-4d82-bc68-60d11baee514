"""fixes submissions with empty names

Revision ID: 46ee53499f7d
Revises: 3d3a88ca9063
Create Date: 2020-10-27 12:38:42.420661+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "46ee53499f7d"
down_revision = "3d3a88ca9063"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE submissions SET name='' WHERE submissions.name IS NULL
        """)


def downgrade():
    pass

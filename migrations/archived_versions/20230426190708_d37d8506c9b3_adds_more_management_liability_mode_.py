"""Adds more management liability mode adjustments

Revision ID: d37d8506c9b3
Revises: 90af694a01e4
Create Date: 2023-04-26 19:07:08.507769+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d37d8506c9b3"
down_revision = "90af694a01e4"
branch_labels = None
depends_on = None

submission_information_management_liability_element_id = "bc01969b-9e4f-4cf5-a25d-397447ff121f"
first_named_insured_header_management_liability_element_id = "700d8106-7ecb-4d76-9518-4374a1d52561"


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
        UPDATE mode_columns SET width = 12 where id = 'c8a3604d-3c6d-4f23-b006-67e5a1e08f1c';
        UPDATE mode_elements SET position = 82 where id = '{submission_information_management_liability_element_id}';
        UPDATE mode_elements SET position = 83 where id = '{first_named_insured_header_management_liability_element_id}';
    """)


def downgrade():
    ...

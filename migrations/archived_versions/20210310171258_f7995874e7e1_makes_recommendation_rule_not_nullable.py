"""Makes recommendation_rule not nullable

Revision ID: f7995874e7e1
Revises: 1abf4965a66a
Create Date: 2021-03-10 17:12:58.273594-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f7995874e7e1"
down_revision = "1abf4965a66a"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("recommendation_rule", "is_immutable", existing_type=sa.BOOLEAN(), nullable=False)


def downgrade():
    op.alter_column("recommendation_rule", "is_immutable", existing_type=sa.BOOLEAN(), nullable=True)

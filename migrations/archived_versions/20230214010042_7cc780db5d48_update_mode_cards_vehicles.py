"""update loss runs card title

Revision ID: 7cc780db5d48
Revises: 8c0a6746fdf7
Create Date: 2023-02-14 01:00:42.853024+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7cc780db5d48"
down_revision = "8c0a6746fdf7"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards set title='Summary', type='VEHICLES_IN_SUBMISSION_SUMMARY' where card_id='vehicles-in-submission-summary';
    """)


def downgrade():
    pass

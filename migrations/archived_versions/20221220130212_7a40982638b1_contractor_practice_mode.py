"""contractor practice mode

Revision ID: 7a40982638b1
Revises: e429f87922d3
Create Date: 2022-12-20 13:02:12.716176+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7a40982638b1"
down_revision = "e429f87922d3"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TABLE mode_cards ALTER COLUMN type TYPE VARCHAR;""")

        op.execute("""
INSERT INTO modes(id, name) VALUES
        ('0da04e4f-b332-4fa3-9f89-08caa046fc24', 'Contractor - Practice mode');
INSERT INTO mode_permissions(id, mode_id, organization_id, is_shared_across_organization) VALUES
        ('8873802a-0617-4b99-b13c-e1b9b7a80ba3', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 3, TRUE);


INSERT INTO mode_rows(id, mode_id, position, is_collapsible, elevation) VALUES
        ('2a902880-b76d-4b61-9ae2-5cff6618a531', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 0, false, 1);
INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
        ('76699b7c-056b-489c-8186-87c2e7986d64', '2a902880-b76d-4b61-9ae2-5cff6618a531', 0, 12, null);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
        ('20f7eba1-f945-42b5-b8eb-0c40fd1d4542', '76699b7c-056b-489c-8186-87c2e7986d64', null, 0, 'CONTRACTOR_HEADER', 'contractor-header');

INSERT INTO mode_rows(id, mode_id, position, title, elevation) VALUES
        ('ff801abf-0e85-471c-aab7-a8f2c04a83c2', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 1, 'Submission information', 1);
INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
        ('ff801abf-0e85-471c-aab7-a8f2c04a83c2', 'ff801abf-0e85-471c-aab7-a8f2c04a83c2', 0, 12, null);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
        ('530bdf8f-f54b-4533-8fc3-309e9761a106', 'ff801abf-0e85-471c-aab7-a8f2c04a83c2', null, 0, 'SUBMISSION_INFORMATION', 'submission-information');



INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('fe161684-dd44-4ed0-8659-8eb77c19693f', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 2, 'Violations');
INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('a53b5cbb-edeb-4b37-9b68-8744ae686851', 'fe161684-dd44-4ed0-8659-8eb77c19693f', 0, 12, null);
INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
    ('974e754f-553c-4da8-9356-e83a9d80e78b', 'a53b5cbb-edeb-4b37-9b68-8744ae686851', null, 0, 'OSHA_VIOLATION', 'osha-violations');
        """)


def downgrade():
    pass

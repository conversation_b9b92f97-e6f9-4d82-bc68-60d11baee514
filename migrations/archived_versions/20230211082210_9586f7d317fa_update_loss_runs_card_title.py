"""update loss runs card title

Revision ID: 9586f7d317fa
Revises: 14a8c46a6274
Create Date: 2023-02-11 08:22:10.877156+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9586f7d317fa"
down_revision = "14a8c46a6274"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards set title='Loss run files' where card_id='loss-files-card';
        update mode_cards set title='Summary of losses' where card_id='loss-summaries-card';
        update mode_cards set title='' where card_id='loss-runs-card';
    """)


def downgrade():
    pass

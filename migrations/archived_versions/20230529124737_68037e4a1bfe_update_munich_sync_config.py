"""update munich sync config

Revision ID: 68037e4a1bfe
Revises: 32e8a2b6b1f5
Create Date: 2023-05-29 12:47:37.901209+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "68037e4a1bfe"
down_revision = "32e8a2b6b1f5"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,create_shell_submissions}', to_jsonb('true'::boolean)) where organization_id=36;
        """)

    op.execute("""
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('3'::integer)) where organization_id=36;
        """)


def downgrade():
    op.execute("""
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,create_shell_submissions}', to_jsonb('false'::boolean)) where organization_id=36;
        """)

    op.execute("""
            update sync_configuration set configuration = jsonb_set(configuration, '{submission_matcher_config,fuzzy_matching_level}', to_jsonb('2'::integer)) where organization_id=36;
        """)

"""rename agency to brokerage

Revision ID: 7a50d86fb305
Revises: 861bd68afc07
Create Date: 2022-09-19 05:35:57.056139+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7a50d86fb305"
down_revision = "861bd68afc07"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("alter table agencies rename to brokerages;")
    conn.execute("alter table agency_offices rename to brokerage_offices;")
    conn.execute("alter table agents rename to brokers;")


def downgrade():
    conn = op.get_bind()
    conn.execute("alter table brokerages rename to agencies;")
    conn.execute("alter table brokerage_offices rename to agency_offices;")
    conn.execute("alter table brokers rename to agents;")

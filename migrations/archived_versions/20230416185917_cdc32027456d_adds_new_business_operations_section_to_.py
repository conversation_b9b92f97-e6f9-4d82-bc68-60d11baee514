"""Adds new business operations section to Management Liability

Revision ID: cdc32027456d
Revises: 09a7055aef92
Create Date: 2023-04-16 18:59:17.986108+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cdc32027456d"
down_revision = "09a7055aef92"
branch_labels = None
depends_on = None

management_liability_mode_id = "1b7d5832-9ac9-44a2-b88a-7e6d09f8b052"
mode_element_id = "e4abf4a4-b6a4-4d81-a3b3-3a7c707a2cfb"
new_mode_row_id = "9dc0939b-a01b-4829-ae01-80966b9fce8c"
new_mode_column_id = "e12f4d53-1b09-42c1-a124-b60bb8859631"

operations_card_id = "0ca8a61a-7121-48a0-b216-d09bc33f4b6d"
financials_card_id = "249542af-8f21-46e0-9589-fa6b77b2848c"

operations_card_props = '{"facts": {"group": "MANAGEMENT_LIABILITY_OPERATIONS_CARD", "parentType": "BUSINESS"}}'
financials_card_props = '{"facts": {"group": "FINANCIALS_CARD", "parentType": "BUSINESS"}}'


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
        INSERT INTO mode_rows (id, mode_id, position, title) VALUES
        ('{new_mode_row_id}', '{management_liability_mode_id}', 90, 'Business Operations');
        INSERT INTO mode_columns (row_id, id, width, position) VALUES
        ('{new_mode_row_id}', '{new_mode_column_id}', 12, 0); 
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('{new_mode_column_id}', '{operations_card_id}', 0, 'business-operations-operations', 'FACTS', 'Operations', '{operations_card_props}'),
        ('{new_mode_column_id}', '{financials_card_id}', 10, 'business-operations-financials', 'FACTS', 'Financials', '{financials_card_props}'); 
        UPDATE mode_elements SET row_id = '{new_mode_row_id}' WHERE id = '{mode_element_id}';
    """)


def downgrade():
    ...

"""Removes organization.signout_return_to_url

Revision ID: 3c8e1817507f
Revises: 0f08b4462c85
Create Date: 2021-05-28 11:55:10.534871-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3c8e1817507f"
down_revision = "0f08b4462c85"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    orgs = conn.execute(
        """select id, signout_return_to_url from organization where signout_return_to_url is not null"""
    ).fetchall()
    for row in orgs:
        org_id = row[0]
        signout_return_to_url = row[1]
        conn.execute(
            f"""UPDATE users set signout_redirection_url='{signout_return_to_url}' where organization_id = {org_id}"""
        )

    op.drop_column("organization", "signout_return_to_url")


def downgrade():
    pass

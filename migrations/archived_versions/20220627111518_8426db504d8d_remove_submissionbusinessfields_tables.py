"""Remove SubmissionBusinessFields tables

Revision ID: 8426db504d8d
Revises: 85788e8d595e
Create Date: 2022-06-27 11:15:18.102428+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8426db504d8d"
down_revision = "85788e8d595e"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '600 s';")  # 10 min

        # SubmissionBusinessFieldValues
        op.drop_index("ix_submission_business_field_values_structure_id", table_name="submission_business_field_values")
        op.drop_index(
            "ix_submission_business_field_values_submission_business_dc75",
            table_name="submission_business_field_values",
        )
        op.drop_index(
            "ix_submission_business_field_values_submission_business_id", table_name="submission_business_field_values"
        )
        op.drop_table("submission_business_field_values")

        # SubmissionBusinessFields
        op.drop_index("ix_submission_business_fields_fact_subtype_id", table_name="submission_business_fields")
        op.drop_index("ix_submission_business_fields_file_id", table_name="submission_business_fields")
        op.drop_index("ix_submission_business_fields_submission_id", table_name="submission_business_fields")
        op.drop_index("submission_business_field_name_submission_idx", table_name="submission_business_fields")
        op.drop_table("submission_business_fields")

    # Aggregation Type
    op.execute("DROP TYPE aggregationtype")


def downgrade():
    op.execute("CREATE TYPE aggregationtype AS ENUM ('SUM', 'MEAN');")

    # SubmissionBusinessFields
    op.create_table(
        "submission_business_fields",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "value_type",
            postgresql.ENUM(
                "TEXT", "NUMBER", "TEXT_ARRAY", "NUMBER_ARRAY", "DATETIME", "BOOLEAN", "INTEGER", name="fieldtype"
            ),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("unit_name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "line_of_business",
            postgresql.ENUM(
                "PROPERTY",
                "GL",
                "WC",
                "UMBRELLA",
                "FLEET",
                "BOP",
                "MULTIPLE",
                "LOB",
                "OTHER",
                name="lineofbusinesstype",
            ),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("submission_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("file_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column(
            "aggregation_type",
            postgresql.ENUM("SUM", "MEAN", name="aggregationtype"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("fact_subtype_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("display_as_fact", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(("file_id",), ["files.id"], name="submission_business_fields_file_id_fkey"),
        sa.ForeignKeyConstraint(
            ("submission_id",),
            ["submissions.id"],
            name="submission_business_fields_submission_id_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name="submission_business_fields_pkey"),
    )
    op.create_index(
        "submission_business_field_name_submission_idx",
        "submission_business_fields",
        ["name", "submission_id"],
        unique=True,
    )
    op.create_index(
        "ix_submission_business_fields_submission_id", "submission_business_fields", ["submission_id"], unique=False
    )
    op.create_index("ix_submission_business_fields_file_id", "submission_business_fields", ["file_id"], unique=False)
    op.create_index(
        "ix_submission_business_fields_fact_subtype_id", "submission_business_fields", ["fact_subtype_id"], unique=False
    )

    # SubmissionBusinessFieldValues
    op.create_table(
        "submission_business_field_values",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("submission_business_field_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("submission_business_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("string_value", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("float_value", postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
        sa.Column("string_array_value", postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
        sa.Column(
            "float_array_value",
            postgresql.ARRAY(postgresql.DOUBLE_PRECISION(precision=53)),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("orig_value", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("structure_identifier", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("fact_parent_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column(
            "fact_parent_type",
            postgresql.ENUM("PREMISES", "BUSINESS", "STRUCTURE", name="factparenttype"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("fact_subtype_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("remote_structure_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("observation_value", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("observation_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ("submission_business_field_id",),
            ["submission_business_fields.id"],
            name="submission_business_field_val_submission_business_field_id_fkey",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ("submission_business_id",),
            ["submission_businesses.id"],
            name="submission_business_field_values_submission_business_id_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name="submission_business_field_values_pkey"),
        sa.UniqueConstraint(
            "submission_business_id",
            "structure_identifier",
            "submission_business_field_id",
            name="submission_business_field_val_submission_business_id_struct_key",
        ),
    )
    op.create_index(
        "ix_submission_business_field_values_submission_business_id",
        "submission_business_field_values",
        ["submission_business_id"],
        unique=False,
    )
    op.create_index(
        "ix_submission_business_field_values_submission_business_dc75",
        "submission_business_field_values",
        ["submission_business_field_id"],
        unique=False,
    )
    op.create_index(
        "ix_submission_business_field_values_structure_id",
        "submission_business_field_values",
        ["remote_structure_id"],
        unique=False,
    )

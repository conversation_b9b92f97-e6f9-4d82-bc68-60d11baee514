"""add parent metric to contractor types metrics

Revision ID: 75d0988bec23
Revises: 37a127ce060b
Create Date: 2021-10-27 15:28:43.856077+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "75d0988bec23"
down_revision = "37a127ce060b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update metric_config set parent_metric='Contractor Services' where display_name='Airport Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Drywall';
    update metric_config set parent_metric='Contractor Services' where display_name='Roofing Service';
    update metric_config set parent_metric='Contractor Services' where display_name='Asbestos Removal';
    update metric_config set parent_metric='Contractor Services' where display_name='Bearing Walls Removal';
    update metric_config set parent_metric='Contractor Services' where display_name='Boiler Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Booms';
    update metric_config set parent_metric='Contractor Services' where display_name='Bridge Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Burglar Alarm';
    update metric_config set parent_metric='Contractor Services' where display_name='Carpenter';
    update metric_config set parent_metric='Contractor Services' where display_name='Commercial Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Cranes';
    update metric_config set parent_metric='Contractor Services' where display_name='Damage Restoration';
    update metric_config set parent_metric='Contractor Services' where display_name='Demolition';
    update metric_config set parent_metric='Contractor Services' where display_name='Digging';
    update metric_config set parent_metric='Contractor Services' where display_name='Directional Boring';
    update metric_config set parent_metric='Contractor Services' where display_name='Drilling';
    update metric_config set parent_metric='Contractor Services' where display_name='Driving Exposure';
    update metric_config set parent_metric='Contractor Services' where display_name='Drone';
    update metric_config set parent_metric='Contractor Services' where display_name='Dust Collection System';
    update metric_config set parent_metric='Contractor Services' where display_name='Electric';
    update metric_config set parent_metric='Contractor Services' where display_name='Elevators Escalators';
    update metric_config set parent_metric='Contractor Services' where display_name='Excavation';
    update metric_config set parent_metric='Contractor Services' where display_name='Exterior Insulation Finishing Systems';
    update metric_config set parent_metric='Contractor Services' where display_name='Exterior Plumbing';
    update metric_config set parent_metric='Contractor Services' where display_name='External Framing';
    update metric_config set parent_metric='Contractor Services' where display_name='Fire Alarm';
    update metric_config set parent_metric='Contractor Services' where display_name='Fireproofing';
    update metric_config set parent_metric='Contractor Services' where display_name='Forklifts';
    update metric_config set parent_metric='Contractor Services' where display_name='Gas Mains';
    update metric_config set parent_metric='Contractor Services' where display_name='Gas Oil Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Hazardous Materials';
    update metric_config set parent_metric='Contractor Services' where display_name='Hoists';
    update metric_config set parent_metric='Contractor Services' where display_name='Industrial Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Ladders';
    update metric_config set parent_metric='Contractor Services' where display_name='Lead Abatement';
    update metric_config set parent_metric='Contractor Services' where display_name='Marine Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Mold Abatement';
    update metric_config set parent_metric='Contractor Services' where display_name='Painting';
    update metric_config set parent_metric='Contractor Services' where display_name='Piling';
    update metric_config set parent_metric='Contractor Services' where display_name='Radon Abatement';
    update metric_config set parent_metric='Contractor Services' where display_name='Rail';
    update metric_config set parent_metric='Contractor Services' where display_name='Remodeling Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Renovation Restoration';
    update metric_config set parent_metric='Contractor Services' where display_name='Residential Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Road Or Street Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Sanding';
    update metric_config set parent_metric='Contractor Services' where display_name='Sewer';
    update metric_config set parent_metric='Contractor Services' where display_name='Solar';
    update metric_config set parent_metric='Contractor Services' where display_name='Solid State Plastic Organic Insulation';
    update metric_config set parent_metric='Contractor Services' where display_name='Spray Painting';
    update metric_config set parent_metric='Contractor Services' where display_name='Sprayable Foam Insulation';
    update metric_config set parent_metric='Contractor Services' where display_name='Subcontractor';
    update metric_config set parent_metric='Contractor Services' where display_name='Traffic Signal Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Tunnel Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Underground Cable Installation';
    update metric_config set parent_metric='Contractor Services' where display_name='Underground Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Welding Work';
    update metric_config set parent_metric='Contractor Services' where display_name='Work In Altitude';
    update metric_config set parent_metric='Contractor Services' where display_name='Wrecking';
    """)


def downgrade():
    pass

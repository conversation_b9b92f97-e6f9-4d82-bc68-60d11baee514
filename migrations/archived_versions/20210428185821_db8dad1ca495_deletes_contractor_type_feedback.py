"""Deletes contractor type feedback

Revision ID: db8dad1ca495
Revises: 2375a20bef5f
Create Date: 2021-04-28 18:58:21.846633+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "db8dad1ca495"
down_revision = "2375a20bef5f"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("delete from feedback where attribute_name = 'contractor_types_insight'")


def downgrade():
    pass

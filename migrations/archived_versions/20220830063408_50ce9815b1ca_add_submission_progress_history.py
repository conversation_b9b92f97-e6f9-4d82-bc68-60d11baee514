"""add submission progress history

Revision ID: 50ce9815b1ca
Revises: 26f59594ccd7
Create Date: 2022-08-30 06:34:08.785216+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "50ce9815b1ca"
down_revision = "26f59594ccd7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("submission_history", "parent_id", existing_type=postgresql.UUID(), nullable=True)
    op.alter_column(
        "submission_history", "parent_type", existing_type=postgresql.ENUM(name="submissionparenttype"), nullable=True
    )

    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionparenttype ADD VALUE IF NOT EXISTS 'REPORT';")

        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'ON_MY_PLATE';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'QUOTED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'DECLINED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'QUOTED_LOST';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'QUOTED_BOUND';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'SHARED';")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "submission_history", "parent_type", existing_type=postgresql.ENUM(name="submissionparenttype"), nullable=False
    )
    op.alter_column("submission_history", "parent_id", existing_type=postgresql.UUID(), nullable=False)
    # ### end Alembic commands ###

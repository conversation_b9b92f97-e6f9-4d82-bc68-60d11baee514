"""Adding type to email templates

Revision ID: 8b15477d4f88
Revises: 89a48818952a
Create Date: 2023-02-16 13:20:50.600203+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8b15477d4f88"
down_revision = "89a48818952a"
branch_labels = None
depends_on = None


def upgrade():
    email_template_type = postgresql.ENUM("DECLINE_EMAIL", "OTHER", name="emailtemplatetype")
    email_template_type.create(op.get_bind())
    op.add_column(
        "email_templates", sa.Column("type", sa.Enum("DECLINE_EMAIL", "OTHER", name="emailtemplatetype"), nullable=True)
    )


def downgrade():
    op.drop_column("email_templates", "type")

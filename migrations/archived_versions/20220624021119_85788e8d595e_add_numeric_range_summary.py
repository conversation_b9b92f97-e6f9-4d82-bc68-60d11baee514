"""add numeric range summary

Revision ID: 85788e8d595e
Revises: 91661ae5e17b
Create Date: 2022-06-24 02:11:19.347307+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "85788e8d595e"
down_revision = "91661ae5e17b"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE metrictype ADD VALUE IF NOT EXISTS 'NUMERIC_RANGE_SUMMARY';""")
    op.create_table(
        "numeric_range_summary",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("minimums", postgresql.ARRAY(sa.Float()), nullable=True),
        sa.Column("means", postgresql.ARRAY(sa.Float()), nullable=True),
        sa.Column("maximums", postgresql.ARRAY(sa.Float()), nullable=True),
        sa.Column("business_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
        sa.Column("structure_ids", postgresql.ARRAY(sa.String()), nullable=True),
        sa.ForeignKeyConstraint(["id"], ["metric.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade():
    op.drop_table("numeric_range_summary")

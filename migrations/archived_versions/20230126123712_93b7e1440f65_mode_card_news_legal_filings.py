"""Mode Card - News and Legal Filings

Revision ID: 93b7e1440f65
Revises: 3912c712ee47
Create Date: 2023-01-26 12:37:12.219332+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "93b7e1440f65"
down_revision = "3912c712ee47"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        insert into mode_rows (id, created_at, mode_id, position, title) values ('f9bcb751-8256-458f-b247-794aba5567b7', current_timestamp, 'a265715d-4411-4411-9fb0-e745695d8aa8', 64, 'News');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('1f94f000-9c62-4cc6-8995-a7e3bb45e313', current_timestamp, '0da04e4f-b332-4fa3-9f89-08caa046fc24', 64, 'News');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('72c86bab-57a5-4ac0-9380-12dedc2f2140', current_timestamp, '2f622131-068c-4ca4-9066-99d7bb8436b8', 64, 'News');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('567b9676-0d9b-4a85-95cc-69584735bb99', current_timestamp, 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 64, 'News');

        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('9b1f2af9-46cd-46a9-9d8d-d791916d3614', current_timestamp, 'f9bcb751-8256-458f-b247-794aba5567b7', 0, 12, 'News');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('ccec49f3-f846-4c2a-b58e-f700d113269a', current_timestamp, '1f94f000-9c62-4cc6-8995-a7e3bb45e313', 0, 12, 'News');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('9331d2e8-40f6-4591-bd16-86361738fffc', current_timestamp, '72c86bab-57a5-4ac0-9380-12dedc2f2140', 0, 12, 'News');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('0a7866ab-310a-4042-bb53-eeaf0c6c3b5c', current_timestamp, '567b9676-0d9b-4a85-95cc-69584735bb99', 0, 12, 'News');

        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('d3726293-88f0-47fd-ad56-ccf5c9838469', current_timestamp, '9b1f2af9-46cd-46a9-9d8d-d791916d3614', 'News', 0, 'NEWS_CARD', 'news-card');
        update mode_cards set column_id = 'ccec49f3-f846-4c2a-b58e-f700d113269a', title='News', position=0, type='NEWS_CARD', card_id='news-card' where id='9d66ae57-85f0-4aab-9bf5-0d2c85b2fca2';
        update mode_cards set column_id = '9331d2e8-40f6-4591-bd16-86361738fffc', title='News', position=0, type='NEWS_CARD', card_id='news-card' where id='c80e0951-03f2-486b-a82b-48df84946243';
        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('f0b7d605-16cc-4048-a715-ad2bbd240939', current_timestamp, '0a7866ab-310a-4042-bb53-eeaf0c6c3b5c', 'News', 0, 'NEWS_CARD', 'news-card');

        insert into mode_rows (id, created_at, mode_id, position, title) values ('fa74ce22-78f5-41f5-9f8d-2845a8108294', current_timestamp, 'a265715d-4411-4411-9fb0-e745695d8aa8', 65, 'Legal Filings');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('01eaa455-dd22-43cb-b07d-9e9d881493e0', current_timestamp, '0da04e4f-b332-4fa3-9f89-08caa046fc24', 65, 'Legal Filings');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('e19faeb6-38e8-47fd-bf5e-195b9a3be7d3', current_timestamp, '2f622131-068c-4ca4-9066-99d7bb8436b8', 65, 'Legal Filings');
        insert into mode_rows (id, created_at, mode_id, position, title) values ('c9ff2b7c-048c-483c-baab-b4026e46ef97', current_timestamp, 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 65, 'Legal Filings');

        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('6da12d2a-6f91-42da-9d7e-984abb228f27', current_timestamp, 'fa74ce22-78f5-41f5-9f8d-2845a8108294', 0, 12, 'Legal Filings');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('57139bb0-6ae0-4bf5-b429-84d439614e98', current_timestamp, '01eaa455-dd22-43cb-b07d-9e9d881493e0', 0, 12, 'Legal Filings');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('f890a0d6-0b35-40ea-ae67-235e517b8806', current_timestamp, 'e19faeb6-38e8-47fd-bf5e-195b9a3be7d3', 0, 12, 'Legal Filings');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('7eac9d46-0f8e-458d-9044-99765c72e550', current_timestamp, 'c9ff2b7c-048c-483c-baab-b4026e46ef97', 0, 12, 'Legal Filings');

        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('3c2229b4-b74b-4a5a-829c-67d04f8ab478', current_timestamp, '6da12d2a-6f91-42da-9d7e-984abb228f27', 'Legal Filings', 0, 'LEGAL_FILINGS_CARD', 'legal-filings-card');
        update mode_cards set column_id='57139bb0-6ae0-4bf5-b429-84d439614e98', title='Legal Filings', position=0, type='LEGAL_FILINGS_CARD', card_id='legal-filings-card' where id='f9f94a42-f555-47b8-946c-9be1b6b743da';
        update mode_cards set column_id='f890a0d6-0b35-40ea-ae67-235e517b8806', title='Legal Filings', position=0, type='LEGAL_FILINGS_CARD', card_id='legal-filings-card' where id='0b596b51-6f73-41ef-b55a-961c05f30d0d';
        update mode_cards set column_id='7eac9d46-0f8e-458d-9044-99765c72e550', title='Legal Filings', position=0, type='LEGAL_FILINGS_CARD', card_id='legal-filings-card' where id='3acf684a-2728-477b-8ce5-ba02193d985a';
    """)


def downgrade():
    pass

"""Addresses holistic management liability mode feedback

Revision ID: ba6d2e5473b3
Revises: 05feccc4ecd3
Create Date: 2023-04-19 13:07:53.083859+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ba6d2e5473b3"
down_revision = "05feccc4ecd3"
branch_labels = None
depends_on = None

management_liability_mode_id = "1b7d5832-9ac9-44a2-b88a-7e6d09f8b052"
edit_premises_mode_element_id = "ea18b7d5-5cdd-4bef-bc26-a2e812dceed5"
mode_elements_to_remove = (
    "'e512b9e9-a7a1-47a7-88e8-8a68d7baf759', '78b8adea-e959-4704-b284-573f65fb5ed8',"
    " 'a8e24f3d-9b10-48bb-9b20-b3e5d5b5af5d'"
)
loss_runs_row_element_id = "89753d47-8a89-4c0e-9d9e-cc525f8cc91b"
legal_filings_row_element_id = "514e6de7-8638-4421-85f1-9c9a7a13b123"
named_insured_row_element_id = "0253c7a9-cd75-49ec-9ee8-71034ec84118"
violations_mode_element_id = "67f14d11-eeb7-44f3-8e18-49b7c12d04f9"
first_named_insured_header_element_id = "902d3ea8-5c73-4011-8d5e-9bc5e8c323cf"

submission_information_row_id = "7a39afb7-fb70-4460-af64-11d29cde844a"
recommendation_row_id = "21b8aa66-edf0-4b05-b4ce-f3963bca2c0f"
new_violations_row_id = "0fbb81e3-2ddb-4b4a-8558-31e6098785a8"
edit_premises_row_id = "95646fc6-e6b7-4d0c-85de-f09a6806819c"
first_named_insured_header_row_id = "8a6acbdd-b530-4101-8eee-d3016ce2a0a6"

new_violations_column_id = "e3648f2b-45ee-4d51-95f2-2ec5e76e2587"
first_named_insured_header_column_id = "7d9b4749-a9ce-4ac0-9261-ed45f7da1375"

first_named_insured_header_card_id = "7c2f45bd-f445-484c-b604-1014f3d62711"
new_osha_violations_card_id = "647eaceb-0b4e-4cd6-9687-74f7c46ef011"
operations_card_id = "0ca8a61a-7121-48a0-b216-d09bc33f4b6d"
financials_card_id = "249542af-8f21-46e0-9589-fa6b77b2848c"
operations_card_props = (
    '{"facts": {"group": "MANAGEMENT_LIABILITY_OPERATIONS_CARD", "parentType": "BUSINESS"}, "businessSelect":'
    ' "primaryInsuredOrFirst"}'
)
financials_card_props = (
    '{"facts": {"group": "FINANCIALS_CARD", "parentType": "BUSINESS"}, "businessSelect": "primaryInsuredOrFirst"}'
)
new_osha_violations_card_props = '{"parentType": "PREMISES"}'


def upgrade():
    conn = op.get_bind()

    conn.execute(f"""
        DELETE FROM mode_elements where mode_id = '{management_liability_mode_id}' and id in ({mode_elements_to_remove});
        UPDATE mode_elements SET position = 95 WHERE mode_id = '{management_liability_mode_id}' and id = '{named_insured_row_element_id}';
        UPDATE mode_elements SET position = 550 WHERE mode_id = '{management_liability_mode_id}' and id = '{legal_filings_row_element_id}';
        UPDATE mode_elements SET position = 1110 WHERE mode_id = '{management_liability_mode_id}' and id = '{loss_runs_row_element_id}';
        UPDATE mode_elements SET position = 1120 WHERE mode_id = '{management_liability_mode_id}' and id = '{edit_premises_mode_element_id}';
        
        INSERT INTO mode_rows (id, mode_id, position, title, is_collapsible) VALUES
        ('{new_violations_row_id}', '{management_liability_mode_id}', 115, 'Violations', null),
        ('{first_named_insured_header_row_id}', '{management_liability_mode_id}', 93, null, false);
        
        INSERT INTO mode_elements (id, mode_id, position, row_id) VALUES
        ('7c9e489f-a813-4b2d-beed-8017ae5fcd6d', '{management_liability_mode_id}', 70, '{recommendation_row_id}'),
        ('bc01969b-9e4f-4cf5-a25d-397447ff121f', '{management_liability_mode_id}', 75, '{submission_information_row_id}'),
        ('700d8106-7ecb-4d76-9518-4374a1d52561', '{management_liability_mode_id}', 78, '{first_named_insured_header_row_id}');
        
       INSERT INTO mode_columns (row_id, id, width, position) VALUES
        ('{new_violations_row_id}', '{new_violations_column_id}', 12, 0), 
        ('{first_named_insured_header_row_id}', '{first_named_insured_header_column_id}', 12, 0);       
        INSERT INTO mode_cards (id, column_id, position, type, card_id, props) VALUES
        ('{new_osha_violations_card_id}', '{new_violations_column_id}', 100, 'OSHA_VIOLATION', 'osha-violations', '{new_osha_violations_card_props}'),
        ('{first_named_insured_header_card_id}', '{first_named_insured_header_column_id}', 0, 'FIRST_NAMED_INSURED_HEADER_CARD', 'first-named-insured-header-card', null);
        UPDATE mode_elements SET row_id = '{new_violations_row_id}' WHERE mode_id = '{management_liability_mode_id}' and id = '{violations_mode_element_id}';
 
        UPDATE mode_cards SET props = '{operations_card_props}' where id = '{operations_card_id}';
        UPDATE mode_cards SET props = '{financials_card_props}' where id = '{financials_card_id}';
        """)


def downgrade():
    ...

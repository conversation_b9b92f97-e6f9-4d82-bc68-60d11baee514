"""Configures CapSpecialty signout redirect URL

Revision ID: 049bb86b9273
Revises: 9530b2702c93
Create Date: 2021-04-13 18:12:24.187268+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "049bb86b9273"
down_revision = "9530b2702c93"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update organization
    set signout_return_to_url = 'https://capspecialty.okta.com/home/<USER>/0oaqe9i4txYoHr7rH0x7/alnqe9ne8pCY3OX5G0x7/'
    where name = 'CapSpecialty'
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
    update organization
    set signout_return_to_url = null
    where name = 'CapSpecialty'
    """)

"""Extend the filetypes enum Part 2

Revision ID: b5f0247191c0
Revises: b95afccaee4a
Create Date: 2023-04-23 23:45:26.070936+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b5f0247191c0"
down_revision = "b95afccaee4a"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'COMPANY_STRUCTURE';")
        op.execute("ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'EMPLOYEE_HANDBOOK';")
        op.execute("ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'HIRING_GUIDELINES';")
        op.execute("ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'COMPANY_BYLAWS';")
        op.execute("ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'RESUME';")


def downgrade():
    pass

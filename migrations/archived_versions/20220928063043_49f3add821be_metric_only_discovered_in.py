"""metric_only_discovered_in

Revision ID: 49f3add821be
Revises: 16eafd416b6b
Create Date: 2022-09-28 06:30:43.354772+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "49f3add821be"
down_revision = "16eafd416b6b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
            ALTER TABLE metric_preference ADD COLUMN IF NOT EXISTS only_discovered_in TEXT[];
        """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
                ALTER TABLE metric_preference DROP COLUMN IF EXISTS only_discovered_in;
            """)

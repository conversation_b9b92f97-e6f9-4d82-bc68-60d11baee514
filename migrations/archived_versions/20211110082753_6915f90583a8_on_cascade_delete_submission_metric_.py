"""on cascade delete submission - metric config

Revision ID: 6915f90583a8
Revises: f45447f03b6b
Create Date: 2021-11-10 08:27:53.338547+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6915f90583a8"
down_revision = "f45447f03b6b"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("metric_config_submission_business_id_fkey", "metric_config", type_="foreignkey")
    op.create_foreign_key(
        None, "metric_config", "submission_businesses", ["submission_business_id"], ["id"], ondelete="CASCADE"
    )


def downgrade():
    op.drop_constraint(None, "metric_config", type_="foreignkey")
    op.create_foreign_key(
        "metric_config_submission_business_id_fkey",
        "metric_config",
        "submission_businesses",
        ["submission_business_id"],
        ["id"],
    )

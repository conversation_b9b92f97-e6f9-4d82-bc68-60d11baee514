"""Add file comments

Revision ID: a20a75ee2cbe
Revises: bf84650023b4
Create Date: 2021-08-02 12:15:24.918408+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a20a75ee2cbe"
down_revision = "bf84650023b4"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("files", sa.Column("comment", sa.String(), nullable=True))


def downgrade():
    op.drop_column("files", "comment")

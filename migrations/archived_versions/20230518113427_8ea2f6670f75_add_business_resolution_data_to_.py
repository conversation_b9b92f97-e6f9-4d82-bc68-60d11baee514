"""add business_resolution_data to processed_file

Revision ID: 8ea2f6670f75
Revises: 9fcdffdc3760
Create Date: 2023-05-18 11:34:27.040617+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8ea2f6670f75"
down_revision = "9fcdffdc3760"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "processed_files", sa.Column("business_resolution_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True)
    )


def downgrade():
    op.drop_column("processed_files", "business_resolution_data")

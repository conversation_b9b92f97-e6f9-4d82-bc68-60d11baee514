"""Improves business fields model

Revision ID: 8cdb69db8f2d
Revises: 38a75eb2c97f
Create Date: 2020-12-17 07:42:26.871872-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8cdb69db8f2d"
down_revision = "d65e4fb9aec0"
branch_labels = None
depends_on = None


def upgrade():
    lineofbusinesstype = sa.Enum(
        "PROPERTY", "GL", "WC", "UMBRELLA", "FLEET", "BOP", "MULTIPLE", "LOB", name="lineofbusinesstype"
    )
    lineofbusinesstype.create(op.get_bind())

    op.create_table(
        "submission_business_field_values",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_business_field_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("string_value", sa.String(), nullable=True),
        sa.Column("float_value", sa.Float(), nullable=True),
        sa.Column("string_array_value", sa.ARRAY(sa.String()), nullable=True),
        sa.Column("float_array_value", sa.ARRAY(sa.Float()), nullable=True),
        sa.ForeignKeyConstraint(
            ["submission_business_field_id"], ["submission_business_fields.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["submission_business_id"], ["submission_businesses.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_business_field_values_submission_business_field_id"),
        "submission_business_field_values",
        ["submission_business_field_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_submission_business_field_values_submission_business_id"),
        "submission_business_field_values",
        ["submission_business_id"],
        unique=False,
    )
    op.add_column(
        "submission_business_fields",
        sa.Column(
            "line_of_business",
            sa.Enum("PROPERTY", "GL", "WC", "UMBRELLA", "FLEET", "BOP", "MULTIPLE", "LOB", name="lineofbusinesstype"),
            nullable=True,
        ),
    )
    op.add_column(
        "submission_business_fields", sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=True)
    )
    op.add_column("submission_business_fields", sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(
        op.f("ix_submission_business_fields_submission_id"),
        "submission_business_fields",
        ["submission_id"],
        unique=False,
    )
    op.create_foreign_key(
        None, "submission_business_fields", "submissions", ["submission_id"], ["id"], ondelete="CASCADE"
    )
    op.create_index(
        "submission_business_field_name_submission_idx",
        "submission_business_fields",
        ["name", "submission_id"],
        unique=False,
    )
    op.create_index(op.f("ix_submissions_file_id"), "submissions", ["file_id"], unique=False)
    conn = op.get_bind()
    conn.execute("""update submission_business_fields set submission_id=submission_businesses.submission_id 
    from submission_businesses where submission_businesses.id = submission_business_fields.submission_business_id;""")


def downgrade():
    op.drop_index(op.f("ix_submissions_file_id"), table_name="submissions")
    op.drop_index("submission_business_field_name_submission_idx", table_name="submission_business_fields")
    op.drop_index(op.f("ix_submission_business_fields_submission_id"), table_name="submission_business_fields")
    op.drop_column("submission_business_fields", "submission_id")
    op.drop_column("submission_business_fields", "line_of_business")
    op.drop_index(
        op.f("ix_submission_business_field_values_submission_business_id"),
        table_name="submission_business_field_values",
    )
    op.drop_index(
        op.f("ix_submission_business_field_values_submission_business_field_id"),
        table_name="submission_business_field_values",
    )
    op.drop_table("submission_business_field_values")
    op.drop_column("submission_business_fields", "file_id")

"""Add users for KIS

Revision ID: 6598ff7d0eaa
Revises: 1ff7055ab1f9
Create Date: 2022-11-14 14:05:40.279551+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6598ff7d0eaa"
down_revision = "1ff7055ab1f9"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        update settings set email_domains = array_append(email_domains, 'everspangroup.com') where organization_id=7;
    """)
    op.execute("""
        insert into users values
        (default, '<EMAIL>', null, 'auth0|637247eb7ad2d3b80adb7970', 7, 'underwriter', '<EMAIL>', null, null, now(), null, true)
    """)
    op.execute("""
        insert into users values
        (default, '<EMAIL>', null, 'auth0|637247b2c4d3733aa2c69239', 7, 'underwriter', '<EMAIL>', null, null, now(), null, true)
    """)


def downgrade():
    pass

"""Adds ers_snapshot_id to submission_business

Revision ID: 69469c43ae52
Revises: de612715d223
Create Date: 2021-05-13 09:36:11.008408-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "69469c43ae52"
down_revision = "de612715d223"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_businesses", sa.Column("ers_snapshot_id", postgresql.UUID(as_uuid=True), nullable=True))


def downgrade():
    op.drop_column("submission_businesses", "ers_snapshot_id")

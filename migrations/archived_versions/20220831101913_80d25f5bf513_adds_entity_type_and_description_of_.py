"""Adds entity_type and description of operations to SubmissionBusiness

Revision ID: 80d25f5bf513
Revises: 50ce9815b1ca
Create Date: 2022-08-31 10:19:13.423714+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "80d25f5bf513"
down_revision = "50ce9815b1ca"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """CREATE TYPE submissionbusinessentitytype AS ENUM ('GENERAL_CONTRACTOR', 'PROJECT', 'OTHER_INSURED');"""
    )
    op.add_column("submission_businesses", sa.Column("description_of_operations", sa.String(), nullable=True))
    op.add_column(
        "submission_businesses",
        sa.Column(
            "entity_type",
            postgresql.ENUM(
                "GENERAL_CONTRACTOR", "PROJECT", "OTHER_INSURED", name="submissionbusinessentitytype", create_type=False
            ),
            nullable=True,
        ),
    )


def downgrade():
    op.drop_column("submission_businesses", "entity_type")
    op.drop_column("submission_businesses", "description_of_operations")

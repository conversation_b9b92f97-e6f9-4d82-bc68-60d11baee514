"""Adds categorical data summary tables

Revision ID: aacf600513b6
Revises: 2db99e1df4c6
Create Date: 2020-09-03 14:22:16.939933

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "aacf600513b6"
down_revision = "2db99e1df4c6"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "categorical_data_summary",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["id"],
            ["metric.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "category_summary",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("categorical_data_summary_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("display_name", sa.String(), nullable=False),
        sa.Column("frequency", sa.Integer(), nullable=False),
        sa.Column("is_glanceable", sa.Boolean(), nullable=False),
        sa.Column("is_modal_category", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["categorical_data_summary_id"],
            ["categorical_data_summary.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_category_summary_categorical_data_summary_id"),
        "category_summary",
        ["categorical_data_summary_id"],
        unique=False,
    )
    op.create_table(
        "category_summary_business",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("category_summary_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("business_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["category_summary_id"],
            ["category_summary.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_category_summary_business_business_id"), "category_summary_business", ["business_id"], unique=False
    )
    op.create_index(
        op.f("ix_category_summary_business_category_summary_id"),
        "category_summary_business",
        ["category_summary_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_category_summary_business_category_summary_id"), table_name="category_summary_business")
    op.drop_index(op.f("ix_category_summary_business_business_id"), table_name="category_summary_business")
    op.drop_table("category_summary_business")
    op.drop_index(op.f("ix_category_summary_categorical_data_summary_id"), table_name="category_summary")
    op.drop_table("category_summary")
    op.drop_table("categorical_data_summary")

"""adds submission_id to report_feedback table

Revision ID: 3c5175405eac
Revises: 7465489760ee
Create Date: 2020-10-05 08:49:10.167420+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3c5175405eac"
down_revision = "7465489760ee"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("report_feedback", sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, "report_feedback", "submissions", ["submission_id"], ["id"])
    op.alter_column("report_feedback", "report_id", existing_type=sa.INTEGER(), nullable=True)


def downgrade():
    op.drop_column("report_feedback", "submission_id")
    op.alter_column("report_feedback", "report_id", existing_type=sa.INTEGER(), nullable=False)

"""Adds mode_card_preview values

Revision ID: 58a3e3bebc45
Revises: a5a9d0726a79
Create Date: 2023-04-05 17:45:58.840415+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "58a3e3bebc45"
down_revision = "a5a9d0726a79"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        ALTER TABLE mode_card_previews DROP COLUMN IF EXISTS card_id;
        DO
        $do$
        BEGIN
        -- premises
        IF EXISTS (select * from modes where id='37d3b340-2c83-468a-9f42-fdd429f7e95d') THEN
        INSERT INTO mode_card_previews (id, mode_id, position, props) VALUES ('3d65d00d-aa03-4b7d-ad26-84fa0c56e446', '37d3b340-2c83-468a-9f42-fdd429f7e95d', 10, '{"highlights": [{"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "Legal Filings", "cardId": "legal-filings-card", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "LEGAL_FILING"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to Legal Filings"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION", "businessSelect": "generalContractorAndDuplicates"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "News", "cardId": "news-card", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "NEWS"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to News"}]}');
        END IF;
        INSERT INTO mode_card_previews (id, mode_id, position, props) VALUES
        -- fleet single business
        ('bfcb0668-9e4c-4fc4-8aac-71a72418693a', '1e0d051b-f0e0-42ba-b872-430b2c583417', 10, '{"highlights": [{"label": "Vehicles (Submitted)", "cardId": "submission-vehicles-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}}, "noValuesLabels": [{"label": "-", "condition": {"max": 0, "name": "submissionFile", "type": "isInRange", "types": ["Drivers", "Vehicles"]}}], "redirectLinkLabel": "Go to Vehicles (submitted)"}, {"label": "Drivers (Submitted)", "cardId": "submission-drivers-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["DRIVERS"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}}, "noValuesLabels": [{"label": "-", "condition": {"max": 0, "name": "submissionFile", "type": "isInRange", "types": ["Drivers", "Vehicles"]}}], "redirectLinkLabel": "Go to Drivers Table"}, {"icons": [{"name": "warning", "color": "warning", "condition": {"min": 10, "type": "isInRange"}}], "label": "FMCSA Violations", "cardId": "fmcsa-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "noValuesLabel": "None found"}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to FMCSA Violations"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "Crashes", "cardId": "crash-list", "source": {"mapper": "numberOfItems", "source": {"expand": ["crash_details"], "parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_CRASH"}}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["FMCSA_VIOLATION_COUNT"]}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to Crash Table"}]}'),
        -- single premise
        ('cdb5ad64-5c02-4913-af8d-75ac06bdfdae', 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 10, '{"highlights": [{"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "News", "cardId": "news-card", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "NEWS"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to News"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "Legal Filings", "cardId": "legal-filings-card", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "LEGAL_FILING"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to Legal Filings"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"}]}'),
        -- single business
        ('b9d134db-d0c7-48f3-8c1e-30340cd9cbae', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 10, '{"highlights": [{"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "Legal Filings", "cardId": "legal-filings-card", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "LEGAL_FILING"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to Legal Filings"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "News", "cardId": "news-card", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "NEWS"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to News"}]}'),
        -- contractor practice
        ('0393e1dc-59d6-4b5e-a44d-923178af55e6', 'cba0324f-c2ef-4614-a3c3-eeb3ce55268e', 10, '{"highlights": [{"label": "Vehicles (Submitted)", "cardId": "submission-vehicles-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}}, "noValuesLabels": [{"label": "-", "condition": {"max": 0, "name": "submissionFile", "type": "isInRange", "types": ["Drivers", "Vehicles"]}}], "redirectLinkLabel": "Go to Vehicles (submitted)"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION", "businessSelect": "generalContractorAndDuplicates"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"}, {"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}, "label": "High-risk exposures", "cardId": "hazardous-work-performed", "source": {"mapper": "numberOfPositiveFacts", "source": {"parentType": "BUSINESS", "sourceType": "FACT", "factSubtypes": ["PROJECT_USE_OF_EIFS", "PROJECT_SCAFFOLDING", "PROJECT_MOLD_REMOVAL", "PROJECT_ROOF_WORK", "PROJECT_CRANE_WORK", "PROJECT_DEMOLITION_WORK", "PROJECT_BLASTING_WORK", "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL", "PROJECT_EXCAVATION_WORK", "PROJECT_BELOW_GRADE", "PROJECT_DEPTH_OF_WORK", "PROJECT_HEIGHT_IN_FT", "PROJECT_HEIGHT_IN_STORIES", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"], "businessSelect": "generalContractor"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to High-risk exposures"}, {"label": "Vehicles from FMCSA not in submission", "cardId": "fmcsa-vehicles-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"exclude": ["SOV", "SOV_PDF"], "discoveredIn": ["FMCSA_INSPECTION", "FMCSA_CRASH"]}}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["FMCSA_VIOLATION_COUNT"]}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to Vehicles (FMCSA)"}, {"icons": [{"name": "warning", "color": "warning", "condition": {"min": 10, "type": "isInRange"}}], "label": "FMCSA Violations", "cardId": "fmcsa-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "noValuesLabel": "None found"}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to FMCSA Violations"}]}'),
        -- contractor project
        ('0f70ab8f-57bf-451d-983a-0a36ca2562da', '1d3c3366-774f-46fe-9372-8f40659306fe', 10, '{"highlights": [{"label": "Vehicles (Submitted)", "cardId": "submission-vehicles-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}}, "noValuesLabels": [{"label": "-", "condition": {"max": 0, "name": "submissionFile", "type": "isInRange", "types": ["Drivers", "Vehicles"]}}], "redirectLinkLabel": "Go to Vehicles (submitted)"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION", "businessSelect": "generalContractorAndDuplicates"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "High-risk exposures", "cardId": "potential-exposures", "source": {"mapper": "numberOfPositiveFacts", "source": {"parentType": "BUSINESS", "sourceType": "FACT", "factSubtypes": ["PROJECT_USE_OF_EIFS", "PROJECT_SCAFFOLDING", "PROJECT_MOLD_REMOVAL", "PROJECT_ROOF_WORK", "PROJECT_CRANE_WORK", "PROJECT_DEMOLITION_WORK", "PROJECT_BLASTING_WORK", "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL", "PROJECT_EXCAVATION_WORK", "PROJECT_BELOW_GRADE", "PROJECT_DEPTH_OF_WORK", "PROJECT_HEIGHT_IN_FT", "PROJECT_HEIGHT_IN_STORIES", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"], "businessSelect": "contractorProject"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to High-risk exposures"}, {"label": "Vehicles from FMCSA not in submission", "cardId": "fmcsa-vehicles-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"exclude": ["SOV", "SOV_PDF"], "discoveredIn": ["FMCSA_INSPECTION", "FMCSA_CRASH"]}}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["FMCSA_VIOLATION_COUNT"]}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to Vehicles (FMCSA)"}, {"icons": [{"name": "warning", "color": "warning", "condition": {"min": 10, "type": "isInRange"}}], "label": "FMCSA Violations", "cardId": "fmcsa-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "noValuesLabel": "None found"}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to FMCSA Violations"}]}');
        END
        $do$
        """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
        ALTER TABLE mode_card_previews ADD COLUMN IF NOT EXISTS card_id
        DELETE FROM mode_card_previews WHERE id IN ('bfcb0668-9e4c-4fc4-8aac-71a72418693a', '3d65d00d-aa03-4b7d-ad26-84fa0c56e446', 'cdb5ad64-5c02-4913-af8d-75ac06bdfdae', 'b9d134db-d0c7-48f3-8c1e-30340cd9cbae', '0393e1dc-59d6-4b5e-a44d-923178af55e6', '0f70ab8f-57bf-451d-983a-0a36ca2562da')
        """)

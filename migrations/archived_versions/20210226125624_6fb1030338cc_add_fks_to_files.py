"""Add FKs to files

Revision ID: 6fb1030338cc
Revises: ab104b763fa4
Create Date: 2021-02-26 12:56:24.071882+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6fb1030338cc"
down_revision = "ab104b763fa4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("files", sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column("files", sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(op.f("ix_files_submission_business_id"), "files", ["submission_business_id"], unique=False)
    op.create_index(op.f("ix_files_submission_id"), "files", ["submission_id"], unique=False)
    op.create_foreign_key(None, "files", "submissions", ["submission_id"], ["id"], ondelete="CASCADE")
    op.create_foreign_key(
        None, "files", "submission_businesses", ["submission_business_id"], ["id"], ondelete="CASCADE"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "files", type_="foreignkey")
    op.drop_constraint(None, "files", type_="foreignkey")
    op.drop_index(op.f("ix_files_submission_id"), table_name="files")
    op.drop_index(op.f("ix_files_submission_business_id"), table_name="files")
    op.drop_column("files", "submission_id")
    op.drop_column("files", "submission_business_id")
    # ### end Alembic commands ###

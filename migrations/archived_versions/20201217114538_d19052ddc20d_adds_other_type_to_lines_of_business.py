"""adds OTHER type to lines_of_business

Revision ID: d19052ddc20d
Revises: b80be9d811ff
Create Date: 2020-12-17 11:45:38.671140-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d19052ddc20d"
down_revision = "b80be9d811ff"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE lineofbusinesstype ADD VALUE 'OTHER';""")


def downgrade():
    pass

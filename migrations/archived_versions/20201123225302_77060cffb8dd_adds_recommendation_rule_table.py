"""Adds recommendation_rule table

Revision ID: 77060cffb8dd
Revises: 0e0df06ac623
Create Date: 2020-11-23 22:53:02.040586+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "77060cffb8dd"
down_revision = "0e0df06ac623"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "recommendation_rule",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("description", sa.String(), nullable=False),
        sa.Column("definition", sa.String(), nullable=False),
        sa.Column("owner_organization_id", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_recommendation_rule_owner_organization_id"),
        "recommendation_rule",
        ["owner_organization_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_recommendation_rule_owner_organization_id"), table_name="recommendation_rule")
    op.drop_table("recommendation_rule")

"""Updates the submission status of processed submissions to COMPLETED

Revision ID: 49f79affff55
Revises: 2280381274ab
Create Date: 2023-05-13 00:24:03.885252+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "49f79affff55"
down_revision = "2280381274ab"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE submissions
        set processing_state='COMPLETED'
        where is_auto_processed = true
          and processing_state != 'COMPLETED'
          and is_verified = true
          and submissions.created_at > '2023-05-07 23:26:24.716219 +00:00'::timestamp;
    """)


def downgrade():
    pass

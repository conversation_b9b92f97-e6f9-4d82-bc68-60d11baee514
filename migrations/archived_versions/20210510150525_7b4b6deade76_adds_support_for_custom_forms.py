"""Adds support for custom forms

Revision ID: 7b4b6deade76
Revises: 2e1cca351933
Create Date: 2021-05-10 15:05:25.087622-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7b4b6deade76"
down_revision = "0b958702f64f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE fieldtype ADD VALUE 'DATETIME'")
        op.execute("ALTER TYPE fieldtype ADD VALUE 'BOOLEAN'")

    op.create_table(
        "form_definition",
        sa.Column("id", sa.String(length=255), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_form_definition_id"), "form_definition", ["id"], unique=False)
    op.create_index(op.f("ix_form_definition_organization_id"), "form_definition", ["organization_id"], unique=False)
    op.create_table(
        "form_field",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("form_definition_id", sa.String(length=255), nullable=False),
        sa.Column("label", sa.String(length=255), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("parent_type", sa.Enum("SUBMISSION", "BUSINESS", name="parenttype"), nullable=False),
        sa.Column("value_type", postgresql.ENUM(name="fieldtype", create_type=False), nullable=True),
        sa.Column("format", sa.Enum("DECIMAL", "PERCENTAGE", "INTEGER", "DATE", name="formattype"), nullable=True),
        sa.Column("options", sa.ARRAY(sa.String()), nullable=True),
        sa.Column("is_required", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(["form_definition_id"], ["form_definition.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_form_field_form_definition_id"), "form_field", ["form_definition_id"], unique=False)
    op.create_index(op.f("ix_form_field_id"), "form_field", ["id"], unique=False)

    op.create_table(
        "form_field_value",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("form_field_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("value_type", postgresql.ENUM(name="fieldtype", create_type=False), nullable=True),
        sa.Column("text_value", sa.String(), nullable=True),
        sa.Column("datetime_value", sa.DateTime(), nullable=True),
        sa.Column("boolean_value", sa.Boolean(), nullable=True),
        sa.Column("number_value", sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(["form_field_id"], ["form_field.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_business_id"], ["submission_businesses.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "form_field_value_submission_businesses_field_idx",
        "form_field_value",
        ["submission_id", "submission_business_id", "form_field_id"],
        unique=True,
    )
    op.create_index(op.f("ix_form_field_value_form_field_id"), "form_field_value", ["form_field_id"], unique=False)
    op.create_index(
        op.f("ix_form_field_value_submission_business_id"), "form_field_value", ["submission_business_id"], unique=False
    )
    op.create_index(op.f("ix_form_field_value_submission_id"), "form_field_value", ["submission_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_form_field_value_submission_id"), table_name="form_field_value")
    op.drop_index(op.f("ix_form_field_value_submission_business_id"), table_name="form_field_value")
    op.drop_index(op.f("ix_form_field_value_form_field_id"), table_name="form_field_value")
    op.drop_index("form_field_value_submission_businesses_field_idx", table_name="form_field_value")
    op.drop_table("form_field_value")
    op.drop_index(op.f("ix_form_field_id"), table_name="form_field")
    op.drop_index(op.f("ix_form_field_form_definition_id"), table_name="form_field")
    op.drop_table("form_field")
    op.drop_index(op.f("ix_form_definition_organization_id"), table_name="form_definition")
    op.drop_index(op.f("ix_form_definition_id"), table_name="form_definition")
    op.drop_table("form_definition")

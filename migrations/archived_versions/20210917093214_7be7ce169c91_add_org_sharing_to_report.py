"""Add org-sharing to report

Revision ID: 7be7ce169c91
Revises: c1b41b988158
Create Date: 2021-09-17 09:32:14.286589+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7be7ce169c91"
down_revision = "c1b41b988158"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "reports_v2",
        sa.Column(
            "organization_permission_level",
            sa.Enum("ADMIN", "OWNER", "EDITOR", "COMMENTER", "VIEWER", name="permissiontype"),
            nullable=True,
        ),
    )
    op.create_index(
        op.f("ix_reports_v2_organization_permission_level"),
        "reports_v2",
        ["organization_permission_level"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_reports_v2_organization_permission_level"), table_name="reports_v2")
    op.drop_column("reports_v2", "organization_permission_level")

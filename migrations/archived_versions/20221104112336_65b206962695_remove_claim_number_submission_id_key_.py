"""Remove claim_number_submission_id key from loss

Revision ID: 65b206962695
Revises: eef7d90d2713
Create Date: 2022-11-04 11:23:36.282201+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "65b206962695"
down_revision = "eef7d90d2713"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    alter table loss drop constraint loss_claim_number_submission_id;
    """)


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

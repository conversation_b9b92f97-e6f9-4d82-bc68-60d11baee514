"""Migrate Notebook Entries

Revision ID: 49cdb50178f6
Revises: 8ca0c30064c4
Create Date: 2021-09-23 10:07:00.283072+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "49cdb50178f6"
down_revision = "8ca0c30064c4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update notebook_thread set dossier_component_paths='{multilabelFacts[?(@.group.id==''CONTRACTOR_TYPES'')]}' where dossier_component_paths='{contractor_operation.contractor_types_insight}';
    update notebook_thread set dossier_component_paths='{multilabelFacts[?(@.group.id==''COOKING_TYPES'')]}' where dossier_component_paths='{equipment.cooking_types}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_EXPOSURE_TO_FLAMMABLES'')]}' where dossier_component_paths='{equipment.has_exposure_to_flammables}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''ALCOHOL_SERVED'')]}' where dossier_component_paths='{facts[?(@.fact_type?.id==''ALCOHOL_SERVED'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''BUSINESS_CATEGORIES'')]}' where dossier_component_paths='{general_operation.business_categories}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''ON_PREMISES_CRIME'')]}' where dossier_component_paths='{general_operation.has_crimes_in_business}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_INFESTATION'')]}' where dossier_component_paths='{general_operation.has_infestation}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_ROOF_ACCESS'')]}' where dossier_component_paths='{general_operation.has_roof_access}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_SECURITY_GUARDS'')]}' where dossier_component_paths='{general_operation.has_security_guards}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HEALTH_INSPECTION_SCORE'')]}' where dossier_component_paths='{general_operation.health_assessment}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HOURS_OF_OPERATION'')]}' where dossier_component_paths='{general_operation.hours}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''INSURANCE_INFORMATION'')]}' where dossier_component_paths='{general_operation.insurance_info.insurances[0]}';
    update notebook_thread set dossier_component_paths='{multilabelFacts[?(@.group.id==''PARKING_TYPES'')]}' where dossier_component_paths='{general_operation.parking_types}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''WEBSITE'')]}' where dossier_component_paths='{general_operation.website}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''AVERAGE_RATING'')]}' where dossier_component_paths='{hotel_operation.average_rating}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_PROSTITUTION'')]}' where dossier_component_paths='{hotel_operation.has_prostitution}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_BALCONY'')]}' where dossier_component_paths='{hotel_operation.has_rooms_with_balconies}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_SWIMMING_POOL'')]}' where dossier_component_paths='{hotel_operation.has_swimming_pool}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HOTEL_CLASS'')]}' where dossier_component_paths='{hotel_operation.hotel_class}';
    update notebook_thread set dossier_component_paths='{multilabelFacts[?(@.group.id==''HOTEL_SPECIAL_FACILITIES'')]}' where dossier_component_paths='{hotel_operation.hotel_special_facilities_types}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''MIN_PRICE'')]}' where dossier_component_paths='{hotel_operation.min_price}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''MIN_PRICE'')]}' where dossier_component_paths='{hotel_operation.price_min,hotel_operation.price_max}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_ADULT_ENTERTAINMENT'')]}' where dossier_component_paths='{restaurant_operation.has_adult_entertainment}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_BOTTLE_SERVICE'')]}' where dossier_component_paths='{restaurant_operation.has_bottle_service}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_CATERING'')]}' where dossier_component_paths='{restaurant_operation.has_catering}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_DANCING'')]}' where dossier_component_paths='{restaurant_operation.has_dancing}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_DELIVERY'')]}' where dossier_component_paths='{restaurant_operation.has_delivery}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''HAS_OUTDOOR_SEATING'')]}' where dossier_component_paths='{restaurant_operation.has_outdoor_seating}';
    update notebook_thread set dossier_component_paths='{multilabelFacts[?(@.group.id==''HAS_WAITER_SERVICE'')]}' where dossier_component_paths='{restaurant_operation.has_waiter_service}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''PERFORMANCE_TYPES'')]}' where dossier_component_paths='{restaurant_operation.performances_types}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_subtype?.id==''ALCOHOL_SERVED'')]}' where dossier_component_paths='{restaurant_operation.serves_alcohol}';
    """)


def downgrade():
    pass

"""add description of operations field

Revision ID: 20d4acb6a146
Revises: 0ae3600e2d29
Create Date: 2020-11-08 22:39:57.726094+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "20d4acb6a146"
down_revision = "72368f5adb78"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submissions", sa.Column("description_of_operations", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submissions", "description_of_operations")
    # ### end Alembic commands ###

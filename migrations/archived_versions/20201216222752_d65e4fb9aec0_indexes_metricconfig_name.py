"""Indexes MetricConfig name

Revision ID: d65e4fb9aec0
Revises: 0273bffccdb3
Create Date: 2020-12-16 22:27:52.813399+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d65e4fb9aec0"
down_revision = "0273bffccdb3"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(op.f("ix_metric_config_display_name"), "metric_config", ["display_name"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_metric_config_display_name"), table_name="metric_config")

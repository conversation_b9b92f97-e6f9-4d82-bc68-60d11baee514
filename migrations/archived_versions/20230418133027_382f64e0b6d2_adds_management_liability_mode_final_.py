"""Adds management liability mode final changes

Revision ID: 382f64e0b6d2
Revises: ab16f807e421
Create Date: 2023-04-18 13:30:27.518382+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "382f64e0b6d2"
down_revision = "ab16f807e421"
branch_labels = None
depends_on = None

management_liability_edit_premises_mode_element_id = "ea18b7d5-5cdd-4bef-bc26-a2e812dceed5"
management_liability_mode_id = "1b7d5832-9ac9-44a2-b88a-7e6d09f8b052"
edit_premises_row_id = "95646fc6-e6b7-4d0c-85de-f09a6806819c"


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        create extension if not exists "uuid-ossp";
        INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
        SELECT 
        uuid_generate_v4() as id,
        'managementLiability' as name,
        'Management Liability' as display_name,
        id as organization_id,
        '{"PRIMARY", "EXCESS"}' as coverage_types
         FROM organization
    """)

    conn.execute(f"""
        DO
        $do$
        BEGIN
            IF EXISTS (select * from mode_rows where id='{edit_premises_row_id}') THEN
                UPDATE mode_rows SET title = 'Edit Premises' WHERE id = '{edit_premises_row_id}';
                INSERT INTO mode_elements (id, mode_id, position, row_id) VALUES 
                ('{management_liability_edit_premises_mode_element_id}', '{management_liability_mode_id}', 30000, '{edit_premises_row_id}');
            END IF;
        END
        $do$
        """)


def downgrade():
    ...

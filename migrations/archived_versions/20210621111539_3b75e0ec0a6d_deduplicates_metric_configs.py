"""Deduplicates metric configs

Revision ID: 3b75e0ec0a6d
Revises: 467f9acdf330
Create Date: 2021-06-21 11:15:39.619010+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3b75e0ec0a6d"
down_revision = "467f9acdf330"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
        DELETE FROM metric_config R1 USING metric_config R2 WHERE R1.created_at < R2.created_at 
        AND R1.display_name = R2.display_name AND R1.report_id = R2.report_id AND R1.submission_business_id is null;
        """)


def downgrade():
    pass

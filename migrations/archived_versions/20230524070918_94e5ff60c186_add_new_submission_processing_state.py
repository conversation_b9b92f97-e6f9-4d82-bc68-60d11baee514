"""add new submission processing state

Revision ID: 94e5ff60c186
Revises: 611d3ebea523
Create Date: 2023-05-23 12:09:18.614605+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "94e5ff60c186"
down_revision = "7610d496cea5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(f"ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'AUTOCONFIRMING';")


def downgrade():
    pass

"""Add names for nationwide users

Revision ID: e066b605d644
Revises: 9118d9fc50a0
Create Date: 2021-06-29 12:50:01.089020+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e066b605d644"
down_revision = "9118d9fc50a0"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""update users set name = '<PERSON>' where email = '<EMAIL>';""")
        op.execute("""update users set name = '<PERSON>' where email = '<EMAIL>';""")
        op.execute("""update users set name = '<PERSON>' where email = '<EMAIL>';""")
        op.execute("""update users set name = '<PERSON><PERSON><PERSON>' where email = '<EMAIL>';""")


def downgrade():
    pass

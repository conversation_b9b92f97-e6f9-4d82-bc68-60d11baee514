"""Adds processorFn to EMPLOYMENT_PRACTICES_CARD

Revision ID: 14ee685eb044
Revises: 5efdd16160a7
Create Date: 2023-05-04 22:24:16.678922+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "14ee685eb044"
down_revision = "5efdd16160a7"
branch_labels = None
depends_on = None

employment_practices_mode_card_id = "748c9336-76bf-48ea-bc61-2cebf0c7f34e"
updated_props = (
    '{"facts": {"group": "EMPLOYMENT_PRACTICES_CARD", "parentType": "BUSINESS"}, "processors":'
    ' ["orderByCustomFactSubtypeGroupOrdering"]}'
)


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
    UPDATE mode_cards SET props = '{updated_props}' WHERE id = '{employment_practices_mode_card_id}'
    """)
    ...


def downgrade():
    ...

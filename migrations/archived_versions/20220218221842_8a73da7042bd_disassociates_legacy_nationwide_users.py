"""Disassociates legacy Nationwide users

Revision ID: 8a73da7042bd
Revises: 5b1ea33bb506
Create Date: 2022-02-18 22:18:42.938141+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "8a73da7042bd"
down_revision = "5b1ea33bb506"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    UPDATE users 
    SET organization_id = null 
    WHERE external_id IS null 
    AND email in (
        '<EMAIL>', 
        '<EMAIL>', 
        '<EMAIL>', 
        '<EMAIL>', 
        '<EMAIL>', 
        '<EMAIL>'
    );
    """)


def downgrade():
    pass

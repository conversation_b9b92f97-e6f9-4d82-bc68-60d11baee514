"""Updates insurance notebook entries

Revision ID: 7968eef0bf39
Revises: 19ceb7e2854f
Create Date: 2022-03-07 09:33:13.729954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7968eef0bf39"
down_revision = "19ceb7e2854f"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
update notebook_thread set dossier_component_paths=array['facts[?(@.fact_type?.id==''INSURANCE_INFORMATION''&@.parent_type==''BUSINESS''&@.parent_id==''fe1961a6-846c-44d6-89d9-1652847c1767''&@.fact_subtype?.id==''INSURANCE_INFORMATION'')].observation.bonds[?(@.agent==''American Contractors Indemnity Company'')]'] where id='f602c6ba-d064-40fd-b500-1bbc94ba7126';
update notebook_thread set dossier_component_paths=array['facts[?(@.fact_type?.id==''INSURANCE_INFORMATION''&@.parent_type==''BUSINESS''&@.parent_id==''05410a01-4d3c-4766-a6c2-ae8a09291213''&@.fact_subtype?.id==''INSURANCE_INFORMATION'')].observation.bonds[?(@.agent==''American Contractors Indemnity Company'')]'] where id='1bbe7fa0-a84d-4e17-bd77-fb82ef34ed47';
update notebook_thread set dossier_component_paths=array['facts[?(@.fact_type?.id==''INSURANCE_INFORMATION''&@.parent_type==''BUSINESS''&@.parent_id==''a3d6a450-bc2d-41a1-9c7a-05c449d8cabb''&@.fact_subtype?.id==''INSURANCE_INFORMATION'')].observation.bonds[?(@.agent==''American Contractors Indemnity Company'')]'] where id='84ee3996-8fab-4d10-9a99-612f2330309c';
update notebook_thread set dossier_component_paths=array['facts[?(@.fact_type?.id==''INSURANCE_INFORMATION''&@.parent_type==''BUSINESS''&@.parent_id==''b73f8c60-06ea-47d4-9119-a4f00c84effe''&@.fact_subtype?.id==''INSURANCE_INFORMATION'')].observation.bonds[?(@.agent==''Travelers Cas & Surety Co'')]'] where id='f6aa9c9c-b710-4abe-bc25-dc29e8193cd9';
update notebook_thread set dossier_component_paths=array['facts[?(@.fact_type?.id==''INSURANCE_INFORMATION''&@.parent_type==''BUSINESS''&@.parent_id==''b286ac62-8dc7-44e1-adbc-9958dcc62645''&@.fact_subtype?.id==''INSURANCE_INFORMATION'')].observation.bonds[?(@.agent=="This License Filed A Contractor''s Bond With")]'] where id='1b3079ae-0180-4065-9d90-22baf8c613b6';
update notebook_thread set dossier_component_paths=array['facts[?(@.fact_type?.id==''INSURANCE_INFORMATION''&@.parent_type==''BUSINESS''&@.parent_id==''a3d6a450-bc2d-41a1-9c7a-05c449d8cabb''&@.fact_subtype?.id==''INSURANCE_INFORMATION'')].observation.bonds[?(@.agent==''Platte River Insurance Company'')]'] where id='eb9630b3-2c30-4a90-83a5-1fdf5ba4baa2';
    """)


def downgrade():
    pass

"""Cascades MetricConfig deletes to base Metric

Revision ID: 0da16e5c94ad
Revises: f593579ee17d
Create Date: 2021-01-27 16:33:17.174257+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "0da16e5c94ad"
down_revision = "f593579ee17d"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("metric_metric_config_id_fkey", "metric", type_="foreignkey")
    op.create_foreign_key(None, "metric", "metric_config", ["metric_config_id"], ["id"], ondelete="CASCADE")


def downgrade():
    op.drop_constraint(None, "metric", type_="foreignkey")
    op.create_foreign_key("metric_metric_config_id_fkey", "metric", "metric_config", ["metric_config_id"], ["id"])

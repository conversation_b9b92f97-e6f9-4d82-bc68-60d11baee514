"""Delete Loss Runs 4

Revision ID: a202505654ab
Revises: dd10843c13e8
Create Date: 2022-12-14 14:16:34.349631+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a202505654ab"
down_revision = "dd10843c13e8"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        """delete from loss where submission_id='57277f08-ff4c-44e8-881c-8c1200fbfadf' or submission_id='4ea528b8-baee-47b5-aba2-29ebb739d0eb' or submission_id='cbbaa580-f53c-4b34-b5b8-1cd01dd05538';"""
    )


def downgrade():
    pass

"""Renames metric configs

Revision ID: a637e42ddffe
Revises: 782218aa9f1d
Create Date: 2022-03-17 22:39:18.440969+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a637e42ddffe"
down_revision = "782218aa9f1d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""UPDATE metric_config set display_name = 'Printing And Related Support Activities' 
    WHERE display_name = 'Printing And Related Support Activities (NAICS: 323)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Machinery Manufacturing' 
    WHERE display_name = 'Machinery Manufacturing (NAICS: 333)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Fabricated Metal Product Manufacturing' 
    WHERE display_name = 'Fabricated Metal Product Manufacturing (NAICS: 332)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Textile Product Mills' 
    WHERE display_name = 'Textile Product Mills (NAICS: 314)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Food Manufacturing' 
    WHERE display_name = 'Food Manufacturing (NAICS: 311)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Paper Manufacturing' 
    WHERE display_name = 'Paper Manufacturing (NAICS: 322)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Computer And Electronic Product Manufacturing' 
    WHERE display_name = 'Computer And Electronic Product Manufacturing (NAICS: 334)'""")
    conn.execute(
        """UPDATE metric_config set display_name = 'Electrical Equipment Appliance And Component Manufacturing' 
    WHERE display_name = 'Electrical Equipment Appliance And Component Manufacturing (NAICS: 336)'"""
    )
    conn.execute("""UPDATE metric_config set display_name = 'Apparel Manufacturing' 
    WHERE display_name = 'Apparel Manufacturing (NAICS: 315)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Beverage And Tobacco Product Manufacturing' 
    WHERE display_name = 'Beverage And Tobacco Product Manufacturing (NAICS: 312)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Primary Metal Manufacturing' 
    WHERE display_name = 'Primary Metal Manufacturing (NAICS: 331)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Transportation Equipment Manufacturing' 
    WHERE display_name = 'Transportation Equipment Manufacturing (NAICS: 337)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Chemical Manufacturing' 
    WHERE display_name = 'Chemical Manufacturing (NAICS: 325)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Furniture And Related Product Manufacturing' 
    WHERE display_name = 'Furniture And Related Product Manufacturing (NAICS: 335)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Petroleum And Coal Products Manufacturing' 
    WHERE display_name = 'Petroleu And Coal Products Manufacturing (NAICS: 324)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Miscellaneous Manufacturing' 
    WHERE display_name = 'Miscellaneous Manufacturing (NAICS: 339)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Leather And Allied Product Manufacturing' 
    WHERE display_name = 'Leather And Allied Product Manufacturing (NAICS: 316)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Nonmetallic Mineral Product Manufacturing' 
    WHERE display_name = 'Nonmetallic Mineral Product Manufacturing (NAICS: 327)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Plastics And Rubber Products Manufacturing' 
    WHERE display_name = 'Plastics And Rubber Products Manufacturing (NAICS: 326)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Wood Product Manufacturing' 
    WHERE display_name = 'Wood Product Manfacturing (NAICS: 321)'""")
    conn.execute("""UPDATE metric_config set display_name = 'Textile Mills' 
    WHERE display_name = 'Textile Mills (NAICS: 313)'""")


def downgrade():
    pass

"""remove access for capspecialty

Revision ID: 37a127ce060b
Revises: 7ab8485a36c9
Create Date: 2021-10-26 15:25:44.254462+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "37a127ce060b"
down_revision = "7ab8485a36c9"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(
            """delete from audit_trails where user_external_id in (select external_id from users where email like '%capspecialty%');"""
        )
        op.execute(
            """update users set email = concat('defunct', id, email), organization_id=null, external_id=null where email like '%capspecialty%';"""
        )


def downgrade():
    pass

"""Delete Old Loss Run Record

Revision ID: 87b9f89fc238
Revises: ea7ed7c3fe38
Create Date: 2022-12-11  23:28:55.145314+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "87b9f89fc238"
down_revision = "ea7ed7c3fe38"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        """delete from loss where submission_id='a1b2d488-7392-4933-95a3-b7981f03249f' or submission_id='a664f924-a752-49e5-a42c-d721ab7e02a5' or submission_id='e618335a-9f9e-4491-a8a3-14194a8ec6b1' or submission_id='3a1ccfde-5f5e-4681-9015-da93ec090936';"""
    )


def downgrade():
    pass

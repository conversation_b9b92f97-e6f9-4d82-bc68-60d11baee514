"""Add enumclaw

Revision ID: 1e6aa3c13911
Revises: 49f3add821be
Create Date: 2022-09-29 13:54:38.517767+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1e6aa3c13911"
down_revision = "49f3add821be"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        insert into organization values
        (11, 'Mutual of Enumclaw', null, null, INTERVAL '90 day', 'mutualofenumclaw.com', null, null, true)
    """)
    op.execute("""
        INSERT INTO settings (
            id, created_at, updated_at, organization_id, is_map_enabled_by_default, support_email, email_domains
        )
        values (
            uuid_generate_v4(), now(), null, 11, false, '<EMAIL>', '{mutualofenumclaw.com, kalepa.co, kalepa.com}'
        )
    """)
    op.execute("""
        insert into users values
        (default, '<EMAIL>', null, 'auth0|6335a7148f0599177f8fcc21', 11, 'manager', '<EMAIL>', null, null, now(), null, false)
    """)


def downgrade():
    pass

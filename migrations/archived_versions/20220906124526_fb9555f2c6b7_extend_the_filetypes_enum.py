"""Extend the filetypes enum

Revision ID: fb9555f2c6b7
Revises: 60163ccb072b
Create Date: 2022-09-06 12:45:26.070936+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fb9555f2c6b7"
down_revision = "60163ccb072b"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'GEOTECH_REPORT';""")


def downgrade():
    pass

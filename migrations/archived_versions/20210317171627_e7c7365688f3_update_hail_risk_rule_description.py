"""Update hail risk rule description

Revision ID: e7c7365688f3
Revises: ed0cf7c20383
Create Date: 2021-03-17 17:16:27.756560+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e7c7365688f3"
down_revision = "ed0cf7c20383"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    UPDATE recommendation_rule
    SET description = 'Hail Guideline'
    WHERE description = 'If number of businesses in high hail risk greater than threshold of total number of businesses, refer'
    AND is_immutable;
  """)


def downgrade():
    op.execute("""
    UPDATE recommendation_rule
    SET description = 'If number of businesses in high hail risk greater than threshold of total number of businesses, refer'
    WHERE description = 'Hail Guideline'
    AND is_immutable;
  """)

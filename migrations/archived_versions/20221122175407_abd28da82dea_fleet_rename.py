"""fleet rename

Revision ID: abd28da82dea
Revises: f4cc0f5efb62
Create Date: 2022-11-22 17:54:07.495114+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "abd28da82dea"
down_revision = "f4cc0f5efb62"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("SET statement_timeout TO '600 s';")  # 10 mins
    conn.execute("""
        UPDATE metric SET name = 'Driver License state' WHERE name = 'Driver License number state';
        UPDATE metric_preference SET display_name = 'Driver License state' WHERE display_name = 'Driver License number state';
        """)


def downgrade():
    pass

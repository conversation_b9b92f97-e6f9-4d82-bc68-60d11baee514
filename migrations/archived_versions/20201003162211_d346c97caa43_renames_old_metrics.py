"""Renames old metrics

Revision ID: d346c97caa43
Revises: 02dd8df71bbf
Create Date: 2020-10-03 16:22:11.500592+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d346c97caa43"
down_revision = "02dd8df71bbf"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update metric
    set name = 'Distance to Police Station'
    where name = 'Police Station';
    
    update metric
    set name = 'Distance to Fire Hydrant'
    where name = 'Fire Hydrant';
    
    update metric
    set name = 'Crime Score: Overall'
    where name = 'Crime';
    
    update metric
    set name = 'Building Age'
    where name = 'Avg. Building Sq. Ft.';
    
    update metric
    set name = 'Building Size'
    where name = 'Avg. Building Sq. Ft.';
    
    update metric
    set name = 'Lot Size'
    where name = 'Avg. Size of Land Lot';
    
    update metric
    set name = 'Crime Score: Assault'
    where name = 'Assault';
    
    update metric
    set name = 'Crime Score: Burglary'
    where name = 'Burglary';
    """)


def downgrade():
    pass

"""Sets qbe renewal_creation_interval to 125 days

Revision ID: 831bc4cc9de6
Revises: 49cdb50178f6
Create Date: 2021-09-29 18:24:52.627559+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "831bc4cc9de6"
down_revision = "49cdb50178f6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""UPDATE organization set renewal_creation_interval = '125 days' WHERE organization.name = 'QBE'""")


def downgrade():
    pass

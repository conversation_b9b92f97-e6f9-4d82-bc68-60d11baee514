"""Adds icon name and metric type to summary preferences

Revision ID: 46cd64f90c2d
Revises: f2513c28572b
Create Date: 2020-09-29 21:14:12.275507+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "46cd64f90c2d"
down_revision = "f2513c28572b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("summary_preference", sa.Column("icon_name", sa.String(), nullable=True))
    op.add_column("summary_preference", sa.Column("metric_type", sa.String(), nullable=True))


def downgrade():
    op.drop_column("summary_preference", "metric_type")
    op.drop_column("summary_preference", "icon_name")

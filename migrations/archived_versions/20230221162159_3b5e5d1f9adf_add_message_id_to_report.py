"""Add message ID to report

Revision ID: 3b5e5d1f9adf
Revises: 6bc6501ff5c1
Create Date: 2023-02-21 16:21:59.569144+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3b5e5d1f9adf"
down_revision = "6bc6501ff5c1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""ALTER TABLE reports_v2 ADD COLUMN IF NOT EXISTS email_message_id VARCHAR(255);""")
    op.execute("""ALTER TABLE reports_v2 ADD COLUMN IF NOT EXISTS email_references VARCHAR;""")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("reports_v2", "email_message_id")
    op.drop_column("reports_v2", "email_references")
    # ### end Alembic commands ###

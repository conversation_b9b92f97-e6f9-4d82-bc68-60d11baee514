"""Introduces form_v2 table

Revision ID: ed0bfe1a8261
Revises: c16d4109175f
Create Date: 2021-07-13 12:30:09.598211+00:00

"""
from datetime import datetime
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed0bfe1a8261"
down_revision = "c16d4109175f"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "form_v2",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("form_definition_id", sa.String(length=255), nullable=False),
        sa.Column(
            "execution_status",
            postgresql.ENUM(
                "STARTED", "SUCCEEDED", "FAILED", "CANCELLED", name="executioneventtype", create_type=False
            ),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["form_definition_id"], ["form_definition.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_form_v2_form_definition_id"), "form_v2", ["form_definition_id"], unique=False)
    op.create_index(op.f("ix_form_v2_submission_id"), "form_v2", ["submission_id"], unique=False)
    op.add_column("form_field_value", sa.Column("form_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(op.f("ix_form_field_value_form_id"), "form_field_value", ["form_id"], unique=False)
    op.create_foreign_key(None, "form_field_value", "form_v2", ["form_id"], ["id"], ondelete="CASCADE")

    conn = op.get_bind()
    res = conn.execute("""select distinct form_field_value.submission_id from form_field_value;""").fetchall()
    submission_ids = [row[0] for row in res]

    for submission_id in submission_ids:
        conn.execute(
            sa.text("""INSERT INTO form_v2(id, created_at, submission_id, form_definition_id, execution_status) 
        VALUES (:idx, :created_at, :submission_id, 'CAT MOD REQUEST', 'STARTED');"""),
            idx=uuid4(),
            created_at=datetime.utcnow(),
            submission_id=submission_id,
        )
        op.execute("""UPDATE form_field_value set form_id = form_v2.id FROM form_v2, form_field WHERE 
        form_v2.submission_id = form_field_value.submission_id and form_v2.form_definition_id = 
        form_field.form_definition_id and form_field_value.form_field_id = form_field.id""")

    conn.execute("""UPDATE form_field_source set source_type = 'ERS' WHERE label in ('Submission Business State',
     'Submission Business Postal Code', 'Submission Business Address Line 1', 'Submission Business City')""")


def downgrade():
    op.drop_column("form_field_value", "form_id")
    op.drop_index(op.f("ix_form_v2_submission_id"), table_name="form_v2")
    op.drop_index(op.f("ix_form_v2_form_definition_id"), table_name="form_v2")
    op.drop_table("form_v2")

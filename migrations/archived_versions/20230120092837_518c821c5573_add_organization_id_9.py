"""Add organization id 9

Revision ID: 518c821c5573
Revises: 5fc4f3a230e9
Create Date: 2023-01-20 09:28:37.077908+00:00

"""
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "518c821c5573"
down_revision = "5fc4f3a230e9"
branch_labels = None
depends_on = None

sensible_calls_init_query = sa.text("""
    INSERT INTO sensible_calls (
        id,
        organization_id, 
        year,
        month,
        day,
        calls_made
    )
    VALUES (
        :id,
        :organization_id, 
        :year, 
        :month, 
        :day, 
        :calls_made
    ) ON CONFLICT DO NOTHING;
    """)
from copilot.models.sensible_calls import DAYS_OF_MONTH


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    for year in range(2022, 2024):
        for month in range(1, 13):
            for day in range(1, DAYS_OF_MONTH[month]):
                query_params = {
                    "id": uuid4(),
                    "organization_id": 9,
                    "year": year,
                    "month": month,
                    "day": day,
                    "calls_made": 0,
                }
                conn.execute(statement=sensible_calls_init_query, **query_params)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

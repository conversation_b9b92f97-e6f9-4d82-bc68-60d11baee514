"""Deduplicates metric_configs

Revision ID: d64d07748919
Revises: 371er18he953
Create Date: 2022-02-15 12:12:31.106741+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d64d07748919"
down_revision = "371er18he953"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    DELETE FROM metric_config mc USING metric_config mc2
    WHERE mc.id < mc2.id AND mc.display_name = mc2.display_name AND mc.report_id is null AND mc2.report_id is null;
    """)


def downgrade():
    pass

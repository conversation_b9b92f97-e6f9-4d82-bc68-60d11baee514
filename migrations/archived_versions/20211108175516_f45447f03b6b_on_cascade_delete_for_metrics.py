"""on cascade delete for metrics

Revision ID: f45447f03b6b
Revises: de36e000e1aa
Create Date: 2021-11-08 17:55:16.765613+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f45447f03b6b"
down_revision = "de36e000e1aa"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("metric_submission_business_id_fkey", "metric", type_="foreignkey")
    op.create_foreign_key(
        None, "metric", "submission_businesses", ["submission_business_id"], ["id"], ondelete="CASCADE"
    )


def downgrade():
    op.drop_constraint(None, "metric", type_="foreignkey")
    op.create_foreign_key(
        "metric_submission_business_id_fkey", "metric", "submission_businesses", ["submission_business_id"], ["id"]
    )

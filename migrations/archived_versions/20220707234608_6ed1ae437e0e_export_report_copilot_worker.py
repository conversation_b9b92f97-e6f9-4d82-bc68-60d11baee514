"""Export report copilot worker

Revision ID: 6ed1ae437e0e
Revises: a4169329fdda
Create Date: 2022-07-07 23:46:08.915216+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6ed1ae437e0e"
down_revision = "a4169329fdda"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE copilotworkerexecutiontype ADD VALUE IF NOT EXISTS 'EXPORT_REPORT';""")


def downgrade():
    pass

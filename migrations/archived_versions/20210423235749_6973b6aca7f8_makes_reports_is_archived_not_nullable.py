"""Makes reports.is_archived not nullable

Revision ID: 6973b6aca7f8
Revises: 43cfbc3ce16e
Create Date: 2021-04-24 09:37:49.732676-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6973b6aca7f8"
down_revision = "43cfbc3ce16e"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("reports_v2", "is_archived", existing_type=sa.BOOLEAN(), nullable=False)


def downgrade():
    op.alter_column("reports_v2", "is_archived", existing_type=sa.BOOLEAN(), nullable=True)

"""Adds agents and agencies

Revision ID: 24ef30a2e3be
Revises: 45775f6ffa0d
Create Date: 2022-06-08 09:06:21.501168+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "24ef30a2e3be"
down_revision = "45775f6ffa0d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "agencies",
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("uq_agency_name", "agencies", [sa.text("lower(name)")], unique=True)
    op.create_table(
        "agency_offices",
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("address", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("agency_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(["agency_id"], ["agencies.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("address", "agency_id", name="uq_agency_office_address_agency_id"),
    )
    op.create_index(op.f("ix_agency_offices_agency_id"), "agency_offices", ["agency_id"], unique=False)
    op.create_table(
        "agents",
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("first_name", sa.String(), nullable=False),
        sa.Column("last_name", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("agency_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(["agency_id"], ["agencies.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "uq_agent_first_name_last_name_agency_id",
        "agents",
        [sa.text("lower(first_name)"), sa.text("lower(last_name)"), "agency_id"],
        unique=True,
    )
    op.create_index(op.f("ix_agents_agency_id"), "agents", ["agency_id"], unique=False)
    op.add_column("submissions", sa.Column("agency_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column("submissions", sa.Column("agent_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, "submissions", "agencies", ["agency_id"], ["id"])
    op.create_foreign_key(None, "submissions", "agents", ["agent_id"], ["id"])
    op.create_index(op.f("ix_submissions_agent_id"), "submissions", ["agent_id"], unique=False)
    op.create_index(op.f("ix_submissions_agency_id"), "submissions", ["agency_id"], unique=False)

    conn = op.get_bind()
    conn.execute("""
                    insert into agencies(id, name)
                    select distinct uuid_generate_v4(), lower(trim(field_value))
                    from user_fields_review
                    where field_key = 'agent/agency' and field_value is not null
                    on conflict do nothing;
                    """)

    conn.execute("""
                    insert into agents(id, first_name, last_name, agency_id)
                    select distinct uuid_generate_v4(),
                                    lower(trim(split_part(agent_field.field_value, ' ', 1))),
                                    lower(trim(replace(agent_field.field_value, split_part(agent_field.field_value, ' ', 1), ''))),
                                    a.id
                    from user_fields_review agent_field
                             left join user_fields_review agency_field
                                       on agent_field.submission_id = agency_field.submission_id and agency_field.field_key = 'agent/agency'
                             left join agencies a on lower(agency_field.field_value) = a.name
                    where agent_field.field_key = 'agent/agentName' and agent_field.field_value is not null
                    on conflict do nothing;
                    """)

    conn.execute("""
                    update submissions s
                    set agency_id = aid
                    from (
                             select agencies.id as aid, submission_id
                             from submissions
                                      inner join user_fields_review ufr on submissions.id = ufr.submission_id
                                      inner join agencies on agencies.name = lower(trim(field_value))
                             where field_key = 'agent/agency') b
                    where b.submission_id = s.id;
                    """)

    conn.execute("""
                    update submissions s
                    set agent_id = aid
                    from (
                             select agents.id as aid, submission_id
                             from submissions
                                      inner join user_fields_review ufr on submissions.id = ufr.submission_id
                                      inner join agents on agents.first_name = lower(trim(split_part(field_value, ' ', 1))) and agents.last_name =
                                                                                                                         lower(trim(replace(field_value, split_part(field_value, ' ', 1), '')))
                             where field_key = 'agent/agentName') b
                    where b.submission_id = s.id;
                    """)


def downgrade():
    op.drop_constraint(None, "submissions", type_="foreignkey")
    op.drop_constraint(None, "submissions", type_="foreignkey")
    op.drop_column("submissions", "agent_id")
    op.drop_column("submissions", "agency_id")
    op.drop_index(op.f("ix_agents_agency_id"), table_name="agents")
    op.drop_table("agents")
    op.drop_index(op.f("ix_agency_offices_agency_id"), table_name="agency_offices")
    op.drop_table("agency_offices")
    op.drop_index(op.f("ix_agencies_name"), table_name="agencies")
    op.drop_table("agencies")

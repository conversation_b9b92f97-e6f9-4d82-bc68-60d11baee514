"""Alter column sensible_cache.response to type json

Revision ID: 2280381274ab
Revises: 3d2686200636
Create Date: 2023-05-12 08:24:03.885252+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "2280381274ab"
down_revision = "3d2686200636"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        SET statement_timeout TO '600 s';
        alter table sensible_document_response_cache alter column response type json;
    """)


def downgrade():
    pass

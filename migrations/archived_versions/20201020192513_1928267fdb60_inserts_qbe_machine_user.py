"""Inserts QBE machine user

Revision ID: 1928267fdb60
Revises: a89736c6a74f
Create Date: 2020-10-20 19:25:13.003742+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1928267fdb60"
down_revision = "a89736c6a74f"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT INTO users (id, email, pass_hash, external_id, organization_id, role, name, photo_s3_file_path) 
    VALUES (DEFAULT, null, null, 'BmAMqYal5VpmY9CIwBNN7FoYeuBNG4sG', (select id from organization where name = 'QBE'), 'manager', 'QBE', null)
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    pass

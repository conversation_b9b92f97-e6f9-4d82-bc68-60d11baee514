"""Add sensible monitoring tables

Revision ID: 0c09949f98f1
Revises: b99ec86949de
Create Date: 2023-06-07 11:02:34.163517+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0c09949f98f1"
down_revision = "b99ec86949de"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "sensible_extraction",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("upload_error", sa.String(), nullable=True),
        sa.Column("extraction_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("extraction_status", sa.String(), nullable=True),
        sa.Column("number_of_pages", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["file_id"], ["files.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_sensible_extraction_submission_id"),
        "sensible_extraction",
        ["submission_id"],
    )
    op.create_index(
        op.f("ix_sensible_extraction_file_id"),
        "sensible_extraction",
        ["file_id"],
    )
    op.create_index(
        op.f("ix_sensible_extraction_extraction_id"),
        "sensible_extraction",
        ["extraction_id"],
    )
    op.create_index(
        op.f("ix_sensible_extraction_created_at"),
        "sensible_extraction",
        ["created_at"],
    )

    op.create_table(
        "sensible_extraction_document",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("extraction_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("document_type", sa.String(), nullable=True),
        sa.Column("configuration", sa.String(), nullable=True),
        sa.Column("start_page", sa.Integer(), nullable=False),
        sa.Column("end_page", sa.Integer(), nullable=False),
        sa.Column("validations", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("errors", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("classification_summary", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("validation_summary", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.ForeignKeyConstraint(["extraction_id"], ["sensible_extraction.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )

    op.create_index(
        op.f("ix_sensible_extraction_document_extraction_id"),
        "sensible_extraction_document",
        ["extraction_id"],
        unique=False,
    )


def downgrade():
    op.drop_table("sensible_extraction_document")
    op.drop_table("sensible_extraction")

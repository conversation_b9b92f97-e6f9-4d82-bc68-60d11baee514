"""Adds units to submission_business_fields table

Revision ID: 3b325d4cef01
Revises: 2b6e0e55fd2a
Create Date: 2020-11-01 15:03:41.058645-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3b325d4cef01"
down_revision = "2b6e0e55fd2a"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_business_fields", sa.Column("unit_name", sa.String(), nullable=True))
    op.alter_column("submission_business_fields", "name", existing_type=sa.VARCHAR(), nullable=False)


def downgrade():
    op.alter_column("submission_business_fields", "name", existing_type=sa.VARCHAR(), nullable=True)
    op.drop_column("submission_business_fields", "unit_name")

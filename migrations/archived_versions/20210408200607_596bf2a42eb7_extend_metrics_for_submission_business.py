"""Extend metrics for submission business

Revision ID: 596bf2a42eb7
Revises: 1c7cddd4d453
Create Date: 2021-04-08 20:06:07.831157+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "596bf2a42eb7"
down_revision = "1c7cddd4d453"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric", sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(op.f("ix_metric_submission_business_id"), "metric", ["submission_business_id"], unique=False)
    op.create_foreign_key(
        "metric_submission_business_id_fkey", "metric", "submission_businesses", ["submission_business_id"], ["id"]
    )
    op.add_column("metric_config", sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(
        op.f("ix_metric_config_submission_business_id"), "metric_config", ["submission_business_id"], unique=False
    )
    op.create_foreign_key(
        "metric_config_submission_business_id_fkey",
        "metric_config",
        "submission_businesses",
        ["submission_business_id"],
        ["id"],
    )


def downgrade():
    op.drop_constraint("metric_config_submission_business_id_fkey", "metric_config", type_="foreignkey")
    op.drop_index(op.f("ix_metric_config_submission_business_id"), table_name="metric_config")
    op.drop_column("metric_config", "submission_business_id")
    op.drop_constraint("metric_submission_business_id_fkey", "metric", type_="foreignkey")
    op.drop_index(op.f("ix_metric_submission_business_id"), table_name="metric")
    op.drop_column("metric", "submission_business_id")

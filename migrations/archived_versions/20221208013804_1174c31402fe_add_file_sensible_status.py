"""Add file sensible status

Revision ID: 1174c31402fe
Revises: 50dc54e5a4d3
Create Date: 2022-12-08 01:38:04.366097+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1174c31402fe"
down_revision = "50dc54e5a4d3"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("files", sa.Column("sensible_status", sa.String(), nullable=True))


def downgrade():
    op.drop_column("files", "sensible_status")

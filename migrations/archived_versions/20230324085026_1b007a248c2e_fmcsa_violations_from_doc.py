"""fmcsa violations from doc

Revision ID: 1b007a248c2e
Revises: 5ff1e2d3fd12
Create Date: 2023-03-24 08:50:26.732807+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1b007a248c2e"
down_revision = "5ff1e2d3fd12"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards
SET props = jsonb_set(props, '{highlights, 3}', '{
  "icons": [
    {
      "name": "warning",
      "color": "warning",
      "condition": { "min": 10, "type": "isInRange" }
    }
  ],
  "label": "FMCSA violations",
  "cardId": "fmcsa-violations",
  "source": {
    "mapper": "numberOfItems",
    "source": {
      "parentType": "BUSINESS",
      "sourceType": "DOCUMENT",
      "documentType": "FMCSA_VIOLATION"
    },
    "noValuesLabel": "None found"
  },
  "conditions": [
    {
      "mapper": "numberOfItems",
      "source": {
        "parentType": "BUSINESS",
        "sourceType": "DOCUMENT",
        "documentType": "FMCSA_VIOLATION"
      },
      "condition": { "min": 1, "type": "isInRange" }
    }
  ],
  "redirectLinkLabel": "Go to violations"
}')
WHERE id IN ('b1237fbe-fc82-4722-87c5-2fda2c4e9766', '65e56147-3559-4ec7-81e0-43fb69271492');
        """)


def downgrade():
    pass

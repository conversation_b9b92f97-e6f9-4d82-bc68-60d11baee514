"""assign unassigned munich subs to dummy user

Revision ID: 97b881a3dee9
Revises: a54f8039a857
Create Date: 2023-06-20 15:08:15.907130+00:00

"""
import os

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "97b881a3dee9"
down_revision = "a54f8039a857"
branch_labels = None
depends_on = None


def upgrade():
    if os.environ["KALEPA_ENV"] == "prod":
        op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
        op.execute("""
            INSERT INTO submissions_users (id, user_id, submission_id)
            SELECT uuid_generate_v4() AS id, 1905 AS user_id, id AS submission_id
            FROM
            (
                SELECT DISTINCT s.id, array_agg(DISTINCT su.user_id) arr
                FROM submissions s
                INNER JOIN submissions_reports sr ON sr.submission_id = s.id
                INNER JOIN reports_v2 r ON sr.report_id = r.id
                LEFT JOIN submissions_users su ON s.id = su.submission_id
                WHERE r.organization_id = 36
                GROUP BY s.id
            ) sq WHERE TRUE = ALL(SELECT unnest(sq.arr) IS NULL);
        """)


def downgrade():
    if os.environ["KALEPA_ENV"] == "prod":
        op.execute("""
               DELETE FROM submissions_users WHERE user_id = 1905;
           """)

"""initial tables

Revision ID: d489c936e029
Revises: 
Create Date: 2020-02-05 15:13:34.492403

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "d489c936e029"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
create table if not exists organization
(
    id serial not null
        constraint organization_pkey
            primary key,
    name varchar(200)
        constraint organization_name_key
            unique,
    description text,
    appetite jsonb
);

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('manager', 'underwriter');
    END IF;
END
$$;

create table if not exists users
(
    id serial not null
        constraint users_pkey
            primary key,
    email varchar(200)
        constraint users_email_key
            unique,
    pass_hash varchar(72),
    external_id varchar(100),
    organization_id integer
        constraint users_organization_id_fkey
            references organization,
    role user_role default 'underwriter'::user_role,
    name varchar(100),
    photo_s3_file_path varchar(1024)
);

create unique index if not exists  user_email_idx
    on users (email);

create unique index if not exists  user_external_id_idx
    on users (external_id);

create table if not exists manager_to_user
(
    manager_id integer not null
        constraint manager_to_user_manager_id_fkey
            references users,
    user_id integer not null
        constraint manager_to_user_user_id_fkey
            references users,
    constraint manager_to_user_pkey
        primary key (manager_id, user_id)
);

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'report_stage') THEN
        CREATE TYPE report_stage AS ENUM ('JUST_IN', 'ON_MY_PLATE', 'WAITING_FOR_OTHERS', 'QUOTED', 'COMPLETED', 'DECLINED');
    END IF;
END
$$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'form_status') THEN
        CREATE TYPE form_status AS ENUM ('queued', 'processing', 'finished', 'failed');
    END IF;
END
$$;

create table if not exists reports
(
    id serial not null
        constraint reports_pkey
            primary key,
    errors text[],
    email varchar(200)
        constraint reports_email_fkey
            references users (email),
    created_at timestamp default CURRENT_TIMESTAMP not null,
    businesses jsonb,
    deleted boolean default false,
    being_processed boolean default false,
    external_id varchar(100)
        constraint reports_external_id_fkey
            references users (external_id),
    proposed_effective_date timestamp,
    kalepa_id text,
    google_place jsonb,
    acord_125 jsonb,
    use_without_dossier boolean default false,
    stage report_stage default 'JUST_IN'::report_stage not null,
    stage_details json,
    stage_updated_at timestamp default CURRENT_TIMESTAMP not null,
    selected_alias_id integer
);

create index if not exists  reports_external_id_idx
    on reports (external_id);

create table if not exists uploaded_forms
(
    id serial not null
        constraint uploaded_forms_pkey
            primary key,
    status form_status default 'queued'::form_status,
    report_id integer
        constraint uploaded_forms_report_id_fkey
            references reports,
    file_name varchar(200) not null,
    s3_file_path varchar(1024) default ''::character varying not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    form_data jsonb,
    errors text[],
    contains_acord_125 boolean default false,
    deleted boolean default false,
    detected_forms jsonb default '{}'::jsonb,
    ocr_engine varchar(50)
);

create table if not exists uploaded_quote
(
    id serial not null
        constraint uploaded_quote_pkey
            primary key,
    report_id integer
        constraint uploaded_quote_report_id_fkey
            references reports,
    file_name varchar(200) not null,
    s3_file_path varchar(1024) default ''::character varying not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    deleted boolean default false
);

create table if not exists missing_business
(
    id serial not null
        constraint missing_business_pkey
            primary key,
    name text,
    address text,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create table if not exists user_fields_review
(
    id serial not null
        constraint user_fields_review_pkey
            primary key,
    report_id integer not null
        constraint user_fields_review_report_id_fkey
            references reports,
    email varchar(200) not null
        constraint user_fields_review_email_fkey
            references users (email),
    field_key varchar(128) not null,
    field_value jsonb,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    external_id varchar(100)
);

create unique index if not exists  user_report_fields_review_idx
    on user_fields_review (report_id, field_key);

create table if not exists reports_stage
(
    id serial not null,
    report_id integer not null
        constraint reports_stage_report_id_fkey
            references reports,
    stage report_stage default 'JUST_IN'::report_stage not null,
    stage_details json,
    stage_updated_at timestamp default CURRENT_TIMESTAMP not null
);

create table if not exists report_evidence
(
    id serial not null
        constraint report_evidence_pkey
            primary key,
    report_id integer not null
        constraint report_evidence_report_id_fkey
            references reports,
    evidence_id varchar(128) not null,
    evidence_value boolean,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create unique index if not exists  report_evidence_unique_idx
    on report_evidence (report_id, evidence_id);

create table if not exists report_snapshot
(
    id serial not null
        constraint report_snapshot_pkey
            primary key,
    report_id integer not null
        constraint report_snapshot_report_id_fkey
            references reports,
    snapshot json,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create table if not exists report_feedback
(
    id serial not null
        constraint report_feedback_pkey
            primary key,
    report_id integer not null
        constraint report_feedback_report_id_fkey
            references reports,
    reason text,
    feedback text,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create table if not exists report_coverage
(
    id serial not null
        constraint report_coverage_pkey
            primary key,
    report_id integer not null
        constraint report_coverage_report_id_fkey
            references reports,
    coverage_id varchar(128) not null,
    estimated_premium numeric(10,2),
    updated_at timestamp default CURRENT_TIMESTAMP not null
);

create unique index if not exists  report_coverage_unique_idx
    on report_coverage (report_id, coverage_id);

create table if not exists report_service
(
    id serial not null
        constraint report_service_pkey
            primary key,
    report_id integer not null
        constraint report_service_report_id_fkey
            references reports,
    service_name varchar(255) not null,
    user_assessment text,
    reason text,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    service_details jsonb,
    constraint report_service_unique
        unique (report_id, service_name)
);

create table if not exists report_dismissed_cards
(
    id serial not null
        constraint report_dismissed_cards_pkey
            primary key,
    report_id integer not null
        constraint report_dismissed_cards_report_id_fkey
            references reports,
    card_id varchar(255) not null,
    reason text,
    feedback text,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create unique index if not exists  report_dismissed_cards_unique_idx
    on report_dismissed_cards (report_id, card_id);

create table if not exists report_aliases
(
    id serial not null
        constraint report_aliases_pkey
            primary key,
    report_id integer not null
        constraint report_aliases_report_id_fkey
            references reports,
    external_id varchar(100)
        constraint report_aliases_external_id_fkey
            references users (external_id),
    kalepa_id text,
    name text,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    verified boolean default false
);

create index if not exists  report_aliases_verified_idx
    on report_aliases (verified);

create index if not exists  report_aliases_kalepa_id_idx
    on report_aliases (kalepa_id);

create table if not exists audit_trails
(
    id serial not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    user_external_id varchar(100)
        constraint audit_trails_user_external_id_fkey
            references users (external_id),
    table_name varchar(100) not null,
    column_name varchar(100) not null,
    row_id integer not null,
    new_value jsonb
);

create table if not exists user_signup_requests
(
    id serial not null
        constraint user_signup_requests_pkey
            primary key,
    email varchar(200)
        constraint user_signup_requests_email_key
            unique,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create table if not exists outlook_visits
(
    id serial not null
        constraint outlook_visits_pkey
            primary key,
    email varchar(200)
        constraint outlook_visits_email_key
            unique,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create table if not exists email_messages
(
    id serial not null
        constraint email_messages_pkey
            primary key,
    external_id varchar(100),
    raw_message jsonb,
    user_external_id varchar(100)
        constraint email_messages_user_external_id_fkey
            references users (external_id)
);

create index if not exists  email_messages_user_external_id_idx
    on email_messages (user_external_id);

create table if not exists report_notebook
(
    id serial not null
        constraint report_notebook_pkey
            primary key,
    report_id integer not null
        constraint report_notebook_report_id_fkey
            references reports,
    notebook jsonb,
    created_at timestamp default CURRENT_TIMESTAMP not null
);

create unique index if not exists  report_notebook_unique_idx
    on report_notebook (report_id);

create table if not exists sic_codes
(
    id serial not null,
    code varchar(20)
        constraint sic_codes_code_key
            unique,
    label varchar(150) not null
        constraint sic_codes_label_key
            unique
);

create index if not exists  sic_codes_code_idx
    on sic_codes (code);

create index if not exists  sic_codes_label_idx
    on sic_codes (label);
""")


def downgrade():
    pass

"""Renames and adds CAT Mod Result fields

Revision ID: f514fa253540
Revises: 32a101083cc1
Create Date: 2021-05-21 09:04:12.075960-04:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f514fa253540"
down_revision = "32a101083cc1"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""UPDATE form_field SET label = 'Total Cat Pricing' WHERE label = 'Total Cat Premium'""")
    conn.execute(f"""UPDATE form_field SET label = 'Retained AAL' WHERE label = 'Retained XS AAL (RMS Only)'""")
    conn.execute(f"""UPDATE form_field SET label = 'Modelled Cat Pricing' WHERE label = 'Large Cat Premium'""")
    conn.execute(f"""UPDATE form_field SET label = 'REM Cat Pricing' WHERE label = 'Attritional Cat Premium'""")
    conn.execute(
        """INSERT INTO form_field(id, created_at, form_definition_id, label, value_type, parent_type, is_required, format, description)
            VALUES (uuid_generate_v4(), now(), 'CAT MOD RESULT', 'Modelled Cat Components', 'NUMBER', 'SUBMISSION', false, null, null)"""
    )
    conn.execute(
        """INSERT INTO form_field(id, created_at, form_definition_id, label, value_type, parent_type, is_required, format, description)
            VALUES (uuid_generate_v4(), now(), 'CAT MOD RESULT', 'REM Cat Components', 'NUMBER', 'SUBMISSION', false, null, null)"""
    )
    conn.execute(
        """INSERT INTO form_field(id, created_at, form_definition_id, label, value_type, parent_type, is_required, format, description)
            VALUES (uuid_generate_v4(), now(), 'CAT MOD RESULT', 'Total Cat Components', 'NUMBER', 'SUBMISSION', false, null, null)"""
    )
    conn.execute(
        f"""DELETE FROM form_field R1 USING form_field R2 WHERE R1.created_at < R2.created_at AND R1.label = R2.label AND R1.form_definition_id='CAT MOD RESULT' AND R1.form_definition_id='CAT MOD RESULT';"""
    )
    conn.execute(
        f"""UPDATE form_field SET parent_type = 'ARBITRARY_PARENT' WHERE parent_type='BUSINESS' AND form_definition_id='CAT MOD RESULT'"""
    )


def downgrade():
    pass

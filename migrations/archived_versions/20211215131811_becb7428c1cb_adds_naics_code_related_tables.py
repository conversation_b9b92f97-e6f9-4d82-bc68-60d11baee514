"""Adds naics code related tables

Revision ID: becb7428c1cb
Revises: a3ff4168e020
Create Date: 2021-12-15 13:18:11.765625+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "becb7428c1cb"
down_revision = "a3ff4168e020"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "naics_code",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("title", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "naics_code_prediction",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("naics_code_id", sa.Integer(), nullable=False),
        sa.Column("confidence", sa.Float(), nullable=False),
        sa.Column("source", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(["naics_code_id"], ["naics_code.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_naics_code_prediction_naics_code_id"), "naics_code_prediction", ["naics_code_id"], unique=False
    )
    op.create_index(
        op.f("ix_naics_code_prediction_submission_id"), "naics_code_prediction", ["submission_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_naics_code_prediction_submission_id"), table_name="naics_code_prediction")
    op.drop_index(op.f("ix_naics_code_prediction_naics_code_id"), table_name="naics_code_prediction")
    op.drop_table("naics_code_prediction")
    op.drop_table("naics_code")

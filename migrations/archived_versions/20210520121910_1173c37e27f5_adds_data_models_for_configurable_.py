"""Adds data models for configurable classifiers

Revision ID: 1173c37e27f5
Revises: 5a9614fd1a18
Create Date: 2021-05-20 12:19:10.341167-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1173c37e27f5"
down_revision = "5a9614fd1a18"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "customizable_classifiers",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("label", sa.String(), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=True),
        sa.Column(
            "type",
            sa.Enum("PHRASE_MATCHING", "EMBEDDINGS_SIMILARITY", name="customizableclassifiertype"),
            nullable=False,
        ),
        sa.Column(
            "classification_business_rule_type",
            sa.Enum(
                "COUNT_THRESHOLD",
                "PERCENTAGE_THRESHOLD",
                "SUM_OF_RANDOM_VARIABLES",
                name="classificationbusinessruletype",
            ),
            nullable=False,
        ),
        sa.Column("count_cutoff", sa.Integer(), nullable=True),
        sa.Column("percentage_cutoff", sa.Float(), nullable=True),
        sa.Column("anchors", sa.ARRAY(sa.String()), nullable=True),
        sa.Column("similarity_cutoff", sa.Float(), nullable=False),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_customizable_classifiers_organization_id"),
        "customizable_classifiers",
        ["organization_id"],
        unique=False,
    )
    op.create_table(
        "customizable_classifier_test_runs",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("customizable_classifier_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("positive_business_ids", sa.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
        sa.Column("negative_business_ids", sa.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
        sa.Column("execution_status", postgresql.ENUM(name="executioneventtype", create_type=False), nullable=True),
        sa.Column("true_positives", sa.Integer(), nullable=True),
        sa.Column("false_positives", sa.Integer(), nullable=True),
        sa.Column("true_negatives", sa.Integer(), nullable=True),
        sa.Column("false_negatives", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["customizable_classifier_id"],
            ["customizable_classifiers.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_customizable_classifier_test_runs_customizable_classifier_id"),
        "customizable_classifier_test_runs",
        ["customizable_classifier_id"],
        unique=False,
    )
    op.create_table(
        "phrase_matching_classifier_phrase",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("customizable_classifier_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("phrase", sa.String(), nullable=False),
        sa.Column("weight", sa.Float(), nullable=False),
        sa.Column("is_disabled", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(
            ["customizable_classifier_id"],
            ["customizable_classifiers.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_phrase_matching_classifier_phrase_customizable_classifier_id"),
        "phrase_matching_classifier_phrase",
        ["customizable_classifier_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f("ix_phrase_matching_classifier_phrase_customizable_classifier_id"),
        table_name="phrase_matching_classifier_phrase",
    )
    op.drop_table("phrase_matching_classifier_phrase")
    op.drop_index(
        op.f("ix_customizable_classifier_test_runs_customizable_classifier_id"),
        table_name="customizable_classifier_test_runs",
    )
    op.drop_table("customizable_classifier_test_runs")
    op.drop_index(op.f("ix_customizable_classifiers_organization_id"), table_name="customizable_classifiers")
    op.drop_table("customizable_classifiers")

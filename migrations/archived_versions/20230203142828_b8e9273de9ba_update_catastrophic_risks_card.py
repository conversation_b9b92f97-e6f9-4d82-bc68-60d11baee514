"""update catastrophic risks card

Revision ID: b8e9273de9ba
Revises: 4aacf6d06a2c
Create Date: 2023-02-03 13:28:28.036050+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b8e9273de9ba"
down_revision = "4aacf6d06a2c"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards
SET type = 'FACTS',
    props = '{
    "businessSelect": "contractorProject",
    "processors": ["orderByImportance"],
    "facts": {
        "parentType": "PREMISES",
        "factSubtypeIds": [
            "CATASTROPHIC_FLOOD_RISK",
            "COASTAL_STORM_SURGE_RISK",
            "ELEVATION",
            "DISTANCE_TO_COAST",
            "FLOOD_RISK",
            "HAIL_RISK",
            "LIGHTNING_RISK",
            "DISTANCE_TO_TOXIC_FACILITY",
            "EARTHQUAKE_RISK",
            "WILDFIRE_RISK",
            "SNOW_LOAD_RISK",
            "FLOOD_ZONE",
            "SINKHOLE_RISK",
            "TORNADO_RISK"
        ]
    }
}'
WHERE id = '6e64374c-f86f-44d2-a4de-f418cf89c11e';
    """)


def downgrade():
    pass

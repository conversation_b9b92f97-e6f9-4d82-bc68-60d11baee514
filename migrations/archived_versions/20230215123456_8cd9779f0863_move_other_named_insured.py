"""move other named insured

Revision ID: 8cd9779f0863
Revises: 7cc780db5d48
Create Date: 2023-02-15 12:34:56.748787+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8cd9779f0863"
down_revision = "7cc780db5d48"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards
SET column_id = 'efd70c1d-be56-43b4-8200-0d9b03088c21',
    position = 83
WHERE id = 'e7ad716f-ace9-402e-87c2-ae3ca2d8275d';

UPDATE mode_cards
SET column_id = 'a0125d26-55ea-46ae-afeb-4ebb055a158c',
    position = 83
WHERE id = 'f6427a97-6f7a-4c78-aa51-e90e0be6f2c7';
    """)


def downgrade():
    pass

"""adds quartiles and values array to Mean Metric

Revision ID: a53f17dbcf42
Revises: 82678baa6832
Create Date: 2020-09-03 13:26:50.222813

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a53f17dbcf42"
down_revision = "82678baa6832"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mean", sa.Column("quartiles", postgresql.ARRAY(sa.Float()), nullable=True))
    op.add_column("mean", sa.Column("values", postgresql.ARRAY(sa.Float()), nullable=True))


def downgrade():
    op.drop_column("mean", "values")
    op.drop_column("mean", "quartiles")

"""Adds should_not_analyze and minimum_required_documents

Revision ID: 9dff37f0e476
Revises: a6c977f5947f
Create Date: 2022-05-11 08:13:01.704107+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9dff37f0e476"
down_revision = "a6c977f5947f"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("customizable_classifiers", sa.Column("minimum_required_documents", sa.Integer(), nullable=True))
    op.add_column("customizable_classifiers", sa.Column("should_not_analyze", sa.ARRAY(sa.String()), nullable=True))


def downgrade():
    op.drop_column("customizable_classifiers", "should_not_analyze")
    op.drop_column("customizable_classifiers", "minimum_required_documents")

"""Additional submission action types

Revision ID: 34ad45942ac5
Revises: ab0d45942ac5
Create Date: 2023-05-11 12:09:25.257958+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "34ad45942ac5"
down_revision = "ab0d45942ac5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_PROCESSING_FAILED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_PROCESSING_CANCELLED';")


def downgrade():
    ...

"""Sets recommendation action to NO_ACTION when no rules were triggered

Revision ID: 4ca5e4e26ac2
Revises: 51abfdc5c8be
Create Date: 2022-03-30 07:57:41.576094+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4ca5e4e26ac2"
down_revision = "51abfdc5c8be"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""UPDATE recommendations set action = 'NO_ACTION' where recommendations.id in 
    (select distinct recommendations.id from recommendations
        JOIN recommendation_explanation re on recommendations.id = re.recommendation_id
    where re.text = 'Copilot did not find any issues.'
     or re.text = 'No businesses have been confirmed in the submission.')""")


def downgrade():
    pass

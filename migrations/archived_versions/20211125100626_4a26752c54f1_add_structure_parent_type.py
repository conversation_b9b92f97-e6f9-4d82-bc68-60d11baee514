"""Add STRUCTURE parent_type

Revision ID: 4a26752c54f1
Revises: 34c2052795a8
Create Date: 2021-11-25 10:06:26.155437+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4a26752c54f1"
down_revision = "34c2052795a8"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE factparenttype ADD VALUE IF NOT EXISTS 'STRUCTURE';""")


def downgrade():
    pass

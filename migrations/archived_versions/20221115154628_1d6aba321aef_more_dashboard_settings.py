"""More dashboard settings

Revision ID: 1d6aba321aef
Revises: 6d6cec556894
Create Date: 2022-11-15 15:46:28.481469+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1d6aba321aef"
down_revision = "6d6cec556894"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        INSERT INTO settings (id, created_at, updated_at, user_id, is_management_dashboard_enabled)
        SELECT uuid_generate_v4(), now(), null, id, true
        FROM users
        WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
                        '<EMAIL>', '<EMAIL>');
    """)


def downgrade():
    pass

"""Cleans up retired metrics

Revision ID: 9beffda2496c
Revises: b4e7bc046f57
Create Date: 2020-12-17 18:32:31.613682+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9beffda2496c"
down_revision = "b4e7bc046f57"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    delete
    from mean
    where id in (select id from metric where name = 'Avg. Age of Building');
    """)
    conn.execute("delete from metric where name = 'Avg. Age of Building';")

    conn.execute("""
    delete
    from grade_summary
    where id in (select id from metric where name = 'Flood ');
        """)
    conn.execute("delete from metric where name = 'Flood ';")

    conn.execute("""
    delete
    from grade_summary
    where id in (select id from metric where name = 'Flooding');
    """)
    conn.execute("delete from metric where name = 'Flooding';")

    conn.execute("""
    delete
    from mean
    where id in (select id from metric where name = 'Building Size');
    """)
    conn.execute("delete from metric where name = 'Building Size';")

    conn.execute("""
    delete
    from mean
    where id in (select id from metric where name = 'Average Lot Size');
    """)
    conn.execute("delete from metric where name = 'Average Lot Size';")

    conn.execute("""
    delete
    from range_summary
    where id in (select id from metric where name = 'Fire Station');
    """)
    conn.execute("delete from metric where name = 'Fire Station';")


def downgrade():
    pass

"""Adjusts mode_preview_cards

Revision ID: e1f32c387bb3
Revises: a1429fa1dc9d
Create Date: 2023-04-06 17:09:42.246255+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e1f32c387bb3"
down_revision = "a1429fa1dc9d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        -- contractor practice
        update mode_card_previews set props = '{"highlights": [{"label": "Vehicles (Submitted)", "cardId": "submission-vehicles-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}}, "noValuesLabels": [{"label": "-", "condition": {"max": 0, "name": "submissionFile", "type": "isInRange", "types": ["Drivers", "Vehicles"]}}], "conditions": [{"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}, "condition": {"min": 1, "type": "isInRange", "types": ["Vehicles"]}}], "redirectLinkLabel": "Go to Vehicles (submitted)"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION", "businessSelect": "generalContractorAndDuplicates"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"}, {"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}, "label": "High-risk exposures", "cardId": "hazardous-work-performed", "source": {"mapper": "numberOfPositiveFacts", "source": {"parentType": "BUSINESS", "sourceType": "FACT", "factSubtypes": ["PROJECT_USE_OF_EIFS", "PROJECT_SCAFFOLDING", "PROJECT_MOLD_REMOVAL", "PROJECT_ROOF_WORK", "PROJECT_CRANE_WORK", "PROJECT_DEMOLITION_WORK", "PROJECT_BLASTING_WORK", "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL", "PROJECT_EXCAVATION_WORK", "PROJECT_BELOW_GRADE", "PROJECT_DEPTH_OF_WORK", "PROJECT_HEIGHT_IN_FT", "PROJECT_HEIGHT_IN_STORIES", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"], "businessSelect": "generalContractor"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to High-risk exposures"}, {"icons": [{"name": "warning", "color": "warning", "condition": {"min": 10, "type": "isInRange"}}], "label": "FMCSA Violations", "cardId": "fmcsa-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "noValuesLabel": "None found"}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to FMCSA Violations"}]}' where id = '0393e1dc-59d6-4b5e-a44d-923178af55e6';
        -- contractor project
        update mode_card_previews set props = '{"highlights": [{"label": "Vehicles (Submitted)", "cardId": "submission-vehicles-table", "source": {"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}}, "noValuesLabels": [{"label": "-", "condition": {"max": 0, "name": "submissionFile", "type": "isInRange", "types": ["Drivers", "Vehicles"]}}], "conditions": [{"mapper": "childrenDiscoveredIn", "source": {"parentType": "SUBMISSION", "sourceType": "FACT", "factSubtypes": ["VEHICLES"]}, "mapperConfig": {"discoveredIn": ["SOV", "SOV_PDF"]}, "condition": {"min": 1, "type": "isInRange", "types": ["Vehicles"]}}], "redirectLinkLabel": "Go to Vehicles (submitted)"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "OSHA_VIOLATION", "businessSelect": "generalContractorAndDuplicates"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"}, {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "High-risk exposures", "cardId": "potential-exposures", "source": {"mapper": "numberOfPositiveFacts", "source": {"parentType": "BUSINESS", "sourceType": "FACT", "factSubtypes": ["PROJECT_USE_OF_EIFS", "PROJECT_SCAFFOLDING", "PROJECT_MOLD_REMOVAL", "PROJECT_ROOF_WORK", "PROJECT_CRANE_WORK", "PROJECT_DEMOLITION_WORK", "PROJECT_BLASTING_WORK", "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL", "PROJECT_EXCAVATION_WORK", "PROJECT_BELOW_GRADE", "PROJECT_DEPTH_OF_WORK", "PROJECT_HEIGHT_IN_FT", "PROJECT_HEIGHT_IN_STORIES", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT", "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"], "businessSelect": "contractorProject"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to High-risk exposures"}, {"icons": [{"name": "warning", "color": "warning", "condition": {"min": 10, "type": "isInRange"}}], "label": "FMCSA Violations", "cardId": "fmcsa-violations", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "noValuesLabel": "None found"}, "conditions": [{"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "FMCSA_VIOLATION"}, "condition": {"min": 1, "type": "isInRange"}}], "redirectLinkLabel": "Go to FMCSA Violations"}]}' where id = '0f70ab8f-57bf-451d-983a-0a36ca2562da'
    """)


def downgrade():
    ...

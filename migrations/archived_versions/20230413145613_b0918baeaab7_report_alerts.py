"""Report alerts

Revision ID: b0918baeaab7
Revises: ca118baeaab7
Create Date: 2023-04-13 10:56:13.814636+00:00

"""
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b0918baeaab7"
down_revision = "ca118baeaab7"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "report_alerts",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False, default=uuid4()),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column("alert_type", sa.Enum("MISSING_NAICS", "MISSING_FILES", name="alert_type"), nullable=False),
        sa.ForeignKeyConstraint(("report_id",), ["reports_v2.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade():
    op.drop_table("report_alerts")

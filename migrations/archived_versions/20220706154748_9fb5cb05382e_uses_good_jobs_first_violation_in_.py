"""Uses GOOD_JOBS_FIRST_VIOLATION in configurable classifiers

Revision ID: 9fb5cb05382e
Revises: a18a2719ee5f
Create Date: 2022-07-06 15:47:48.000360+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9fb5cb05382e"
down_revision = "a18a2719ee5f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
             update customizable_classifiers set should_analyze = should_analyze || '{"GOOD_JOBS_FIRST_VIOLATION"}'
             where fact_subtype_id != 'ON_PREMISES_CRIME' and NOT ('GOOD_JOBS_FIRST_VIOLATION' = ANY(should_analyze))
         """)


def downgrade():
    pass

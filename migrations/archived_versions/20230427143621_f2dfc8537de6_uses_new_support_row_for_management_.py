"""Uses new support row for management liability mode

Revision ID: f2dfc8537de6
Revises: 2615bdb44222
Create Date: 2023-04-27 14:36:21.740803+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f2dfc8537de6"
down_revision = "2615bdb44222"
branch_labels = None
depends_on = None

management_liability_mode_support_element = "06d81905-9376-48c6-8c1c-18cfc0e6e330"
support_row_with_fein_dot_and_backend_processes = "2c3a1201-ed66-43a0-b6d5-f69640198684"


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
        DO
        $do$
        BEGIN
        IF EXISTS (select * from mode_rows where id='{support_row_with_fein_dot_and_backend_processes}') THEN
            UPDATE mode_elements SET row_id = '{support_row_with_fein_dot_and_backend_processes}' WHERE id = '{management_liability_mode_support_element}';
        END IF;
        END
        $do$
        """)


def downgrade():
    ...

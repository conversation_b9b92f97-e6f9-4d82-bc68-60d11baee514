"""Adds is_applicable_to_naics_codes to configurable classifiers

Revision ID: 55ba99dcefd6
Revises: edf4e5af14ee
Create Date: 2022-10-27 16:15:57.369547+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "55ba99dcefd6"
down_revision = "edf4e5af14ee"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "customizable_classifiers", sa.Column("is_applicable_to_naics_codes", sa.ARRAY(sa.String()), nullable=True)
    )


def downgrade():
    op.drop_column("customizable_classifiers", "is_applicable_to_naics_codes")

"""Add copy value to origin type

Revision ID: abd0896c4a80
Revises: e9d0896c4a80
Create Date: 2023-02-08 10:16:38.104533+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "abd0896c4a80"
down_revision = "e9d0896c4a80"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(f"ALTER TYPE origin ADD VALUE IF NOT EXISTS 'COPY'")


def downgrade():
    pass

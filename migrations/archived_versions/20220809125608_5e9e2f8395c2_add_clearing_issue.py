"""Add CLEARING_ISSUE

Revision ID: 5e9e2f8395c2
Revises: 1636448db117
Create Date: 2022-08-09 12:56:08.798284+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5e9e2f8395c2"
down_revision = "1636448db117"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionstage ADD VALUE 'BLOCKED'")


def downgrade():
    pass

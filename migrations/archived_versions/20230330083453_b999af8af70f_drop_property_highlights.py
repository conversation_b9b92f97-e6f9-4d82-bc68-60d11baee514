"""drop property highlights

Revision ID: b999af8af70f
Revises: b230292ec75e
Create Date: 2023-03-30 08:34:53.961717+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b999af8af70f"
down_revision = "b230292ec75e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
DELETE FROM mode_cards where id = '3616a066-b4ef-4a5c-b3fb-85001110ddea';
        """)


def downgrade():
    conn = op.get_bind()

    conn.execute("""
INSERT INTO mode_cards (id, column_id, title, position, type, card_id)
VALUES ('3616a066-b4ef-4a5c-b3fb-85001110ddea', 'c07de7ce-3f55-419b-825d-4f77bd4e678d', 'Property highlights', 200, 'SUMMARY_HIGHLIGHTS_CARD', 'property-highlights');
        """)

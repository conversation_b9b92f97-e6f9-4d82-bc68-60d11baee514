"""Extends RecommendationActionEnum

Revision ID: 1464bc837883
Revises: 7893061831ef
Create Date: 2022-03-29 14:36:39.086534+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1464bc837883"
down_revision = "7893061831ef"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE recommendationtype ADD VALUE IF NOT EXISTS 'NO_ACTION';""")


def downgrade():
    pass

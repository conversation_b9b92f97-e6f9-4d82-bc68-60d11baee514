"""Add INTEGER as field type

Revision ID: 2cd859aa2dba
Revises: 765c2790584b
Create Date: 2021-11-22 10:18:31.841628+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2cd859aa2dba"
down_revision = "765c2790584b"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE fieldtype ADD VALUE IF NOT EXISTS 'INTEGER';""")


def downgrade():
    pass

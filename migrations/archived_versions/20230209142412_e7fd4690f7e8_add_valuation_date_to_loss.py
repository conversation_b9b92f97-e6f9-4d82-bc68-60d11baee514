"""Add valuation date to loss

Revision ID: e7fd4690f7e8
Revises: a589e47b668a
Create Date: 2023-02-09 14:24:12.628845+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e7fd4690f7e8"
down_revision = "a589e47b668a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("loss", sa.Column("report_valuation_date", sa.Date(), nullable=True))
    op.add_column("loss", sa.Column("report_generated_date", sa.Date(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

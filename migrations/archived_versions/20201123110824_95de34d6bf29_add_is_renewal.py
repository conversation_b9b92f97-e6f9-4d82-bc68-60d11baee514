"""Add is_renewal

Revision ID: 95de34d6bf29
Revises: 75f242e56356
Create Date: 2020-11-23 11:08:24.740449+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "95de34d6bf29"
down_revision = "75f242e56356"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submissions", sa.Column("is_renewal", sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submissions", "is_renewal")
    # ### end Alembic commands ###

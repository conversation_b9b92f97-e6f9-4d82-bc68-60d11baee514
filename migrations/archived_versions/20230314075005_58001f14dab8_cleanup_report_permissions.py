"""cleanup report permissions

Revision ID: 58001f14dab8
Revises: 465ac0838d9c
Create Date: 2023-03-14 07:50:05.427401+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "58001f14dab8"
down_revision = "465ac0838d9c"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
delete from report_permissions where grantee_user_id IN (select id from users where email = '<EMAIL>');
        """)


def downgrade():
    pass

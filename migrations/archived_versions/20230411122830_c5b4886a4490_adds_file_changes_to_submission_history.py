"""Adds file changes to submission_history

Revision ID: c5b4886a4490
Revises: 25e004da03d3
Create Date: 2023-04-11 12:28:30.532657+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "c5b4886a4490"
down_revision = "25e004da03d3"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'FILE_ADDED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'FILE_DELETED';")


def downgrade():
    ...

"""recalls and letters

Revision ID: 54e55650002a
Revises: a5a9d0726a79
Create Date: 2023-04-03 15:13:51.638585+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "54e55650002a"
down_revision = "a5a9d0726a79"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
  DO
    $do$
    BEGIN
        IF EXISTS (select * from modes where id='37d3b340-2c83-468a-9f42-fdd429f7e95d') THEN        
-- Single business: Recalls
INSERT INTO mode_rows(id, position, title)
VALUES('f57c2770-aa9c-4d5c-a6f0-06c92237f552', 100, 'Recalls');
INSERT INTO mode_columns(id, row_id, position,  width)
VALUES('3e1640af-69d5-469e-a1b5-ca812cdd8cf6', 'f57c2770-aa9c-4d5c-a6f0-06c92237f552', 0, 12);
INSERT INTO mode_cards (id, column_id, position, type, card_id, props)
VALUES('607523e5-cbd1-4687-9730-deaa427f32c8', '3e1640af-69d5-469e-a1b5-ca812cdd8cf6', 0, 'TABLE', 'product-recalls', '{"type": "RECALLS"}');

INSERT INTO mode_sections (id, name)
VALUES ('a309f669-c169-4b36-80c1-66b8c7d5c9bc', 'Recalls without entity picker');

INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
VALUES('e189cbdf-f267-4898-8c8f-0aa155478db8', 'a309f669-c169-4b36-80c1-66b8c7d5c9bc', 100, 'f57c2770-aa9c-4d5c-a6f0-06c92237f552');

INSERT INTO mode_elements(id, mode_id, position, section_id)
VALUES('43eaf6d4-d1bd-4181-ada8-4021e69d1fd8', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 950, 'a309f669-c169-4b36-80c1-66b8c7d5c9bc');

-- Single business: warning letters
INSERT INTO mode_rows(id, position, title)
VALUES('4ec90d3c-9eaf-46b9-9c1e-d52c7ce484a8', 100, 'Warning Letters');
INSERT INTO mode_columns(id, row_id, position,  width)
VALUES('0e09b770-6b38-4cf0-bd88-6ee57744d3ea', '4ec90d3c-9eaf-46b9-9c1e-d52c7ce484a8', 0, 12);
INSERT INTO mode_cards (id, column_id, position, type, card_id, props)
VALUES('f8216495-b77c-4c1f-992e-af7bba6b52c4', '0e09b770-6b38-4cf0-bd88-6ee57744d3ea', 0, 'TABLE', 'warning-letters', '{"type": "WARNING_LETTERS"}');

INSERT INTO mode_sections (id, name)
VALUES ('69d9ae8b-3640-4b16-aa51-e72456a770f1', 'Warning letters without entity picker');

INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
VALUES('a2a7084f-b2ed-482c-8a2f-f8980a0db734', '69d9ae8b-3640-4b16-aa51-e72456a770f1', 100, '4ec90d3c-9eaf-46b9-9c1e-d52c7ce484a8');

INSERT INTO mode_elements(id, mode_id, position, section_id)
VALUES('573b5484-107d-4dbd-9648-c4448c560c6f', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 960, '69d9ae8b-3640-4b16-aa51-e72456a770f1');

--- Premises Recalls
INSERT INTO mode_rows(id, position, title)
VALUES('39c2c021-aedd-4df3-9e75-6b8a2b2b79a5', 100, 'Recalls  (All Premises)');
INSERT INTO mode_columns(id, row_id, position,  width)
VALUES('b4a64e66-a71d-489b-a8af-e32d154c5009', '39c2c021-aedd-4df3-9e75-6b8a2b2b79a5', 0, 12);

INSERT INTO mode_cards (id, column_id, position, type, card_id, props, title)
VALUES('7f26f411-340d-413e-98aa-22892e58b27a', 'b4a64e66-a71d-489b-a8af-e32d154c5009', 0, 'ENTITY_FILTER', 'product-recalls-filter', '{"entityFilterKey": "recalls"}', 'Filter By');

INSERT INTO mode_cards (id, column_id, position, type, card_id, props)
VALUES('054160be-8df1-48ef-8a1f-d08a6cadd2cf', 'b4a64e66-a71d-489b-a8af-e32d154c5009', 100, 'TABLE', 'product-recalls', '{"type": "RECALLS", "entityFilterKey": "recalls"}');

INSERT INTO mode_sections (id, name)
VALUES ('30c36045-90dc-462e-ac45-b36efcf6cc8e', 'Recalls with entity filter');

INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
VALUES('75265eab-c83e-4333-a6b9-822947636ae2', '30c36045-90dc-462e-ac45-b36efcf6cc8e', 100, '39c2c021-aedd-4df3-9e75-6b8a2b2b79a5');

INSERT INTO mode_elements(id, mode_id, position, section_id)
VALUES('d5748044-b240-4839-a9a2-fc7914d9cd0c', '37d3b340-2c83-468a-9f42-fdd429f7e95d', 950, '30c36045-90dc-462e-ac45-b36efcf6cc8e');


UPDATE mode_cards
SET props = jsonb_set(
  props::jsonb,
  array['highlights'],
  (props->'highlights')::jsonb || '{
  "icons": [
    {
      "name": "warning",
      "color": "error",
      "condition": { "min": 1, "type": "isInRange" }
    }
  ],
  "label": "Recalls",
  "cardId": "054160be-8df1-48ef-8a1f-d08a6cadd2cf",
  "source": {
    "mapper": "numberOfItems",
    "source": {
      "parentType": "ORGANIZATION",
      "sourceType": "DOCUMENT",
      "documentType": "PRODUCT_RECALL"
    }
  },
  "noValuesLabel": "None found",
  "redirectLinkLabel": "Go to Recalls",
  "conditions": [
    {
      "mapper": "numberOfItems",
      "source": {
        "parentType": "ORGANIZATION",
        "sourceType": "DOCUMENT",
        "documentType": "PRODUCT_RECALL"
      },
      "condition": { "min": 1, "type": "isInRange" }
    }
  ]
}'::jsonb)
WHERE id = 'e7268e13-fdc2-4979-b4c4-8cf581a6889b';

--- Premises Warning Letters
INSERT INTO mode_rows(id, position, title)
VALUES('e9f8450f-9c73-42b2-8684-31527220d1b2', 100, 'Warning Letters (All Premises)');
INSERT INTO mode_columns(id, row_id, position,  width)
VALUES('07687f28-9609-4e27-a6d7-0bbc91eefee6', 'e9f8450f-9c73-42b2-8684-31527220d1b2', 0, 12);

INSERT INTO mode_cards (id, column_id, position, type, card_id, props, title)
VALUES('d5b185de-3116-4ba2-84bd-59d73258c35b', '07687f28-9609-4e27-a6d7-0bbc91eefee6', 0, 'ENTITY_FILTER', 'warning-letters-filter', '{"entityFilterKey": "warning-letters"}', 'Filter By');
INSERT INTO mode_cards (id, column_id, position, type, card_id, props)
VALUES('0c4d637e-db4d-493e-83f8-cd29e34c111e', '07687f28-9609-4e27-a6d7-0bbc91eefee6', 100, 'TABLE', 'warning-letters', '{"type": "WARNING_LETTERS", "entityFilterKey": "warning-letters"}');

INSERT INTO mode_sections (id, name)
VALUES ('4a69f3f2-f215-4fbe-a315-1ca438725ebc', 'Warning letters with entity filter');

INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
VALUES('8aafd2c9-d767-435c-a520-b6a672f0c475', '4a69f3f2-f215-4fbe-a315-1ca438725ebc', 100, 'e9f8450f-9c73-42b2-8684-31527220d1b2');

INSERT INTO mode_elements(id, mode_id, position, section_id)
VALUES('3222b19b-5dd7-4dc1-85c5-04d7204c3c82', '37d3b340-2c83-468a-9f42-fdd429f7e95d', 960, '4a69f3f2-f215-4fbe-a315-1ca438725ebc');

UPDATE mode_cards
SET props = jsonb_set(
  props::jsonb,
  array['highlights'],
  (props->'highlights')::jsonb || '{
  "icons": [
    {
      "name": "warning",
      "color": "error",
      "condition": { "min": 1, "type": "isInRange" }
    }
  ],
  "label": "Warning Letters",
  "cardId": "0c4d637e-db4d-493e-83f8-cd29e34c111e",
  "source": {
    "mapper": "numberOfItems",
    "source": {
      "parentType": "ORGANIZATION",
      "sourceType": "DOCUMENT",
      "documentType": "FDA_WARNING_LETTER"
    }
  },
  "noValuesLabel": "None found",
  "redirectLinkLabel": "Go to Warning Letters",
  "conditions": [
    {
      "mapper": "numberOfItems",
      "source": {
        "parentType": "ORGANIZATION",
        "sourceType": "DOCUMENT",
        "documentType": "FDA_WARNING_LETTER"
      },
      "condition": { "min": 1, "type": "isInRange" }
    }
  ]
}'::jsonb)
WHERE id = 'e7268e13-fdc2-4979-b4c4-8cf581a6889b';

---

DELETE FROM mode_elements WHERE id = 'b616b948-479b-4d75-9b25-afec9874f857';

INSERT INTO mode_rows(id, position, title)
VALUES('2bcab64a-0620-48d9-9ce4-0355250d37ef', 100, 'Important highlights');
INSERT INTO mode_columns(id, row_id, position,  width)
VALUES('70b1fc1f-7c42-498e-9360-69306aabf78c', '2bcab64a-0620-48d9-9ce4-0355250d37ef', 0, 12);
INSERT INTO mode_cards (id, column_id, position, type, card_id, title, props)
VALUES('a74cf63a-e033-406e-a455-95cef76d646a', '70b1fc1f-7c42-498e-9360-69306aabf78c', 0, 'IMPORTANT_HIGHLIGHTS_CARD', 'named-insured-imp-highlights', 'Named Insured Highlights', '{
  "columns": 4,
  "highlights": [
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "Legal Filings",
      "cardId": "legal-filings-card",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "LEGAL_FILING"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to Legal Filings"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "OSHA Violations",
      "cardId": "osha-violations",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "OSHA_VIOLATION"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to OSHA Violations"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "News",
      "cardId": "news-card",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "NEWS"
        }
      },
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to News"
    },
    {
      "icons": [
        {
          "name": "warning",
          "color": "error",
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "label": "EPA Inspections",
      "cardId": "epa-inspections",
      "source": {
        "mapper": "numberOfItems",
        "source": {
          "parentType": "BUSINESS",
          "sourceType": "DOCUMENT",
          "documentType": "EPA_INSPECTION"
        }
      },
      "conditions": [
        {
          "mapper": "numberOfItems",
          "source": {
            "parentType": "BUSINESS",
            "sourceType": "DOCUMENT",
            "documentType": "EPA_INSPECTION"
          },
          "condition": { "min": 1, "type": "isInRange" }
        }
      ],
      "noValuesLabel": "None found",
      "redirectLinkLabel": "Go to EPA Inspections"
    },
    {
        "icons": [
          {
            "name": "warning",
            "color": "error",
            "condition": { "min": 1, "type": "isInRange" }
          }
        ],
        "label": "Recalls",
        "cardId": "607523e5-cbd1-4687-9730-deaa427f32c8",
        "source": {
          "mapper": "numberOfItems",
          "source": {
            "parentType": "ORGANIZATION",
            "sourceType": "DOCUMENT",
            "documentType": "PRODUCT_RECALL"
          }
        },
        "conditions": [
          {
            "mapper": "numberOfItems",
            "source": {
              "parentType": "ORGANIZATION",
              "sourceType": "DOCUMENT",
              "documentType": "PRODUCT_RECALL"
            },
            "condition": { "min": 1, "type": "isInRange" }
          }
        ],
        "noValuesLabel": "None found",
        "redirectLinkLabel": "Go to Recalls"
      },
      {
        "icons": [
          {
            "name": "warning",
            "color": "error",
            "condition": { "min": 1, "type": "isInRange" }
          }
        ],
        "label": "Warning Letters",
        "cardId": "f8216495-b77c-4c1f-992e-af7bba6b52c4",
        "source": {
          "mapper": "numberOfItems",
          "source": {
            "parentType": "ORGANIZATION",
            "sourceType": "DOCUMENT",
            "documentType": "FDA_WARNING_LETTER"
          }
        },
        "conditions": [
          {
            "mapper": "numberOfItems",
            "source": {
              "parentType": "ORGANIZATION",
              "sourceType": "DOCUMENT",
              "documentType": "FDA_WARNING_LETTER"
            },
            "condition": { "min": 1, "type": "isInRange" }
          }
        ],
        "noValuesLabel": "None found",
        "redirectLinkLabel": "Go to Warning Letters"
      }
  ]
}');

INSERT INTO mode_sections (id, name)
VALUES ('0d31f5ee-dfcb-4763-a5ee-8d9ce05110d4', 'Named important highlights');

INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
VALUES('83ea102a-2b93-43d5-b011-ee3527cb51a3', '0d31f5ee-dfcb-4763-a5ee-8d9ce05110d4', 100, '2bcab64a-0620-48d9-9ce4-0355250d37ef');

INSERT INTO mode_elements(id, mode_id, position, section_id)
VALUES('270d6127-f407-4d28-bb63-3d31801fdf5a', 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 200, '0d31f5ee-dfcb-4763-a5ee-8d9ce05110d4');


INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
VALUES ('5bb14d90-916b-42f8-bd58-63232a2903bd', '6091e660-6fab-4f05-b4c2-0dd7d8930fff', 450, 'f57c2770-aa9c-4d5c-a6f0-06c92237f552');
INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
VALUES ('d1bb9c78-de98-4956-8052-aa0a8ea6983f', '6091e660-6fab-4f05-b4c2-0dd7d8930fff', 460, '4ec90d3c-9eaf-46b9-9c1e-d52c7ce484a8');

        END IF;
    END
    $do$
        """)


def downgrade():
    pass

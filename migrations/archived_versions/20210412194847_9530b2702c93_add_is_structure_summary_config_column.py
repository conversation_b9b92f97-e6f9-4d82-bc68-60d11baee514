"""Add is_structure_summary_config column

Revision ID: 9530b2702c93
Revises: 663f4b0e566c
Create Date: 2021-04-12 19:48:47.772306+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9530b2702c93"
down_revision = "663f4b0e566c"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("metric_config", sa.Column("is_structure_summary_config", sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("metric_config", "is_structure_summary_config")
    # ### end Alembic commands ###

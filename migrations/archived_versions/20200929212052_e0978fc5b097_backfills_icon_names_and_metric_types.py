"""Backfills icon names and metric types

Revision ID: e0978fc5b097
Revises: 46cd64f90c2d
Create Date: 2020-09-29 21:20:52.836327+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e0978fc5b097"
down_revision = "46cd64f90c2d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update summary_preference
    set metric_type = 'range_summary', icon_name = 'icn-police'
    where display_name = 'Distance to Police Station';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Building Age';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Building Area';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Building Construction Type';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Building Stories';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'storm'
    where display_name = 'Coastal Storm Surge';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Cooking Types';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Crime in Premises Risk';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Assault';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Burglary';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Drug and Alcohol Deaths';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Larceny';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Motor Vehicle Theft';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Murder';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Overall';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Rape';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'icn-police'
    where display_name = 'Crime Score: Robbery';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'coast'
    where display_name = 'Distance to Coast';
    
    update summary_preference
    set metric_type = 'range_summary', icon_name = 'fire-station'
    where display_name = 'Distance to Fire Station';
    
    update summary_preference
    set metric_type = 'range_summary', icon_name = 'hospital'
    where display_name = 'Distance to Hospital';
    
    update summary_preference
    set metric_type = 'range_summary', icon_name = 'fire'
    where display_name = 'Distance to Hydrant';
    
    update summary_preference
    set metric_type = 'range_summary', icon_name = 'police'
    where display_name = 'Distance to Police Station';
    
    update summary_preference
    set metric_type = 'range_summary'
    where display_name = 'Distance to Toxic Facility';
    
    update summary_preference
    set metric_type = 'range_summary', icon_name = 'hospital'
    where display_name = 'Distance to Urgent Care';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'earthquake'
    where display_name = 'Earthquake';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Elevation';
    
    update summary_preference
    set metric_type = 'employees_summary'
    where display_name = 'Employees';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Entertainment Types';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'fire'
    where display_name = 'Fire Protection Class';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'flood'
    where display_name = 'Flood';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'hail'
    where display_name = 'Hail';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Adult Entertainment';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Balconies';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Bottle Service';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Bouncers';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has BYOB';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Catering';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Dancing';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Delivery';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Infestation';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Outdoor Seating';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Roof Access';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Security Guards';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Swimming Pool';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Has Waiter Service';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Hotel Amenities';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Hotel Average Rating';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Hotel Class';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Hotel: Maximum Price';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Hotel: Minimum Price';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Hotel: Number of Rooms';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'lightning'
    where display_name = 'Lightning';
    
    update summary_preference
    set metric_type = 'mean'
    where display_name = 'Lot Size';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Parking Types';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Performance Types';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Prostitution Risk';
    
    update summary_preference
    set metric_type = 'categorical_data_summary'
    where display_name = 'Serves Alcohol';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'sinkhole'
    where display_name = 'Sinkhole';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'snow'
    where display_name = 'Snow Load';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'tornado'
    where display_name = 'Tornado';
    
    update summary_preference
    set metric_type = 'list_summary'
    where display_name = 'Websites';
    
    update summary_preference
    set metric_type = 'grade_summary', icon_name = 'fire'
    where display_name = 'Wildfire';
    """)


def downgrade():
    pass

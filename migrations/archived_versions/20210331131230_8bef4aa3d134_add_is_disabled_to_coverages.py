"""Add is_disabled to coverages

Revision ID: 8bef4aa3d134
Revises: 8e6bdca28b42
Create Date: 2021-03-31 13:12:30.641166+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8bef4aa3d134"
down_revision = "8e6bdca28b42"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("coverages", sa.Column("is_disabled", sa.<PERSON>(), nullable=True))
    op.execute("""UPDATE coverages set is_disabled = false""")
    op.execute("""UPDATE coverages set is_disabled = true where id in (
        'a474d62b-359a-494d-9867-545434c42aa5',
        '129a9856-ee1d-42e6-ae74-5823223f0253',
        'c5a887d9-b164-47ea-be93-87c1d6226b78',
        '53f771cd-c8fc-43f7-8ddf-7895a6c937f9',
        'c5f013d5-a59a-4ac1-8c92-3d6dee903271',
        '84fa7716-11ee-4ac1-9d1c-8e4efc25dd6d',
        '250fe426-1162-4a16-bdb8-68ab24c81284',
        '4cc1dab9-16b8-4940-b2ee-e85c17c9c027',
        'f9def5cd-0b0d-4322-9ee0-3798d9e410dd',
        'de2146aa-72bd-4ad4-a602-751cd1f8a25d',
        'af0ed983-bc7d-49c5-a4ff-789789b9642d',
        '39c85b49-f693-46d3-bf4f-5d8d3b206e73',
        '903699d8-5547-4fff-ab69-8836a44c2ad6',
        'dbc13678-6fc6-4bb6-adb1-11ad66ef1450',
        'c6b40d1a-155d-41a2-8f8a-cb9ac1f22238',
        '66dda691-72e0-4463-ab2f-42f079a2c146',
        '4a0a7084-d889-446d-8a34-32a5ddbfc05b'
    )""")
    op.execute("""INSERT INTO coverages VALUES (
        'aba4f871-6e8e-4514-869e-dcbc5f5eff05', now(), null, 'excessAndSurplus', 'Excess and Surplus', false
    )""")


def downgrade():
    op.drop_column("coverages", "is_disabled")

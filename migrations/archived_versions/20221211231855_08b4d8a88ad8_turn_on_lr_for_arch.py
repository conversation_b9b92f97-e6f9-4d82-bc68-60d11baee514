"""Turn on LR for Arch

Revision ID: 08b4d8a88ad8
Revises: ff9e5de11457
Create Date: 2022-12-11  23:18:55.145314+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "08b4d8a88ad8"
down_revision = "ff9e5de11457"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("""update settings set loss_runs_enabled = null where settings.user_id in (
    select users.id from users
    where users.organization_id=10)""")
    conn.execute("""update settings set loss_runs_enabled = true where settings.organization_id=10""")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

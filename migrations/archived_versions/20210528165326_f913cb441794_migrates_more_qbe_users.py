"""Migrates more QBE users

Revision ID: f913cb441794
Revises: 871bfc1c60c6
Create Date: 2021-05-28 16:53:26.489457+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "f913cb441794"
down_revision = "871bfc1c60c6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<PERSON><PERSON><EMAIL>'
    where email = '<EMAIL>' and id = 66;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 77;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 78;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<PERSON><PERSON>@us.qbe.com'
    where email = '<EMAIL>' and id = 79;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 82;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 94;
    """)

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>' and id = 153;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 95;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 96;
    """)

    conn.execute("""
    update users
    set external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 97;
    """)

    conn.execute("""
    update users
    set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>' and id = 160;
    """)


def downgrade():
    conn = op.get_bind()

    conn.execute("""
    update users
    set external_id = 'auth0|5f5aee52e61fe3006c3bfe39'
    where email = '<EMAIL>' and id = 66;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|5f6251b48d4d0c00768d8f55'
    where email = '<EMAIL>' and id = 77;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|5f6251fd1e076c0079814a87'
    where email = '<EMAIL>' and id = 78;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|5f625101eb90340070521aea'
    where email = '<EMAIL>' and id = 79;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|5f6251522a3d510072a199b6'
    where email = '<EMAIL>' and id = 82;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|60398c3442384d0070ebbccb'
    where email = '<EMAIL>' and id = 94;
    """)

    conn.execute("""
    update users
    set email = '<EMAIL>', organization_id = 5, external_id = 'samlp|QBE|<EMAIL>'
    where email = '<EMAIL>' and id = 153;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|60398e2ce5978d007180c0fb'
    where email = '<EMAIL>' and id = 95;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|60398b8d2dfa32006a8497c0'
    where email = '<EMAIL>' and id = 96;
    """)

    conn.execute("""
    update users
    set external_id = 'auth0|60398bb5b4f3a50068c8e142'
    where email = '<EMAIL>' and id = 97;
    """)

    conn.execute("""
    update users
    set email = '<EMAIL>', external_id = 'auth0|5f5b0c361e076c00797b7200'
    where email = '<EMAIL>' and id = 160;
    """)

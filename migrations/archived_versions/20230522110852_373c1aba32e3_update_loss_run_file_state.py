"""Update sensible status of file id 12e09a5a-7ff9-4d8b-a6d5-6442b45210b1

Revision ID: 373c1aba32e3
Revises: 1ae69214e9c1
Create Date: 2023-05-22 11:08:52.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

revision = "373c1aba32e3"
down_revision = "1ae69214e9c1"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update files set sensible_status = 'COMPLETE' where id='12e09a5a-7ff9-4d8b-a6d5-6442b45210b1';
    """)


def downgrade():
    pass

"""Update the important highlights

Revision ID: 81931c54bbda
Revises: 6e2ae5db8d51
Create Date: 2023-04-20 21:47:53.804992+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "81931c54bbda"
down_revision = "ab0cffc8dd84"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
DO
$do$
BEGIN
    IF EXISTS (select * from modes where id='37d3b340-2c83-468a-9f42-fdd429f7e95d')
    AND NOT EXISTS (select * from mode_sections where id='36c5d69f-3432-4a57-bfcb-6a422e1455e5') THEN

-- Highlights got Umbrella/Excess Liability and not matching
insert into mode_sections (id, created_at, updated_at, props, name)
values ('36c5d69f-3432-4a57-bfcb-6a422e1455e5', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"report","contains":"^((?!Commercial Inland Marine|Commercial Property|Commercial General Liability|Business Auto|Management Liability|Liquor Liability|Crime|Workers Compensation).)*$","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"}}}', 'Highlights got Umbrella/Excess Liability and not matching');

insert into mode_rows (id, created_at, updated_at, mode_id, position, elevation, is_collapsible, is_default_open, title)
values ('0bb1514a-c88c-4ec5-8348-90233cf1e0c7', now(), null, null, 100, null, true, true, 'Important highlights');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('c67b17f7-66f8-48ac-ab13-88d388902d72', now(), null, '36c5d69f-3432-4a57-bfcb-6a422e1455e5', 100, '0bb1514a-c88c-4ec5-8348-90233cf1e0c7', null);

insert into mode_columns (id, created_at, updated_at, row_id, position, width, section_title) values
('e42355df-0969-4cbf-b928-e413a1268f65', now(), null, '0bb1514a-c88c-4ec5-8348-90233cf1e0c7', 0, 12, null);

insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip) values
('59773394-9cdb-4e58-8fbb-39cd8ded0d9e', now(), null, 'e42355df-0969-4cbf-b928-e413a1268f65' ,'Highlights', 100, 'IMPORTANT_HIGHLIGHTS_CARD', 'important-highlights', '{"columns":3,"showPremisesSummary":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"highlights":[{"icons":[{"name":"warning","color":"error"}],"label":"OSHA Violations","cardId":"osha-violations","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"OSHA_VIOLATION"}},"redirectLinkLabel":"Go to OSHA Violations"},{"icons":[{"name":"warning","color":"error"}],"label":"Recalls","cardId":"product-recalls","source":{"mapper":"numberOfItems","source":{"parentType":"ORGANIZATION","sourceType":"DOCUMENT","documentType":"PRODUCT_RECALL"}},"redirectLinkLabel":"Go to Recalls"},{"icons":[{"name":"warning","color":"error"}],"label":"Warning Letters","cardId":"warning-letters","source":{"mapper":"numberOfItems","source":{"parentType":"ORGANIZATION","sourceType":"DOCUMENT","documentType":"FDA_WARNING_LETTER"}},"redirectLinkLabel":"Go to Warning Letters"},{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"redirectLinkLabel":"Go to Legal Filings"},{"icons":[{"name":"warning","color":"warning"}],"label":"Overall Crime Score","cardId":"summaries-static-Crime","source":{"mapper":"summary","mapperConfig":{"name":"crimeScore"},"source":{"sourceType":"SUMMARY"}},"redirectLinkLabel":"Go to Crime Summary"},{"label":"News","cardId":"news-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"NEWS"}},"redirectLinkLabel":"Go to News"}]}', null);
-- End section definition

-- Highlights for Commercial General Liability
insert into mode_sections (id, created_at, updated_at, props, name)
values ('6e7f0ea4-f601-4b0e-865e-cb888dc75b31', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"report","contains":"^Commercial General Liability$","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"}}}', 'Highlights for Commercial General Liability');

insert into mode_rows (id, created_at, updated_at, mode_id, position, elevation, is_collapsible, is_default_open, title)
values ('ba460c91-8395-4526-a812-6229e36a22b0', now(), null, null, 100, null, true, true, 'Important highlights');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('3814ede4-9427-4c81-95ee-51a810361d4b', now(), null, '6e7f0ea4-f601-4b0e-865e-cb888dc75b31', 100, 'ba460c91-8395-4526-a812-6229e36a22b0', null);

insert into mode_columns (id, created_at, updated_at, row_id, position, width, section_title) values
('cdc245f5-d8ca-4957-a9d7-af8465d84c49', now(), null, 'ba460c91-8395-4526-a812-6229e36a22b0', 0, 12, null);

insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip) values
(
    '8123b1f3-9c05-4e3c-aef8-2462c5a3b55b',
    now(),
    null,
    'cdc245f5-d8ca-4957-a9d7-af8465d84c49',
    'Highlights',
    100,
    'IMPORTANT_HIGHLIGHTS_CARD',
    'important-highlights',
    '{"columns":3,"showPremisesSummary":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"highlights":[{"icons":[{"name":"warning","color":"error"}],"label":"Recalls","cardId":"product-recalls","source":{"mapper":"numberOfItems","source":{"parentType":"ORGANIZATION","sourceType":"DOCUMENT","documentType":"PRODUCT_RECALL"}},"redirectLinkLabel":"Go to Recalls"},{"icons":[{"name":"warning","color":"error"}],"label":"Warning Letters","cardId":"warning-letters","source":{"mapper":"numberOfItems","source":{"parentType":"ORGANIZATION","sourceType":"DOCUMENT","documentType":"FDA_WARNING_LETTER"}},"redirectLinkLabel":"Go to Warning Letters"},{"icons":[{"name":"warning","color":"warning"}],"label":"Overall Crime Score","cardId":"summaries-static-Crime","source":{"mapper":"summary","mapperConfig":{"name":"crimeScore"},"source":{"sourceType":"SUMMARY"}},"redirectLinkLabel":"Go to Crime Summary"},{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"redirectLinkLabel":"Go to Legal Filings"},{"label":"News","cardId":"news-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"NEWS"}},"redirectLinkLabel":"Go to News"}]}',
    null);
-- End section definition

-- Highlights for Management Liability
insert into mode_sections (id, created_at, updated_at, props, name)
values ('c962727c-89ad-4e6b-b222-c4e91b114c03', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"report","contains":"^Management Liability$","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"}}}', 'Highlights for Management Liability');

insert into mode_rows (id, created_at, updated_at, mode_id, position, elevation, is_collapsible, is_default_open, title)
values ('d026e6bc-0647-4122-be4d-ec91dcea3888', now(), null, null, 100, null, true, true, 'Important highlights');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('f670cf50-f4ac-4bc4-84ee-f3f8c3570901', now(), null, 'c962727c-89ad-4e6b-b222-c4e91b114c03', 100, 'd026e6bc-0647-4122-be4d-ec91dcea3888', null);

insert into mode_columns (id, created_at, updated_at, row_id, position, width, section_title) values
('a6a50fc2-5ece-4785-be6c-020599176015', now(), null, 'd026e6bc-0647-4122-be4d-ec91dcea3888', 0, 12, null);

insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip) values
(
    '60eeea7a-cd35-46f6-9579-ff4edd28070f',
    now(),
    null,
    'a6a50fc2-5ece-4785-be6c-020599176015',
    'Highlights',
    100,
    'IMPORTANT_HIGHLIGHTS_CARD',
    'important-highlights',
    '{"columns":3,"showPremisesSummary":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"highlights":[{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"redirectLinkLabel":"Go to Legal Filings"},{"icons":[{"name":"warning","color":"error"}],"label":"OSHA Violations","cardId":"osha-violations","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"OSHA_VIOLATION"}},"redirectLinkLabel":"Go to OSHA Violations"},{"label":"News","cardId":"news-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"NEWS"}},"redirectLinkLabel":"Go to News"}]}',
    null);
-- End section definition

-- Highlights for Liquor Liability and Crime
insert into mode_sections (id, created_at, updated_at, props, name)
values ('d0ee5ba9-f499-49f8-b95f-a417903f4000', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"report","contains":"^(Liquor Liability|Crime)$","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"}}}', 'Highlights for Liquor Liability and Crime');

insert into mode_rows (id, created_at, updated_at, mode_id, position, elevation, is_collapsible, is_default_open, title)
values ('4e8a1cff-2eec-4318-a10f-66b0c010db70', now(), null, null, 100, null, true, true, 'Important highlights');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('71040172-685c-4e43-9aa1-a42aa8fda4ea', now(), null, 'd0ee5ba9-f499-49f8-b95f-a417903f4000', 100, '4e8a1cff-2eec-4318-a10f-66b0c010db70', null);

insert into mode_columns (id, created_at, updated_at, row_id, position, width, section_title) values
('b7efd6c4-a8cb-4fba-a419-345b5694a20e', now(), null, '4e8a1cff-2eec-4318-a10f-66b0c010db70', 0, 12, null);

insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip) values
(
    'fb87ada1-7895-45fd-9e6c-7e1c572f3f62',
    now(),
    null,
    'b7efd6c4-a8cb-4fba-a419-345b5694a20e',
    'Highlights',
    100,
    'IMPORTANT_HIGHLIGHTS_CARD',
    'important-highlights',
    '{"columns":3,"showPremisesSummary":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"highlights":[{"icons":[{"name":"warning","color":"warning"}],"label":"Overall Crime Score","cardId":"summaries-static-Crime","source":{"mapper":"summary","mapperConfig":{"name":"crimeScore"},"source":{"sourceType":"SUMMARY"}},"redirectLinkLabel":"Go to Crime Summary"},{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"redirectLinkLabel":"Go to Legal Filings"},{"label":"News","cardId":"news-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"NEWS"}},"redirectLinkLabel":"Go to News"}]}',
    null);
-- End section definition

-- Highlights for Workers Compensation
insert into mode_sections (id, created_at, updated_at, props, name)
values ('e305034f-2803-48e7-b476-3bc455b32f44', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"report","contains":"^Workers Compensation$","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"}}}', 'Highlights for Workers Compensation');

insert into mode_rows (id, created_at, updated_at, mode_id, position, elevation, is_collapsible, is_default_open, title)
values ('f0ea30ed-da93-40af-b477-5c0c2de24e89', now(), null, null, 100, null, true, true, 'Important highlights');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('1cc9714a-3fae-4638-b762-5d9a4f9bcdc7', now(), null, 'e305034f-2803-48e7-b476-3bc455b32f44', 100, 'f0ea30ed-da93-40af-b477-5c0c2de24e89', null);

insert into mode_columns (id, created_at, updated_at, row_id, position, width, section_title) values
('3566493b-bce2-4e1c-a0b7-419405320683', now(), null, 'f0ea30ed-da93-40af-b477-5c0c2de24e89', 0, 12, null);

insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip) values
(
    '4a6a1fa7-708b-4ccf-b4b4-31fc89332510',
    now(),
    null,
    '3566493b-bce2-4e1c-a0b7-419405320683',
    'Highlights',
    100,
    'IMPORTANT_HIGHLIGHTS_CARD',
    'important-highlights',
    '{"columns":3,"showPremisesSummary":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"highlights":[{"icons":[{"name":"warning","color":"error"}],"label":"OSHA Violations","cardId":"osha-violations","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"OSHA_VIOLATION"}},"redirectLinkLabel":"Go to OSHA Violations"},{"icons":[{"name":"warning","color":"warning"}],"label":"Overall Crime Score","cardId":"summaries-static-Crime","source":{"mapper":"summary","mapperConfig":{"name":"crimeScore"},"source":{"sourceType":"SUMMARY"}},"redirectLinkLabel":"Go to Crime Summary"},{"icons":[{"name":"warning","color":"error"}],"label":"Warning Letters","cardId":"warning-letters","source":{"mapper":"numberOfItems","source":{"parentType":"ORGANIZATION","sourceType":"DOCUMENT","documentType":"FDA_WARNING_LETTER"}},"redirectLinkLabel":"Go to Warning Letters"},{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"redirectLinkLabel":"Go to Legal Filings"},{"label":"News","cardId":"news-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"NEWS"}},"redirectLinkLabel":"Go to News"}]}',
    null);
-- End section definition

-- Highlights for Commercial Property
insert into mode_sections (id, created_at, updated_at, props, name)
values ('99f2b5a2-a487-40d7-b559-ee8e97c6e03e', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"report","contains":"^Commercial Property$","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"}}}', 'Highlights for Commercial Property');

insert into mode_rows (id, created_at, updated_at, mode_id, position, elevation, is_collapsible, is_default_open, title)
values ('1a9ac770-f71f-42f9-8d86-1f42fe6eb9c9', now(), null, null, 100, null, true, true, 'Important highlights');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('e3cf51e1-7534-4c99-84c0-96faef1e09aa', now(), null, '99f2b5a2-a487-40d7-b559-ee8e97c6e03e', 100, '1a9ac770-f71f-42f9-8d86-1f42fe6eb9c9', null);

insert into mode_columns (id, created_at, updated_at, row_id, position, width, section_title) values
('17d781ed-ebd0-486b-90f5-d17105c6af37', now(), null, '1a9ac770-f71f-42f9-8d86-1f42fe6eb9c9', 0, 12, null);

insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip) values
(
    'cb4756da-fee2-4308-915e-eaf9bcabba6e',
    now(),
    null,
    '17d781ed-ebd0-486b-90f5-d17105c6af37',
    'Highlights',
    100,
    'IMPORTANT_HIGHLIGHTS_CARD',
    'important-highlights',
    '{"columns":3,"showPremisesSummary":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"highlights":[{"label":"Building Condition","icons":[{"name":"warning","color":"error"}],"source":{"mapper":"jsonPathValue","source":{"parentType":"PREMISES","sourceType":"FACT","factSubtypes":["BUILDING_CONDITION"]},"mapperConfig":{"jsonPath":"$.observation.value","aggregation":"filter","value":"(Poor|Unsound)"}},"conditions":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"^((?!37d3b340-2c83-468a-9f42-fdd429f7e95d).)*$"}}],"valueFormatter":{"type":"string","label":"Building condition evaluated as unsound/poor","showNumber":false},"cardId":"premises","redirectLinkLabel":"Go to Premises Section"},{"label":"Unsound/poor building condition","icons":[{"name":"warning","color":"error"}],"source":{"mapper":"jsonPathValue","source":{"parentType":"PREMISES","sourceType":"FACT","factSubtypes":["BUILDING_CONDITION"]},"mapperConfig":{"jsonPath":"$.observation.value","aggregation":"filter","value":"(Poor|Unsound)"}},"conditions":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"valueFormatter":{"type":"string","label":"location"},"cardId":"premises","redirectLinkLabel":"Go to Premises Section"},{"label":"Overall Crime Score","cardId":"summaries-static-Crime","icons":[{"name":"warning","color":"error"}],"source":{"mapper":"summary","mapperConfig":{"name":"crimeScore"},"source":{"sourceType":"SUMMARY"}},"redirectLinkLabel":"Go to Crime Section"},{"label":"Natural hazards risks","source":{"mapper":"summary","mapperConfig":{"name":"propertyRisk"},"source":{"sourceType":"SUMMARY"}},"cardId":"summaries-static-Property","redirectLinkLabel":"Go to Risks Section"},{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"redirectLinkLabel":"Go to Legal Filings"},{"icons":[{"name":"warning","color":"error"}],"label":"OSHA Violations","cardId":"osha-violations","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"OSHA_VIOLATION"}},"redirectLinkLabel":"Go to OSHA Violations"},{"label":"News","cardId":"news-card","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"NEWS"}},"redirectLinkLabel":"Go to News"}]}',
    null);
-- End section definition

-- Highlights for Business Auto and Commercial Inland Marine
insert into mode_sections (id, created_at, updated_at, props, name)
values ('b78e8c55-93d3-4a27-ba0b-457fb9a8da03', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"report","contains":"^(Commercial Inland Marine|Business Auto)$","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"}}}', 'Highlights for Business Auto and Commercial Inland Marine');

insert into mode_rows (id, created_at, updated_at, mode_id, position, elevation, is_collapsible, is_default_open, title)
values ('30ad03f5-2f30-42e0-b965-c63469d59c25', now(), null, null, 100, null, true, true, 'Important highlights');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('696aeb33-f802-4032-9a03-eb262d0476f0', now(), null, 'b78e8c55-93d3-4a27-ba0b-457fb9a8da03', 100, '30ad03f5-2f30-42e0-b965-c63469d59c25', null);

insert into mode_columns (id, created_at, updated_at, row_id, position, width, section_title) values
('182d559a-3ca1-42ce-8573-d18bb1a10ada', now(), null, '30ad03f5-2f30-42e0-b965-c63469d59c25', 0, 12, null);

insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip) values
(
    '5d380a6e-b263-4e01-935b-e5476139ead3',
    now(),
    null,
    '182d559a-3ca1-42ce-8573-d18bb1a10ada',
    'Highlights',
    100,
    'IMPORTANT_HIGHLIGHTS_CARD',
    'important-highlights',
    '{"columns":3,"showPremisesSummary":[{"condition":{"type":"hasReportValue","jsonPath":"$.submissions[0].copilot_2_mode_id","value":"37d3b340-2c83-468a-9f42-fdd429f7e95d"}}],"highlights":[{"label":"Vehicles (Submitted)","cardId":"submission-vehicles-table","source":{"mapper":"childrenDiscoveredIn","source":{"parentType":"SUBMISSION","sourceType":"FACT","factSubtypes":["VEHICLES"]},"mapperConfig":{"discoveredIn":["SOV","SOV_PDF"]}},"redirectLinkLabel":"Go to Vehicles (submitted)"},{"label":"Vehicles (FMCSA)","cardId":"fmcsa-vehicles-table","source":{"mapper":"childrenDiscoveredIn","source":{"parentType":"SUBMISSION","sourceType":"FACT","factSubtypes":["VEHICLES"]},"mapperConfig":{"exclude":["SOV","SOV_PDF"],"discoveredIn":["FMCSA_INSPECTION","FMCSA_CRASH"]}},"redirectLinkLabel":"Go to Vehicles (FMCSA)"},{"label":"Drivers (Submitted)","cardId":"submission-drivers-table","source":{"mapper":"childrenDiscoveredIn","source":{"parentType":"SUBMISSION","sourceType":"FACT","factSubtypes":["DRIVERS"]},"mapperConfig":{"discoveredIn":["SOV","SOV_PDF"]}},"redirectLinkLabel":"Go to Drivers Table"},{"icons":[{"name":"warning","color":"error"}],"label":"FMCSA Violations","cardId":"fmcsa-violations","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"FMCSA_VIOLATION"},"noValuesLabel":"None found"},"redirectLinkLabel":"Go to FMCSA Violations"},{"icons":[{"name":"warning","color":"error"}],"label":"Crashes","cardId":"crash-list","source":{"mapper":"numberOfItems","source":{"expand":["crash_details"],"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"FMCSA_CRASH"}},"redirectLinkLabel":"Go to Crash Table"},{"icons":[{"name":"warning","color":"error"}],"label":"Fatalities","cardId":"crash-list","source":{"mapper":"jsonPathValue","source":{"expand":["crash_details"],"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"FMCSA_CRASH"},"mapperConfig":{"path":"$.number_of_fatalities","aggregation":"sum"}},"redirectLinkLabel":"Go to Crash Table"},{"icons":[{"name":"warning","color":"error"}],"label":"OSHA Violations","cardId":"osha-violations","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"OSHA_VIOLATION"}},"redirectLinkLabel":"Go to OSHA Violations"},{"icons":[{"name":"warning","color":"error"}],"label":"Legal Filings","cardId":"legal-filings","source":{"mapper":"numberOfItems","source":{"parentType":"BUSINESS","sourceType":"DOCUMENT","documentType":"LEGAL_FILING"}},"redirectLinkLabel":"Go to Legal Filings"}]}',
    null);

-- Create sections for modes

-- Multi Premises
delete from mode_elements
where id='feedae69-09a9-4af3-8fe1-eac49b6b7192';

-- Create a container section for all highlights section
insert into mode_sections (id, created_at, updated_at, props, name)
values ('2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', now(), null, '{"strategy": {"type": "merge-rows-columns"}}', 'Multi-premises conditional highlights');

-- Insert conditional rows
insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('b41d8a85-20a6-475a-bbb5-5846eddf8ea8', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 100, null, '36c5d69f-3432-4a57-bfcb-6a422e1455e5');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('b61f998d-978e-4ac1-ac1d-4b90d3b8b27b', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 101, null, '6e7f0ea4-f601-4b0e-865e-cb888dc75b31');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('29335e43-5b21-46a3-84a2-e40834bb1597', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 102, null, 'c962727c-89ad-4e6b-b222-c4e91b114c03');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('778a0948-063d-4baa-aeba-543d6702424d', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 103, null, 'd0ee5ba9-f499-49f8-b95f-a417903f4000');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('ece17fac-dff1-4215-935b-44a179afc4a0', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 104, null, 'e305034f-2803-48e7-b476-3bc455b32f44');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('c8e51337-231a-4bda-949d-163e48c477b5', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 105, null, '99f2b5a2-a487-40d7-b559-ee8e97c6e03e');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('e2bf5fbc-d1d5-47c0-9717-ea844abcee62', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 106, null, 'b78e8c55-93d3-4a27-ba0b-457fb9a8da03');

-- insert fleet
insert into mode_sections (id, created_at, updated_at, props, name)
values ('f5192b16-5ef5-42dd-b7b1-eaa90ddf70d7', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"and","conditions":[{"type":"report","containsOtherValues":"(Commercial Inland Marine|Business Auto)","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"},{"type":"or","conditions":[{"type":"hasFleetFacts"},{"type":"report","contains":"(Vehicles|Drivers)","jsonPath":"$.submissions[0].files[*].file_type"}]}]}}}', 'Important Fleet Facts for multi-premises');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('2a45ddd0-20ee-4c2b-a00d-b57e8757344f', now(), null, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', 200, null, 'f5192b16-5ef5-42dd-b7b1-eaa90ddf70d7');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('20168699-4559-4052-a7bd-1bdc1c2eae9f', now(), null, 'f5192b16-5ef5-42dd-b7b1-eaa90ddf70d7', 100, '5367b9d5-08d6-439e-a607-119668019aa8', null);

-- insert mode_element
insert into mode_elements (id, created_at, updated_at, mode_id, position, section_id, row_id)
values ('91317956-bb2f-4477-a392-90354373ab2a', now(), null, '37d3b340-2c83-468a-9f42-fdd429f7e95d', 150, '2ca887fe-ddab-42e5-9e0c-5c0d9e2e76eb', null);

-- Single Premises
delete from mode_elements
where id='e4d7a3ab-fd40-499c-b487-56a8db72bb27';

-- Create a container section for all highlights section
insert into mode_sections (id, created_at, updated_at, props, name)
values ('53c5e8aa-3889-4562-9719-dd5d83b6b47d', now(), null, '{"strategy": {"type": "merge-rows-columns"}}', 'Single-premises conditional highlights');

-- Insert conditional rows
insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('8e9dd177-8fbb-427a-a8d2-93d1c8d99aeb', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 100, null, '36c5d69f-3432-4a57-bfcb-6a422e1455e5');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('cbfc670b-8fa3-4d5f-829f-c6e75e358320', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 101, null, '6e7f0ea4-f601-4b0e-865e-cb888dc75b31');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('8d0c015e-49a6-476e-ba70-fe6639bff37b', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 102, null, 'c962727c-89ad-4e6b-b222-c4e91b114c03');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('5b60c6ae-a1cc-4427-aa08-af11d581e33e', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 103, null, 'd0ee5ba9-f499-49f8-b95f-a417903f4000');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('f16ec903-3670-45c9-93ff-abc45dce3ca4', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 104, null, 'e305034f-2803-48e7-b476-3bc455b32f44');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('98465567-e224-44cd-9d67-18100f8e4383', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 105, null, '99f2b5a2-a487-40d7-b559-ee8e97c6e03e');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('2bb4c173-4a24-48a5-a6e6-8b231e7eb84a', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 106, null, 'b78e8c55-93d3-4a27-ba0b-457fb9a8da03');

-- insert fleet
insert into mode_sections (id, created_at, updated_at, props, name)
values ('ffb0c0f2-839c-4be6-aa50-1d95e803f7ec', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"and","conditions":[{"type":"report","containsOtherValues":"(Commercial Inland Marine|Business Auto)","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"},{"type":"or","conditions":[{"type":"hasFleetFacts"},{"type":"report","contains":"(Vehicles|Drivers)","jsonPath":"$.submissions[0].files[*].file_type"}]}]}}}', 'Important Fleet Facts for multi-premises');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('d5edca90-bfe9-468e-ae5b-082242c351b5', now(), null, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', 200, null, 'ffb0c0f2-839c-4be6-aa50-1d95e803f7ec');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('df211d46-15ae-4dad-ab8f-381423e9d9e3', now(), null, 'ffb0c0f2-839c-4be6-aa50-1d95e803f7ec', 100, '5367b9d5-08d6-439e-a607-119668019aa8', null);

-- insert mode_element
insert into mode_elements (id, created_at, updated_at, mode_id, position, section_id, row_id)
values ('35122742-b5f9-4f3d-8f01-ae224a07e254', now(), null, 'bee6c9bf-92b3-403e-a53e-63f0b2f13e86', 200, '53c5e8aa-3889-4562-9719-dd5d83b6b47d', null);

-- Single Business
delete from mode_elements
where id='270d6127-f407-4d28-bb63-3d31801fdf5a';

-- Create a container section for all highlights section
insert into mode_sections (id, created_at, updated_at, props, name)
values ('8e8ff055-4990-49cb-9fe4-544ba709a0dc', now(), null, '{"strategy": {"type": "merge-rows-columns"}}', 'Single business conditional highlights');

-- Insert conditional rows
insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('6c2facd3-504f-41d7-b437-89fc996d34aa', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 100, null, '36c5d69f-3432-4a57-bfcb-6a422e1455e5');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('31f08e5e-25af-4f0d-9e89-43a431e6eea4', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 101, null, '6e7f0ea4-f601-4b0e-865e-cb888dc75b31');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('a7fda7d4-3e30-4f76-bb5f-1ffd121000c7', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 102, null, 'c962727c-89ad-4e6b-b222-c4e91b114c03');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('14c58b43-a1b4-43da-ab15-18bc334be533', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 103, null, 'd0ee5ba9-f499-49f8-b95f-a417903f4000');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('a68d6a57-432d-4dc3-98a8-143e7f3ff56c', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 104, null, 'e305034f-2803-48e7-b476-3bc455b32f44');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('6cff739b-3d0e-4b6a-8320-7827ad437eef', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 105, null, '99f2b5a2-a487-40d7-b559-ee8e97c6e03e');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('abd5d566-1dbb-40db-8d65-2f5f9d8f9216', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 106, null, 'b78e8c55-93d3-4a27-ba0b-457fb9a8da03');

-- insert fleet
insert into mode_sections (id, created_at, updated_at, props, name)
values ('677d655a-71fc-4c1c-ab59-10744448436f', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"and","conditions":[{"type":"report","containsOtherValues":"(Commercial Inland Marine|Business Auto)","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"},{"type":"or","conditions":[{"type":"hasFleetFacts"},{"type":"report","contains":"(Vehicles|Drivers)","jsonPath":"$.submissions[0].files[*].file_type"}]}]}}}', 'Important Fleet Facts for multi-premises');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('9b848b76-8c5d-4d46-9664-f6a04a85ef62', now(), null, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', 200, null, '677d655a-71fc-4c1c-ab59-10744448436f');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('46db2cf4-44a9-4b58-9e43-9e802ea3e4c8', now(), null, '677d655a-71fc-4c1c-ab59-10744448436f', 100, '5367b9d5-08d6-439e-a607-119668019aa8', null);

-- insert mode_element
insert into mode_elements (id, created_at, updated_at, mode_id, position, section_id, row_id)
values ('820c5e9f-afca-44db-aa92-dacc55cf706a', now(), null, 'a4430e79-3077-4f9e-88b9-0477ce7eb13b', 200, '8e8ff055-4990-49cb-9fe4-544ba709a0dc', null);

-- Management Liability
-- Create a container section for all highlights section
insert into mode_sections (id, created_at, updated_at, props, name)
values ('d712be86-1ffa-49c6-877a-4430375d1ba2', now(), null, '{"strategy": {"type": "merge-rows-columns"}}', 'Single business conditional highlights');

-- Insert conditional rows
insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('6a22f86d-fe5f-42a0-bc3e-dd9af8ffaf63', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 100, null, '36c5d69f-3432-4a57-bfcb-6a422e1455e5');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('1b2f2591-24bf-48ce-b5bf-ced0640a86b0', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 101, null, '6e7f0ea4-f601-4b0e-865e-cb888dc75b31');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('31f59370-a928-4330-b72d-1b57412df550', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 102, null, 'c962727c-89ad-4e6b-b222-c4e91b114c03');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('b0a9d702-ac98-4636-bb10-ee9362b283ea', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 103, null, 'd0ee5ba9-f499-49f8-b95f-a417903f4000');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('6764b0c1-b802-4549-9950-208e6b347299', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 104, null, 'e305034f-2803-48e7-b476-3bc455b32f44');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('f9acfe04-d366-475b-8275-4ac4f696b799', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 105, null, '99f2b5a2-a487-40d7-b559-ee8e97c6e03e');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('e17aa874-3757-424a-acc8-5263699cfc70', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 106, null, 'b78e8c55-93d3-4a27-ba0b-457fb9a8da03');

-- insert fleet
insert into mode_sections (id, created_at, updated_at, props, name)
values ('987edd1a-3f88-4d08-af6d-c19089e05d2a', now(), null, '{"strategy":{"type":"conditional","condition":{"type":"and","conditions":[{"type":"report","containsOtherValues":"(Commercial Inland Marine|Business Auto)","jsonPath":"$.submissions[0].coverages[0].coverage.display_name"},{"type":"or","conditions":[{"type":"hasFleetFacts"},{"type":"report","contains":"(Vehicles|Drivers)","jsonPath":"$.submissions[0].files[*].file_type"}]}]}}}', 'Important Fleet Facts for multi-premises');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('26ab8f35-560d-4a97-a58a-a8c49fc4b550', now(), null, 'd712be86-1ffa-49c6-877a-4430375d1ba2', 200, null, '987edd1a-3f88-4d08-af6d-c19089e05d2a');

insert into mode_section_elements (id, created_at, updated_at, section_id, position, child_row_id, child_section_id)
values ('74072072-3a7c-4f3b-bb78-314f39ddc44a', now(), null, '987edd1a-3f88-4d08-af6d-c19089e05d2a', 100, '5367b9d5-08d6-439e-a607-119668019aa8', null);

-- insert mode_element
insert into mode_elements (id, created_at, updated_at, mode_id, position, section_id, row_id)
values ('53f6b5af-d8f8-4de6-85bc-883cf92aa145', now(), null, '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 80, 'd712be86-1ffa-49c6-877a-4430375d1ba2', null);

    END IF;
END
$do$
    """)


def downgrade():
    pass

"""Adds employee summary tables

Revision ID: f6a000ce478f
Revises: 021663d1f10b
Create Date: 2020-09-04 18:17:20.436555+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f6a000ce478f"
down_revision = "021663d1f10b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "employees_summary",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["id"],
            ["metric.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_employees_summary_id"), "employees_summary", ["id"], unique=False)
    op.create_table(
        "role_summary",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("employees_summary_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("role", sa.String(), nullable=False),
        sa.Column("naics_code", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["employees_summary_id"],
            ["employees_summary.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_role_summary_employees_summary_id"), "role_summary", ["employees_summary_id"], unique=False
    )
    op.create_table(
        "role_summary_business",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("category_summary_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("business_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["category_summary_id"],
            ["role_summary.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_role_summary_business_business_id"), "role_summary_business", ["business_id"], unique=False
    )
    op.create_index(
        op.f("ix_role_summary_business_category_summary_id"),
        "role_summary_business",
        ["category_summary_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_role_summary_business_category_summary_id"), table_name="role_summary_business")
    op.drop_index(op.f("ix_role_summary_business_business_id"), table_name="role_summary_business")
    op.drop_table("role_summary_business")
    op.drop_index(op.f("ix_role_summary_employees_summary_id"), table_name="role_summary")
    op.drop_table("role_summary")
    op.drop_index(op.f("ix_employees_summary_id"), table_name="employees_summary")
    op.drop_table("employees_summary")
    # ### end Alembic commands ###

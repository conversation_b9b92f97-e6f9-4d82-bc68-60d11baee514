"""Adds quintiles to sum and mean metrics

Revision ID: f0aab33a9e01
Revises: cc0967efab77
Create Date: 2021-07-28 15:55:53.725573+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f0aab33a9e01"
down_revision = "cc0967efab77"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mean", sa.Column("interquintile_numbers", postgresql.ARRAY(sa.Integer()), nullable=True))
    op.add_column("mean", sa.Column("quintiles", postgresql.ARRAY(sa.Float()), nullable=True))
    op.add_column("sum", sa.Column("interquartile_numbers", postgresql.ARRAY(sa.Integer()), nullable=True))
    op.add_column("sum", sa.Column("interquintile_numbers", postgresql.ARRAY(sa.Integer()), nullable=True))
    op.add_column("sum", sa.Column("quintiles", postgresql.ARRAY(sa.Float()), nullable=True))


def downgrade():
    op.drop_column("sum", "quintiles")
    op.drop_column("sum", "interquintile_numbers")
    op.drop_column("sum", "interquartile_numbers")
    op.drop_column("mean", "quintiles")
    op.drop_column("mean", "interquintile_numbers")

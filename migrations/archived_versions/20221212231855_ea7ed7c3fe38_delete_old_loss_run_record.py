"""Delete Old Loss Run Record

Revision ID: ea7ed7c3fe38
Revises: 08b4d8a88ad8
Create Date: 2022-12-11  23:18:55.145314+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ea7ed7c3fe38"
down_revision = "08b4d8a88ad8"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""delete from loss where submission_id='a1b2d488-7392-4933-95a3-b7981f03249f';""")


def downgrade():
    pass

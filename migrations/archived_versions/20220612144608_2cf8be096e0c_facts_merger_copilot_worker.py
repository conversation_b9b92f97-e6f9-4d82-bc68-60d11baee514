"""Facts merger copilot worker

Revision ID: 2cf8be096e0c
Revises: ce27d5db857b
Create Date: 2022-06-12 14:46:08.915216+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2cf8be096e0c"
down_revision = "ce27d5db857b"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE copilotworkerexecutiontype ADD VALUE IF NOT EXISTS 'MERGE_BUSINESSES_FACTS';""")


def downgrade():
    pass

"""Update incorrect form_field labels

Revision ID: a9f987ae7be0
Revises: e36b7f9ddd4e
Create Date: 2021-08-25 15:54:00.114274+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "a9f987ae7be0"
down_revision = "e36b7f9ddd4e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        "UPDATE form_field set label='SQUARE FOOTAGE' where id='1cc58ca1-c591-4825-a14c-c569abe49fdd' and label='SQUARE"
        " FOOTAGE (Used: Total Area)'"
    )
    conn.execute(
        "UPDATE form_field set label='BI VALUE' where id='9e5d4057-f234-42a2-b58d-02feb36ca1e7' and label='BI VALUE"
        " (Used: BI w/ EE)'"
    )
    conn.execute(
        "UPDATE form_field set label='# of STORIES' where id='b2fd2e25-0d60-4ac9-91e1-31c47c0d3345' and label='# of"
        " STORIES (Used: undefined)'"
    )
    conn.execute(
        "UPDATE form_field set label='TIV' where id='15de4ef3-1827-410b-bee9-5876b2bf37fc' and label='TIV (Used: TIV)'"
    )
    conn.execute(
        "UPDATE form_field set label='CONTENTS VALUE' where id='509ab581-4bb7-4639-9ec1-972f3a3839b1' and"
        " label='CONTENTS VALUE (Used: BPP)'"
    )
    conn.execute(
        "UPDATE form_field set label='CONSTRUCTION CLASS' where id='e1866b63-bec3-468c-aa2f-93a1946a8426' and"
        " label='CONSTRUCTION CLASS (Used: Construction)'"
    )


def downgrade():
    pass

"""Add dry run recommendation tables

Revision ID: 774c5533ba23
Revises: 66742d6e7547
Create Date: 2021-03-11 17:36:45.980318+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "774c5533ba23"
down_revision = "9327b7195d8d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "dry_run_recommendations",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("expected_value", sa.Float(), nullable=True),
        sa.Column(
            "recommendation_action",
            postgresql.ENUM("ACCEPT", "DECLINE", "REFER", name="recommendationtype", create_type=False),
            nullable=True,
        ),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("rule_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(["rule_id"], ["recommendation_rule.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_dry_run_recommendations_rule_id"), "dry_run_recommendations", ["rule_id"], unique=False)
    op.create_index(
        op.f("ix_dry_run_recommendations_submission_id"), "dry_run_recommendations", ["submission_id"], unique=False
    )
    op.add_column(
        "recommendation_explanation",
        sa.Column("dry_run_recommendation_id", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.create_index(
        op.f("ix_recommendation_explanation_dry_run_recommendation_id"),
        "recommendation_explanation",
        ["dry_run_recommendation_id"],
        unique=False,
    )
    op.create_foreign_key(
        "recommendation_explanation_dry_run_id_fkey",
        "recommendation_explanation",
        "dry_run_recommendations",
        ["dry_run_recommendation_id"],
        ["id"],
        ondelete="CASCADE",
    )

    # op.alter_column('recommendation_explanation', 'submission_id',
    #            existing_type=postgresql.UUID(),
    #            nullable=True)
    # Executing as RAW SQL with statement timeout
    op.execute("""
    BEGIN; 
    SET LOCAL statement_timeout = 1000;
    ALTER TABLE recommendation_explanation ALTER COLUMN submission_id DROP NOT NULL;
    COMMIT;
    """)


def downgrade():
    # op.alter_column('recommendation_explanation', 'submission_id',
    #            existing_type=postgresql.UUID(),
    #            nullable=False)
    # Executing as RAW SQL with statement timeout
    op.execute("""
    BEGIN; 
    SET LOCAL statement_timeout = 1000;
    ALTER TABLE recommendation_explanation ALTER COLUMN submission_id SET NOT NULL;
    COMMIT;
    """)
    op.drop_constraint("recommendation_explanation_dry_run_id_fkey", "recommendation_explanation", type_="foreignkey")
    op.drop_index(
        op.f("ix_recommendation_explanation_dry_run_recommendation_id"), table_name="recommendation_explanation"
    )
    op.drop_column("recommendation_explanation", "dry_run_recommendation_id")
    op.drop_index(op.f("ix_dry_run_recommendations_submission_id"), table_name="dry_run_recommendations")
    op.drop_index(op.f("ix_dry_run_recommendations_rule_id"), table_name="dry_run_recommendations")
    op.drop_table("dry_run_recommendations")

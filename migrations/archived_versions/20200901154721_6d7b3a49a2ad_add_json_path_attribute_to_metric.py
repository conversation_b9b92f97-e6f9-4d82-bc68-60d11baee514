"""add Json path attribute to metric

Revision ID: 6d7b3a49a2ad
Revises: 2fb0fa53cd54
Create Date: 2020-09-01 15:47:21.526794

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6d7b3a49a2ad"
down_revision = "2fb0fa53cd54"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("metric", sa.Column("path", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("metric", "path")
    # ### end Alembic commands ###

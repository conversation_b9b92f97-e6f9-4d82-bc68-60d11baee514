"""Migrate Flight Plan to Kalepa MGA

Revision ID: e39bacd13ef9
Revises: b3f8dd188906
Create Date: 2022-04-19 08:00:40.661963+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "e39bacd13ef9"
down_revision = "b3f8dd188906"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""update recommendation_rule set owner_organization_id = 7 where owner_organization_id = 3;""")
    conn.execute(
        """update users set organization_id = 7 where email = '<EMAIL>' or email = '<EMAIL>';"""
    )


def downgrade():
    conn = op.get_bind()
    conn.execute("""update recommendation_rule set owner_organization_id = 3 where owner_organization_id = 7;""")
    conn.execute(
        """update users set organization_id = 3 where email = '<EMAIL>' or email = '<EMAIL>';"""
    )

"""Add display name to coverages

Revision ID: ed1018276169
Revises: 0ae3600e2d29
Create Date: 2020-11-09 12:20:39.187552+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed1018276169"
down_revision = "a2abd7fd1ed4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    # Step 1: Assign coverage_id to coverage with the same name but min(created_at)
    conn.execute("""
        UPDATE submission_coverages sc SET coverage_id =
            (SELECT id FROM
                (SELECT name, MIN(created_at) as created_at FROM coverages GROUP BY name) c1
            JOIN coverages c2
            ON c1.name = c2.name
            AND c1.created_at = c2.created_at
            WHERE c1.name =
                (SELECT name FROM coverages WHERE id = sc.coverage_id));
    """)
    # Step 2: Out of coverages with the same name, leave only those with the min(created_at)
    conn.execute("""
        DELETE FROM coverages WHERE id NOT IN
            (SELECT id FROM
                (SELECT name, MIN(created_at) as created_at FROM coverages GROUP BY name) c1
            JOIN coverages c2
            ON c1.name = c2.name
            AND c1.created_at = c2.created_at)
    """)
    # Step 3: Populate display_name
    op.add_column("coverages", sa.Column("display_name", sa.String(), nullable=True))

    conn.execute(
        """UPDATE coverages SET display_name = 'Accounts Receivable / Valuable Papers' where name = 'accountsReceivable'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Additional Interest Schedule' where name = 'additionalInterestSchedule'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Additional Premises Information Schedule' where name = 'additionalPremisesInfoSchedule'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Apartment Building Supplement' where name = 'apartmentBuildingSupplement'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Boiler & Machinery' where name = 'boilerMachinery'""")
    conn.execute("""UPDATE coverages SET display_name = 'Business Auto' where name = 'businessAuto'""")
    conn.execute("""UPDATE coverages SET display_name = 'Business Owners' where name = 'businessOwners'""")
    conn.execute(
        """UPDATE coverages SET display_name = 'Commercial Inland Marine' where name = 'commercialInlandMarine'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Condo ASSN Bylaws (For D&O Coverage Only)' where name = 'condoAssnBylaws'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Contractors Supplement' where name = 'contractorsSupplement'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Coverages Schedule' where name = 'coveragesSchedule'""")
    conn.execute("""UPDATE coverages SET display_name = 'Crime' where name = 'crime'""")
    conn.execute("""UPDATE coverages SET display_name = 'Cyber and Privacy' where name = 'cyberPrivacy'""")
    conn.execute("""UPDATE coverages SET display_name = 'Dealers Section' where name = 'dealersSection'""")
    conn.execute(
        """UPDATE coverages SET display_name = 'Driver Information Schedule' where name = 'driverInfoSchedule'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Electronic Data Processing Section' where name = 'electronicDataProcessingSection'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Fiduciary Liability' where name = 'fiduciaryLiability'""")
    conn.execute("""UPDATE coverages SET display_name = 'Garage and Dealers' where name = 'garageDealers'""")
    conn.execute(
        """UPDATE coverages SET display_name = 'Commercial General Liability' where name = 'generalLiability'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Glass and Sign Section' where name = 'glassSignSection'""")
    conn.execute(
        """UPDATE coverages SET display_name = 'Hotel / Motel Supplement' where name = 'hotelMotelSupplement'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Installation / Builders Risk Section' where name = 'installationBuildersRiskSection'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'International Liability Exposure Supplement' where name = 'internationalLiabilityExposureSupplement'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'International Property Exposure Supplement' where name = 'internationalPropertyExposureSupplement'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Liquor Liability' where name = 'liquorLiability'""")
    conn.execute("""UPDATE coverages SET display_name = 'Motor Carriers' where name = 'motorCarriers'""")
    conn.execute("""UPDATE coverages SET display_name = 'Open Cargo Section' where name = 'openCargoSection'""")
    conn.execute(
        """UPDATE coverages SET display_name = 'Professional Liability Supplement' where name = 'professionalLiabilitySupplement'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Commercial Property' where name = 'property'""")
    conn.execute(
        """UPDATE coverages SET display_name = 'Restaurant / Tavern Supplement' where name = 'restaurantTavernSupplement'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'Statement / Schedule of Values' where name = 'statementScheduleOfValues'"""
    )
    conn.execute(
        """UPDATE coverages SET display_name = 'State Supplement (if applicable)' where name = 'stateSupplement'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Truckers' where name = 'truckers'""")
    conn.execute("""UPDATE coverages SET display_name = 'Umbrella' where name = 'umbrella'""")
    conn.execute(
        """UPDATE coverages SET display_name = 'Vacant Building Supplement' where name = 'vacantBuildingSupplement'"""
    )
    conn.execute("""UPDATE coverages SET display_name = 'Vehicle Schedule' where name = 'vehicleSchedule'""")
    conn.execute("""UPDATE coverages SET display_name = 'Workers Compensation' where name = 'workersComp'""")
    conn.execute(
        """INSERT INTO coverages VALUES ('aba4f871-6e8e-4514-869e-dcbc5f5eff04', now(), null, 'package', 'Package')"""
    )

    op.create_unique_constraint(None, "coverages", ["display_name"])
    op.create_unique_constraint(None, "coverages", ["name"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("coverages", "display_name")
    # ### end Alembic commands ###

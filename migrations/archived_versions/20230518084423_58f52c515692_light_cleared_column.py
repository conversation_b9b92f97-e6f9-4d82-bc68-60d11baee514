"""light cleared column

Revision ID: 58f52c515692
Revises: de8e197d72c7
Create Date: 2023-05-18 08:44:23.325595+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "58f52c515692"
down_revision = "de8e197d72c7"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("light_cleared", sa.<PERSON>(), nullable=True))
    conn = op.get_bind()
    conn.execute("UPDATE submission_clearing_issue set is_light = False;")
    op.alter_column("submission_clearing_issue", "is_light", existing_type=sa.BOOLEAN(), nullable=False)


def downgrade():
    pass

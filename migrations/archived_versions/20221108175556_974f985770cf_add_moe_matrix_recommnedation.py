"""Add MoE matrix recommnedation

Revision ID: 974f985770cf
Revises: 8174c4414c83
Create Date: 2022-11-08 17:55:56.623544+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "974f985770cf"
down_revision = "8174c4414c83"
branch_labels = None
depends_on = None

rule = "".join("""
{
   "conditions":{
      "all":[
         {
            "name": "moe_lookup_table",
            "operator": "is_true",
            "value": true
         }
      ]
   },
   "actions": []
}
""".split())


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
        insert into recommendation_rule
        (id, created_at, description, definition, owner_organization_id, is_active, is_immutable)
        values (uuid_generate_v4(), now(),'Risk score matrix', '{rule}', 11, false, true);
    """)
    conn.execute(f"""
        insert into recommendation_rule
        (id, created_at, description, definition, owner_organization_id, is_active, is_immutable)
        values (uuid_generate_v4(), now(),'Risk score matrix', '{rule}', 12, false, true);
    """)


def downgrade():
    pass

"""Makes Submission name non-nullable

Revision ID: 945fdea919e3
Revises: 75f242e56356
Create Date: 2020-11-21 18:05:30.097275+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "945fdea919e3"
down_revision = "75f242e56356"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("update submissions set name = '' where name is null;")
    op.alter_column("submissions", "name", existing_type=sa.VARCHAR(), nullable=False)


def downgrade():
    op.alter_column("submissions", "name", existing_type=sa.VARCHAR(), nullable=True)

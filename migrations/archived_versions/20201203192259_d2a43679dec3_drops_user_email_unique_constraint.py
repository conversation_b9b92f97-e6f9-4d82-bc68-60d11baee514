"""Drops user email unique constraint

Revision ID: d2a43679dec3
Revises: 1b657690e4e2
Create Date: 2020-12-03 19:22:59.857745+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d2a43679dec3"
down_revision = "1b657690e4e2"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
alter table user_fields_review drop constraint user_fields_review_email_fkey;
alter table reports drop constraint reports_email_fkey;
alter table users drop constraint users_email_key;
""")


def downgrade():
    conn = op.get_bind()
    conn.execute("""
alter table users add constraint users_email_key unique (email);
alter table user_fields_review
	add constraint user_fields_review_email_fkey
		foreign key (email) references users (email);
alter table reports
	add constraint reports_email_fkey
		foreign key (email) references users (email);
""")

"""Fixes user

Revision ID: 724b087a5262
Revises: d7f610d9bcec
Create Date: 2021-04-19 21:04:41.224518+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "724b087a5262"
down_revision = "d7f610d9bcec"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users
    set external_id = null
    where id = 102 and email = '<EMAIL>';
    """)

    conn.execute("""
    update users
    set external_id = null
    where id = 106 and email = '<EMAIL>';
    """)

    conn.execute("""
    update users
    set email = '<EMAIL>'
    where id = 49 and email = '<EMAIL>';
    """)


def downgrade():
    pass

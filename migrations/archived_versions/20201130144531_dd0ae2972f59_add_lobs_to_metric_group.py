"""add LoBs to metric group

Revision ID: dd0ae2972f59
Revises: 71566a6c2a7e
Create Date: 2020-11-30 14:45:31.573653+00:00

"""
import uuid

from alembic import op

# revision identifiers, used by Alembic.

revision = "dd0ae2972f59"
down_revision = "c52eaa6de9bb"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(f"""INSERT INTO metric_group(id, display_name)
SELECT '{uuid.uuid4()}', 'General Liability'
WHERE NOT EXISTS(SELECT 1 FROM metric_group WHERE metric_group.display_name = 'General Liability');
""")
    op.execute(f"""INSERT INTO metric_group(id, display_name)
SELECT '{uuid.uuid4()}', 'Property'
WHERE NOT EXISTS(SELECT 1 FROM metric_group WHERE metric_group.display_name = 'Property');
""")
    op.execute(f"""INSERT INTO metric_group(id, display_name)
SELECT '{uuid.uuid4()}', 'Crime'
WHERE NOT EXISTS(SELECT 1 FROM metric_group WHERE metric_group.display_name = 'Crime');
""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'General Liability' LIMIT 1)
WHERE display_name IN (
'Cooking Types',
'Has Adult Entertainment',
'Serves Alcohol',
'Entertainment Types',
'Has Bottle Service',
'Has Bouncers',
'Has BYOB',
'Has Catering',
'Has Dancing',
'Has Delivery',
'Has Outdoor Seating',
'Has Waiter Service',
'Performance Types',
'Has Infestation',
'Has Roof Access',
'Has Security Guards',
'Parking Types',
'Has Balconies',
'Hotel Amenities',
'Hotel Average Rating',
'Hotel: Number of Rooms',
'Hotel: Maximum Price',
'Hotel: Minimum Price',
'Hotel Class',
'Distance to Hospital',
'Distance to Urgent Care',
'Low Income Units',
'Low Income Units Fraction',
'Health Inspection Score',
'Employees',
'Websites'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Crime' LIMIT 1)
WHERE display_name IN (
'Crime Score: Overall',
'Crime Score: Assault',
'Crime Score: Burglary',
'Crime Score: Drug and Alcohol Deaths',
'Crime Score: Larceny',
'Crime Score: Motor Vehicle Theft',
'Crime Score: Murder',
'Crime Score: Rape',
'Crime Score: Robbery',
'Prostitution Risk',
'Crime in Premises Risk',
'Distance to Police Station'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Property' LIMIT 1)
WHERE display_name IN (
'Has Swimming Pool',
'Building Construction Type',
'Elevation',
'Building Age',
'Building Stories',
'Building Area',
'Coastal Storm Surge',
'Earthquake',
'Fire Protection Class',
'Flood', 
'Hail',
'Lightning',
'Sinkhole',
'Snow Load',
'Tornado',
'Wildfire',
'Distance to Coast',
'Distance to Fire Station',
'Distance to Hydrant',
'Distance to Toxic Facility',
'Lot Size'
    );""")


def downgrade():
    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Equipment Insights')
WHERE display_name IN (
                       'Cooking Types'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Operations Insights')
WHERE display_name IN (
'Has Adult Entertainment',
'Serves Alcohol',
'Entertainment Types',
'Has Bottle Service',
'Has Bouncers',
'Has BYOB',
'Has Catering',
'Has Dancing',
'Has Delivery',
'Has Outdoor Seating',
'Has Waiter Service',
'Performance Types',
'Has Infestation',
'Has Roof Access',
'Has Security Guards',
'Parking Types',
'Has Balconies',
'Hotel Amenities',
'Has Swimming Pool',
'Hotel Average Rating',
'Hotel: Number of Rooms',
'Hotel: Maximum Price',
'Hotel: Minimum Price',
'Hotel Class',
'Low Income Units',
'Low Income Units Fraction'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Property Insights')
WHERE display_name IN (
'Building Construction Type',
'Building Age',
'Building Stories',
'Building Area',
'Lot Size'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Catastrophe Risks')
WHERE display_name IN (
'Elevation',
'Coastal Storm Surge',
'Earthquake',
'Flood', 
'Hail',
'Lightning',
'Sinkhole',
'Snow Load',
'Tornado',
'Wildfire',
'Distance to Coast'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Crime Risks')
WHERE display_name IN (
'Crime Score: Overall',
'Crime Score: Assault',
'Crime Score: Burglary',
'Crime Score: Drug and Alcohol Deaths',
'Crime Score: Larceny',
'Crime Score: Motor Vehicle Theft',
'Crime Score: Murder',
'Crime Score: Rape',
'Crime Score: Robbery',
'Prostitution Risk',
'Crime in Premises Risk',
'Distance to Police Station'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Fire Risks')
WHERE display_name IN (
'Fire Protection Class',
'Distance to Fire Station',
'Distance to Hydrant'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'Other Risks')
WHERE display_name IN (
'Distance to Hospital',
'Distance to Toxic Facility',
'Distance to Urgent Care',
'Health Inspection Score'
    );""")

    op.execute("""UPDATE metric_config
SET metric_group_id=(SELECT id FROM metric_group WHERE metric_group.display_name = 'General')
WHERE display_name IN (
'Employees',
'Websites'
    );""")

    op.execute(f"DELETE FROM metric_group WHERE display_name = 'General Liability';")
    op.execute(f"DELETE FROM metric_group WHERE display_name = 'Property';")
    op.execute(f"DELETE FROM metric_group WHERE display_name = 'Crime';")

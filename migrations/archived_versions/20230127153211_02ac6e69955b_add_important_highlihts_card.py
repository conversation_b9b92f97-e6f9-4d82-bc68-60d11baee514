"""Add important highlihts card

Revision ID: 02ac6e69955b
Revises: 759bf92f3114
Create Date: 2023-01-27 15:32:11.863456+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "02ac6e69955b"
down_revision = "759bf92f3114"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update mode_cards
    set type='IMPORTANT_HIGHLIGHTS_CARD', props='{"columns":4,"highlights":[{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"LEGAL_FILING","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"Legal filings","cardId":"f9f94a42-f555-47b8-946c-9be1b6b743da","redirectLinkLabel":"Go to legal filings","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"OSHA_VIOLATION","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"Osha violations","cardId":"974e754f-553c-4da8-9356-e83a9d80e78b","redirectLinkLabel":"Go to Osha violations","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"EPA_INSPECTION","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"EPA","cardId":"dd5fa1a1-5d4f-45fb-ac68-5b99d3f62c1d","redirectLinkLabel":"Go to EPA Inspections","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"FACT","parentType":"BUSINESS","factSubtypes":["PROJECT_USE_OF_EIFS","PROJECT_SCAFFOLDING","PROJECT_MOLD_REMOVAL","PROJECT_ROOF_WORK","PROJECT_CRANE_WORK","PROJECT_DEMOLITION_WORK","PROJECT_BLASTING_WORK","PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL","PROJECT_EXCAVATION_WORK","PROJECT_BELOW_GRADE","PROJECT_DEPTH_OF_WORK","PROJECT_HEIGHT_IN_FT","PROJECT_HEIGHT_IN_STORIES","PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT","PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"],"businessSelect":"generalContractor"},"mapper":"numberOfPositiveFacts"},"label":"High-risk exposures","cardId":"fc9c391b-b490-4589-bae5-5fb15b48bd09","redirectLinkLabel":"Go to High-risk exposures","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"}]}'
    where id='59d67a6c-b911-4790-b598-da276d2ff290';
    
    update mode_cards
    set type='IMPORTANT_HIGHLIGHTS_CARD', props='{"columns":4,"highlights":[{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"LEGAL_FILING","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"Legal filings","cardId":"0b596b51-6f73-41ef-b55a-961c05f30d0d","redirectLinkLabel":"Go to legal filings","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"OSHA_VIOLATION","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"Osha violations","cardId":"11b3e3d4-3124-41b2-9e19-7bf98ce8aced","redirectLinkLabel":"Go to Osha violations","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"EPA_INSPECTION","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"EPA","cardId":"82ba32a1-052f-4621-9ae8-143ac7915bb0","redirectLinkLabel":"Go to EPA Inspections","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"FACT","parentType":"BUSINESS","factSubtypes":["PROJECT_USE_OF_EIFS","PROJECT_SCAFFOLDING","PROJECT_MOLD_REMOVAL","PROJECT_ROOF_WORK","PROJECT_CRANE_WORK","PROJECT_DEMOLITION_WORK","PROJECT_BLASTING_WORK","PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL","PROJECT_EXCAVATION_WORK","PROJECT_BELOW_GRADE","PROJECT_DEPTH_OF_WORK","PROJECT_HEIGHT_IN_FT","PROJECT_HEIGHT_IN_STORIES","PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT","PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"],"businessSelect":"generalContractor"},"mapper":"numberOfPositiveFacts"},"label":"High-risk exposures","cardId":"91da8d98-fdbb-4816-93e7-18ccb4951054","redirectLinkLabel":"Go to High-risk exposures","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"}]}'
    where id='052cab3f-8d48-45b0-8397-4bc0b66e583f';
    """)


def downgrade():
    pass

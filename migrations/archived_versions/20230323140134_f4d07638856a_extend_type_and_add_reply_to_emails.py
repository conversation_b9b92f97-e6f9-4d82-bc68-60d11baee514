"""Extend type and add reply to emails

Revision ID: f4d07638856a
Revises: 270cf095d26d
Create Date: 2023-03-23 14:01:34.547527+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f4d07638856a"
down_revision = "270cf095d26d"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.add_column("emails", sa.Column("email_reply_to", sa.String(), nullable=True))
        op.execute("""ALTER TYPE emailtype ADD VALUE IF NOT EXISTS 'ROOT';""")


def downgrade():
    op.drop_column("emails", "email_reply_to")

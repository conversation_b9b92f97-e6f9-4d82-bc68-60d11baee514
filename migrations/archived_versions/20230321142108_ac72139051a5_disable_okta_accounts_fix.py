"""Disable old accounts

Revision ID: ac72139051a5
Revises: 01ce7caab645
Create Date: 2023-03-21 14:21:08.008617+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ac72139051a5"
down_revision = "01ce7caab645"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # These are old accounts related to OKTA (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>)
    conn.execute("""
        delete from report_permissions 
        where grantee_user_id in (165,166,226,231)
    """)


def downgrade():
    pass

"""Migrate due date

Revision ID: 3e086265fb16
Revises: 2af2857180ab
Create Date: 2021-02-17 07:13:50.822195+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3e086265fb16"
down_revision = "2af2857180ab"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""update submissions set due_date = proposed_effective_date, proposed_effective_date = null 
                    where due_date is null and proposed_effective_date is not null;""")


def downgrade():
    pass

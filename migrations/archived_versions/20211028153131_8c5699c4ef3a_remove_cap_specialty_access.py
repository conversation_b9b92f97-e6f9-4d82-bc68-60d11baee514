"""remove cap specialty access

Revision ID: 8c5699c4ef3a
Revises: f823c62c4568
Create Date: 2021-10-28 15:31:31.893682+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8c5699c4ef3a"
down_revision = "f823c62c4568"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(
            """delete from audit_trails where user_external_id in (select external_id from users where email like '%capspecialty%');"""
        )
        op.execute(
            """update users set email = concat('defunct', id, email), organization_id=null, external_id=null where email like '%capspecialty%';"""
        )


def downgrade():
    pass

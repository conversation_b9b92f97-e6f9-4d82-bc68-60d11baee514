"""Add snapshot_id index for Report

Revision ID: b99ec86949de
Revises: 87b701a2c59f
Create Date: 2023-06-06 17:53:33.844260+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b99ec86949de"
down_revision = "87b701a2c59f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '900 s';")  # 15 min
        op.execute(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_submission_businesses_ers_snapshot_id ON submission_businesses"
            " (ers_snapshot_id);"
        )


def downgrade():
    pass

"""Add processes card

Revision ID: 9a5926520ef4
Revises: b5f0247191c0
Create Date: 2023-04-24 11:23:53.391076+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9a5926520ef4"
down_revision = "b5f0247191c0"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    DO
    $do$
    BEGIN
        IF EXISTS (select * from mode_columns where id='a1893117-92f8-442d-840b-0e027a6f54b8') AND NOT EXISTS (select * from mode_cards where id='4c5d957e-133e-438d-bfea-7f5b530a730f') THEN
            insert into mode_cards (id, column_id, title, position, type, card_id, props) values
            (
                '4c5d957e-133e-438d-bfea-7f5b530a730f',
                'a1893117-92f8-442d-840b-0e027a6f54b8',
                'Submission Backend Processes',
                30,
                'SUBMISSION_BACKEND_PROCESSES_CARD',
                'submission-backend-processes',
                null
            );
        END IF;
    END
    $do$
    """)


def downgrade():
    pass

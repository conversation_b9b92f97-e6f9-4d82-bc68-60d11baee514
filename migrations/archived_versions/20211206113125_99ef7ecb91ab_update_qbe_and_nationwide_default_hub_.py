"""Update QBE and Nationwide default hub template

Revision ID: 99ef7ecb91ab
Revises: 8f42cc55924e
Create Date: 2021-12-06 11:31:25.367593+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "99ef7ecb91ab"
down_revision = "8f42cc55924e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # Set default filter template for all QBE and Nationwide users to include the “owned by me” filter (only for those that havent yet).
    conn.execute("""
        insert into hub_templates (id, created_at, updated_at, user_id, name, is_default, template)
            select
                uuid_generate_v4(),
                now(),
                now(),
                id,
                'Reports Owned By Me',
                true,
                '{"sort": "CREATED_AT", "agent": "", "order": "NEWEST", "agency": "", "dateTo": "", "search": "", "dateFrom": "", "dateType": "CREATED_AT", "ownership": "OWNED_BY_ME", "recommendedAction": "ALL", "reasonForDeclining": ""}'
            from users 
            where (organization_id = 5 or organization_id = 6) and id not in (select user_id from hub_templates where is_default is true);
    """)


def downgrade():
    pass

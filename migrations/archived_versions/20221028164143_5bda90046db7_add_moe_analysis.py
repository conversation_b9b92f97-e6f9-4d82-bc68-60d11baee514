"""Add moe-analysis

Revision ID: 5bda90046db7
Revises: aeb58ef7ca02
Create Date: 2022-10-28 16:41:43.456731+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5bda90046db7"
down_revision = "aeb58ef7ca02"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        insert into organization values
        (12, 'MoE Analysis', null, null, INTERVAL '90 day', null, null, null, false)
    """)
    op.execute("""
        INSERT INTO settings (
            id, created_at, updated_at, organization_id, is_map_enabled_by_default, support_email
        )
        values (
            uuid_generate_v4(), now(), null, 12, false, '<EMAIL>'
        )
    """)
    op.execute("""
        insert into users values
        (default, '<EMAIL>', null, 'auth0|635c072889c96975293e6789', 12, 'manager', '<EMAIL>', null, null, now(), null, false)
    """)


def downgrade():
    pass

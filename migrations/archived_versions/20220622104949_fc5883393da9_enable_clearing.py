"""Enable clearing

Revision ID: fc5883393da9
Revises: 9deae35943e5
Create Date: 2022-06-22 10:49:49.446596+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fc5883393da9"
down_revision = "9deae35943e5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""update settings set is_clearing_enabled = true where organization_id = 7;""")


def downgrade():
    pass

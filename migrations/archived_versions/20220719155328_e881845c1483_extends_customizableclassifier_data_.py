"""Extends CustomizableClassifier data models to support QA

Revision ID: e881845c1483
Revises: b1512cab3ade
Create Date: 2022-07-19 15:53:28.225089+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e881845c1483"
down_revision = "b1512cab3ade"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE customizableclassifiertype ADD VALUE IF NOT EXISTS 'QUESTION_ANSWERING';""")
        op.execute(
            """CREATE TYPE questionansweringclassifiertype AS ENUM ('MRM8488_DEBERTA_V3_BASE_FINETUNED_SQUAD2', 'DEEPSET_ROBERTA_LARGE_SQUAD2', 'DEEPSET_ROBERTA_BASE_SQUAD2');"""
        )
    op.add_column(
        "customizable_classifiers",
        sa.Column(
            "qa_classifier_type",
            sa.Enum(
                "MRM8488_DEBERTA_V3_BASE_FINETUNED_SQUAD2",
                "DEEPSET_ROBERTA_BASE_SQUAD2",
                "DEEPSET_ROBERTA_LARGE_SQUAD2",
                name="questionansweringclassifiertype",
            ),
            nullable=True,
        ),
    )
    op.add_column("customizable_classifiers", sa.Column("questions", sa.ARRAY(sa.String()), nullable=True))


def downgrade():
    op.drop_column("customizable_classifiers", "questions")
    op.drop_column("customizable_classifiers", "qa_classifier_type")

"""eng_6727_naics_code_nullable_title

Revision ID: 421832c5df4d
Revises: 038c66d5d16e
Create Date: 2022-01-10 11:06:28.197538+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "421832c5df4d"
down_revision = "038c66d5d16e"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("naics_code", "title", existing_type=sa.VARCHAR(), nullable=True)


def downgrade():
    op.alter_column("naics_code", "title", existing_type=sa.VARCHAR(), nullable=False)

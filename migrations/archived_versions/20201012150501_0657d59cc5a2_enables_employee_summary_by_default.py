"""Enables employee summary by default

Revision ID: 0657d59cc5a2
Revises: a6c6bab31ede
Create Date: 2020-10-12 15:05:01.012579+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0657d59cc5a2"
down_revision = "a6c6bab31ede"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT
    INTO
    summary_preference(id, display_name, group_display_name, is_default)
    VALUES('8efb112b-88ad-428e-92c3-ddc392569762', 'Employees', 'General', true)
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("DELETE FROM summary_preference WHERE id='8efb112b-88ad-428e-92c3-ddc392569762';")

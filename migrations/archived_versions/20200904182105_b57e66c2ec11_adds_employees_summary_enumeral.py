"""Adds employees summary enumeral

Revision ID: b57e66c2ec11
Revises: f6a000ce478f
Create Date: 2020-09-04 18:21:05.290472+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b57e66c2ec11"
down_revision = "f6a000ce478f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE metrictype ADD VALUE IF NOT EXISTS 'EMPLOYEES_SUMMARY';""")


def downgrade():
    pass

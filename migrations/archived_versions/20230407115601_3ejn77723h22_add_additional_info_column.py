"""Adding additional_info column to files

Revision ID: 3ejn77723h22
Revises: a1429fa1dc9d
Create Date: 2023-04-07 11:56:01.217500+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
from sqlalchemy.engine.reflection import Inspector
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3ejn77723h22"
down_revision = "acd4d0729e41"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    columns = inspector.get_columns("files")
    if "files" not in columns:
        op.add_column("files", sa.Column("additional_info", postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade():
    op.drop_column("files", "additional_info")

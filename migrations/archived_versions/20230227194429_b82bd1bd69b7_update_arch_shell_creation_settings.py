"""Update Arch Shell Creation Settings

Revision ID: b82bd1bd69b7
Revises: f2ac08e79153
Create Date: 2023-02-27 19:44:29.554225+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b82bd1bd69b7"
down_revision = "f2ac08e79153"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    arch_config = (
        '{"handlers": [{"config": {}, "handler_type": "POLICY_HANDLER"}, {"config": {}, "handler_type":'
        ' "SUBMISSION_HANDLER"}, {"config": {"remove_non_matching_coverages": true}, "handler_type":'
        ' "COVERAGE_HANDLER"}, {"config": {"default_primary_naics": {}, "remove_previous_assignees": true},'
        ' "handler_type": "UNDERWRITER_HANDLER"}], "max_attempts": 0, "submission_matcher_config":'
        ' {"shell_owner_email": "<EMAIL>", "fuzzy_matching_level": 1, "duplicate_submissions": true,'
        ' "original_max_days_old": 6, "original_min_days_old": 2, "name_match_max_days_old": 3,'
        ' "create_shell_submissions": true, "shell_submissions_days_passed": 7}}'
    )
    op.execute(f"""
    update sync_configuration set configuration = '{arch_config}' where organization_id=10;
    """)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

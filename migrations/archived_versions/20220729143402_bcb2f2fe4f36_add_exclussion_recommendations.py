"""Add exclusion recommendations

Revision ID: bcb2f2fe4f36
Revises: 12ba3cd71970
Create Date: 2022-07-29 14:34:02.371202+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bcb2f2fe4f36"
down_revision = "12ba3cd71970"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "recommendation_exclusions",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("text", sa.String(length=128), nullable=True),
        sa.Column("recommendation_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("dry_run_recommendation_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(["dry_run_recommendation_id"], ["dry_run_recommendations.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["recommendation_id"], ["recommendations.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_recommendation_exclusions_dry_run_recommendation_id"),
        "recommendation_exclusions",
        ["dry_run_recommendation_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_recommendation_exclusions_recommendation_id"),
        "recommendation_exclusions",
        ["recommendation_id"],
        unique=False,
    )

    op.add_column(
        "recommendation_explanation",
        sa.Column("recommendation_exclusion_id", postgresql.UUID(as_uuid=True), nullable=True),
    )
    op.create_index(
        op.f("ix_recommendation_explanation_recommendation_exclusion_id"),
        "recommendation_explanation",
        ["recommendation_exclusion_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "recommendation_explanation", type_="foreignkey")

    op.drop_index(
        op.f("ix_recommendation_explanation_recommendation_exclusion_id"), table_name="recommendation_explanation"
    )
    op.drop_column("recommendation_explanation", "recommendation_exclusion_id")

    op.drop_index(op.f("ix_recommendation_exclusions_recommendation_id"), table_name="recommendation_exclusions")
    op.drop_index(
        op.f("ix_recommendation_exclusions_dry_run_recommendation_id"), table_name="recommendation_exclusions"
    )
    op.drop_table("recommendation_exclusions")
    # ### end Alembic commands ###

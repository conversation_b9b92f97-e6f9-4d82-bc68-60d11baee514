"""Moves FE FFs to settings

Revision ID: 063dbabaca2f
Revises: 14ee685eb044
Create Date: 2023-05-06 10:48:02.509066+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "063dbabaca2f"
down_revision = "14ee685eb044"
branch_labels = None
depends_on = None

show_account_id_column = "show_account_id"
reply_emails_enabled_column = "reply_emails_enabled"


def upgrade():
    with op.get_context().autocommit_block():
        op.add_column("settings", sa.Column(show_account_id_column, sa.<PERSON>an(), nullable=True))
        op.add_column("settings", sa.Column(reply_emails_enabled_column, sa.Bo<PERSON>an(), nullable=True))
        op.execute(f"UPDATE settings SET {show_account_id_column} = true WHERE organization_id = 10;")
        op.execute(
            f"UPDATE settings SET {reply_emails_enabled_column} = true WHERE organization_id in (3, 9, 10, 36, 37);"
        )
        op.execute(f"""
        UPDATE settings SET {reply_emails_enabled_column} = true WHERE user_id in (select id from users where email in ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'));
        """)


def downgrade():
    ...

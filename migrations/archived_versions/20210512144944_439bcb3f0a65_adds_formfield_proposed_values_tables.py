"""Adds FormField proposed values tables

Revision ID: 439bcb3f0a65
Revises: ad2d9085d421
Create Date: 2021-05-12 14:49:44.380870-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "439bcb3f0a65"
down_revision = "ad2d9085d421"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "form_field_source",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("form_field_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "source_type",
            sa.Enum("USER", "SUBMISSION", "SUBMISSION_BUSINESS", "DOSSIER", "ERS", name="sourcetype"),
            nullable=False,
        ),
        sa.Column("label", sa.String(length=256), nullable=False),
        sa.Column("path", sa.String(length=256), nullable=False),
        sa.ForeignKeyConstraint(["form_field_id"], ["form_field.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_form_field_source_form_field_id"), "form_field_source", ["form_field_id"], unique=False)
    op.create_table(
        "form_field_source_value",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("form_field_source_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("form_field_value_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("text_value", sa.String(), nullable=True),
        sa.Column("datetime_value", sa.DateTime(), nullable=True),
        sa.Column("boolean_value", sa.Boolean(), nullable=True),
        sa.Column("number_value", sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(["form_field_source_id"], ["form_field_source.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["form_field_value_id"], ["form_field_value.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_form_field_source_value_form_field_source_id"),
        "form_field_source_value",
        ["form_field_source_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_form_field_source_value_form_field_value_id"),
        "form_field_source_value",
        ["form_field_value_id"],
        unique=False,
    )
    op.drop_index("ix_form_field_id", table_name="form_field")
    op.alter_column("form_field_value", "submission_id", existing_type=postgresql.UUID(), nullable=True)
    op.add_column("submissions", sa.Column("account_name", sa.String(), nullable=True))


def downgrade():
    op.drop_column("submissions", "account_name")
    op.alter_column("form_field_value", "submission_id", existing_type=postgresql.UUID(), nullable=False)
    op.create_index("ix_form_field_id", "form_field", ["id"], unique=False)
    op.drop_index(op.f("ix_form_field_source_value_form_field_value_id"), table_name="form_field_source_value")
    op.drop_index(op.f("ix_form_field_source_value_form_field_source_id"), table_name="form_field_source_value")
    op.drop_table("form_field_source_value")
    op.drop_index(op.f("ix_form_field_source_form_field_id"), table_name="form_field_source")
    op.drop_table("form_field_source")

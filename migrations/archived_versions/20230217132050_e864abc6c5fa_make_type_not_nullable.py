"""Making email template type not null

Revision ID: e864abc6c5fa
Revises: 66df20cbb7b1
Create Date: 2023-02-16 13:20:50.600203+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e864abc6c5fa"
down_revision = "66df20cbb7b1"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        ALTER TABLE email_templates ALTER COLUMN type SET NOT NULL;
        """)


def downgrade():
    pass

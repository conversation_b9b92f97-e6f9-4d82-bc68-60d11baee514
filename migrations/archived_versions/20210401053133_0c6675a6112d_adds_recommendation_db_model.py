"""Adds Recommendation db model

Revision ID: 0c6675a6112d
Revises: 8bef4aa3d134
Create Date: 2021-04-01 05:31:33.632157-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0c6675a6112d"
down_revision = "8bef4aa3d134"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "recommendations",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("expected_value", sa.Float(), nullable=True),
        sa.Column(
            "action",
            postgresql.ENUM("ACCEPT", "DECLINE", "REFER", name="recommendationtype", create_type=False),
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "recommendation_explanation", sa.Column("recommendation_id", postgresql.UUID(as_uuid=True), nullable=True)
    )
    op.create_index(
        op.f("ix_recommendation_explanation_recommendation_id"),
        "recommendation_explanation",
        ["recommendation_id"],
        unique=False,
    )
    op.create_foreign_key(
        None, "recommendation_explanation", "recommendations", ["recommendation_id"], ["id"], ondelete="CASCADE"
    )


def downgrade():
    op.drop_constraint(None, "recommendation_explanation", type_="foreignkey")
    op.drop_index(op.f("ix_recommendation_explanation_recommendation_id"), table_name="recommendation_explanation")
    op.drop_column("recommendation_explanation", "recommendation_id")
    op.drop_table("recommendations")

"""notebook threads migration 3

Revision ID: 9205731fba50
Revises: 19138f301e7d
Create Date: 2021-10-13 16:30:10.517064+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9205731fba50"
down_revision = "19138f301e7d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ASSAULT_GRADE'')]}' where dossier_component_paths='{facts[?(@.fact_subtype?.id==''ASSAULT_GRADE'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''MURDER_GRADE'')]}' where dossier_component_paths='{facts[?(@.fact_subtype?.id==''MURDER_GRADE'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.parent_type==''PREMISES''&@.fact_subtype?.id==''ON_PREMISES_CRIME'')]}' where dossier_component_paths='{facts[?(@.fact_subtype?.id==''ON_PREMISES_CRIME'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''OVERALL_CRIME_GRADE'')]}' where dossier_component_paths='{facts[?(@.fact_subtype?.id==''OVERALL_CRIME_GRADE'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''RAPE_GRADE'')]}' where dossier_component_paths='{facts[?(@.fact_subtype?.id==''RAPE_GRADE'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ROBBERY_GRADE'')]}' where dossier_component_paths='{facts[?(@.fact_subtype?.id==''ROBBERY_GRADE'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.parent_type==''PREMISES''&@.fact_subtype?.id==''ELEVATION'')]}' where dossier_component_paths='{facts[?(@.fact_type?.id==''ELEVATION''&@.fact_subtype?.id==''ELEVATION'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''CATASTROPHIC_FLOOD_RISK'')]}' where dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.fact_subtype?.id==''CATASTROPHIC_FLOOD_RISK'')]}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''COASTAL_STORM_SURGE_RISK'')]}' where dossier_component_paths='{location.coastal_storm_surge,location.coastal_storm_surge_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ASSAULT_GRADE'')]}' where dossier_component_paths='{location.crime.assault_score,location.crime.assault_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''BURGLARY_GRADE'')]}' where dossier_component_paths='{location.crime.burglary_score,location.crime.burglary_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''DRUG_ALCOHOL_RELATED_DEATHS_GRADE'')]}' where dossier_component_paths='{location.crime.drug_alcohol_deaths_score,location.crime.drug_alcohol_deaths_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''VEHICLE_THEFT_GRADE'')]}' where dossier_component_paths='{location.crime.motor_vehicle_theft_score,location.crime.motor_vehicle_theft_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''MURDER_GRADE'')]}' where dossier_component_paths='{location.crime.murder_score,location.crime.murder_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''OVERALL_CRIME_GRADE'')]}' where dossier_component_paths='{location.crime.overall_score,location.crime.overall_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''RAPE_GRADE'')]}' where dossier_component_paths='{location.crime.rape_score,location.crime.rape_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''ROBBERY_GRADE'')]}' where dossier_component_paths='{location.crime.robbery_score,location.crime.robbery_value}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''FIRE_PROTECTION_GRADE'')]}' where dossier_component_paths='{location.fire_protection,location.fire_protection_text,location.fire_protection_class}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''HAIL_RISK'')]}' where dossier_component_paths='{location.hail,location.hail_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''LIGHTNING_RISK'')]}' where dossier_component_paths='{location.lightning,location.lightning_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''SNOW_LOAD_RISK'')]}' where dossier_component_paths='{location.snow_load,location.snow_load_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''TORNADO_RISK'')]}' where dossier_component_paths='{location.tornado,location.tornado_text}';
    update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''LETTER_GRADE''&@.parent_type==''PREMISES''&@.fact_subtype?.id==''WILDFIRE_RISK'')]}' where dossier_component_paths='{location.wildfire,location.wildfire_text}';
    """)


def downgrade():
    pass

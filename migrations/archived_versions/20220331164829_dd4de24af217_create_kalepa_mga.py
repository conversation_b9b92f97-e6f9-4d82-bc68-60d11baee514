"""Create Kalepa MGA Organization

Revision ID: dd4de24af217
Revises: 4ca5e4e26ac2
Create Date: 2020-11-09 15:46:26.661963+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "dd4de24af217"
down_revision = "4ca5e4e26ac2"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("organization", sa.Column("for_analysis", sa.<PERSON>(), nullable=True, server_default="t"))
    conn = op.get_bind()
    conn.execute(
        """INSERT INTO organization (id, name, renewal_creation_interval) VALUES (7, 'Kalepa MGA', INTERVAL '90 day') ON CONFLICT DO NOTHING;"""
    )
    conn.execute("""
    update organization set for_analysis = true where id in (4,5,6);
    update organization set for_analysis = false where id not in (4,5,6);
    update users set organization_id = 7 where email in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>');
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
    update users set organization_id = 3 where email in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>');
    """)
    conn.execute("delete from organization where id=7")
    op.drop_column("organization", "for_analysis")

"""Indexes for verification

Revision ID: 32e8a2b6b1f5
Revises: 2692a7793c15
Create Date: 2023-05-26 12:40:47.911286+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "32e8a2b6b1f5"
down_revision = "3f3b70458768"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.create_index(
            op.f("ix_submission_history_occurred_at"),
            "submission_history",
            ["occurred_at"],
            unique=False,
            postgresql_concurrently=True,
        )


def downgrade():
    pass

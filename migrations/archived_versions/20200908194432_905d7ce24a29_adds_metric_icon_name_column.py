"""Adds metric icon name column

Revision ID: 905d7ce24a29
Revises: b4467778a8a8
Create Date: 2020-09-08 19:44:32.055862+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "905d7ce24a29"
down_revision = "b4467778a8a8"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric", sa.Column("icon_name", sa.String(), nullable=True))


def downgrade():
    op.drop_column("metric", "icon_name")

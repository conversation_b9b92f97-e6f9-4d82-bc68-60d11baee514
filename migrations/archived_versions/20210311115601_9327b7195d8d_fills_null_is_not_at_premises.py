"""Fills null is_not_at_premises

Revision ID: 9327b7195d8d
Revises: 66742d6e7547
Create Date: 2021-03-11 11:56:01.561830-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9327b7195d8d"
down_revision = "66742d6e7547"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""UPDATE tenant_feedback SET is_not_at_premises = FALSE
                     where is_not_at_premises IS NULL""")


def downgrade():
    pass

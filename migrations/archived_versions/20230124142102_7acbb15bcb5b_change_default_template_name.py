"""Change default template name

Revision ID: 7acbb15bcb5b
Revises: d11880d63c74
Create Date: 2023-01-24 14:21:02.012538+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7acbb15bcb5b"
down_revision = "d11880d63c74"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("""update email_templates set name = 'Copilot Decline Template' where owner_id is null""")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

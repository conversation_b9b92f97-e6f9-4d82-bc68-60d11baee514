"""Removes CAT MOD RESULT forms created by mistake

Revision ID: 1913504723ee
Revises: 34daa05c5bb4
Create Date: 2021-09-30 11:21:05.261046+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1913504723ee"
down_revision = "34daa05c5bb4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        """DELETE from form_v2 where form_definition_id = 'CAT MOD RESULT' and created_at > '2021-09-29 19:00:00.0+00';"""
    )


def downgrade():
    pass

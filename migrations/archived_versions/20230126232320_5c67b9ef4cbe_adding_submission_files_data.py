"""Adding submission_files_data

Revision ID: 5c67b9ef4cbe
Revises: 4212e1d78421
Create Date: 2023-01-23 23:23:20.940277+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5c67b9ef4cbe"
down_revision = "4212e1d78421"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "submission_files_data",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("entity_mapped_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("onboarded_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_files_data_submission_id"), "submission_files_data", ["submission_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_submission_files_data_submission_id"), table_name="submission_files_data")
    op.drop_table("submission_files_data")

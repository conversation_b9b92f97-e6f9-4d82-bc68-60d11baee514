"""Enables nationwide ranking rules in prod

Revision ID: 45c717df01db
Revises: f80e8f044375
Create Date: 2021-08-03 22:55:58.435847+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "45c717df01db"
down_revision = "63ad11f06e08"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("UPDATE recommendation_rule SET is_active = true WHERE owner_organization_id=6;")
    conn.execute("UPDATE users SET role = 'manager' WHERE email='<EMAIL>';")


def downgrade():
    pass

"""Add due_date to submission

Revision ID: c83a9112ea08
Revises: 9008c37efc03
Create Date: 2021-02-08 03:47:04.518255-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c83a9112ea08"
down_revision = "9008c37efc03"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submissions", sa.Column("due_date", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submissions", "due_date")
    # ### end Alembic commands ###

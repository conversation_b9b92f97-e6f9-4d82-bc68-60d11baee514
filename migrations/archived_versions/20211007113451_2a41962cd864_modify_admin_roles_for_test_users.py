"""modify admin roles for test users

Revision ID: 2a41962cd864
Revises: 9c491af5e8da
Create Date: 2021-10-07 11:34:51.266652+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2a41962cd864"
down_revision = "9c491af5e8da"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""update users set role = 'manager' where id = '15';""")
        op.execute("""update users set organization_id = '3' where id = '15';""")
        op.execute("""update users set organization_id = '3' where id = '16';""")
        op.execute("""update users set organization_id = '3' where id = '17';""")


def downgrade():
    pass

"""Deletes reports created to test CapSpecialty integration

Revision ID: 730cf167d4f7
Revises: fbfb813dbd9d
Create Date: 2021-01-22 20:09:14.518615+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "730cf167d4f7"
down_revision = "fbfb813dbd9d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update reports_v2
    set is_deleted = TRUE
    where id in (
        'a07c6443-4b95-413b-8f49-f8b149e3ac35',
        '5ffac621-e89e-4aa6-b3cd-091417c27fed',
        'efd0b4a1-7185-4eca-9778-6eb3ff0242b6',
        '65fe3f0c-715a-4b4e-80d8-8b17a38ea6f6',
        '3487f67f-4339-4994-aece-ad6e41592834',
        '59c221fb-297a-416f-b308-77b40c88c68d',
        '42034ee5-c125-40fd-8e1a-9c204259ef5f',
        '2fa8b1b8-3ec0-4ca0-959c-4ac92ddd6fd7',
        'a889e93b-3bd1-4357-9f03-f8f2a7bf0939',
        '43030528-17d4-409d-9495-ff8463052fe1',
        '5551a90a-5a4a-474e-b141-f65df2c06acf',
        '2f75462a-b755-4444-82a2-1f2e68c603bd',
        'af68935f-4a64-429a-8c4b-4ad0bd7dea2c',
        'd5b432b1-fa1c-495f-83a5-312c19c93f5f',
        'd078f31a-8d0f-47d9-8d5a-5074ef9581d3',
        '1c1f1977-167b-4409-8206-062790745a64',
        'ba4ffb5e-c8f8-4288-8c8a-5460ccb3d2f5',
        'ec8605ce-4d90-4cf1-a8f7-7e5ba6a23593',
        '7f7068c1-2919-45c1-8b6a-51f00e159eb5',
        'b00c164b-6f11-487a-ab4b-d7526b3dac22',
        '97cc7e61-971c-46e3-8e47-8957735a1a5a',
        'f4d657ad-d3ff-440c-8cb7-4a468accca6d',
        '3561a011-83c6-4f4a-87ab-f498eeea7838',
        '9611be7b-b81b-47a8-8eff-2090ba67ae85',
        '9e27ecdb-1c83-4c90-89cd-2d60bd96fb04',
        'c7278965-b8f7-4d5e-b3ac-c7f3745eed93',
        '4bd25b02-97f6-4702-a5aa-bf4d60461af0',
        '2065e0bb-ef5b-47d2-aef6-0902b14b8d98',
        '11186ac4-96f6-4ae1-828b-c4ed2f0d99d0',
        'a1cc8380-cd99-4cfb-bbb1-2ca3d2d66233',
        'e5a682fa-0a2d-475b-b75b-611e5a9f3ab5',
        '54f65c0f-368c-49ab-8159-c5ebec36432e',
        'b9fa3e81-2362-475b-9583-ce06310cf985',
        '675cf474-63b9-4aca-92b6-0fa964bb06f3',
        'd755fcb3-409b-4451-89cd-1c085bc9ac78',
        '9f04d4d2-31ca-42c2-88f5-befe7f1203e2',
        '2999fc09-261e-42e3-8813-0dcb69eec176',
        'e4fb0f8a-6006-4143-9f2a-c27a5e142388',
        '784055d6-62a0-4ed7-bc08-589df593ed78'
    )
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
    update reports_v2
    set is_deleted = FALSE
    where id in (
        'a07c6443-4b95-413b-8f49-f8b149e3ac35',
        '5ffac621-e89e-4aa6-b3cd-091417c27fed',
        'efd0b4a1-7185-4eca-9778-6eb3ff0242b6',
        '65fe3f0c-715a-4b4e-80d8-8b17a38ea6f6',
        '3487f67f-4339-4994-aece-ad6e41592834',
        '59c221fb-297a-416f-b308-77b40c88c68d',
        '42034ee5-c125-40fd-8e1a-9c204259ef5f',
        '2fa8b1b8-3ec0-4ca0-959c-4ac92ddd6fd7',
        'a889e93b-3bd1-4357-9f03-f8f2a7bf0939',
        '43030528-17d4-409d-9495-ff8463052fe1',
        '5551a90a-5a4a-474e-b141-f65df2c06acf',
        '2f75462a-b755-4444-82a2-1f2e68c603bd',
        'af68935f-4a64-429a-8c4b-4ad0bd7dea2c',
        'd5b432b1-fa1c-495f-83a5-312c19c93f5f',
        'd078f31a-8d0f-47d9-8d5a-5074ef9581d3',
        '1c1f1977-167b-4409-8206-062790745a64',
        'ba4ffb5e-c8f8-4288-8c8a-5460ccb3d2f5',
        'ec8605ce-4d90-4cf1-a8f7-7e5ba6a23593',
        '7f7068c1-2919-45c1-8b6a-51f00e159eb5',
        'b00c164b-6f11-487a-ab4b-d7526b3dac22',
        '97cc7e61-971c-46e3-8e47-8957735a1a5a',
        'f4d657ad-d3ff-440c-8cb7-4a468accca6d',
        '3561a011-83c6-4f4a-87ab-f498eeea7838',
        '9611be7b-b81b-47a8-8eff-2090ba67ae85',
        '9e27ecdb-1c83-4c90-89cd-2d60bd96fb04',
        'c7278965-b8f7-4d5e-b3ac-c7f3745eed93',
        '4bd25b02-97f6-4702-a5aa-bf4d60461af0',
        '2065e0bb-ef5b-47d2-aef6-0902b14b8d98',
        '11186ac4-96f6-4ae1-828b-c4ed2f0d99d0',
        'a1cc8380-cd99-4cfb-bbb1-2ca3d2d66233',
        'e5a682fa-0a2d-475b-b75b-611e5a9f3ab5',
        '54f65c0f-368c-49ab-8159-c5ebec36432e',
        'b9fa3e81-2362-475b-9583-ce06310cf985',
        '675cf474-63b9-4aca-92b6-0fa964bb06f3',
        'd755fcb3-409b-4451-89cd-1c085bc9ac78',
        '9f04d4d2-31ca-42c2-88f5-befe7f1203e2',
        '2999fc09-261e-42e3-8813-0dcb69eec176',
        'e4fb0f8a-6006-4143-9f2a-c27a5e142388',
        '784055d6-62a0-4ed7-bc08-589df593ed78'
    )
    """)

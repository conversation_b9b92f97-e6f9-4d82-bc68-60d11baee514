"""Sets ondelete=CASCADE for label_id

Revision ID: 0f640d291152
Revises: 3b75e0ec0a6d
Create Date: 2021-06-21 13:54:17.268817+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0f640d291152"
down_revision = "3b75e0ec0a6d"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("test_run_classification_task_label_id_fkey", "test_run_classification_task", type_="foreignkey")
    op.create_foreign_key(
        "ix_test_run_classification_task_label_id",
        "test_run_classification_task",
        "customizable_classifier_labels",
        ["label_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade():
    op.drop_constraint("ix_test_run_classification_task_label_id", "test_run_classification_task", type_="foreignkey")
    op.create_foreign_key(
        "test_run_classification_task_label_id_fkey",
        "test_run_classification_task",
        "customizable_classifier_labels",
        ["label_id"],
        ["id"],
    )

"""More dashboard settings - mike ward

Revision ID: 4d7abe128cba
Revises: a3654ca65f6e
Create Date: 2022-11-23 08:46:28.481469+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4d7abe128cba"
down_revision = "a3654ca65f6e"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        INSERT INTO settings (id, created_at, updated_at, user_id, is_management_dashboard_enabled)
        SELECT uuid_generate_v4(), now(), null, id, true
        FROM users
        WHERE email IN ('<EMAIL>');
    """)


def downgrade():
    pass

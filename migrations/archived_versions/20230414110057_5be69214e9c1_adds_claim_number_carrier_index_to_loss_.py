"""Adds (claim_number, carrier) index to Loss table

Revision ID: 5be69214e9c1
Revises: 1eb7055aef92
Create Date: 2023-04-14 11:00:57.465783+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "5be69214e9c1"
down_revision = "1eb7055aef92"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.create_index(
            "ix_claim_number_carrier", "loss", ["claim_number", "carrier"], unique=False, postgresql_concurrently=True
        )


def downgrade():
    op.drop_index("ix_claim_number_carrier", table_name="loss")

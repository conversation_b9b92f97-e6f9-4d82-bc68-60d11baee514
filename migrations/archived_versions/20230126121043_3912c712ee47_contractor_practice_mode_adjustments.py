"""contractor practice mode adjustments

Revision ID: 3912c712ee47
Revises: 159bf92f3114
Create Date: 2023-01-25 16:10:43.375091+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3912c712ee47"
down_revision = "159bf92f3114"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""

-- Swap positions:
UPDATE mode_cards SET position = 100 WHERE id IN ('d42dd329-85c0-4b10-bc0d-09a74da8607a', 'efffe5be-8cc3-4def-93dc-9c2031bef610');
UPDATE mode_cards SET position = 2 WHERE id IN ('90d2b1dd-7348-47a3-b8c9-fdf933e3e50b', 'a3a079cc-fbbe-4716-bb37-1550c5dc5b18');
UPDATE mode_cards SET position = 3 WHERE id IN ('d42dd329-85c0-4b10-bc0d-09a74da8607a', 'efffe5be-8cc3-4def-93dc-9c2031bef610');

----- Contractor practice mode

-- Remove Roofing methods used, Pitch of roofs worked on,  Demolition methods used, Location of work
DELETE from mode_cards WHERE id IN ('34ced634-9db3-4d42-814f-2c0cbf6d817d', '0ac00b5c-7a37-4466-ae6a-f293b9c024b3', 'c26eaa98-6685-4620-9c3d-5f0953f1ad4e', '2e93449a-0ee1-4b90-b9a9-b831bb0391c6');

-- Remove Contractor experience
DELETE from mode_cards WHERE id IN ('a60790f1-1676-4c27-8cad-808081150b08', '6569afc9-d094-4f17-b333-e54495ebb0d2');
DELETE from mode_columns WHERE id = '70513e35-9058-4f6e-84b8-ee3f7cde9abc';
DELETE FROM mode_rows WHERE id ='648ed96a-0a72-4a91-ab33-5562cc985676';

-- Add the “Potential Exposures” card position 85
INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('ef3259ab-db69-4fc6-b5fd-e77283406614', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 85, 'Potential exposures');

INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('947234ba-2321-4892-ab53-f74074ea6cbb', 'ef3259ab-db69-4fc6-b5fd-e77283406614', 0, 12, null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    ('fc9c391b-b490-4589-bae5-5fb15b48bd09', '947234ba-2321-4892-ab53-f74074ea6cbb', 'Hazardous work performed', 10, 'FACTS', 'hazardous-work-performed', '{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"]
        },
        {
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"]
        },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"]
        },
        {
          "id": "PROJECT_ROOF_WORK",
          "parentSubtypeIds": ["ROOFING_SERVICE"]
        },
        {
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"]
        },
        {
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"]
        },
        {
          "parentSubtypeIds": ["BLASTING_OPERATIONS"]
        },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"]
        },
        {
          "id": "PROJECT_EXCAVATION_WORK"
        },
        {
          "id": "PROJECT_BELOW_GRADE"
        },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_HEIGHT_IN_FT"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_HEIGHT_IN_STORIES"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
        }
      ]
    },
    "ELECTRICAL_CONTRACTOR": {
      "factSubtypes": [
        {
          "id": "PROJECT_ELECTRICAL_WORK"
        },
        {
          "id": "PROJECT_TRAFFIC_LIGHTING_SIGNALS_WORK"
        },
        {
          "id": "PROJECT_AIRPORT_WORK"
        },
        {
          "id": "PROJECT_BELOW_GRADE"
        },
        {
          "id": "PROJECT_DEPTH_OF_WORK"
        },
        {
          "id": "PROJECT_HEIGHT_IN_FT"
        },
        {
          "id": "PROJECT_HEIGHT_IN_STORIES"
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
        }
      ]
    },
    "HVAC_AND_PLUMBING_PRACTICE": {
      "factSubtypes": [
        {
          "id": "PROJECT_HVAC_WORK",
          "parentSubtypeIds": ["HVAC_INSTALLATION_AND_SERVICE"]
        },
        {
          "id": "PROJECT_PLUMBING_WORK",
          "parentSubtypeIds": ["PLUMBING"]
        },
        {
          "id": "PROJECT_BOILER_WORK"
        },
        {
          "id": "PROJECT_ELECTRICAL_WORK"
        },
        {
          "id": "PROJECT_GAS_LINE_WORK"
        },
        {
          "id": "PROJECT_SPRINKLER_SYSTEM_WORK"
        },
        {
          "id": "PROJECT_SEWER_WORK"
        },
        {
          "id": "PROJECT_ROOF_WORK",
          "parentSubtypeIds": ["ROOFING_SERVICE"]
        },
        {
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"]
        },
        {
          "id": "PROJECT_EXCAVATION_WORK"
        },
        {
          "id": "PROJECT_HEIGHT_IN_FT"
        },
        {
          "id": "PROJECT_HEIGHT_IN_STORIES"
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
        }
      ]
    },
    "ROOFING_AND_SIDING_PRACTICE": {
      "factSubtypes": [
        {
          "id": "PROJECT_ROOF_WORK",
          "parentSubtypeIds": ["ROOFING_SERVICE"]
        },
        {
          "id": "PROJECT_SIDING_WORK"
        },
        {
          "id": "PROJECT_INSULATION_WORK"
        },
        {
          "id": "PROJECT_WATER_PROOFING_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_HEIGHT_IN_FT"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_HEIGHT_IN_STORIES"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
        }
      ]
    },
    "SITE_PREPARATION_PRACTICE": {
      "factSubtypes": [
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_EXCAVATION_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"]
        },
        {
          "id": "PROJECT_GRADING_WORK"
        },
        {
          "id": "PROJECT_PILE_DRIVING_WORK"
        },
        {
          "id": "PROJECT_PAVING_WORK"
        },
        {
          "id": "PROJECT_DRILLING_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_BELOW_GRADE"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"]
        }
      ]
    }
  }
}');


INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    ('fff31f7f-812c-4409-b126-e1a52d5c4fa5', '947234ba-2321-4892-ab53-f74074ea6cbb', 'Other', 20, 'FACTS', 'other', '{
  "collapsible": true,
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": []
    },
    "ELECTRICAL_CONTRACTOR": {
      "factSubtypes": [
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_ALARM_SYSTEMS_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_SOLAR_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_TOWER_ANTENNAS_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_FIBER_OPTICS_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_EXCAVATION_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": [
            "EXTERIOR_INSULATION_FINISHING_SYSTEMS"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": [
            "SCAFFOLDING"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": [
            "MOLD_ABATEMENT"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_ROOF_WORK",
          "parentSubtypeIds": [
            "ROOFING_SERVICE"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": [
            "CRANES"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": [
            "DEMOLITION"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": [
            "BLASTING_OPERATIONS"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": [
            "HAZARDOUS_MATERIALS",
            "RADON_ABATEMENT"
          ]
        }
      ]
    },
    "HVAC_AND_PLUMBING_PRACTICE": {
      "factSubtypes": [
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_SHEET_METAL_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_WELDING_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": [
            "EXTERIOR_INSULATION_FINISHING_SYSTEMS"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": [
            "MOLD_ABATEMENT"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": [
            "CRANES"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": [
            "HAZARDOUS_MATERIALS",
            "RADON_ABATEMENT"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": [
            "DEMOLITION"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": [
            "BLASTING_OPERATIONS"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_BELOW_GRADE"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": [
            "PROJECT_BELOW_GRADE"
          ]
        }
      ]
    },
    "ROOFING_AND_SIDING_PRACTICE": {
      "factSubtypes": [
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_RAIN_GUTTER_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_SHEET_METAL_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_CARPENTRY_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": [
            "EXTERIOR_INSULATION_FINISHING_SYSTEMS"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": [
            "SCAFFOLDING"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": [
            "MOLD_ABATEMENT"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": [
            "CRANES"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": [
            "HAZARDOUS_MATERIALS",
            "RADON_ABATEMENT"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": [
            "DEMOLITION"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": [
            "BLASTING_OPERATIONS"
          ]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_EXCAVATION_WORK"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_BELOW_GRADE"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": [
            "PROJECT_BELOW_GRADE"
          ]
        }
      ]
    },
    "SITE_PREPARATION_PRACTICE": {
      "factSubtypes": [
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"]
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_HEIGHT_IN_FT"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PROJECT_HEIGHT_IN_STORIES"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"
        },
        {
          "hiddenWhenNegative": true,
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
        }
      ]
    }
  }
}
');

UPDATE mode_cards
SET props = '{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"]
        },
        {
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"]
        },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"]
        },
        {
          "id": "PROJECT_ROOF_WORK",
          "parentSubtypeIds": ["ROOFING_SERVICE"]
        },
        {
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"]
        },
        {
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"]
        },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"]
        },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"]
        },
        {
          "id": "PROJECT_EXCAVATION_WORK"
        },
        {
          "id": "PROJECT_BELOW_GRADE"
        },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"]
        },
        {
          "id": "PROJECT_HEIGHT_IN_FT"
        },
        {
          "id": "PROJECT_HEIGHT_IN_STORIES"
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT"
        },
        {
          "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"
        }
      ]
    }
  }
}'
WHERE id = '91da8d98-fdbb-4816-93e7-18ccb4951054';
    """)


def downgrade():
    pass

"""Removes fact_subtype_id unique constraint

Revision ID: 4a4c7bababfd
Revises: ce43d7b72e25
Create Date: 2021-06-28 21:23:49.109954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4a4c7bababfd"
down_revision = "ce43d7b72e25"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint(
        "customizable_classifiers_fact_subtype_id_unique_constraint", "customizable_classifiers", type_="unique"
    )


def downgrade():
    op.create_unique_constraint(
        "customizable_classifiers_fact_subtype_id_unique_constraint", "customizable_classifiers", ["fact_subtype_id"]
    )

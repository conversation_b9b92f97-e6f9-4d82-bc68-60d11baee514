"""Adds user-specific signout redirection URL

Revision ID: ed40b20cc957
Revises: 604d2b1510d6
Create Date: 2021-04-30 14:52:38.671157+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed40b20cc957"
down_revision = "604d2b1510d6"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("users", sa.Column("signout_redirection_url", sa.String(), nullable=True))

    conn = op.get_bind()
    conn.execute("""
    update
    users
    set signout_redirection_url = 'https://capspecialty.okta.com/home/<USER>/0oaqe9i4txYoHr7rH0x7/alnqe9ne8pCY3OX5G0x7/'
    where organization_id = (select id from organization where name = 'CapSpecialty')
    and email not in (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    )
    """)


def downgrade():
    op.drop_column("users", "signout_redirection_url")

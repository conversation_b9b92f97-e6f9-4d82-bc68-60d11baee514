"""migrate_coverage_types_sql

Revision ID: 900c4c7d3046
Revises: 5acf832d3cb8
Create Date: 2022-12-13 18:16:09.073939+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "900c4c7d3046"
down_revision = "5acf832d3cb8"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
SET statement_timeout TO '6000 s';

CREATE TABLE coverages_copy AS SELECT * FROM coverages;
CREATE TABLE submission_coverages_copy AS SELECT * FROM submission_coverages;
CREATE TABLE submission_deductibles_copy AS SELECT * FROM submission_deductibles;
CREATE TABLE loss_copy AS SELECT * FROM loss;

--- Remove coverages from data model that are no longer needed
DELETE FROM coverages where name in (
'accountsReceivable',
'additionalInterestSchedule',
'additionalPremisesInfoSchedule',
'apartmentBuildingSupplement',
'condoAssnBylaws',
'contractorsSupplement',
'convectiveStorm-HighandSevereZones',
'convectiveStorm/Winterstorm(SCS)',
'coveragesSchedule',
'dealersSection',
'driverInfoSchedule',
'earthquake(EQ)',
'earthquake(EQ)-California',
'earthquake(EQ)-NewMadrid(NM)',
'earthquake(EQ)-OtherHighHazard(HI,AK,PR)',
'earthquake(EQ)-PacificNorthwest(PNW)',
'earthquake(EQSL)',
'electronicDataProcessingSection',
'flood(FL)',
'flood(FL)-<0.5milefromcoast(stormsurge)',
'flood(FL)-ZoneA',
'flood(FL)-ZoneB',
'glassSignSection',
'hotelMotelSupplement',
'installationBuildersRiskSection',
'namedWind(WS)',
'namedWind(WS)-Tier1',
'namedWind(WS)-Tier2',
'restaurantTavernSupplement',
'stateSupplement',
'statementScheduleOfValues',
'terrorism(TR)',
'vehicleSchedule',
'wildfire');

-- Delete any submission coverages data related to QBE
DELETE FROM submission_coverages WHERE submission_id in (
    SELECT submissions.id FROM submissions
    JOIN users u on submissions.owner_id = u.id
    JOIN organization o on u.organization_id = o.id
    WHERE organization_id = 5);
-- TODO: also deductibles and loss?
DELETE FROM submission_deductibles WHERE submission_id in (
    SELECT submissions.id FROM submissions
    JOIN users u on submissions.owner_id = u.id
    JOIN organization o on u.organization_id = o.id
    WHERE organization_id = 5);
DELETE FROM loss WHERE submission_id in (
    SELECT submissions.id FROM submissions
    JOIN users u on submissions.owner_id = u.id
    JOIN organization o on u.organization_id = o.id
    WHERE organization_id = 5);
DELETE FROM coverages where organization_id = 5;
---

-- Migrate submission coverages with coverage.organization_id = null to coverage with org.id
ALTER TABLE coverages DROP CONSTRAINT coverages_name_key;
ALTER TABLE coverages DROP CONSTRAINT coverages_display_name_key;

UPDATE coverages SET organization_id = null;

ALTER TABLE coverages ADD CONSTRAINT coverages_org_id_name UNIQUE (organization_id, name);

INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'boilerMachinery', 'Equipment Breakdown', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'businessAuto', 'Business Auto', id, '{"PRIMARY", "EXCESS"}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'businessOwners', 'Business Owner''s Policy (BOP)', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'commercialInlandMarine', 'Commercial Inland Marine', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'crime', 'Crime', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'cyberPrivacy', 'Cyber Liability', id, '{"PRIMARY", "EXCESS"}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'fiduciaryLiability', 'Directors & Officers (D&O)', id, '{"PRIMARY", "EXCESS"}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'foreignLiability', 'Foreign Liability', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'garageDealers', 'Garage', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'generalLiability', 'Commercial General Liability', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'liquorLiability', 'Liquor Liability', id, '{"PRIMARY", "EXCESS"}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'package', 'Package', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'terrorism', 'Terrorism (TRIA)', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'truckers', 'Truckers', id, '{"PRIMARY", "EXCESS"}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'umbrella', 'Umbrella/Excess Liability', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'workersComp', 'Workers Compensation', id, '{"PRIMARY", "EXCESS"}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'property', 'Commercial Property', id, '{"PRIMARY", "EXCESS"}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'other', 'Other', id, '{}'
FROM organization;

-- THESE WILL BE DELETED
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'yacht', 'yacht', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'excessAndSurplus', 'excessAndSurplus', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'miscLiability1', 'miscLiability1', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'miscLiability2', 'miscLiability2', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'policyFee', 'Policy Fee', id, '{}'
FROM organization;
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
SELECT uuid_generate_v4(), 'motorCarriers', 'motorCarriers', id, '{}'
FROM organization;
---

--- Move all data from old coverages without org id to new coverages with org id
WITH ez AS (
    SELECT
        submission_coverages.id submission_coverages_id,
        c.name coverages_name,
        o.id org_id
    FROM submission_coverages
    JOIN coverages c on submission_coverages.coverage_id = c.id
    JOIN submissions s on submission_coverages.submission_id = s.id
    JOIN users u on s.owner_id = u.id
    JOIN organization o on u.organization_id = o.id
),
to_update AS (
    SELECT
        submission_coverages_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON coverages.name = ez.coverages_name
    AND organization_id = ez.org_id
    )
UPDATE submission_coverages
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_coverages.id = to_update.submission_coverages_id;

WITH ez AS (
    SELECT
        submission_deductibles.id submission_deductibles_id,
        c.name coverages_name,
        o.id org_id
    FROM submission_deductibles
    JOIN coverages c on submission_deductibles.coverage_id = c.id
    JOIN submissions s on submission_deductibles.submission_id = s.id
    JOIN users u on s.owner_id = u.id
    JOIN organization o on u.organization_id = o.id
),
to_update AS (
    SELECT
        submission_deductibles_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON coverages.name = ez.coverages_name
    AND organization_id = ez.org_id
    )
UPDATE submission_deductibles
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_deductibles.id = to_update.submission_deductibles_id;

WITH ez AS (
    SELECT
        loss.id loss_id,
        c.name coverages_name,
        o.id org_id
    FROM loss
    JOIN coverages c on loss.coverage_id = c.id
    JOIN submissions s on loss.submission_id = s.id
    JOIN users u on s.owner_id = u.id
    JOIN organization o on u.organization_id = o.id
),
to_update AS (
    SELECT
        loss_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON coverages.name = ez.coverages_name
    AND organization_id = ez.org_id
    )
UPDATE loss
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE loss.id = to_update.loss_id;

-- Delete all coverages without ord id
DELETE FROM coverages WHERE organization_id IS NULL;

-- Migrate miscLiability1 and miscLiability2 to new Other LOB
-- TODO: make sure there is only one Other LOB per submission?
WITH ez AS (
    SELECT
        submission_coverages.id submission_coverages_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM submission_coverages
    JOIN coverages c on submission_coverages.coverage_id = c.id
    WHERE c.name = 'miscLiability1'
),
to_update AS (
    SELECT
        submission_coverages_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'other'
    AND organization_id = ez.org_id
    )
UPDATE submission_coverages
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_coverages.id = to_update.submission_coverages_id;

WITH ez AS (
    SELECT
        submission_deductibles.id submission_deductibles_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM submission_deductibles
    JOIN coverages c on submission_deductibles.coverage_id = c.id
    WHERE c.name = 'miscLiability1'
),
to_update AS (
    SELECT
        submission_deductibles_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'other'
    AND organization_id = ez.org_id
    )
UPDATE submission_deductibles
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_deductibles.id = to_update.submission_deductibles_id;

WITH ez AS (
    SELECT
        loss.id loss_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM loss
    JOIN coverages c on loss.coverage_id = c.id
    WHERE c.name = 'miscLiability1'
),
to_update AS (
    SELECT
        loss_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'other'
    AND organization_id = ez.org_id
    )
UPDATE loss
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE loss.id = to_update.loss_id;

-- misc2

WITH ez AS (
    SELECT
        submission_coverages.id submission_coverages_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM submission_coverages
    JOIN coverages c on submission_coverages.coverage_id = c.id
    WHERE c.name = 'miscLiability2'
),
to_update AS (
    SELECT
        submission_coverages_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'other'
    AND organization_id = ez.org_id
    )
UPDATE submission_coverages
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_coverages.id = to_update.submission_coverages_id;

WITH ez AS (
    SELECT
        submission_deductibles.id submission_deductibles_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM submission_deductibles
    JOIN coverages c on submission_deductibles.coverage_id = c.id
    WHERE c.name = 'miscLiability2'
),
to_update AS (
    SELECT
        submission_deductibles_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'other'
    AND organization_id = ez.org_id
    )
UPDATE submission_deductibles
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_deductibles.id = to_update.submission_deductibles_id;

WITH ez AS (
    SELECT
        loss.id loss_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM loss
    JOIN coverages c on loss.coverage_id = c.id
    WHERE c.name = 'miscLiability2'
),
to_update AS (
    SELECT
        loss_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'other'
    AND organization_id = ez.org_id
    )
UPDATE loss
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE loss.id = to_update.loss_id;

-- Migrate motorCarriers to businessAuto

WITH ez AS (
    SELECT
        submission_coverages.id submission_coverages_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM submission_coverages
    JOIN coverages c on submission_coverages.coverage_id = c.id
    WHERE c.name = 'motorCarriers'
),
to_update AS (
    SELECT
        submission_coverages_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'businessAuto'
    AND organization_id = ez.org_id
    )
UPDATE submission_coverages
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_coverages.id = to_update.submission_coverages_id;

WITH ez AS (
    SELECT
        submission_deductibles.id submission_deductibles_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM submission_deductibles
    JOIN coverages c on submission_deductibles.coverage_id = c.id
    WHERE c.name = 'motorCarriers'
),
to_update AS (
    SELECT
        submission_deductibles_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'businessAuto'
    AND organization_id = ez.org_id
    )
UPDATE submission_deductibles
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE submission_deductibles.id = to_update.submission_deductibles_id;

WITH ez AS (
    SELECT
        loss.id loss_id,
        c.name coverages_name,
        c.organization_id org_id
    FROM loss
    JOIN coverages c on loss.coverage_id = c.id
    WHERE c.name = 'motorCarriers'
),
to_update AS (
    SELECT
        loss_id,
        coverages.id target_coverage_id
    FROM coverages
    JOIN ez ON name = 'businessAuto'
    AND organization_id = ez.org_id
    )
UPDATE loss
SET coverage_id = to_update.target_coverage_id
FROM to_update
WHERE loss.id = to_update.loss_id;


-- MoE - all LOBs where coverage type is applicable, coverage type = Primary
UPDATE submission_coverages sc
SET coverage_type = 'PRIMARY'
WHERE sc.id IN (
    SELECT
        isc.id
    FROM submission_coverages isc
    JOIN coverages c on isc.coverage_id = c.id
    WHERE c.coverage_types::text[] && ARRAY['PRIMARY']
    AND c.organization_id IN (11,12));
    
UPDATE submission_deductibles sd
SET coverage_type = 'PRIMARY'
WHERE sd.id IN (
    SELECT
        isd.id 
    FROM submission_deductibles isd
    JOIN coverages c on isd.coverage_id = c.id
    WHERE c.coverage_types::text[] && ARRAY['PRIMARY']
    AND c.organization_id IN (11,12));
    
UPDATE loss l
SET coverage_type = 'PRIMARY'
WHERE l.id IN (
    SELECT
        il.id
    FROM loss il
    JOIN coverages c on il.coverage_id = c.id
    WHERE c.coverage_types::text[] && ARRAY['PRIMARY']
    AND c.organization_id IN (11,12));

-- KIS - all LOBs where coverage type is applicable, coverage type = Excess
UPDATE submission_coverages sc
SET coverage_type = 'EXCESS'
WHERE sc.id IN (
    SELECT
        isc.id
    FROM submission_coverages isc
    JOIN coverages c on isc.coverage_id = c.id
    WHERE c.coverage_types::text[] && ARRAY['EXCESS']
    AND c.organization_id IN (7));
    
UPDATE submission_deductibles sd
SET coverage_type = 'EXCESS'
WHERE sd.id IN (
    SELECT
        isd.id
    FROM submission_deductibles isd
    JOIN coverages c on isd.coverage_id = c.id
    WHERE c.coverage_types::text[] && ARRAY['EXCESS']
    AND c.organization_id IN (7));
    
UPDATE loss l
SET coverage_type = 'EXCESS'
WHERE l.id IN (
    SELECT
        il.id
    FROM loss il
    JOIN coverages c on il.coverage_id = c.id
    WHERE c.coverage_types::text[] && ARRAY['EXCESS']
    AND c.organization_id IN (7));
    """)


def downgrade():
    pass

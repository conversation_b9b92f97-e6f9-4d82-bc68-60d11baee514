"""Fixes fact_subtype_id field in SubmissionBusinessFieldValue

Revision ID: fbe94746e907
Revises: c5275269c2cf
Create Date: 2021-09-01 20:02:45.278290+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fbe94746e907"
down_revision = "c5275269c2cf"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        "ALTER TABLE submission_business_field_values ALTER COLUMN fact_subtype_id "
        "TYPE varchar USING fact_subtype_id::text"
    )


def downgrade():
    pass

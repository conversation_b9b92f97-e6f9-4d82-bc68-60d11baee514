"""Inserts CapSpecialty ClientApplication

Revision ID: 72368f5adb78
Revises: 01ae1b8bc234
Create Date: 2020-11-09 15:46:26.661963+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "72368f5adb78"
down_revision = "01ae1b8bc234"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""INSERT INTO organization (name) VALUES ('CapSpecialty') ON CONFLICT DO NOTHING;""")
    conn.execute("""INSERT INTO organization (name) VALUES ('QBE') ON CONFLICT DO NOTHING;""")
    conn.execute("""
    INSERT INTO client_application
        (id, created_at, updated_at, name, organization_id, client_id, client_secret)
    VALUES
        ('b2b2642b-eb71-4ae4-b9b0-723d7635fe9d', DEFAULT, null, 'CapSpecialty Integration', (select id from organization where name = 'CapSpecialty'), 'Pmzuk5yF8dcglm7suLYZB1tKT72V02vl', 'QwOWT6ucLtB7VX0EqBABxx2YIkbMmnVoTFmc5F01Aw3JSEbBoE3m-oNeOISExRPZ')
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    pass

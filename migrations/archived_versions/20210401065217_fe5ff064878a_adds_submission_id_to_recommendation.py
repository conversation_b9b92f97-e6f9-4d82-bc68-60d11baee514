"""Adds submission_id to recommendation

Revision ID: fe5ff064878a
Revises: 0c6675a6112d
Create Date: 2021-04-01 06:52:17.956577-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fe5ff064878a"
down_revision = "0c6675a6112d"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("recommendations", sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False))
    op.create_index(op.f("ix_recommendations_submission_id"), "recommendations", ["submission_id"], unique=False)
    op.create_foreign_key(None, "recommendations", "submissions", ["submission_id"], ["id"], ondelete="CASCADE")


def downgrade():
    op.drop_index(op.f("ix_recommendations_submission_id"), table_name="recommendations")
    op.drop_column("recommendations", "submission_id")

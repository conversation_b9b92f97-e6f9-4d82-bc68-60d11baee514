"""Add two indices

Revision ID: b706ac30e61a
Revises: 49cdb50178f6
Create Date: 2021-09-29 15:17:11.211388+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b706ac30e61a"
down_revision = "49cdb50178f6"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(op.f("ix_reports_v2_is_deleted"), "reports_v2", ["is_deleted"], unique=False)
    op.create_index(op.f("ix_submissions_stage"), "submissions", ["stage"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_submissions_stage"), table_name="submissions")
    op.drop_index(op.f("ix_reports_v2_is_deleted"), table_name="reports_v2")

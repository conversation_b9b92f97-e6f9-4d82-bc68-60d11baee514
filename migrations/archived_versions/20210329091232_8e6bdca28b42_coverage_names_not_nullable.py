"""Coverage names not nullable

Revision ID: 8e6bdca28b42
Revises: 47ca0431bd2d
Create Date: 2021-03-29 09:12:32.944883+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8e6bdca28b42"
down_revision = "47ca0431bd2d"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("coverages", "display_name", existing_type=sa.VARCHAR(), nullable=False)
    op.alter_column("coverages", "name", existing_type=sa.VARCHAR(), nullable=False)


def downgrade():
    op.alter_column("coverages", "name", existing_type=sa.VARCHAR(), nullable=True)
    op.alter_column("coverages", "display_name", existing_type=sa.VARCHAR(), nullable=True)

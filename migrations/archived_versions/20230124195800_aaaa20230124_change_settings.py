"""Set is_clarion_door_enabled to false for all

Revision ID: aaaa20230124
Revises: 9262f6988ca9
Create Date: 2023-01-24 19:58:00.000000+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "aaaa20230124"
down_revision = "9262f6988ca9"
branch_labels = None
depends_on = None


def upgrade():
    con = op.get_bind()
    con.execute("UPDATE settings SET is_clarion_door_enabled = false WHERE is_clarion_door_enabled = true;")


def downgrade():
    pass

"""adding budget file type

 Revision ID: 79c4221h8f9d
 Revises: 8ea2f6670f75
 Create Date: 2023-05-16 03:06:09.531749+00:00

 """
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "79c4221h8f9d"
down_revision = "8ea2f6670f75"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'BUDGET';""")


def downgrade():
    pass

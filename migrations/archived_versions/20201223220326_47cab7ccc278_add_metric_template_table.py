"""add metric template table

Revision ID: 47cab7ccc278
Revises: 8d30d9eb419b
Create Date: 2020-12-23 22:03:26.453979+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "47cab7ccc278"
down_revision = "8d30d9eb419b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "metric_template",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_metric_template_user_id"), "metric_template", ["user_id"], unique=False)
    op.create_table(
        "metric_preferences_templates",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("template_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("metric_preferences_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(["metric_preferences_id"], ["metric_preference.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["template_id"], ["metric_template.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_metric_preferences_templates_metric_preferences_id"),
        "metric_preferences_templates",
        ["metric_preferences_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_metric_preferences_templates_template_id"),
        "metric_preferences_templates",
        ["template_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_metric_preferences_templates_template_id"), table_name="metric_preferences_templates")
    op.drop_index(
        op.f("ix_metric_preferences_templates_metric_preferences_id"), table_name="metric_preferences_templates"
    )
    op.drop_table("metric_preferences_templates")
    op.drop_index(op.f("ix_metric_template_user_id"), table_name="metric_template")
    op.drop_table("metric_template")
    # ### end Alembic commands ###

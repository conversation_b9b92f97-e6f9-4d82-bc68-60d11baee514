"""Drops report_coverages

Revision ID: 453ebac3617d
Revises: 5d8bbbff492b
Create Date: 2021-05-21 20:43:00.283619+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "453ebac3617d"
down_revision = "5d8bbbff492b"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("report_coverage_unique_idx", table_name="report_coverage")
    op.drop_table("report_coverage")


def downgrade():
    op.create_table(
        "report_coverage",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("report_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("coverage_id", sa.VARCHAR(length=128), autoincrement=False, nullable=False),
        sa.Column("estimated_premium", sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["report_id"], ["reports.id"], name="report_coverage_report_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="report_coverage_pkey"),
    )
    op.create_index("report_coverage_unique_idx", "report_coverage", ["report_id", "coverage_id"], unique=True)

"""Make user.org_id index

Revision ID: f897906d1ef6
Revises: 4df2a116f3fa
Create Date: 2020-12-10 13:19:12.401132+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f897906d1ef6"
down_revision = "4df2a116f3fa"
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(op.f("ix_users_organization_id"), "users", ["organization_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_users_organization_id"), table_name="users")

"""add submission is_naics_verified

Revision ID: 1bf215faeb5a
Revises: 7a50d86fb305
Create Date: 2022-09-20 03:15:09.794083+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1bf215faeb5a"
down_revision = "7a50d86fb305"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("is_naics_verified", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("submissions", "is_naics_verified")

"""Migrates and cleanups metric configs

Revision ID: 2075ec39dbb3
Revises: e2739c160136
Create Date: 2022-02-04 13:05:12.855871+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2075ec39dbb3"
down_revision = "e2739c160136"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # Migrates metric_config.display_name if metric_config with target name does not exist yet
    conn.execute(
        sa.text("""
    update metric_config mc set display_name = REPLACE(mc.display_name, :keyword, '') where mc.display_name ilike :ilike_keyword AND NOT EXISTS (select mc.id from metric_config
    where metric_config.report_id = mc.report_id and ((metric_config.submission_business_id is null and mc.submission_business_id is null)
    or metric_config.submission_business_id = mc.submission_business_id)
    and metric_config.display_name = REPLACE(mc.display_name, :keyword, ''));
    """),
        keyword=" (Provided in SOV)",
        ilike_keyword="% (Provided in SOV)%",
    )
    # Deletes legacy metric configs which have already new representation without additional phrase
    conn.execute(
        sa.text("""
    delete from metric_config where metric_config.display_name ilike :ilike_keyword;
    """),
        keyword=" (Provided in SOV)",
        ilike_keyword="% (Provided in SOV)%",
    )


def downgrade():
    pass

"""Adds is_not_at_premise to tenant feedback

Revision ID: 2ea3ade8ccbd
Revises: 75f242e56356
Create Date: 2020-11-23 07:44:22.656787-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2ea3ade8ccbd"
down_revision = "95de34d6bf29"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("tenant_feedback", sa.Column("is_not_at_premises", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("tenant_feedback", "is_not_at_premises")

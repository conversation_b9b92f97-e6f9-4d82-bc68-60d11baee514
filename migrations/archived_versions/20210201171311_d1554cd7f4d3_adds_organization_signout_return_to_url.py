"""Adds organization.signout_return_to_url

Revision ID: d1554cd7f4d3
Revises: 8fc66dd47514
Create Date: 2021-02-01 17:13:11.122565+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d1554cd7f4d3"
down_revision = "8fc66dd47514"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("organization", sa.Column("signout_return_to_url", sa.String(length=1000), nullable=True))


def downgrade():
    op.drop_column("organization", "signout_return_to_url")

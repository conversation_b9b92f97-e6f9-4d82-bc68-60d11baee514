"""Migrate hail path

Revision ID: 7da2fa7da168
Revises: e18dd72b8bf4
Create Date: 2021-03-09 11:39:23.363153+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7da2fa7da168"
down_revision = "e18dd72b8bf4"
branch_labels = None
depends_on = None

RULE_DESCRIPTION = (
    "If number of businesses in high hail risk greater than threshold of total number of businesses, refer"
)
RULE_DEFINITION = """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "hail_values", "operator": "shares_at_least_one_element_with", "value": ["D", "F"]}]}, "actions": [{"name": "extract_value_from_dossier", "params": {"path": "location.hail.value"}}]}}]}, "actions": [{"name": "append_high_hail_risk_explanation", "params": {"path": "location.hail.value", "label": "Has High Hail Risk", "scalar": 0.01, "action": "REFER"}}]}"""


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""UPDATE recommendation_rule SET definition = '{RULE_DEFINITION}'
                     where description = '{RULE_DESCRIPTION}'""")


def downgrade():
    pass

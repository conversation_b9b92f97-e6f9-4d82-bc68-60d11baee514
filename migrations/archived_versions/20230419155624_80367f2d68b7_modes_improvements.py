"""modes-improvements

Revision ID: 80367f2d68b7
Revises: ddad2b85962c
Create Date: 2023-04-19 15:56:24.411899+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "80367f2d68b7"
down_revision = "ddad2b85962c"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        UPDATE mode_cards SET title = 'General Information' WHERE id = 'f6384a0a-5ce3-4c9c-afad-44a540f38777'; 
        UPDATE mode_cards SET title = 'Work Locations' WHERE id = '30f60fbf-06aa-4fb1-b16e-849f55087114'; 
        UPDATE mode_cards SET card_id = 'contractor-permits', props = '{"type":"PERMIT","header":"Contractor Permits"}' WHERE id = 'ec863ed9-42a7-46fb-979d-614f7e7f9e59'; 
        UPDATE mode_cards SET card_id = 'contractor-permits', props = '{"type":"PERMIT","header":"Contractor Permits"}' WHERE id = 'ab1e6d31-735f-451c-b1b8-001de21eae9b';
        
        INSERT INTO mode_rows (id, position, title, is_collapsible, is_default_open) VALUES
        ('bb0b7760-ee28-4879-940b-128501ec3c81', 100, 'Contractor Permits', NULL, NULL);        
        INSERT INTO mode_columns (row_id, id, width, position) VALUES
        ('bb0b7760-ee28-4879-940b-128501ec3c81', 'b8872bd8-3182-4a5b-8d50-47eac619f2e4', 12, 0);
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('b8872bd8-3182-4a5b-8d50-47eac619f2e4', '75530f8e-683e-4dd0-957f-0aec44d45e03', 10, 'contractor-permits', 'TABLE', NULL, '{"type":"PERMIT","header":"Contractor Permits"}');
        
        UPDATE mode_sections SET props = '{"overrides":[{"title":"Contractor Permits","type":"row-title"}]}',
                                 name = 'Contractor Permits' 
            WHERE id = 'f079a313-6e05-492d-9e3f-e0e6bdf371ce'; 
        UPDATE mode_section_elements SET child_row_id = 'bb0b7760-ee28-4879-940b-128501ec3c81' WHERE id = 'cd29f4ba-ff85-4f68-93e3-e57b737c62bd'
        """)


def downgrade():
    pass

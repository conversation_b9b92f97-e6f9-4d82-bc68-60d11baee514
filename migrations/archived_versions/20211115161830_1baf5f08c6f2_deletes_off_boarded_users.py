"""Deletes off-boarded users

Revision ID: 1baf5f08c6f2
Revises: 320c3f16d9f4
Create Date: 2021-11-15 16:18:30.654766+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "1baf5f08c6f2"
down_revision = "320c3f16d9f4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>';
    
    update users set email = 'defunct' || id || '@kalepa.co', organization_id = null, external_id = null
    where email = '<EMAIL>';
    """)


def downgrade():
    pass

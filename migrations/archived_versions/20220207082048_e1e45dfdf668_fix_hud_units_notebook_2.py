"""Migrates old HUD Units notebook entries part 2

Revision ID: e1e45dfdf668
Revises: 96ad9dfa9979
Create Date: 2022-02-07 08:20:48.948001+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e1e45dfdf668"
down_revision = "96ad9dfa9979"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        """update notebook_thread set dossier_component_paths='{facts[?(@.fact_type?.id==''INTEGER''&@.parent_type==''PREMISES''&@.parent_id==''44bf02d8-7d46-4643-944a-ebc4499471e8''&@.fact_subtype?.id==''HUD_LOW_INCOME_HUD_UNITS_COUNT'')]}' where id='cad653c2-80ad-4688-be90-9fe18bdce6a5';"""
    )


def downgrade():
    pass

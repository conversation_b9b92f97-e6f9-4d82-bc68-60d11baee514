"""Deactivates legacy classifiers

Revision ID: 4385ebfe2598
Revises: a3ff4168e020
Create Date: 2021-12-14 22:57:48.103062+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "4385ebfe2598"
down_revision = "a3ff4168e020"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    UPDATE customizable_classifiers 
    SET is_active = false 
    WHERE fact_subtype_id in ('CARPENTER', 'RESIDENTIAL_WORK', 'ELECTRIC', 'ROOFING_SERVICE');
    """)


def downgrade():
    conn = op.get_bind()

    conn.execute("""
    UPDATE customizable_classifiers 
    SET is_active = true 
    WHERE fact_subtype_id in ('CARPENTER', 'RESIDENTIAL_WORK', 'ELECTRIC', 'ROOFING_SERVICE');
    """)

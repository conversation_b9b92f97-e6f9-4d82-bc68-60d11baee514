"""Adds summary table

Revision ID: f9ae1d1623aa
Revises: b630b117b0f9
Create Date: 2020-06-07 21:45:04.945521

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f9ae1d1623aa"
down_revision = "3fdf15723d42"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "summaries",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("report_id", sa.Integer(), nullable=False),
        sa.Column("summary", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(
            ["report_id"],
            ["reports.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_summaries_report_id"), "summaries", ["report_id"], unique=True)


def downgrade():
    op.drop_index(op.f("ix_summaries_report_id"), table_name="summaries")
    op.drop_table("summaries")

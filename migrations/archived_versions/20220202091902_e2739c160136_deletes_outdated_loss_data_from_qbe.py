"""Deletes outdated loss data from QBE

Revision ID: e2739c160136
Revises: 97c0274f77db
Create Date: 2022-02-02 09:19:02.948001+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e2739c160136"
down_revision = "97c0274f77db"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""DELETE from loss where updated_at is null and organization_id = 5 
    and submission_id is null and created_at < '2022-02-01 00:00:00.000000 +00:00';""")


def downgrade():
    pass

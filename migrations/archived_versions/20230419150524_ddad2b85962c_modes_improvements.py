"""modes-improvements

Revision ID: ddad2b85962c
Revises: 05feccc4ecd3
Create Date: 2023-04-19 15:05:24.695378+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ddad2b85962c"
down_revision = "05feccc4ecd3"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        INSERT INTO mode_sections (id, name, props) VALUES
        ('d69eefe0-4848-40b8-8828-4026b2185173', 'Premise Location', '{"context":{"parentType":"PREMISES","type":"parents-select"}}'),
        ('61d5668e-cd30-43f2-9c3b-d3198423a351', 'Premise CAT Scores', '{"context":{"parentType":"PREMISES","type":"parents-select"}}');
        
        
        INSERT INTO mode_rows (id, position, title, is_collapsible, is_default_open) VALUES
        ('92199552-ef5e-4306-925f-9483a6ad99a0', 100, 'Location', NULL, NULL),
        ('0c6cb127-21ae-4df6-b8e5-0ba066ee0509', 100, 'CAT Scores', NULL, NULL);
        
        
        INSERT INTO mode_columns (row_id, id, width, position) VALUES
        ('92199552-ef5e-4306-925f-9483a6ad99a0', 'bcf50190-ddf0-4770-81ce-014917489ed9', 12, 0),
        ('0c6cb127-21ae-4df6-b8e5-0ba066ee0509', '337f40cd-5d84-4248-94bd-b9eb2e0cc6b9', 12, 0);
        
        
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('bcf50190-ddf0-4770-81ce-014917489ed9', 'f38d451b-294a-4acc-a723-ffcfd826c561', 100, 'premise-location-facts-distance', 'FACTS', 'Distance to', '{"facts":{"group":"DISTANCE_TO_HAZARDS","parentType":"PREMISES"}}'),
        ('337f40cd-5d84-4248-94bd-b9eb2e0cc6b9', 'd6f55fbc-3c1e-43b3-a45a-a91630eb9a21', 200, 'premise-location-facts-risks', 'FACTS', 'Risks', '{"facts":{"group":"LOCATION_RISKS","parentType":"PREMISES"},"processors":["orderByRawValue"]}');
        
        
        INSERT INTO mode_section_elements (id, section_id, position, child_row_id, child_section_id) VALUES
        ('083a1707-af01-46dc-a25d-a7c76418630f', 'd69eefe0-4848-40b8-8828-4026b2185173', 100, '92199552-ef5e-4306-925f-9483a6ad99a0', NULL),
        ('2f6d656b-68d9-4f47-8d2a-d5629e90e799', '61d5668e-cd30-43f2-9c3b-d3198423a351', 100, '0c6cb127-21ae-4df6-b8e5-0ba066ee0509', NULL);
        
        DELETE FROM mode_section_elements WHERE id IN ('4c4bb38f-31dc-4ab0-b5fa-5f1785e915e6');
        
        -- backfill dummy mode_section for local env
        INSERT INTO mode_sections (id, name) VALUES ('6091e660-6fab-4f05-b4c2-0dd7d8930fff', 'Premises Picker') ON CONFLICT DO NOTHING;
        
        INSERT INTO mode_section_elements (id, section_id, position, child_row_id, child_section_id) VALUES 
        ('4e380d33-1e2f-4e1e-aab4-a951c08612e5', '6091e660-6fab-4f05-b4c2-0dd7d8930fff', 250, NULL, 'd69eefe0-4848-40b8-8828-4026b2185173'),
        ('7c2b0bd9-1473-445d-9122-7e4a54755ba9', '6091e660-6fab-4f05-b4c2-0dd7d8930fff', 275, NULL, '61d5668e-cd30-43f2-9c3b-d3198423a351');
        """)


def downgrade():
    pass

"""add new file processing state

Revision ID: 2692a7793c15
Revises: 3f3b70458768
Create Date: 2023-05-26 07:06:40.710540+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2692a7793c15"
down_revision = "e5b751113c93"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(f"ALTER TYPE fileprocessingstate ADD VALUE IF NOT EXISTS 'AUTOCONFIRMING';")


def downgrade():
    pass

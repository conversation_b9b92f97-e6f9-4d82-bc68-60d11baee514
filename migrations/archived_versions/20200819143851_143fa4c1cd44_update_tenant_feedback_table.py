"""Update tenant_feedback table

Revision ID: 143fa4c1cd44
Revises: 0a257024a633
Create Date: 2020-08-19 14:38:51.112163

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "143fa4c1cd44"
down_revision = "0a257024a633"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("tenant_feedback", "business_name", existing_type=sa.VARCHAR(length=128), nullable=False)
    op.alter_column("tenant_feedback", "formatted_address", existing_type=sa.VARCHAR(length=128), nullable=False)
    op.alter_column("tenant_feedback", "premise_id", existing_type=postgresql.UUID(), nullable=False)


def downgrade():
    op.alter_column("tenant_feedback", "premise_id", existing_type=postgresql.UUID(), nullable=True)
    op.alter_column("tenant_feedback", "formatted_address", existing_type=sa.VARCHAR(length=128), nullable=True)
    op.alter_column("tenant_feedback", "business_name", existing_type=sa.VARCHAR(length=128), nullable=True)

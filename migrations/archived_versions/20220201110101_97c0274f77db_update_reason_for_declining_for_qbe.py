"""update reason_for_declining for qbe

Revision ID: 97c0274f77db
Revises: 3022908555b9
Create Date: 2022-02-01 11:01:01.702012+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "97c0274f77db"
down_revision = "3022908555b9"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            update submissions
            set reason_for_declining = (replace(replace(stage_details ->> 'lostReasons','["', ''), '"]',''))
            from users u
            inner join organization o on u.organization_id = o.id
            where organization_id = 5 and submissions.reason_for_declining is null and stage='DECLINED' and stage_details ->> 'lostReasons' is not null and u.id = submissions.owner_id
        """)
        op.execute("""
            update submissions
            set stage_details = jsonb_set(stage_details, '{reason}', to_jsonb(submissions.reason_for_declining))
            from users u
            inner join organization o on u.organization_id = o.id
            where organization_id = 5 and submissions.reason_for_declining is not null and stage='DECLINED' and stage_details ->> 'lostReasons' is not null and u.id = submissions.owner_id
        """)


def downgrade():
    pass

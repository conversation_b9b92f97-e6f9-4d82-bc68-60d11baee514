"""alert types

Revision ID: 1acf694a01e4
Revises: 3e5f694a01e4
Create Date: 2023-04-25 13:41:47.571339+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1acf694a01e4"
down_revision = "3e5f694a01e4"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE alert_type ADD VALUE IF NOT EXISTS 'MISSING_NAICS_L2';""")
        op.execute("""ALTER TYPE alert_type ADD VALUE IF NOT EXISTS 'MISSING_NAICS_SLA';""")


def downgrade():
    pass

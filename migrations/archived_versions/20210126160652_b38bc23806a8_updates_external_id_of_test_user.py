"""Updates external ID of test user

Revision ID: b38bc23806a8
Revises: 730cf167d4f7
Create Date: 2021-01-26 16:06:52.902488+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b38bc23806a8"
down_revision = "730cf167d4f7"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update users
    set external_id = 'samlp|CAPSPECIALTY|r<PERSON><PERSON><PERSON><PERSON><EMAIL>'
    where email = 'r<PERSON><PERSON><PERSON><PERSON><PERSON>@capspecialty.com'
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
    update users
    set external_id = 'auth0|5fac026c59a0970078acf5f3'
    where email = 'r<PERSON><PERSON><PERSON><PERSON><PERSON>@capspecialty.com'
    """)

"""Add submission is_metrics_set_manually

Revision ID: caa1a1af14bc
Revises: c4338657aefd
Create Date: 2022-10-23 17:00:04.612840+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "caa1a1af14bc"
down_revision = "c4338657aefd"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("is_metrics_set_manually", sa.<PERSON>(), nullable=True))
    op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
    op.execute("""update submissions set is_metrics_set_manually = true;""")


def downgrade():
    op.drop_column("submissions", "is_metrics_set_manually")

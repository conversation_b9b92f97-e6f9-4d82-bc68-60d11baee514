"""Adds submission_business_fields table

Revision ID: 2b6e0e55fd2a
Revises: 80ff83566332
Create Date: 2020-11-01 14:46:18.653523-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2b6e0e55fd2a"
down_revision = "80ff83566332"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "submission_business_fields",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column(
            "value_type", sa.<PERSON>um("TEXT", "NUMBER", "TEXT_ARRAY", "NUMBER_ARRAY", name="fieldtype"), nullable=True
        ),
        sa.Column("string_value", sa.String(), nullable=True),
        sa.Column("float_value", sa.Float(), nullable=True),
        sa.Column("string_array_value", sa.ARRAY(sa.String()), nullable=True),
        sa.Column("float_array_value", sa.ARRAY(sa.Float()), nullable=True),
        sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(["submission_business_id"], ["submission_businesses.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_business_fields_submission_business_id"),
        "submission_business_fields",
        ["submission_business_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_submission_business_fields_submission_business_id"), table_name="submission_business_fields")
    op.drop_table("submission_business_fields")

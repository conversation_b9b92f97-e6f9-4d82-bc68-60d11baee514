"""More dashboard settings

Revision ID: 1d7cef321cba
Revises: aaaa20221121
Create Date: 2022-11-22 08:46:28.481469+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1d7cef321cba"
down_revision = "aaaa20221121"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        INSERT INTO settings (id, created_at, updated_at, user_id, is_management_dashboard_enabled)
        SELECT uuid_generate_v4(), now(), null, id, true
        FROM users
        WHERE email IN ('<EMAIL>', '<EMAIL>');
    """)


def downgrade():
    pass

"""change nationwide renewal creation date

Revision ID: 3c6a445d5a1b
Revises: f708b6764ed8
Create Date: 2022-01-19 14:33:45.228360+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3c6a445d5a1b"
down_revision = "f708b6764ed8"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            UPDATE submissions s
            SET renewal_creation_date = policy_expiration_date - Interval '90 day'
            FROM users u
            where s.owner_id = u.id and u.organization_id=6 and s.renewal_creation_date is not null and s.policy_expiration_date is not null
        """)

        op.execute("""
            UPDATE organization o
            SET renewal_creation_interval = INTERVAL '90 day'
            where o.id = 6
        """)


def downgrade():
    pass

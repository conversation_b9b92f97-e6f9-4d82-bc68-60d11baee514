"""Adds children_type to metric and preference

Revision ID: ce27d5db857b
Revises: 03bd30a2c8cb
Create Date: 2022-06-08 22:36:01.512951+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ce27d5db857b"
down_revision = "03bd30a2c8cb"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("SET statement_timeout TO '1200 s';")
    op.add_column("metric", sa.Column("children_type", sa.VARCHAR(length=200), nullable=True, default="BUSINESS"))
    op.add_column(
        "metric_preference", sa.Column("children_type", sa.VARCHAR(length=200), nullable=True, default="BUSINESS")
    )
    conn = op.get_bind()
    conn.execute("""UPDATE metric SET children_type = 'BUSINESS' WHERE children_type is null""")
    conn.execute("""UPDATE metric_preference SET children_type = 'BUSINESS' WHERE children_type is null""")
    op.alter_column("metric", "children_type", existing_type=sa.VARCHAR(length=200), nullable=False)
    op.alter_column("metric", "children_type", existing_type=sa.VARCHAR(length=200), nullable=False)


def downgrade():
    op.drop_column("metric_preference", "children_type")
    op.drop_column("metric", "children_type")

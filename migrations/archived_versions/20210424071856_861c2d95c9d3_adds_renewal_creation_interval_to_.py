"""Adds renewal_creation_interval to organization

Revision ID: 861c2d95c9d3
Revises: 43cfbc3ce16e
Create Date: 2021-04-24 07:18:56.442591-04:00

"""
from datetime import timedelta

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "861c2d95c9d3"
down_revision = "6973b6aca7f8"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("organization", sa.Column("renewal_creation_interval", sa.Interval(), nullable=True))
    conn = op.get_bind()
    conn.execute(
        f"""UPDATE organization SET renewal_creation_interval = '{timedelta(days=9*30)}'""",
        """WHERE renewal_creation_interval IS NULL""",
    )


def downgrade():
    op.drop_column("organization", "renewal_creation_interval")

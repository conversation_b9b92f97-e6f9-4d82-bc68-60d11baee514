"""ENG-7405 user created at

Revision ID: c20cc04a0417
Revises: 7893061831ef
Create Date: 2022-03-29 12:16:40.823711+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c20cc04a0417"
down_revision = "7893061831ef"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "users", sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True)
    )
    op.add_column("users", sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True))


def downgrade():
    op.drop_column("users", "created_at")
    op.drop_column("users", "updated_at")

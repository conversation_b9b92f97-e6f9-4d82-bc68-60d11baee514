"""Makes renewal_creation_interval not nullable

Revision ID: 856eac8aa4b5
Revises: 861c2d95c9d3
Create Date: 2021-04-24 09:34:09.897456-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "856eac8aa4b5"
down_revision = "861c2d95c9d3"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("organization", "renewal_creation_interval", existing_type=postgresql.INTERVAL(), nullable=False)


def downgrade():
    op.alter_column("organization", "renewal_creation_interval", existing_type=postgresql.INTERVAL(), nullable=True)

"""Removes defunct users from orgs

Revision ID: 99f9726215ea
Revises: c4e6ee080c36
Create Date: 2021-04-23 20:41:05.719042+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "99f9726215ea"
down_revision = "c4e6ee080c36"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users set email = 'defunct' || id || '@kalepa.co', organization_id = null 
    where id in (58, 59, 61, 88, 90, 92, 93, 102, 103, 104, 108, 110, 111)
    """)


def downgrade():
    pass

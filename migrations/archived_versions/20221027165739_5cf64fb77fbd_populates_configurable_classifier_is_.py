"""Populates configurable classifier is_applicable_to_naics_codes field

Revision ID: 5cf64fb77fbd
Revises: 55ba99dcefd6
Create Date: 2022-10-27 16:57:39.273617+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5cf64fb77fbd"
down_revision = "55ba99dcefd6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_31-33}'
        WHERE is_applicable_to = '{Manufacturing}'
        """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7224}'
    WHERE is_applicable_to = '{Nightlife}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7225,NAICS_7224,NAICS_7223}'
    WHERE is_applicable_to = '{Nightlife, Restaurants, Food}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_23}'
    WHERE is_applicable_to = '{Contractors, Construction & Engineering}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213,NAICS_53}'
    WHERE is_applicable_to = '{Hotels & Travel, Residential Real Estate}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7224}'
    WHERE is_applicable_to = '{Nightlife,Event Planning & Services}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7225, NAICS_7223}'
    WHERE is_applicable_to = '{Restaurants}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_44-45}'
    WHERE is_applicable_to = '{Shopping, Health & Medical}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213, NAICS_53,NAICS_7224}'
    WHERE is_applicable_to = '{Nightlife, Hotels & Travel, Residential Real Estate}'
    """)
    conn.execute("""
        UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213,NAICS_53,NAICS_7224}'
        WHERE is_applicable_to = '{Nightlife, Hotels & Travel, Residential Real Estate}'
        """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7225,NAICS_7224,NAICS_7223}'
    WHERE is_applicable_to = '{Nightlife, Restaurants, Food}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7225,NAICS_7224,NAICS_7223}'
    WHERE is_applicable_to = '{Restaurants, Nightlife}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7225,NAICS_7224,NAICS_7223,NAICS_7211,NAICS_7211,NAICS_7213}'
    WHERE is_applicable_to = '{Restaurants, Hotels & Travel, Nightlife}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7225,NAICS_7223}'
    WHERE is_applicable_to = '{Restaurants, Food, Event Planning & Services}'
    """)
    conn.execute("""
    UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213,NAICS_53,NAICS_7224,NAICS_7225,NAICS_7223}'
    WHERE is_applicable_to = '{Nightlife, Restaurants, Hotels & Travel, Food, Event Planning & Services, Commercial Real Estate, Residential Real
     Estate, Real Estate}'
    """)
    conn.execute("""
        UPDATE customizable_classifiers SET is_applicable_to_all_industries = true
        WHERE is_applicable_to = '{Nightlife, Restaurants, Health & Medical, Shopping, Professional Services, Education, Active Life, Beauty & Spas,
     Hotels & Travel, Public Services & Government, Food, Local Flavor, Financial Services, Pets, Event
     Planning & Services, Automotive, Bicycles, Local Services, Religious Organizations, Home Services, Mass Media,
     Arts & Entertainment, Commercial Real Estate, Residential Real Estate, Real Estate}'
        """)
    conn.execute("""
        UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213,NAICS_7224,NAICS_7225,NAICS_7223}'
        WHERE is_applicable_to = '{Nightlife, Restaurants, Food, Event Planning & Services, Arts & Entertainment}'
        """)
    conn.execute("""
        UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213,NAICS_53,NAICS_7224}'
        WHERE is_applicable_to = '{Nightlife, Hotels & Travel, Residential Real Estate}'
        """)
    conn.execute("""
        UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213,NAICS_53,NAICS_7224}'
        WHERE is_applicable_to = '{Nightlife, Hotels & Travel, Home Services, Residential Real Estate}'
        """)
    conn.execute("""
        UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7211,NAICS_7211,NAICS_7213,NAICS_53,NAICS_7224,NAICS_7225,NAICS_7223}'
        WHERE is_applicable_to = '{Nightlife, Restaurants, Hotels & Travel, Food, Event Planning & Services, Commercial Real Estate, Residential Real Estate, Real Estate}'
        """)
    conn.execute("""
            UPDATE customizable_classifiers SET is_applicable_to_naics_codes = '{NAICS_7225,NAICS_7224,NAICS_7223}'
            WHERE is_applicable_to = '{Nightlife, Restaurants, Food, Event Planning & Services}'
            """)
    conn.execute("""
            UPDATE customizable_classifiers SET is_applicable_to_all_industries = true
            WHERE is_applicable_to_naics_codes is null
            """)


def downgrade():
    pass

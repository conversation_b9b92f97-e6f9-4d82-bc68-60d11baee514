"""bundled submissions

Revision ID: c41880d63c74
Revises: cab991f29030
Create Date: 2023-01-23 13:13:20.219332+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c41880d63c74"
down_revision = "cab991f29030"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "report_bundle",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column("reports_v2", sa.Column("report_bundle_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, "reports_v2", "report_bundle", ["report_bundle_id"], ["id"])
    op.add_column(
        "settings", sa.Column("allow_bundled_submissions", sa.Boolean(), nullable=True, server_default="true")
    )


def downgrade():
    op.drop_column("settings", "allow_bundled_submissions")
    op.drop_column("reports_v2", "report_bundle_id")
    op.drop_table("report_bundle")

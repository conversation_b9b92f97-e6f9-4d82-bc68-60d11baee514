"""Migrate QBE permissions

Revision ID: 3e766996be97
Revises: 69edc1ef26fb
Create Date: 2021-08-31 12:54:11.282844+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3e766996be97"
down_revision = "69edc1ef26fb"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # <NAME_EMAIL> reports to have current users as “owners”
    conn.execute("""
        update report_permissions set permission_type = 'OWNER' where report_id in 
            (select id from reports_v2 where owner_id = (select id from users where email = '<EMAIL>'));
    """)
    # Set default filter template for all QBE users to include the “owned by me” filter.
    conn.execute("""
        insert into hub_templates (id, created_at, updated_at, user_id, name, is_default, template)
            select
                uuid_generate_v4(),
                now(),
                now(),
                id,
                'Reports Owned By Me',
                true,
                '{"sort": "CREATED_AT", "agent": "", "order": "NEWEST", "agency": "", "dateTo": "", "search": "", "dateFrom": "", "dateType": "CREATED_AT", "ownership": "OWNED_BY_ME", "recommendedAction": "ALL", "reasonForDeclining": ""}'
            from users 
            where organization_id = 5;
    """)


def downgrade():
    pass

"""Add brokerage is_confirmed

Revision ID: 21426423e4bf
Revises: c5882657aaba
Create Date: 2022-10-19 11:49:41.794951+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "21426423e4bf"
down_revision = "c5882657aaba"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("brokerages", sa.Column("is_confirmed", sa.<PERSON>(), nullable=True))
    op.execute("""update brokerages set is_confirmed = true;""")


def downgrade():
    op.drop_column("brokerages", "is_confirmed")

"""Adds execution_event table

Revision ID: a2abd7fd1ed4
Revises: 0ae3600e2d29
Create Date: 2020-11-09 16:47:37.258852+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a2abd7fd1ed4"
down_revision = "72368f5adb78"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "execution_event",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "event_type",
            postgresql.ENUM("STARTED", "SUCCEEDED", "FAILED", "CANCELLED", name="executioneventtype"),
            nullable=False,
        ),
        sa.Column("occurred_at", sa.String(), nullable=True),
        sa.Column("execution_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"],
            ["reports_v2.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_execution_event_execution_id"), "execution_event", ["execution_id"], unique=False)
    op.create_index(op.f("ix_execution_event_occurred_at"), "execution_event", ["occurred_at"], unique=False)
    op.create_index(op.f("ix_execution_event_report_id"), "execution_event", ["report_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_execution_event_report_id"), table_name="execution_event")
    op.drop_index(op.f("ix_execution_event_occurred_at"), table_name="execution_event")
    op.drop_index(op.f("ix_execution_event_execution_id"), table_name="execution_event")
    op.drop_table("execution_event")

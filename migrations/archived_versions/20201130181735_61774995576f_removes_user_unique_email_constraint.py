"""Removes user unique email constraint

Revision ID: 61774995576f
Revises: c52eaa6de9bb
Create Date: 2020-11-30 18:17:35.619240+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "61774995576f"
down_revision = "c52eaa6de9bb"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("user_email_idx", table_name="users")


def downgrade():
    op.create_index("user_email_idx", "users", ["email"], unique=True)

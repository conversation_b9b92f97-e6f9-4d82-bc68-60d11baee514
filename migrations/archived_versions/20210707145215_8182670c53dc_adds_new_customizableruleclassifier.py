"""Adds new CustomizableRuleClassifier

Revision ID: 8182670c53dc
Revises: e066b605d644
Create Date: 2021-07-07 14:52:15.527234+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8182670c53dc"
down_revision = "e066b605d644"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE customizableclassifiertype ADD VALUE IF NOT EXISTS 'SIMPLE_RULES';""")
    op.create_table(
        "rules_classifier_rule",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("customizable_classifier_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("fact_subtype_id", sa.String(), nullable=False),
        sa.Column(
            "points_towards", postgresql.ENUM(name="binaryclassificationclasstype", create_type=False), nullable=True
        ),
        sa.Column("weight", sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(
            ["customizable_classifier_id"],
            ["customizable_classifiers.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_rules_classifier_rule_customizable_classifier_id"),
        "rules_classifier_rule",
        ["customizable_classifier_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_rules_classifier_rule_customizable_classifier_id"), table_name="rules_classifier_rule")
    op.drop_table("rules_classifier_rule")

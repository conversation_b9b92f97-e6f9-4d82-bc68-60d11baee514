"""Adds CAT Form Results Model Form

Revision ID: 8a0a58be4b5f
Revises: 441ff14d4adf
Create Date: 2021-05-19 11:08:23.609217-04:00

"""
from datetime import datetime
from uuid import uuid4

from alembic import op
import sqlalchemy as sa

from copilot.models.types import FieldType, FormatType, ParentType

# revision identifiers, used by Alembic.
revision = "8a0a58be4b5f"
down_revision = "441ff14d4adf"
branch_labels = None
depends_on = None


CAT_FORM_FIELDS = (
    ("Retained XS AAL (RMS Only)", FieldType.NUMBER, ParentType.SUBMISSION, False, None, None),
    ("Non-Modelled AAL", FieldType.NUMBER, ParentType.SUBMISSION, False, None, None),
    ("Cost of Reinsurance", FieldType.NUMBER, ParentType.SUBMISSION, False, None, None),
    ("Capital Cost & Cat Profit", FieldType.NUMBER, ParentType.SUBMISSION, False, None, None),
    ("Expenses", FieldType.NUMBER, ParentType.SUBMISSION, False, FormatType.PERCENTAGE, None),
    ("Commission", FieldType.NUMBER, ParentType.SUBMISSION, False, FormatType.PERCENTAGE, None),
    ("Large Cat Premium", FieldType.NUMBER, ParentType.SUBMISSION, False, None, None),
    ("Attritional Cat Premium", FieldType.NUMBER, ParentType.SUBMISSION, False, None, None),
    ("Total Cat Premium", FieldType.NUMBER, ParentType.SUBMISSION, False, None, None),
    ("Total CAT Premium: EQ", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: EQ", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: WS", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: CS", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: WT", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: FL", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: WF", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: REM", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
    ("Total CAT Premium: TOTAL", FieldType.NUMBER, ParentType.BUSINESS, False, None, None),
)


def upgrade():
    form_name = "CAT MOD RESULT"
    conn = op.get_bind()
    conn.execute(f"""INSERT INTO form_definition(id) values ('{form_name}');""")
    for row in CAT_FORM_FIELDS:
        label, field_type, field_level, is_required, format_type, description = row
        conn.execute(
            """
        INSERT INTO form_field(id, form_definition_id, created_at, label, value_type, parent_type, is_required, format, description)
        VALUES ({}, {}, {}, {}, {}, {}, {}, {}, {})""".format(
                f"'{uuid4()}'",
                f"'{form_name}'",
                f"'{datetime.utcnow()}'",
                f"'{label}'",
                f"'{field_type.name}'",
                f"'{field_level.name}'",
                str(is_required if is_required is not None else False).lower(),
                f"'{format_type.name}'" if format_type else "null",
                f"'{description}'" if description else "null",
            )
        )


def downgrade():
    pass

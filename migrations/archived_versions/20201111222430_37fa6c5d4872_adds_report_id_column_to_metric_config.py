"""Adds report_id column to metric_config

Revision ID: 37fa6c5d4872
Revises: 0cab1a3d7b32
Create Date: 2020-11-11 22:24:30.236575+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "37fa6c5d4872"
down_revision = "0cab1a3d7b32"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric_config", sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=True))


def downgrade():
    op.drop_column("metric_config", "report_id")

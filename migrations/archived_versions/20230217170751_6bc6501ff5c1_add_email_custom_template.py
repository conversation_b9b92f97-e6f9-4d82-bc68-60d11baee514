"""Add email custom template

Revision ID: 6bc6501ff5c1
Revises: 6c38e31a1da2
Create Date: 2023-02-17 17:07:51.848666+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6bc6501ff5c1"
down_revision = "6c38e31a1da2"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("decline_custom_template", sa.String(), nullable=True))


def downgrade():
    op.drop_column("submissions", "decline_custom_template")

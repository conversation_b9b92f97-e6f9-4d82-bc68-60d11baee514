"""Cascades Metric deletes to relationships

Revision ID: 683bc6da4aef
Revises: 0da16e5c94ad
Create Date: 2021-01-27 16:36:46.773973+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "683bc6da4aef"
down_revision = "0da16e5c94ad"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("category_summary_categorical_data_summary_id_fkey", "category_summary", type_="foreignkey")
    op.create_foreign_key(
        None,
        "category_summary",
        "categorical_data_summary",
        ["categorical_data_summary_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(
        "category_summary_business_category_summary_id_fkey", "category_summary_business", type_="foreignkey"
    )
    op.create_foreign_key(
        None, "category_summary_business", "category_summary", ["category_summary_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("list_summary_item_list_summary_id_fkey", "list_summary_item", type_="foreignkey")
    op.create_foreign_key(None, "list_summary_item", "list_summary", ["list_summary_id"], ["id"], ondelete="CASCADE")


def downgrade():
    op.drop_constraint(None, "list_summary_item", type_="foreignkey")
    op.create_foreign_key(
        "list_summary_item_list_summary_id_fkey", "list_summary_item", "list_summary", ["list_summary_id"], ["id"]
    )
    op.drop_constraint(None, "category_summary_business", type_="foreignkey")
    op.create_foreign_key(
        "category_summary_business_category_summary_id_fkey",
        "category_summary_business",
        "category_summary",
        ["category_summary_id"],
        ["id"],
    )
    op.drop_constraint(None, "category_summary", type_="foreignkey")
    op.create_foreign_key(
        "category_summary_categorical_data_summary_id_fkey",
        "category_summary",
        "categorical_data_summary",
        ["categorical_data_summary_id"],
        ["id"],
    )

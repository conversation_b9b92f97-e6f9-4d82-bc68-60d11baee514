"""add submission recommendation triggered at

Revision ID: 2debbbb0c71b
Revises: f897906d1ef6
Create Date: 2020-12-11 20:17:38.284920+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2debbbb0c71b"
down_revision = "f897906d1ef6"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submissions", sa.Column("recommendation_invoked_at", sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submissions", "recommendation_invoked_at")
    # ### end Alembic commands ###

"""Populates default configs

Revision ID: 18492dbc1fc9
Revises: c04ae8bc4a00
Create Date: 2021-01-28 08:09:32.566801-05:00

"""
import uuid

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "18492dbc1fc9"
down_revision = "c04ae8bc4a00"
branch_labels = None
depends_on = None

WILDFIRE_MAPPING = [
    {"text": "Very High", "score": "F"},
    {"text": "Low", "score": "B"},
    {"text": "Moderate", "score": "C"},
    {"text": "Very Low", "score": "A"},
    {"text": "High", "score": "D"},
]
EARTHQUAKE_MAPPING = [
    {"text": "No Damage", "score": "A"},
    {"text": "Very Heavy Damage", "score": "D"},
    {"text": "Light Damage", "score": "B"},
    {"text": "Moderate Damage", "score": "C"},
    {"text": "Heavy Damage", "score": "D"},
]
FLOOD_RISK_MAPPING = [
    {"score": "A", "text": "Very Low"},
    {"score": "B", "text": "Low"},
    {"score": "C", "text": "Moderate"},
    {"score": "D", "text": "High"},
    {"score": "F", "text": "Very High"},
]
HAIL_MAPPING = [
    {"text": "Moderate", "score": "C"},
    {"text": "Very Low", "score": "A"},
    {"text": "Very High", "score": "F"},
    {"text": "Low", "score": "B"},
    {"text": "High", "score": "D"},
]
LIGHTNING_MAPPING = [
    {"text": "Moderate", "score": "C"},
    {"text": "Very High", "score": "F"},
    {"text": "Very Low", "score": "A"},
    {"text": "Low", "score": "B"},
    {"text": "High", "score": "D"},
]
COASTAL_MAPPING = [
    {"text": "In SurgeMax Zone", "score": "F"},
    {"text": "Not In SurgeMax Zone", "score": "N/A"},
]
SINKHOLE_MAPPING = [
    {"text": "High Risk of Sinkhole Occurrence", "score": "D"},
    {"text": "Very High Risk of Sinkhole Occurrence", "score": "F"},
    {"text": "Elevated Sinkhole Risk", "score": "C"},
    {"text": "Minimal Sinkhole Risk", "score": "B"},
]
SNOW_LOAD_MAPPING = [
    {"score": "D", "text": "High"},
    {"score": "B", "text": "Very Low"},
    {"score": "F", "text": "Very High"},
    {"score": "A", "text": "None"},
]
TORNADO_MAPPING = [
    {"score": "D", "text": "High"},
    {"score": "C", "text": "Moderate"},
    {"score": "F", "text": "Very High"},
    {"score": "A", "text": "Very Low"},
    {"score": "B", "text": "Low"},
]

CONFIGS = [
    {"name": "Wildfire", "path": "location.wildfire", "mapping": WILDFIRE_MAPPING},
    {"name": "Earthquake", "path": "location.earthquake", "mapping": EARTHQUAKE_MAPPING},
    {"name": "Flood Risk", "path": "location.flood_risk", "mapping": FLOOD_RISK_MAPPING},
    {"name": "Hail Risk", "path": "location.hail", "mapping": HAIL_MAPPING},
    {"name": "Lightning Risk", "path": "location.lightning", "mapping": LIGHTNING_MAPPING},
    {"name": "Coastal Storm Surge Risk", "path": "location.coastal_storm_surge", "mapping": COASTAL_MAPPING},
    {"name": "Sinkhole Risk", "path": "location.sinkhole", "mapping": SINKHOLE_MAPPING},
    {"name": "Snow Load Risk", "path": "location.snow_load", "mapping": SNOW_LOAD_MAPPING},
    {"name": "Tornado Risk", "path": "location.tornado", "mapping": TORNADO_MAPPING},
]


def upgrade():
    values = []
    conn = op.get_bind()
    for config in CONFIGS:
        field_id = str(uuid.uuid4())
        field_options = tuple([f'"{row["text"]}"' for row in config["mapping"]])
        field_options = "{" + ",".join(field_options) + "}"
        conn.execute(f"""INSERT INTO interpretable_field(id, name, dossier_path, interpretation_options) 
                          values ('{field_id}', '{config['name']}', '{config['path']}', '{field_options}')""")
        for row in config["mapping"]:
            values.append(f"(uuid_generate_v4(), '{field_id}', '{row['score']}', '{row['text']}')")
    conn.execute(f"""INSERT INTO interpretation_config(id, field_id, field_value, interpretation) 
                  values {", ".join(values)}""")


def downgrade():
    pass

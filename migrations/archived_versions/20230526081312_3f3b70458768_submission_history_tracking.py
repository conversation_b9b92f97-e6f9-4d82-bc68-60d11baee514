"""submission history tracking

Revision ID: 3f3b70458768
Revises: 182ab96f0a48
Create Date: 2023-05-24 08:13:12.776024+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3f3b70458768"
down_revision = "2692a7793c15"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
DROP TRIGGER IF EXISTS submission_history_insert_stage_change_trigger on submissions;

CREATE TRIGGER submission_history_insert_stage_change_trigger
AFTER UPDATE ON submissions
FOR EACH ROW
WHEN (NEW.stage <> OLD.stage)
EXECUTE PROCEDURE submission_history_insert_stage_change();
""")

    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'INDICATED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'COMPLETED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'WAITING_FOR_OTHERS';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'CLEARING_ISSUE';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'BLOCKED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'EXPIRED';")


def downgrade():
    pass

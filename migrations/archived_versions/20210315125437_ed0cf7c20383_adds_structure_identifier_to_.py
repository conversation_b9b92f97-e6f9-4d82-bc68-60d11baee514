"""Adds structure identifier to SubmissionBusinessFieldValue

Revision ID: ed0cf7c20383
Revises: 59abc7c54d55
Create Date: 2021-03-15 12:54:37.478311-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed0cf7c20383"
down_revision = "59abc7c54d55"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_business_field_values", sa.Column("structure_identifier", sa.String(), nullable=True))
    op.execute("COMMIT")
    op.create_index(
        "submission_business_field_val_submission_business_id_struct_key",
        "submission_business_field_values",
        ["submission_business_id", "structure_identifier", "submission_business_field_id"],
        unique=True,
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_constraint(None, "submission_business_field_values", type_="unique")
    op.drop_column("submission_business_field_values", "structure_identifier")

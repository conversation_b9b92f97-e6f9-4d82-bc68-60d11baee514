"""management liability mode

Revision ID: a5a9d0726a79
Revises: bc318baeaab7
Create Date: 2023-04-04 23:54:10.781364+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a5a9d0726a79"
down_revision = "bc318baeaab7"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # create the mode
    conn.execute("""
        INSERT INTO modes (id, name, props) VALUES ('1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 'Management Liability',
        '{"context": {"type": "parents-select", "parentType": "PREMISES"}}')
        ON CONFLICT DO NOTHING;
        
        INSERT INTO mode_sections (id, name, props)
        VALUES ('0c8bf681-fb6e-4a56-83d3-8e00e537a0d9', null, '{
          "strategy": {
            "type": "conditional",
            "condition": {
              "conditions": [
                {
                  "type": "report",
                  "jsonPath": "$.submissions[0].coverages[*].coverage.name",
                  "containsOtherValues": "fiduciaryLiability" 
                }
              ],
              "type": "or"
            }
          }
        }'),
        ('a8e1680f-1c71-472e-a1e3-3b76e23c0865','Premises Building Permits', '{
            "context": {"type": "parents-select", "parentType": "PREMISES"}, 
            "overrides": [{"type": "row-title", "title": "Premises Building Permits"}],
            "strategy": {
                "type": "conditional",
                "condition": {
                  "conditions": [
                    {
                      "type": "report",
                      "jsonPath": "$.submissions[0].coverages[*].coverage.name",
                      "containsOtherValues": "fiduciaryLiability" 
                    }
                  ],
                  "type": "or"
                }
          }
        }'
        )
        ON CONFLICT DO NOTHING;
        
        INSERT INTO mode_permissions(id, mode_id, organization_id, is_shared_across_organization)
        VALUES ('95766c56-5fc5-4b71-8fc9-f9fc5b54d5e2', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 3, TRUE)
        ON CONFLICT DO NOTHING;  
        
                    
        --SUPPORT    
        INSERT INTO mode_rows (id, mode_id, position, title)
        VALUES('7a872de9-27e7-44ab-a495-61c932df1d3d', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 0, 'Support');
        
        INSERT INTO mode_columns (id, row_id, position, width)
        VALUES('c2c206b3-68d9-4f27-b7b5-05ba80a7f3f3', '7a872de9-27e7-44ab-a495-61c932df1d3d', 0, 12);
        
        INSERT INTO mode_cards (id, column_id, title, position, type, card_id)
        VALUES('2ca2c497-50d2-48c6-9d1f-dc6bca84935d', 'c2c206b3-68d9-4f27-b7b5-05ba80a7f3f3', 'Support', 0, 'SUBMISSION_INFO_SUPPORT', 'support');
               
               
        INSERT INTO mode_elements (id, mode_id, position, row_id, section_id) VALUES
        ('06d81905-9376-48c6-8c1c-18cfc0e6e330', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 0, '7a872de9-27e7-44ab-a495-61c932df1d3d', NULL),
        ('0253c7a9-cd75-49ec-9ee8-71034ec84118', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 50, 'fa6f1865-7777-455e-92c0-fa0a4e83502a', NULL),
        ('89753d47-8a89-4c0e-9d9e-cc525f8cc91b', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 60, 'e357c13b-c054-4796-8ba2-7e5fc15b30ab', NULL),
        ('e4abf4a4-b6a4-4d81-a3b3-3a7c707a2cfb', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 100, 'aefdad38-63fe-4d95-9cb9-cf119e7562d0', NULL),
        ('44f1b1c7-34ec-4a7d-8bb3-3c69a6ef2fc9', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 200, '8dd78b11-7673-4f71-bf33-f5b0c5aab32c', NULL),
        ('514e6de7-8638-4421-85f1-9c9a7a13b123', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 300, 'fbe14804-eff5-414c-bdd2-c56aaacf5603', NULL),
        ('e5d5b87f-7e1e-46d5-a73f-7409c33e3486', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 400, 'fa533c0e-4c6d-42f7-992d-6bfe1dc99c30', NULL),
        ('67f14d11-eeb7-44f3-8e18-49b7c12d04f9', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 500, 'd9d4d5a3-31cf-4c08-947c-c03f0499ba7f', NULL),
        ('068f8c0b-6f1a-44d7-aed8-d6e87f6b38d6', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 600, '6c246235-46ef-403e-bd2c-a519c5aa0f50', NULL),
        ('a8e24f3d-9b10-48bb-9b20-b3e5d5b5af5d', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 1000, NULL, '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9'),
        ('e512b9e9-a7a1-47a7-88e8-8a68d7baf759', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 1100, NULL, 'a8e1680f-1c71-472e-a1e3-3b76e23c0865')
        ON CONFLICT
            DO NOTHING;
        
        INSERT INTO mode_section_elements (id, section_id, position, child_row_id)
        VALUES 
            ('6f7b93c2-03f4-4e4d-9d2e-2f1871e0cc94', '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9', 100, '1647f338-99e7-41d8-a5c0-847cd1a9bf8a'),
            ('f0ba93b7-5576-4a6d-b13b-d93fcf03aa49', '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9', 200, '33c91db4-32b8-4fa8-8852-1131fbfd3e25'),
            ('c4a0e6d9-6a98-4a92-8c7d-bf6308191c35', '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9', 300, '287e71a2-e24a-48d5-b774-0da2315779aa'),
            ('7b60e94e-40f5-4f24-a356-81e840ea5ce5', '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9', 400, '0c21aa0d-c055-4c11-8a0b-83b511e0e121'),
            ('dde5e4a4-4c4a-4b69-8366-5755e5a5eb5c', 'a8e1680f-1c71-472e-a1e3-3b76e23c0865', 500, '8dd78b11-7673-4f71-bf33-f5b0c5aab32c')
                ON CONFLICT DO NOTHING;

    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("""
            DELETE
            FROM mode_section_elements
            WHERE section_id = '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9';
            DELETE
            FROM mode_elements
            WHERE mode_id = '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052';
            DELETE
            FROM mode_sections
            WHERE id = '0c8bf681-fb6e-4a56-83d3-8e00e537a0d9';
            DELETE
            FROM mode_cards
            WHERE id = '2ca2c497-50d2-48c6-9d1f-dc6bca84935d';
            DELETE
            FROM mode_columns
            WHERE id = 'c2c206b3-68d9-4f27-b7b5-05ba80a7f3f3';
            DELETE
            FROM mode_rows
            WHERE mode_id = '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052';
            DELETE
            FROM mode_permissions
            WHERE mode_id = '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052';
        """)

"""Allows null metric_config_id in metric

Revision ID: e8290e69ca98
Revises: 0e444ce81c3e
Create Date: 2022-06-03 12:05:42.974635+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e8290e69ca98"
down_revision = "0e444ce81c3e"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("metric", "metric_config_id", existing_type=postgresql.UUID(), nullable=True)


def downgrade():
    op.alter_column("metric", "metric_config_id", existing_type=postgresql.UUID(), nullable=False)

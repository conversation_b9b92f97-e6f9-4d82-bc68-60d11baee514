"""Disassociates legacy Nationwide user and Organization

Revision ID: 8de722eb02f7
Revises: 6cba7ddc8a9b
Create Date: 2022-02-23 21:52:22.542605+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "8de722eb02f7"
down_revision = "6cba7ddc8a9b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    UPDATE users 
    SET organization_id = null 
    WHERE external_id IS null 
    AND email = '<EMAIL>';
    """)

    conn.execute("""
    UPDATE report_permissions
    SET grantee_user_id = (SELECT id FROM users WHERE email = '<EMAIL>')
    WHERE grantee_user_id = (SELECT id FROM users WHERE email = '<EMAIL>');
    """)


def downgrade():
    pass

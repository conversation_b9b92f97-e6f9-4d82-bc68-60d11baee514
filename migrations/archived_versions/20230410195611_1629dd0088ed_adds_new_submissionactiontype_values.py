"""Adds new SubmissionActionType values

Revision ID: 1629dd0088ed
Revises: 1bab671b2b76
Create Date: 2023-04-10 19:56:11.365042+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1629dd0088ed"
down_revision = "1bab671b2b76"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_PROCESSING_COMPLETED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_ENTITY_MAPPING_COMPLETED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_DATA_ONBOARDING_COMPLETED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_VERIFICATION_COMPLETED';")


def downgrade():
    ...

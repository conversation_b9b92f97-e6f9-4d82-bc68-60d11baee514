"""Update sensible status of file id ece88292-d8e8-4243-889e-53aa06689509

Revision ID: 611d3ebea523
Revises: 373c1aba32e3
Create Date: 2023-05-22 12:49:29.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

revision = "611d3ebea523"
down_revision = "373c1aba32e3"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update files set sensible_status = 'COMPLETE' where id='ece88292-d8e8-4243-889e-53aa06689509';
    """)


def downgrade():
    pass

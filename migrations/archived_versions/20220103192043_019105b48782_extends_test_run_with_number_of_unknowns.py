"""Extends test run with number_of_unknowns

Revision ID: 019105b48782
Revises: e59fa1733e2f
Create Date: 2022-01-03 19:20:43.154209+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "019105b48782"
down_revision = "e59fa1733e2f"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("customizable_classifier_test_runs", sa.Column("number_of_unknowns", sa.Integer(), nullable=True))


def downgrade():
    op.drop_column("customizable_classifier_test_runs", "number_of_unknowns")

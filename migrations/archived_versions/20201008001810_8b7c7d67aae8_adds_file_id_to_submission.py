"""Adds file_id to Submission

Revision ID: 8b7c7d67aae8
Revises: c6184b2e7359
Create Date: 2020-10-08 00:18:10.820346+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8b7c7d67aae8"
down_revision = "c6184b2e7359"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=True))


def downgrade():
    op.drop_column("submissions", "file_id")

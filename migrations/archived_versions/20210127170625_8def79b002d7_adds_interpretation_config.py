"""Adds interpretation config

Revision ID: 8def79b002d7
Revises: ed2f057fc6f6
Create Date: 2021-01-27 17:06:25.113606-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8def79b002d7"
down_revision = "ed2f057fc6f6"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "interpretable_field",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("dossier_path", sa.String(), nullable=False),
        sa.Column("interpretation_options", sa.ARRAY(sa.String()), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "interpretation_config",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("field_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("field_value", sa.String(), nullable=False),
        sa.Column("interpretation", sa.String(), nullable=False),
        sa.Column("organization_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["field_id"],
            ["interpretable_field.id"],
        ),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "interpretation_config_org_field_value_index",
        "interpretation_config",
        ["field_id", "field_value", "organization_id"],
        unique=True,
    )
    op.create_index(op.f("ix_interpretation_config_field_id"), "interpretation_config", ["field_id"], unique=False)
    op.create_index(
        op.f("ix_interpretation_config_organization_id"), "interpretation_config", ["organization_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_interpretation_config_organization_id"), table_name="interpretation_config")
    op.drop_index(op.f("ix_interpretation_config_field_name"), table_name="interpretation_config")
    op.drop_index("customizable_interpretation_config_org_filed_name_value_index", table_name="interpretation_config")
    op.drop_table("interpretation_config")

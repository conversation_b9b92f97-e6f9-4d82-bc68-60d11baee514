"""Add LOB for organizations

Revision ID: 085435787452
Revises: bcb2f2fe4f36
Create Date: 2022-08-03 10:07:13.584752+00:00

"""
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "085435787452"
down_revision = "bcb2f2fe4f36"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(f"INSERT INTO lob(id, display_name) VALUES ('{uuid4()}', 'Primary Construction')")
    conn.execute(f"INSERT INTO lob(id, display_name) VALUES ('{uuid4()}', 'Excess Construction')")
    conn.execute(f"INSERT INTO lob(id, display_name) VALUES ('{uuid4()}', 'Manufacturing')")
    conn.execute(f"INSERT INTO lob(id, display_name) VALUES ('{uuid4()}', 'Property')")
    conn.execute(f"INSERT INTO lob(id, display_name) VALUES ('{uuid4()}', 'Excess Casualty')")
    conn.execute(
        f"update settings set allowed_submission_lobs=array_append(allowed_submission_lobs,lob.id) from lob where"
        f" lob.display_name='Primary Construction' and settings.organization_id=6;"
    )
    conn.execute(
        f"update settings set allowed_submission_lobs=array_append(allowed_submission_lobs,lob.id) from lob where"
        f" lob.display_name='Excess Construction' and settings.organization_id=6;"
    )
    conn.execute(
        f"update settings set allowed_submission_lobs=array_append(allowed_submission_lobs,lob.id) from lob where"
        f" lob.display_name='Manufacturing' and settings.organization_id=6;"
    )
    conn.execute(
        f"update settings set allowed_submission_lobs=array_append(allowed_submission_lobs,lob.id) from lob where"
        f" lob.display_name='Property' and settings.organization_id=6;"
    )
    conn.execute(
        f"update settings set allowed_submission_lobs=array_append(allowed_submission_lobs,lob.id) from lob where"
        f" lob.display_name='Excess Casualty' and settings.organization_id=7;"
    )


def downgrade():
    pass

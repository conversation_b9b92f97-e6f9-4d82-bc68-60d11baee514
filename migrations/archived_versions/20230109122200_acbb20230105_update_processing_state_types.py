"""Add column email_subject to reports_v2 table

Revision ID: acbb20230105
Revises: bbbb20230105
Create Date: 2023-01-05 22:22:00.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

revision = "acbb20230105"
down_revision = "bbbb20230105"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(f"ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'ENTITY_MAPPING';")
        op.execute(f"ALTER TYPE fileprocessingstate ADD VALUE IF NOT EXISTS 'WAITING_FOR_ENTITY_MAPPING';")


def downgrade():
    pass

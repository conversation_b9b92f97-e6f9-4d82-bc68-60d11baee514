"""remove settings for Management Dashboard

Revision ID: bb64aa7784ec
Revises: f53285449904
Create Date: 2023-02-01 12:07:11.067340+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2eb7055aef24"
down_revision = "f53285449904"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("settings", "is_management_dashboard_enabled")
    op.drop_column("settings", "is_underwriter_dashboard_enabled")


def downgrade():
    op.add_column("settings", sa.Column("is_management_dashboard_enabled", sa.<PERSON>(), nullable=True))
    op.add_column("settings", sa.Column("is_underwriter_dashboard_enabled", sa.<PERSON>(), nullable=True))

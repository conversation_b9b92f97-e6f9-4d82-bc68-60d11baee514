"""Set role as manager for Danny

Revision ID: 5aef92b0c6e4
Revises: 11f41db9c025
Create Date: 2022-01-15 17:11:58.058907+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5aef92b0c6e4"
down_revision = "11f41db9c025"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            update users set role = 'manager' where email = '<EMAIL>'
        """)


def downgrade():
    pass

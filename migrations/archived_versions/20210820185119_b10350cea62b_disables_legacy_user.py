"""Disables legacy user

Revision ID: b10350cea62b
Revises: b668232cafc6
Create Date: 2021-08-20 18:51:19.363958+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "b10350cea62b"
down_revision = "b668232cafc6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users set email = 'defunct' || id || '@kalepa.co', organization_id = null 
    where id = 187 and email = '<PERSON>.<PERSON>@us.qbe.com'
    """)


def downgrade():
    pass

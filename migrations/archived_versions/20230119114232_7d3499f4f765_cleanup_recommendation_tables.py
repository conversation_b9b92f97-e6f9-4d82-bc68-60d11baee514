"""Cleanup recommendation tables

Revision ID: 7d3499f4f765
Revises: 61b1bf950c58
Create Date: 2023-01-19 11:42:32.063558+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7d3499f4f765"
down_revision = "61b1bf950c58"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("submissions", "expected_value")
    op.drop_column("submissions", "recommendation_action")
    op.drop_column("submissions", "recommendation_invoked_at")


def downgrade():
    pass

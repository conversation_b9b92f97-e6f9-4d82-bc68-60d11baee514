"""Removes older recommendations and makes submission_id unique

Revision ID: 00bb9fc3be86
Revises: 3fb4ad5285c8
Create Date: 2021-05-14 16:19:50.488769-04:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "00bb9fc3be86"
down_revision = "3fb4ad5285c8"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""DELETE FROM recommendations R1 USING   recommendations R2
         WHERE R1.created_at < R2.created_at AND R1.submission_id  = R2.submission_id;""")
    op.drop_index("ix_recommendations_submission_id", table_name="recommendations")
    op.create_index(op.f("ix_recommendations_submission_id"), "recommendations", ["submission_id"], unique=True)


def downgrade():
    op.drop_index(op.f("ix_recommendations_submission_id"), table_name="recommendations")
    op.create_index("ix_recommendations_submission_id", "recommendations", ["submission_id"], unique=False)

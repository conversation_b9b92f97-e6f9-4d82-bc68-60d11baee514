"""Adds submission processing_state index 

Revision ID: 1ae69214e9c1
Revises: a98dffdc3760
Create Date: 2023-05-22 11:00:57.465783+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "1ae69214e9c1"
down_revision = "a98dffdc3760"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.create_index(
            "ix_submissions_processing_state",
            "submissions",
            ["processing_state"],
            unique=False,
            postgresql_concurrently=True,
        )


def downgrade():
    op.drop_index("ix_submissions_processing_state", table_name="submissions")

"""Get rid of metric_config tables

Revision ID: 04fca500a749
Revises: 4c35a0d43b34
Create Date: 2022-07-05 13:01:41.549977+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "04fca500a749"
down_revision = "4c35a0d43b34"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '600 s';")  # 10 min

        # Metric
        op.drop_index("ix_metric_metric_config_id", table_name="metric")
        op.drop_constraint("metric_metric_config_id_fkey", "metric", type_="foreignkey")
        op.drop_column("metric", "metric_config_id")

        # MetricPreference
        op.drop_index("ix_metric_preference_metric_config_id", table_name="metric_preference")
        op.drop_constraint("metric_preference_report_id_metric_config_id_key", "metric_preference", type_="unique")
        op.drop_constraint("metric_preference_metric_config_id_fkey", "metric_preference", type_="foreignkey")
        op.drop_column("metric_preference", "metric_config_id")

        # MetricConfig
        op.drop_index("ix_metric_config_display_name", table_name="metric_config")
        op.drop_index("ix_metric_config_metric_group_id", table_name="metric_config")
        op.drop_index("ix_metric_config_metric_type", table_name="metric_config")
        op.drop_index("ix_metric_config_report_id", table_name="metric_config")
        op.drop_index("ix_metric_config_submission_business_id", table_name="metric_config")
        op.drop_index("metric_config_name_report_business_idx", table_name="metric_config")
        op.drop_index("metric_config_name_report_idx", table_name="metric_config")

        op.drop_table("metric_config")


def downgrade():
    # MetricConfig
    op.create_table(
        "metric_config",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("display_name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("icon_name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "metric_type",
            postgresql.ENUM(
                "MEAN",
                "RANGE_SUMMARY",
                "GRADE_SUMMARY",
                "CATEGORICAL_DATA_SUMMARY",
                "LIST_SUMMARY",
                "EMPLOYEES_SUMMARY",
                "SUM",
                "DATE_RANGE_SUMMARY",
                "NUMERIC_RANGE_SUMMARY",
                name="metrictype",
            ),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("metric_group_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column("report_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("is_submitted_data", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column("submission_business_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("is_structure_summary_config", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column("parent_metric", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ("metric_group_id",),
            ["metric_group.id"],
            name="metric_config_metric_group_id_fkey",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(("report_id",), ["reports_v2.id"], name="metric_config_report_id_fkey"),
        sa.ForeignKeyConstraint(
            ("submission_business_id",),
            ["submission_businesses.id"],
            name="metric_config_submission_business_id_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name="metric_config_pkey"),
    )
    op.create_index("metric_config_name_report_idx", "metric_config", ["display_name", "report_id"], unique=True)
    op.create_index(
        "metric_config_name_report_business_idx",
        "metric_config",
        ["display_name", "report_id", "submission_business_id"],
        unique=True,
    )
    op.create_index(
        "ix_metric_config_submission_business_id",
        "metric_config",
        ["submission_business_id"],
        unique=False,
    )
    op.create_index("ix_metric_config_report_id", "metric_config", ["report_id"], unique=False)
    op.create_index("ix_metric_config_metric_type", "metric_config", ["metric_type"], unique=False)
    op.create_index("ix_metric_config_metric_group_id", "metric_config", ["metric_group_id"], unique=False)
    op.create_index("ix_metric_config_display_name", "metric_config", ["display_name"], unique=False)

    # MetricPreference
    op.add_column(
        "metric_preference",
        sa.Column("metric_config_id", postgresql.UUID(), autoincrement=False, nullable=True),
    )
    op.create_foreign_key(
        "metric_preference_metric_config_id_fkey",
        "metric_preference",
        "metric_config",
        ["metric_config_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_unique_constraint(
        "metric_preference_report_id_metric_config_id_key",
        "metric_preference",
        ["report_id", "metric_config_id"],
    )
    op.create_index("ix_metric_preference_metric_config_id", "metric_preference", ["metric_config_id"], unique=False)

    # Metric
    op.add_column("metric", sa.Column("metric_config_id", postgresql.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key(
        "metric_metric_config_id_fkey",
        "metric",
        "metric_config",
        ["metric_config_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_index("ix_metric_metric_config_id", "metric", ["metric_config_id"], unique=False)

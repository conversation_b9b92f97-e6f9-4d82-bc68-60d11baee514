"""Add org-level-permission to permissions

Revision ID: 8ca0c30064c4
Revises: 7be7ce169c91
Create Date: 2021-09-17 09:49:36.267234+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8ca0c30064c4"
down_revision = "7be7ce169c91"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "report_permissions", sa.Column("is_org_permission", sa.<PERSON><PERSON>(), nullable=False, server_default="f")
    )
    op.create_index(
        op.f("ix_report_permissions_is_org_permission"), "report_permissions", ["is_org_permission"], unique=False
    )
    conn = op.get_bind()
    conn.execute("""
        update reports_v2 set organization_permission_level = 'EDITOR'
        where owner_id = (select id from users where email = '<EMAIL>')
    """)
    conn.execute("""
        update report_permissions set is_org_permission = 'true'
        where report_id in
            (select id from reports_v2 where owner_id = (select id from users where email = '<EMAIL>'))
        and
            permission_type = 'EDITOR'
    """)


def downgrade():
    op.drop_index(op.f("ix_report_permissions_is_org_permission"), table_name="report_permissions")
    op.drop_column("report_permissions", "is_org_permission")

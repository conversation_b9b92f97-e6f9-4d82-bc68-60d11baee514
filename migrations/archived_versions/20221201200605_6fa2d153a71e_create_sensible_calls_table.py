"""some message

Revision ID: 6fa2d153a71e
Revises: a9424ca12e2f
Create Date: 2022-12-01 20:06:05.867221+00:00

"""
from uuid import uuid4

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

from copilot.models.sensible_calls import DAYS_OF_MONTH, organization_id_for_migration

# revision identifiers, used by Alembic.
revision = "6fa2d153a71e"
down_revision = "a9424ca12e2f"
branch_labels = None
depends_on = None


sensible_calls_init_query = sa.text("""
    INSERT INTO sensible_calls (
        id,
        organization_id, 
        year,
        month,
        day,
        calls_made
    )
    VALUES (
        :id,
        :organization_id, 
        :year, 
        :month, 
        :day, 
        :calls_made
    ) ON CONFLICT DO NOTHING;
    """)


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.create_table(
        "sensible_calls",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False, default=uuid4()),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.Column("year", sa.Integer(), nullable=False),
        sa.Column("month", sa.Integer(), nullable=False),
        sa.Column("day", sa.Integer(), nullable=False),
        sa.Column("calls_made", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["organization_id"], ["organization.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("sensible_calls_ix", "sensible_calls", ["day", "organization_id", "year", "month"])
    conn = op.get_bind()

    for organization_id in organization_id_for_migration:
        for year in range(2022, 2024):
            for month in range(1, 13):
                for day in range(1, DAYS_OF_MONTH[month]):
                    query_params = {
                        "id": uuid4(),
                        "organization_id": organization_id,
                        "year": year,
                        "month": month,
                        "day": day,
                        "calls_made": 0,
                    }
                    conn.execute(statement=sensible_calls_init_query, **query_params)

    conn.execute(
        "UPDATE settings SET loss_runs_enabled = true WHERE user_id =16;"
    )  # turn on loss runs for one test user
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_table("sensible_calls")
    conn = op.get_bind()
    conn.execute("UPDATE settings SET loss_runs_enabled = false WHERE user_id =16;")
    # ### end Alembic commands ###

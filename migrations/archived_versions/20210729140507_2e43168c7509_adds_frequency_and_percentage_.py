"""Adds frequency and percentage todifferent metric distributions

Revision ID: 2e43168c7509
Revises: f0aab33a9e01
Create Date: 2021-07-29 14:05:07.798734+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2e43168c7509"
down_revision = "f0aab33a9e01"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("category_summary", sa.Column("percentage", sa.Float(), nullable=True))
    op.add_column("role_summary", sa.Column("frequency", sa.Integer(), nullable=True))
    op.add_column("role_summary", sa.Column("percentage", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("role_summary", "percentage")
    op.drop_column("role_summary", "frequency")
    op.drop_column("category_summary", "percentage")

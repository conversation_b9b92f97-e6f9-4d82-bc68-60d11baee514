"""Creates copilot worker execution events

Revision ID: 7893061831ef
Revises: dc8565357ef5
Create Date: 2022-03-28 08:52:09.949369+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7893061831ef"
down_revision = "dc8565357ef5"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "copilot_worker_execution_event",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("submission_business_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column("first_party_field_original_name", sa.String(), nullable=True),
        sa.Column("old_parent_id", postgresql.UUID(), nullable=True),
        sa.Column(
            "execution_type",
            postgresql.ENUM("REASSIGN_FACT_SUBTYPE", "REASSIGN_PARENT", name="copilotworkerexecutiontype"),
            nullable=True,
        ),
        sa.Column(
            "event_type",
            postgresql.ENUM(
                "STARTED", "SUCCEEDED", "FAILED", "CANCELLED", name="executioneventtype", create_type=False
            ),
            nullable=False,
        ),
        sa.Column("occurred_at", sa.String(), nullable=True),
        sa.Column("execution_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["submission_business_id"],
            ["submission_businesses.id"],
        ),
        sa.ForeignKeyConstraint(
            ["submission_id"],
            ["submissions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "copilot_worker_execution_event_index",
        "copilot_worker_execution_event",
        ["submission_id", "submission_business_id", "first_party_field_original_name", "execution_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_copilot_worker_execution_event_execution_id"),
        "copilot_worker_execution_event",
        ["execution_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_copilot_worker_execution_event_occurred_at"),
        "copilot_worker_execution_event",
        ["occurred_at"],
        unique=False,
    )
    op.create_index(
        op.f("ix_copilot_worker_execution_event_submission_business_id"),
        "copilot_worker_execution_event",
        ["submission_business_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_copilot_worker_execution_event_submission_id"),
        "copilot_worker_execution_event",
        ["submission_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_copilot_worker_execution_event_submission_id"), table_name="copilot_worker_execution_event")
    op.drop_index(
        op.f("ix_copilot_worker_execution_event_submission_business_id"), table_name="copilot_worker_execution_event"
    )
    op.drop_index(op.f("ix_copilot_worker_execution_event_occurred_at"), table_name="copilot_worker_execution_event")
    op.drop_index(op.f("ix_copilot_worker_execution_event_execution_id"), table_name="copilot_worker_execution_event")
    op.drop_index("copilot_worker_execution_event_index", table_name="copilot_worker_execution_event")
    op.drop_table("copilot_worker_execution_event")

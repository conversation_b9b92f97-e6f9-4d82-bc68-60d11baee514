"""Adds list summary tables

Revision ID: 12dfbedcc867
Revises: 3b7f4626520b
Create Date: 2020-09-04 15:51:21.433015+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "12dfbedcc867"
down_revision = "3b7f4626520b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "list_summary",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("list_item_type", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["id"],
            ["metric.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_list_summary_id"), "list_summary", ["id"], unique=False)
    op.create_table(
        "list_summary_item",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("list_summary_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("value", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["list_summary_id"],
            ["list_summary.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_list_summary_item_list_summary_id"), "list_summary_item", ["list_summary_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_list_summary_item_list_summary_id"), table_name="list_summary_item")
    op.drop_table("list_summary_item")
    op.drop_index(op.f("ix_list_summary_id"), table_name="list_summary")
    op.drop_table("list_summary")

"""Adds fact_subtype_id to submission_business_field

Revision ID: 3d0522d5d47a
Revises: b10350cea62b
Create Date: 2021-08-24 08:42:54.907507+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3d0522d5d47a"
down_revision = "b10350cea62b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_business_fields", sa.Column("fact_subtype_id", sa.String(), nullable=True))
    op.create_index(
        op.f("ix_submission_business_fields_fact_subtype_id"),
        "submission_business_fields",
        ["fact_subtype_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(op.f("ix_submission_business_fields_fact_subtype_id"), table_name="submission_business_fields")
    op.drop_column("submission_business_fields", "fact_subtype_id")

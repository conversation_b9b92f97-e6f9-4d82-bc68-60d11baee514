"""update stage trigger

Revision ID: 7b9b26fdc275
Revises: 97a8113f7e4c
Create Date: 2023-04-21 10:58:47.219308+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7b9b26fdc275"
down_revision = "97a8113f7e4c"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
CREATE OR REPLACE FUNCTION submission_history_insert_stage_change() RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at) 
    VALUES (uuid_generate_v4(), NEW.id, NEW.stage::text::submissionactiontype, now());

    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';
CREATE TRIGGER submission_history_insert_stage_change_trigger
AFTER UPDATE ON submissions
FOR EACH ROW
WHEN (NEW.stage <> OLD.stage AND NEW.stage IN ('ON_MY_PLATE', 'QUOTED', 'DECLINED', 'QUOTED_LOST', 'QUOTED_BOUND'))
EXECUTE PROCEDURE submission_history_insert_stage_change();

CREATE OR REPLACE FUNCTION submission_history_insert_submission_creation() RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at) VALUES (uuid_generate_v4(), NEW.id, 'ON_MY_PLATE', now());
    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';
CREATE TRIGGER submission_history_insert_submission_creation_trigger
AFTER INSERT ON submissions
FOR EACH ROW
EXECUTE PROCEDURE submission_history_insert_submission_creation();

CREATE OR REPLACE FUNCTION submission_history_insert_report_permission() RETURNS TRIGGER AS $$
    DECLARE
        s_id UUID;
        is_owner_perm bool;
        history_rows SMALLINT;
BEGIN
    SELECT submission_id into s_id FROM submissions_reports WHERE report_id = NEW.report_id;

    SELECT COUNT(*)
    INTO history_rows
    FROM submission_history sh
    WHERE sh.submission_action_type = 'SHARED' AND sh.submission_id = s_id;

    SELECT CASE WHEN (owner_id = NEW.grantee_user_id)
        THEN TRUE
        ELSE FALSE END
        INTO is_owner_perm
        FROM reports_v2
        WHERE id = NEW.report_id;


    IF history_rows = 0 AND NOT is_owner_perm THEN
            INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at, parent_type, parent_id)
            VALUES (uuid_generate_v4(), s_id, 'SHARED', now(), 'REPORT', NEW.report_id);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';
CREATE TRIGGER submission_history_insert_report_permission_trigger
AFTER INSERT ON report_permissions
FOR EACH ROW
WHEN (NEW.is_org_permission = FALSE)
EXECUTE PROCEDURE submission_history_insert_report_permission();

CREATE OR REPLACE FUNCTION submission_history_insert_report_org_permission() RETURNS TRIGGER AS $$
    DECLARE
        s_id UUID;
        history_rows SMALLINT;
BEGIN
    SELECT submission_id into s_id FROM submissions_reports WHERE report_id = NEW.id;

    SELECT COUNT(*)
    INTO history_rows
    FROM submission_history sh
    WHERE sh.submission_action_type = 'SHARED' AND sh.submission_id = s_id;

    IF history_rows = 0 THEN
            INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at, parent_type, parent_id)
            VALUES (uuid_generate_v4(), s_id, 'SHARED', now(), 'REPORT', NEW.id);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';
CREATE TRIGGER submission_history_insert_report_org_permission_r_upd_trigger
AFTER UPDATE ON reports_v2
FOR EACH ROW
WHEN (OLD.organization_permission_level IS NULL AND NEW.organization_permission_level IS NOT NULL)
EXECUTE PROCEDURE submission_history_insert_report_org_permission();

CREATE OR REPLACE FUNCTION submission_history_insert_report_subm_org_permission() RETURNS TRIGGER AS $$
    DECLARE
        org_perm_level permissiontype;
        history_rows SMALLINT;
BEGIN
    SELECT organization_permission_level
    INTO org_perm_level
    FROM reports_v2
    WHERE id = NEW.report_id;

    IF org_perm_level IS NULL THEN
        RETURN NEW;
    END IF;

    SELECT COUNT(*)
    INTO history_rows
    FROM submission_history sh
    WHERE sh.submission_action_type = 'SHARED' AND sh.submission_id = new.submission_id;

    IF history_rows = 0 THEN
            INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at, parent_type, parent_id)
            VALUES (uuid_generate_v4(), NEW.submission_id, 'SHARED', now(), 'REPORT', NEW.report_id);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

CREATE TRIGGER submission_history_insert_report_subm_org_permission_trigger
AFTER INSERT ON submissions_reports
FOR EACH ROW
EXECUTE PROCEDURE submission_history_insert_report_subm_org_permission();
        """)


def downgrade():
    pass

"""unifies data format and changes insight_feedback.value to jsonb

Revision ID: a6c6bab31ede
Revises: b0f4a32da6a8
Create Date: 2020-10-09 12:00:48.479189+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a6c6bab31ede"
down_revision = "b0f4a32da6a8"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        sa.text(
            """update feedback set value='"' || feedback.value || '"' where (value not LIKE '"%' and not value LIKE '%"') and (value not LIKE '[%' and not value LIKE ']"')"""
        )
    )
    conn.execute("""alter table feedback alter column value type jsonb using value::JSONB;""")


def downgrade():
    pass

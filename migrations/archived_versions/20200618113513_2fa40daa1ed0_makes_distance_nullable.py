"""Makes distance nullable

Revision ID: 2fa40daa1ed0
Revises: 5775afec9ce8
Create Date: 2020-06-18 11:35:13.159974

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2fa40daa1ed0"
down_revision = "5775afec9ce8"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("range", "distance", existing_type=postgresql.DOUBLE_PRECISION(precision=53), nullable=True)


def downgrade():
    op.alter_column("range", "distance", existing_type=postgresql.DOUBLE_PRECISION(precision=53), nullable=False)

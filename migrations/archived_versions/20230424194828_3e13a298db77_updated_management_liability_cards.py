"""Extend the filetypes enum Part 2

Revision ID: 3e13a298db77
Revises: a407fc07c8a6
Create Date: 2023-04-24 19:48:28.070936+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3e13a298db77"
down_revision = "a407fc07c8a6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        INSERT INTO mode_rows (id, mode_id, position, title, is_collapsible, is_default_open) VALUES
        ('67533e24-760b-4acb-8cb0-b35c9d7b0f4e', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 80, 'Corporate Relations', true, true);
        INSERT INTO mode_columns (row_id, id, width, position) VALUES
        ('67533e24-760b-4acb-8cb0-b35c9d7b0f4e', 'fb9d82a1-fb92-452b-9466-c985ce26e18e', 9, 10);
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('fb9d82a1-fb92-452b-9466-c985ce26e18e', '55be546d-0aba-4534-81ec-97541b023aca', 0, 'corporate-relations-company-structure', 'FILE_CARDS', 'Company Structure', '{"type":"Company Structure","header":"Company Structure"}');
        INSERT INTO mode_elements (id, mode_id, position, row_id) VALUES ('63549a16-a625-4863-a89e-e28e222e0bb7', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 90, '67533e24-760b-4acb-8cb0-b35c9d7b0f4e');

        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('c8a3604d-3c6d-4f23-b006-67e5a1e08f1c', 'cf32fec6-9ea4-4c3d-b1ed-ea57616ba9b1', 10, 'employee-handbook', 'FILE_CARDS', 'Employee Handbook', '{"type":"Employee Handbook","header":"Employee Handbook"}');
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('c8a3604d-3c6d-4f23-b006-67e5a1e08f1c', '6e37997c-a332-4b36-a44a-0e0e0d8306b6', 20, 'hiring-guidelines', 'FILE_CARDS', 'Hiring Guidelines', '{"type":"Hiring Guidelines","header":"Hiring Guidelines"}');
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('c8a3604d-3c6d-4f23-b006-67e5a1e08f1c', '89a0ee04-fcf4-4f0c-aceb-ae845acc25ea', 30, 'company-bylaws', 'FILE_CARDS', 'Company''s Bylaws', '{"type":"Company''s Bylaws","header":"Company''s Bylaws"}');
        update mode_columns set width=9 where id='c8a3604d-3c6d-4f23-b006-67e5a1e08f1c';

        INSERT INTO mode_rows (id, mode_id, position, title, is_collapsible, is_default_open) VALUES
        ('1388b617-f3f5-4c8b-a8d7-68b1ff026683', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 110, 'Resumes and Biographies', true, true);
        INSERT INTO mode_columns (row_id, id, width, position) VALUES
        ('1388b617-f3f5-4c8b-a8d7-68b1ff026683', '5d69e91d-3d20-49f8-92ff-4a134fbc7a2f', 9, 0);
        INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
        ('5d69e91d-3d20-49f8-92ff-4a134fbc7a2f', '50353fe2-508f-4f1f-b0e7-084326089f1a', 0, 'resumes-and-biographies', 'FILE_CARDS', 'Resumes / Biographies', '{"type":"Resume","header":"Resumes / Biographies"}');
        INSERT INTO mode_elements (id, mode_id, position, row_id) VALUES ('649c777c-6a60-46ef-bf59-b1fb133c0da4', '1b7d5832-9ac9-44a2-b88a-7e6d09f8b052', 160, '1388b617-f3f5-4c8b-a8d7-68b1ff026683');
    """)


def downgrade():
    pass

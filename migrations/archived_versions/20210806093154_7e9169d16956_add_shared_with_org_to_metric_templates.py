"""Add shared_with_org to metric templates

Revision ID: 7e9169d16956
Revises: 9266cbe3a5ac
Create Date: 2021-08-06 09:31:54.254589+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7e9169d16956"
down_revision = "9266cbe3a5ac"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric_template", sa.Column("is_shared", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("metric_template", "is_shared")

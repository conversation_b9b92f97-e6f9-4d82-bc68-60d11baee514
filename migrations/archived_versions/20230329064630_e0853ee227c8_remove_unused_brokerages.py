"""remove unused brokerages

Revision ID: e0853ee227c8
Revises: f4242bf6226a
Create Date: 2023-03-29 06:46:30.238271+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e0853ee227c8"
down_revision = "f4242bf6226a"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
DELETE FROM brokerages WHERE id IN
('7a86d74b-99d7-4ce6-bd54-e038a03b58db', '6ca812ba-c5cc-44c2-8b7c-f14da39c5b8a');
        """)


def downgrade():
    pass

"""primary insured

Revision ID: 60163ccb072b
Revises: ce99ee7eb4f6
Create Date: 2022-09-05 09:35:03.324918+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "60163ccb072b"
down_revision = "ce99ee7eb4f6"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE submissionbusinessentitytype ADD VALUE IF NOT EXISTS 'PRIMARY_INSURED';""")


def downgrade():
    pass

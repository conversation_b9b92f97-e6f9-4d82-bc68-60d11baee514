"""Adds form_v2.execution_succeeded_at

Revision ID: 9ae963dee9fc
Revises: ed0bfe1a8261
Create Date: 2021-07-13 18:58:57.016333+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9ae963dee9fc"
down_revision = "ed0bfe1a8261"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("form_v2", sa.Column("execution_succeeded_at", sa.DateTime(), nullable=True))


def downgrade():
    op.drop_column("form_v2", "execution_succeeded_at")

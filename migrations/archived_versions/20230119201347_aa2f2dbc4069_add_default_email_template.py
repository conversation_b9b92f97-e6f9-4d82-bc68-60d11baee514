"""Add default email template

Revision ID: aa2f2dbc4069
Revises: 671bf32f3224
Create Date: 2023-01-19 20:13:47.673839+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "aa2f2dbc4069"
down_revision = "671bf32f3224"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()

    conn.execute("""
    INSERT INTO email_templates (id, external_version_id, external_template_id, name, subject, html_content, owner_id, is_shared_with_organization) 
    VALUES ('8ccb0faa-184d-43b7-ad2a-a813b4332053','3e8fd053-c6b3-422c-88cb-e386cca86eb7','d-1c359a35b8f44b1c865bfc0056fd58fd', 'User Declined Submission',
     'subject',
      '<html>
<head>
    <title></title>
</head>
<body>
This email is to inform you that the application for {{ submission_name }} has been declined. Please contact {{ user_email }} for further information, thank you.

- Copilot
</body>
</html>',
       null, true)
    ON CONFLICT DO NOTHING;
    """)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

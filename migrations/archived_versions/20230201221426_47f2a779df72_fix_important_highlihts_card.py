"""Fix important highlihts card

Revision ID: 47f2a779df72
Revises: be98a101063e
Create Date: 2023-02-01 22:14:26.831567+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "47f2a779df72"
down_revision = "be98a101063e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update mode_cards
    set props='{"columns":4,"highlights":[{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"LEGAL_FILING"},"mapper":"numberOfItems"},"label":"Legal filings","cardId":"0b596b51-6f73-41ef-b55a-961c05f30d0d","redirectLinkLabel":"Go to legal filings","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"OSHA_VIOLATION","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"Osha violations","cardId":"11b3e3d4-3124-41b2-9e19-7bf98ce8aced","redirectLinkLabel":"Go to Osha violations","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"DOCUMENT","parentType":"BUSINESS","documentType":"EPA_INSPECTION","businessSelect":"generalContractorAndDuplicates"},"mapper":"numberOfItems"},"label":"EPA Inspections","cardId":"82ba32a1-052f-4621-9ae8-143ac7915bb0","redirectLinkLabel":"Go to EPA Inspections","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"},{"source":{"source":{"sourceType":"FACT","parentType":"BUSINESS","factSubtypes":["PROJECT_USE_OF_EIFS","PROJECT_SCAFFOLDING","PROJECT_MOLD_REMOVAL","PROJECT_ROOF_WORK","PROJECT_CRANE_WORK","PROJECT_DEMOLITION_WORK","PROJECT_BLASTING_WORK","PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL","PROJECT_EXCAVATION_WORK","PROJECT_BELOW_GRADE","PROJECT_DEPTH_OF_WORK","PROJECT_HEIGHT_IN_FT","PROJECT_HEIGHT_IN_STORIES","PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT","PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES"],"businessSelect":"contractorProject"},"mapper":"numberOfPositiveFacts"},"label":"High-risk exposures","cardId":"91da8d98-fdbb-4816-93e7-18ccb4951054","redirectLinkLabel":"Go to High-risk exposures","icons":[{"name":"warning","color":"error","condition":{"type":"isInRange","min":1}}],"noValuesLabel":"None found"}]}'
    where id='052cab3f-8d48-45b0-8397-4bc0b66e583f';
    """)


def downgrade():
    pass

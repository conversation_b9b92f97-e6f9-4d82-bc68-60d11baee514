"""Fixes user_submission_stage

Revision ID: 1bb81f5d8270
Revises: 441ff14d4adf
Create Date: 2021-05-19 11:01:39.177930-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1bb81f5d8270"
down_revision = "cba1b10ceb06"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""DELETE FROM
         user_submission_stage R1 USING user_submission_stage R2
         WHERE R1.created_at < R2.created_at AND R1.submission_id  = R2.submission_id AND R1.user_id = R2.user_id;""")
    with op.get_context().autocommit_block():
        op.create_index(
            "user_submission_stage_user_submission_idx",
            "user_submission_stage",
            ["user_id", "submission_id"],
            unique=True,
        )
    with op.get_context().autocommit_block():
        conn = op.get_bind()
        conn.execute("""insert into user_submission_stage(id, submission_id, user_id, stage) 
            select uuid_generate_v4() as id, submissions.id as submission_id, report_permissions.grantee_user_id 
            as user_id, 'JUST_IN' from report_permissions JOIN submissions_reports 
            ON submissions_reports.report_id = report_permissions.report_id 
            JOIN submissions ON submissions.id = submissions_reports.submission_id ON CONFLICT DO NOTHING;""")


def downgrade():
    op.drop_index("user_submission_stage_user_submission_idx", table_name="user_submission_stage")

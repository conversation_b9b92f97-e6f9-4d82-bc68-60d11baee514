"""Fix structure cards type names

Revision ID: acf18f9c840b
Revises: 661459ff59a6
Create Date: 2023-03-17 15:58:08.460355+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "acf18f9c840b"
down_revision = "661459ff59a6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
update mode_cards
set type='STRUCTURES_SUMMARY'
where type='STRUCTURES-SUMMARY';

update mode_cards
set type='STRUCTURES_INFO'
where type='STRUCTURES-INFO';
    """)


def downgrade():
    pass

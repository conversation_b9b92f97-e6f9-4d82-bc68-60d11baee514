"""Add Copilot API User

Revision ID: e22530d0bb73
Revises: 1c7cddd4d453
Create Date: 2021-04-12 06:36:48.993814-04:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e22530d0bb73"
down_revision = "1c7cddd4d453"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT INTO users (id, email, pass_hash, external_id, organization_id, role, name, photo_s3_file_path) 
    VALUES (DEFAULT, '<EMAIL>', null, '<EMAIL>', (select id from organization where name = '<PERSON><PERSON><PERSON>'), 'admin', 'CAPI User', null)
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    pass

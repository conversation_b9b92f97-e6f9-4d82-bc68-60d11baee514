"""Adds client_application table

Revision ID: 01ae1b8bc234
Revises: 0ae3600e2d29
Create Date: 2020-11-09 15:44:38.504129+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "01ae1b8bc234"
down_revision = "0ae3600e2d29"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "client_application",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("name", sa.String(length=30), nullable=False),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.Column("client_id", sa.String(length=64), nullable=False),
        sa.Column("client_secret", sa.String(length=128), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_client_application_organization_id"), "client_application", ["organization_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_client_application_organization_id"), table_name="client_application")
    op.drop_table("client_application")

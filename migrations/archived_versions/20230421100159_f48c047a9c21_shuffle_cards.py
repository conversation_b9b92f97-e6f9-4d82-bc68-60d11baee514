"""shuffle cards

Revision ID: f48c047a9c21
Revises: 91c5d664ae96
Create Date: 2023-04-21 10:01:59.800212+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f48c047a9c21"
down_revision = "ab15d664ae96"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
            UPDATE mode_cards SET column_id = '0f5f7c8d-44bf-4abd-9fdb-67e87269cb35',
                                  position = 450
                WHERE id = 'f38d451b-294a-4acc-a723-ffcfd826c561';
                
            DELETE FROM mode_section_elements WHERE id = '4e380d33-1e2f-4e1e-aab4-a951c08612e5';
            DELETE FROM mode_cards WHERE id = '689ac7de-78a1-4d1c-8bd9-82a4d9a685a5';
            UPDATE mode_rows SET title = 'CAT Scores' WHERE id = '1647f338-99e7-41d8-a5c0-847cd1a9bf8a';
        """)


def downgrade():
    pass

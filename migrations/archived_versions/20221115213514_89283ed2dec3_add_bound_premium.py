"""Add bound_premium

Revision ID: 89283ed2dec3
Revises: 78035597338c
Create Date: 2022-11-15 21:35:14.126586+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

from copilot.models.types import SubmissionStage

# revision identifiers, used by Alembic.
revision = "89283ed2dec3"
down_revision = "78035597338c"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_coverages", sa.Column("bound_premium", sa.Float(), nullable=True))
    op.execute("SET statement_timeout TO '600 s';")
    op.execute(
        "update submission_coverages set bound_premium = quoted_premium where submission_id in (select id from"
        f" submissions where stage='{SubmissionStage.QUOTED_BOUND}');"
    )


def downgrade():
    op.drop_column("submission_coverages", "bound_premium")

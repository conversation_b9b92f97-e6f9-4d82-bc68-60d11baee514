"""new client coverages

Revision ID: 252323b8bb2f
Revises: 5f8b73522923
Create Date: 2023-06-18 18:54:13.939847+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "252323b8bb2f"
down_revision = "5f8b73522923"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # create the mode
    conn.execute("""
INSERT INTO coverages (id, name, display_name, organization_id, coverage_types)
VALUES
    ('18f0c2e1-ba88-4533-9818-3f8c83d079e9', 'generalLiability', 'Commercial General Liability', 40, '{}'),
    ('9122062a-26d2-4554-a7f4-01e71a689469', 'property', 'Commercial Property', 40, '{PRIMARY}'),
    ('4d422849-bf70-4140-b41d-5ecb7f28e563', 'businessOwners', 'Business Owner''s Policy (BOP)', 40, '{}'),
    ('369109be-90f3-4ae3-b5b3-0538115f9caf', 'businessAuto', 'Business Auto', 40, '{PRIMARY}'),
    ('cbfc4d33-3be7-4e52-a30f-d2b051aff65f', 'umbrella', 'Umbrella/Excess Liability', 40, '{}');
        """)


def downgrade():
    pass

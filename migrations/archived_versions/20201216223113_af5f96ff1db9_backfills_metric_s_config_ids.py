"""Backfills Metric's Config IDs

Revision ID: af5f96ff1db9
Revises: d65e4fb9aec0
Create Date: 2020-12-16 22:31:13.554570+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "af5f96ff1db9"
down_revision = "d65e4fb9aec0"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
update metric
set metric_config_id = (select id from metric_config where display_name = metric.name and (report_id = metric.report_v2_id or report_id is null))
where metric_config_id is null;
    """)


def downgrade():
    pass

"""Add org id to coverages

Revision ID: 9a589ceb1822
Revises: 490f9d29a4a7
Create Date: 2021-06-14 12:47:27.403561+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9a589ceb1822"
down_revision = "490f9d29a4a7"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("coverages", sa.Column("organization_id", sa.Integer(), nullable=True))
    op.create_index(op.f("ix_coverages_organization_id"), "coverages", ["organization_id"], unique=False)
    op.create_foreign_key(None, "coverages", "organization", ["organization_id"], ["id"])


def downgrade():
    op.drop_constraint(None, "coverages", type_="foreignkey")
    op.drop_index(op.f("ix_coverages_organization_id"), table_name="coverages")
    op.drop_column("coverages", "organization_id")

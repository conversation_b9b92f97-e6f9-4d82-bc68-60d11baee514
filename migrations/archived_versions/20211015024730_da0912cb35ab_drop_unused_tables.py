"""drop unused tables

Revision ID: da0912cb35ab
Revises: 9205731fba50
Create Date: 2021-10-15 02:47:30.273746+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "da0912cb35ab"
down_revision = "9205731fba50"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table("missing_business")
    op.drop_table("reports_stage")
    op.drop_table("report_dismissed_cards")
    op.drop_table("report_snapshot")
    op.drop_table("manager_to_user")
    op.drop_table("report_evidence")
    op.drop_table("report_service")
    op.drop_table("report_notebook")
    op.drop_table("sic_codes")
    op.drop_table("uploaded_quote")


def downgrade():
    pass

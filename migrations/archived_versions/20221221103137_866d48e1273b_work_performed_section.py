"""work performed section

Revision ID: 866d48e1273b
Revises: 7a40982638b1
Create Date: 2022-12-21 10:31:37.402364+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "866d48e1273b"
down_revision = "7a40982638b1"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('24a03c0b-c106-4179-8c90-0227df34bbf1', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 3, 'Work performed');

INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('4d42de92-ef5a-42d8-b7d4-6076d6e21d8d', '24a03c0b-c106-4179-8c90-0227df34bbf1', 0, 12, null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id) VALUES
    ('4251788d-f9a7-4b54-ba51-dfb24bfe3394', '4d42de92-ef5a-42d8-b7d4-6076d6e21d8d', null, 0, 'WORK_PERFORMED', 'work-performed');
    """)


def downgrade():
    conn = op.get_bind()

    conn.execute("""
DELETE FROM mode_cards WHERE id = '4251788d-f9a7-4b54-ba51-dfb24bfe3394';
DELETE FROM mode_columns WHERE id = '4d42de92-ef5a-42d8-b7d4-6076d6e21d8d';
DELETE FROM mode_rows WHERE id = '24a03c0b-c106-4179-8c90-0227df34bbf1';
    """)

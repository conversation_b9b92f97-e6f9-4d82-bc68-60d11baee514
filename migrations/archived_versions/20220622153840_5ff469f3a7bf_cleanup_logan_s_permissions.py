"""Clean<PERSON> <PERSON>'s permissions

Revision ID: 5ff469f3a7bf
Revises: fc5883393da9
Create Date: 2022-06-22 15:38:40.641646+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5ff469f3a7bf"
down_revision = "fc5883393da9"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            delete from report_permissions
            where 
                grantee_user_id = '297' 
            and
                report_id not in ('8de6a2d6-5637-44c6-86e9-00365f023aa3', '81d54bf6-12b1-479e-bc53-e236aa81a759')
        """)
        op.execute("""
            update reports_v2 set owner_id = '10' where owner_id in ('297', '237') and id 
            not in ('8de6a2d6-5637-44c6-86e9-00365f023aa3', '81d54bf6-12b1-479e-bc53-e236aa81a759')
        """)
        op.execute("""
            delete from report_permissions
            where 
                grantee_user_id = '237' 
            and
                report_id not in ('8de6a2d6-5637-44c6-86e9-00365f023aa3', '81d54bf6-12b1-479e-bc53-e236aa81a759')
        """)
        op.execute("""
            update users set role = 'manager' where email = '<EMAIL>';
        """)
        op.execute("""
            insert into users values
            (default, '<EMAIL>', null, 'auth0|62b3684a9c81c9ba6558f5f9', 9, 'manager', 'Connie Germano', null, null, now(), null)
        """)
        op.execute("""
            insert into users values
            (default, '<EMAIL>', null, 'auth0|62b2033b6c03d686c7135764', 7, 'manager', 'Kalepa Insurance', null, null, now(), null)
        """)


def downgrade():
    pass

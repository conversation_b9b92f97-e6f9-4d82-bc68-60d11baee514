"""Removes fields configs from copilot_api

Revision ID: 87d0ee9bcf87
Revises: 8fc66dd47514
Create Date: 2021-02-01 12:25:56.588952-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "87d0ee9bcf87"
down_revision = "d1554cd7f4d3"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("interpretation_config_org_field_value_index", table_name="interpretation_config")
    op.drop_index("ix_interpretation_config_field_id", table_name="interpretation_config")
    op.drop_index("ix_interpretation_config_organization_id", table_name="interpretation_config")
    op.drop_table("interpretation_config")
    op.drop_index("ix_interpretable_field_name", table_name="interpretable_field")
    op.drop_table("interpretable_field")


def downgrade():
    op.create_table(
        "interpretable_field",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("name", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("dossier_path", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("interpretation_options", postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint("id", name="interpretable_field_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index("ix_interpretable_field_name", "interpretable_field", ["name"], unique=False)
    op.create_table(
        "interpretation_config",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("field_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("field_value", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("interpretation", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("organization_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(["field_id"], ["interpretable_field.id"], name="interpretation_config_field_id_fkey"),
        sa.ForeignKeyConstraint(
            ["organization_id"], ["organization.id"], name="interpretation_config_organization_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="interpretation_config_pkey"),
    )
    op.create_index(
        "ix_interpretation_config_organization_id", "interpretation_config", ["organization_id"], unique=False
    )
    op.create_index("ix_interpretation_config_field_id", "interpretation_config", ["field_id"], unique=False)
    op.create_index(
        "interpretation_config_org_field_value_index",
        "interpretation_config",
        ["field_id", "field_value", "organization_id"],
        unique=True,
    )

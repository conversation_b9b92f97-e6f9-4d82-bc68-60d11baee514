"""Sets similarity cutoff nullable

Revision ID: 43e3bd36d5c4
Revises: f514fa253540
Create Date: 2021-05-21 13:15:31.543411-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "43e3bd36d5c4"
down_revision = "f514fa253540"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "customizable_classifiers",
        "similarity_cutoff",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=True,
    )


def downgrade():
    op.alter_column(
        "customizable_classifiers",
        "similarity_cutoff",
        existing_type=postgresql.DOUBLE_PRECISION(precision=53),
        nullable=False,
    )

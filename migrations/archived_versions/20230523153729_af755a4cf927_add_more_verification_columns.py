"""Add more verification columns

Revision ID: af755a4cf927
Revises: 8ea2f6670f75
Create Date: 2023-05-18 15:37:29.746128+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "af755a4cf927"
down_revision = "fe3f040923b9"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("is_auto_verified", sa.<PERSON><PERSON>(), nullable=True))
    op.add_column("submissions", sa.Column("auto_verified_at", sa.DateTime(), nullable=True))
    op.add_column("submissions", sa.Column("is_manual_verified", sa.<PERSON><PERSON><PERSON>(), nullable=True))
    op.add_column("submissions", sa.Column("manual_verified_at", sa.DateTime(), nullable=True))
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_AUTO_VERIFICATION_COMPLETED';")


def downgrade():
    pass

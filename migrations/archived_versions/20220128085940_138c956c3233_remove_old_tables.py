"""Remove old tables

Revision ID: 138c956c3233
Revises: f54a05cfba3c
Create Date: 2022-01-28 08:59:40.979436+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "138c956c3233"
down_revision = "f54a05cfba3c"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table("uploaded_forms")


def downgrade():
    op.create_table(
        "uploaded_forms",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "status",
            postgresql.ENUM("queued", "processing", "finished", "failed", name="form_status"),
            server_default=sa.text("'queued'::form_status"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("report_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("file_name", sa.VARCHAR(length=200), autoincrement=False, nullable=False),
        sa.Column(
            "s3_file_path",
            sa.VARCHAR(length=1024),
            server_default=sa.text("''::character varying"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("form_data", postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
        sa.Column("errors", postgresql.ARRAY(sa.TEXT()), autoincrement=False, nullable=True),
        sa.Column(
            "contains_acord_125", sa.BOOLEAN(), server_default=sa.text("false"), autoincrement=False, nullable=True
        ),
        sa.Column("deleted", sa.BOOLEAN(), server_default=sa.text("false"), autoincrement=False, nullable=True),
        sa.Column(
            "detected_forms",
            postgresql.JSONB(astext_type=sa.Text()),
            server_default=sa.text("'{}'::jsonb"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("ocr_engine", sa.VARCHAR(length=50), autoincrement=False, nullable=True),
        sa.Column("submission_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(["report_id"], ["reports.id"], name="uploaded_forms_report_id_fkey"),
        sa.ForeignKeyConstraint(
            ["report_id"], ["reports.id"], name="uploaded_forms_report_id_fkey1", ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["submission_id"], ["submissions.id"], name="uploaded_forms_submission_id_fkey", ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id", name="uploaded_forms_pkey"),
    )

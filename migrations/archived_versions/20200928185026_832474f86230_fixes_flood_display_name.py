"""Fixes flood summary preference name

Revision ID: 832474f86230
Revises: 259e679c1f80
Create Date: 2020-09-28 18:50:26.782852+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "832474f86230"
down_revision = "259e679c1f80"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("UPDATE summary_preference SET display_name = 'Flood' where display_name = 'Flood '")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("UPDATE summary_preference SET display_name = 'Flood ' where display_name = 'Flood'")
    # ### end Alembic commands ###

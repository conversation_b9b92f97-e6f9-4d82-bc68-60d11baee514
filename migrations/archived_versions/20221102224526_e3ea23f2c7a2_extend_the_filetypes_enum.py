"""Extend the filetypes enum

Revision ID: e3ea23f2c7a2
Revises: ec1a013768e3
Create Date: 2022-11-02 22:45:26.070936+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e3ea23f2c7a2"
down_revision = "ec1a013768e3"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'SUPPLEMENTAL_FORM';""")


def downgrade():
    pass

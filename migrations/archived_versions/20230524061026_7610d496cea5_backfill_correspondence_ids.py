"""Backfill correspondence ids

Revision ID: 7610d496cea5
Revises: jh64gf8f7sy7
Create Date: 2023-05-24 06:10:26.765869+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "7610d496cea5"
down_revision = "jh64gf8f7sy7"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE reports_v2
        SET correspondence_id=s.correspondence_id
        FROM (SELECT reports_v2.id AS report_id, rec.id AS correspondence_id
              FROM reports_v2
                       JOIN report_email_correspondence rec on rec.thread_id = additional_data ->> 'thread_id'
              WHERE additional_data ? 'thread_id'
                AND correspondence_id IS NULL) AS s
        WHERE reports_v2.id = s.report_id;
        """)


def downgrade():
    pass

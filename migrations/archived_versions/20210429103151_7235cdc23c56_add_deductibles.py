"""Add deductibles

Revision ID: 7235cdc23c56
Revises: db8dad1ca495
Create Date: 2021-04-29 10:31:51.907117+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7235cdc23c56"
down_revision = "db8dad1ca495"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "submission_deductibles",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("coverage_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("policy_limit", sa.Float(), nullable=True),
        sa.Column("policy_level", sa.Float(), nullable=True),
        sa.Column(
            "policy_level_type", sa.Enum("PERCENTAGE", "NUMERIC", name="policyleveldeductibletype"), nullable=True
        ),
        sa.Column("minimum", sa.Float(), nullable=True),
        sa.Column("comment", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(["coverage_id"], ["coverages.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_deductibles_coverage_id"), "submission_deductibles", ["coverage_id"], unique=False
    )
    op.create_index(
        op.f("ix_submission_deductibles_submission_id"), "submission_deductibles", ["submission_id"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_submission_deductibles_submission_id"), table_name="submission_deductibles")
    op.drop_index(op.f("ix_submission_deductibles_coverage_id"), table_name="submission_deductibles")
    op.drop_table("submission_deductibles")

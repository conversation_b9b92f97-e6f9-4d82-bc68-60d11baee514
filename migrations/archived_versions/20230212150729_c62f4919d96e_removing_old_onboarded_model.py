"""Removing old onboarded model

Revision ID: c62f4919d96e
Revises: 9586f7d317fa
Create Date: 2023-02-12 15:07:29.168792+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c62f4919d96e"
down_revision = "9586f7d317fa"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("ix_onboarded_files_submission_id", table_name="onboarded_files")
    op.drop_table("onboarded_files")


def downgrade():
    pass

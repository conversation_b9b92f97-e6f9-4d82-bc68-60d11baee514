"""Extends metric and metric preferences with new fields

Revision ID: 984c0f1fd815
Revises: b36d7e06c436
Create Date: 2022-05-17 12:59:50.426997+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "984c0f1fd815"
down_revision = "b36d7e06c436"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("SET statement_timeout TO '60 s';")
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE parenttype ADD VALUE IF NOT EXISTS 'PREMISES'")
        op.execute("ALTER TYPE parenttype ADD VALUE IF NOT EXISTS 'ORGANIZATION'")
        op.execute("ALTER TYPE parenttype ADD VALUE IF NOT EXISTS 'REPORT'")
    op.add_column("metric", sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column(
        "metric",
        sa.Column(
            "parent_type",
            sa.Enum(
                "BUSINESS",
                "PREMISES",
                "DOCUMENT",
                "STRUCTURE",
                "ORGANIZATION",
                "SUBMISSION",
                "DRIVER",
                "VEHICLE",
                name="parenttype",
            ),
            nullable=True,
        ),
    )
    op.add_column("metric", sa.Column("summary_config_id", sa.String(), nullable=True))
    op.create_unique_constraint(
        "metric_parent_id_parent_type_summary_config_id_execution_id",
        "metric",
        ["parent_id", "parent_type", "summary_config_id", "execution_id"],
    )
    op.add_column("metric_preference", sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column(
        "metric_preference",
        sa.Column(
            "parent_type",
            sa.Enum(
                "BUSINESS",
                "PREMISES",
                "DOCUMENT",
                "STRUCTURE",
                "ORGANIZATION",
                "SUBMISSION",
                "DRIVER",
                "VEHICLE",
                name="parenttype",
            ),
            nullable=True,
        ),
    )
    op.add_column("metric_preference", sa.Column("summary_config_id", sa.String(), nullable=True))
    op.execute("COMMIT")
    op.create_index(op.f("ix_metric_parent_id"), "metric", ["parent_id"], unique=False, postgresql_concurrently=True)
    op.create_index(
        op.f("ix_metric_parent_type"), "metric", ["parent_type"], unique=False, postgresql_concurrently=True
    )
    op.create_index(
        op.f("ix_metric_summary_config_id"), "metric", ["summary_config_id"], unique=False, postgresql_concurrently=True
    )
    op.create_index(
        op.f("ix_metric_preference_parent_id"),
        "metric_preference",
        ["parent_id"],
        unique=False,
        postgresql_concurrently=True,
    )
    op.create_index(
        op.f("ix_metric_preference_parent_type"),
        "metric_preference",
        ["parent_type"],
        unique=False,
        postgresql_concurrently=True,
    )
    op.create_index(
        op.f("ix_metric_preference_summary_config_id"),
        "metric_preference",
        ["summary_config_id"],
        unique=False,
        postgresql_concurrently=True,
    )
    op.create_index(
        "metric_preference_report_parent_summary_config_id",
        "metric_preference",
        ["report_id", "parent_id", "parent_type", "summary_config_id"],
        unique=True,
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_index("parent_id", table_name="metric_preference")
    op.drop_index(op.f("ix_metric_preference_summary_config_id"), table_name="metric_preference")
    op.drop_index(op.f("ix_metric_preference_parent_type"), table_name="metric_preference")
    op.drop_index(op.f("ix_metric_preference_parent_id"), table_name="metric_preference")
    op.drop_column("metric_preference", "summary_config_id")
    op.drop_column("metric_preference", "parent_type")
    op.drop_column("metric_preference", "parent_id")
    op.drop_constraint(None, "metric", type_="unique")
    op.drop_index(op.f("ix_metric_summary_config_id"), table_name="metric")
    op.drop_index(op.f("ix_metric_parent_type"), table_name="metric")
    op.drop_index(op.f("ix_metric_parent_id"), table_name="metric")
    op.drop_column("metric", "summary_config_id")
    op.drop_column("metric", "parent_type")
    op.drop_column("metric", "parent_id")

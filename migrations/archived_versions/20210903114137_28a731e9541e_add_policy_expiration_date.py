"""Add policy_expiration_date

Revision ID: 28a731e9541e
Revises: cf28fdc0099f
Create Date: 2021-09-03 11:41:37.549553+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "28a731e9541e"
down_revision = "cf28fdc0099f"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("policy_expiration_date", sa.DateTime(), nullable=True))
    op.create_index(
        op.f("ix_submissions_policy_expiration_date"), "submissions", ["policy_expiration_date"], unique=False
    )
    conn = op.get_bind()
    conn.execute("""
        update submissions set 
            policy_expiration_date = proposed_effective_date + interval '1 year'
        where proposed_effective_date is not null;
    """)
    conn.execute("""update organization set renewal_creation_interval = '365 days' - renewal_creation_interval;""")


def downgrade():
    op.drop_index(op.f("ix_submissions_policy_expiration_date"), table_name="submissions")
    op.drop_column("submissions", "policy_expiration_date")

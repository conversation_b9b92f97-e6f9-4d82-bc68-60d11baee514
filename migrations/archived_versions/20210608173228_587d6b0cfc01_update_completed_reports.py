"""update completed reports

Revision ID: 587d6b0cfc01
Revises: b8b79190a87b
Create Date: 2021-06-08 17:32:28.588663+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "587d6b0cfc01"
down_revision = "b8b79190a87b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='1226302c-8fd9-4315-ac72-bb7cf1e39237';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='29b5fd33-21f2-4ece-a775-2e3edb1b715d';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='1de089ed-1d6f-4195-b4d2-5e0bf5e648cd';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='8ab6db08-8acd-4165-b865-fc85d6ffdf61';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='9db833c2-9d6a-48bc-8a69-20db4a28ea5b';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='3f8e196d-4781-4d10-97b1-c2047e14463c';"
    )
    conn.execute(
        "update user_submission_stage set stage='DECLINED' where submission_id='2994123b-0061-437e-adaf-14806b470473';"
    )
    conn.execute(
        "update user_submission_stage set stage='ON_MY_PLATE' where id='31bd2ed4-d34a-4208-81b9-0e45ed7f32b7';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='9f45050b-f744-416f-9bc2-244d923669a3';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='042a6375-69a4-4406-aad1-9aaa94b096db';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='66045346-baca-4c59-9dca-06d913e9f771';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='cc67bb2b-5877-4d5c-ac94-88b2814f80ed';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='1b3deba8-f64b-48a1-bf6d-816a95a0d249';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='39157728-cedc-4be4-988c-5329575bd81f';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='66045346-baca-4c59-9dca-06d913e9f771';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_BOUND' where"
        " submission_id='294a01d1-da3f-4c89-8cc3-52515aed9863';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='77f300ef-00db-442f-9bba-8a03faef0747';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_BOUND' where"
        " submission_id='50c24412-6cf0-47d5-a79b-c47ac9a26d32';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='494219b0-96d4-45be-bb93-1369c84f9a71';"
    )
    conn.execute(
        "update user_submission_stage set stage='DECLINED' where submission_id='5a4c2635-6ee3-445e-af0c-121033431ba0';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='31643953-bd2f-47e2-a4b3-43fbdc3ada99';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='66045346-baca-4c59-9dca-06d913e9f771';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='ff875f4a-fb2c-452d-ad1f-dce41b2114bb';"
    )
    conn.execute(
        "update user_submission_stage set stage='ON_MY_PLATE' where"
        " submission_id='f42b53bb-a02d-4d79-a914-cf82dbdf116b';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='3c19b489-609a-47fa-a884-23abbb3288e9';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='c5662d14-4ca1-4590-b2e8-56157d2f4329';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='0ff4403a-ca41-4a08-9c3a-088ea70a3f0b';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='2a18b858-35d4-4d54-9fba-ae9d4b47a60c';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='9a5e8576-787b-45fc-8891-147d48abde3e';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='01a22f31-af73-4a8e-bef5-f91681f6fd02';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='1aed5768-b5a5-4171-9a88-88b2f638b92b';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_BOUND' where"
        " submission_id='00d73cdb-ac69-4c1c-b1ee-d87a98a0a89a';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='91788518-8ed5-4008-8ec9-57a32893c67d';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='7f2e8104-8659-4e7f-a73f-300005ab7ce4';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='b3f24ae5-7b10-4a6d-b784-04aa9db00011';"
    )
    conn.execute(
        "update user_submission_stage set stage='QUOTED_LOST' where"
        " submission_id='492fda57-7346-491e-acd3-2ef1ae38573a';"
    )
    conn.execute("update reports_v2 set is_deleted=true where id='90c5876d-0925-43fb-a6e6-e61b28633436';")
    conn.execute("update reports_v2 set is_deleted=true where id='9619053a-975f-4903-82e4-7858d5875b60';")
    conn.execute("update reports_v2 set is_deleted=true where id='fe910943-be13-47ab-b1d4-3bc9637bba1b';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='1226302c-8fd9-4315-ac72-bb7cf1e39237';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='29b5fd33-21f2-4ece-a775-2e3edb1b715d';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='1de089ed-1d6f-4195-b4d2-5e0bf5e648cd';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='8ab6db08-8acd-4165-b865-fc85d6ffdf61';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='9db833c2-9d6a-48bc-8a69-20db4a28ea5b';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='3f8e196d-4781-4d10-97b1-c2047e14463c';")
    conn.execute("update submissions set stage='DECLINED' where id='2994123b-0061-437e-adaf-14806b470473';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='9f45050b-f744-416f-9bc2-244d923669a3';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='042a6375-69a4-4406-aad1-9aaa94b096db';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='66045346-baca-4c59-9dca-06d913e9f771';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='cc67bb2b-5877-4d5c-ac94-88b2814f80ed';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='1b3deba8-f64b-48a1-bf6d-816a95a0d249';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='39157728-cedc-4be4-988c-5329575bd81f';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='66045346-baca-4c59-9dca-06d913e9f771';")
    conn.execute("update submissions set stage='QUOTED_BOUND' where id='294a01d1-da3f-4c89-8cc3-52515aed9863';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='77f300ef-00db-442f-9bba-8a03faef0747';")
    conn.execute("update submissions set stage='QUOTED_BOUND' where id='50c24412-6cf0-47d5-a79b-c47ac9a26d32';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='494219b0-96d4-45be-bb93-1369c84f9a71';")
    conn.execute("update submissions set stage='DECLINED' where id='5a4c2635-6ee3-445e-af0c-121033431ba0';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='31643953-bd2f-47e2-a4b3-43fbdc3ada99';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='66045346-baca-4c59-9dca-06d913e9f771';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='ff875f4a-fb2c-452d-ad1f-dce41b2114bb';")
    conn.execute("update submissions set stage='ON_MY_PLATE' where id='f42b53bb-a02d-4d79-a914-cf82dbdf116b';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='3c19b489-609a-47fa-a884-23abbb3288e9';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='c5662d14-4ca1-4590-b2e8-56157d2f4329';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='0ff4403a-ca41-4a08-9c3a-088ea70a3f0b';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='2a18b858-35d4-4d54-9fba-ae9d4b47a60c';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='9a5e8576-787b-45fc-8891-147d48abde3e';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='01a22f31-af73-4a8e-bef5-f91681f6fd02';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='1aed5768-b5a5-4171-9a88-88b2f638b92b';")
    conn.execute("update submissions set stage='QUOTED_BOUND' where id='00d73cdb-ac69-4c1c-b1ee-d87a98a0a89a';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='91788518-8ed5-4008-8ec9-57a32893c67d';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='7f2e8104-8659-4e7f-a73f-300005ab7ce4';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='b3f24ae5-7b10-4a6d-b784-04aa9db00011';")
    conn.execute("update submissions set stage='QUOTED_LOST' where id='492fda57-7346-491e-acd3-2ef1ae38573a';")


def downgrade():
    pass

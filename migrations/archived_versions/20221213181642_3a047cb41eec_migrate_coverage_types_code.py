"""migrate_coverage_types_code

Revision ID: 3a047cb41eec
Revises: 900c4c7d3046
Create Date: 2022-12-13 18:16:42.557222+00:00

"""
# from alembic import op
# import sqlalchemy as sa
# from sqlalchemy import orm
# from sqlalchemy.dialects import postgresql
# from sqlalchemy.orm import joinedload
#
# from copilot.models import User
# from copilot.models.reports import SubmissionCoverage, SubmissionDeductible, Submission, Coverage, SubmissionUser
# from copilot.models.types import CoverageType

# revision identifiers, used by Alembic.
revision = "3a047cb41eec"
down_revision = "900c4c7d3046"
branch_labels = None
depends_on = None


# This migration run on production already and importing models in this file will
# block updating the models in the future. So we are commenting out the code for now.

# def merge_duplicate_coverages(submission: Submission):
#     coverages = submission.coverages
#     coverages.sort(key=lambda x: x.coverage.name)
#
#     to_remove = []
#
#     for i in range(len(coverages) - 1):
#         if coverages[i].coverage.name == coverages[i + 1].coverage.name:
#             add_coverage_premium(coverages[i + 1], coverages[i])
#             to_remove.append(coverages[i])
#
#     for item in to_remove:
#         submission.coverages.remove(item)
#
#
# def sum_quotes(a, b):
#     if not a and not b:
#         return None
#     if not a:
#         return b
#     if not b:
#         return a
#
#     return a + b
#
#
# def add_coverage_premium(to: SubmissionCoverage, source: SubmissionCoverage):
#     to.quoted_premium = sum_quotes(to.quoted_premium, source.quoted_premium)
#     to.estimated_premium = sum_quotes(to.estimated_premium, source.estimated_premium)
#     to.bound_premium = sum_quotes(to.bound_premium, source.bound_premium)
#
#
# def add_deductible_premium(to: SubmissionDeductible, source: SubmissionDeductible):
#     to.policy_limit = sum_quotes(to.policy_limit, source.policy_limit)
#     to.policy_level = sum_quotes(to.policy_level, source.policy_level)
#     to.minimum = sum_quotes(to.minimum, source.minimum)
#
#
# def mark_all_applicable_as(submission: Submission, coverageType: CoverageType):
#     for sc in submission.coverages:
#         if coverageType in sc.coverage.coverage_types:
#             sc.coverage_type = coverageType
#
#     for sd in submission.deductibles:
#         if coverageType in sd.coverage.coverage_types:
#             sd.coverage_type = coverageType
#
#
# def one_or_none(items):
#     if len(items) == 0:
#         return None
#
#     return items[0]
#
#
# def other_mark_all_applicable_as_excess(submission: Submission):
#     excess_coverage = one_or_none(list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.coverages)))
#     umbrella_coverage = one_or_none(list(filter(lambda x: x.coverage.name == 'umbrella', submission.coverages)))
#
#     excess_deductible = one_or_none(
#         list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.deductibles)))
#     umbrella_deductible = one_or_none(list(filter(lambda x: x.coverage.name == 'umbrella', submission.deductibles)))
#
#     if not excess_coverage:
#         mark_all_applicable_as(submission, CoverageType.PRIMARY)
#         return
#
#     mark_all_applicable_as(submission, CoverageType.EXCESS)
#
#     if umbrella_coverage:
#         add_coverage_premium(umbrella_coverage, excess_coverage)
#         submission.coverages.remove(excess_coverage)
#
#         if umbrella_deductible:
#             if excess_deductible:
#                 add_deductible_premium(umbrella_deductible, excess_deductible)
#                 submission.deductibles.remove(excess_deductible)
#
#     else:
#         excess_coverages = list(
#             filter(lambda x: CoverageType.EXCESS in x.coverage.coverage_types, submission.coverages))
#
#         excess_deductibles = list(
#             filter(lambda x: CoverageType.EXCESS in x.coverage.coverage_types, submission.deductibles))
#
#         if excess_coverages:
#             for ec in excess_coverages:
#                 ec.quoted_premium = (ec.quoted_premium or 0) + (excess_coverage.quoted_premium or 0) / len(
#                     excess_coverages)
#                 ec.estimated_premium = (ec.estimated_premium or 0) + (excess_coverage.estimated_premium or 0) / len(
#                     excess_coverages)
#                 ec.bound_premium = (ec.bound_premium or 0) + (excess_coverage.bound_premium or 0) / len(
#                     excess_coverages)
#
#             submission.coverages.remove(excess_coverage)
#
#             if excess_deductible:
#                 for ed in excess_deductibles:
#                     ed.policy_limit = (ed.policy_limit or 0) + (excess_deductible.policy_limit or 0) / len(
#                         excess_deductibles)
#                     ed.policy_level = (ed.policy_level or 0) + (excess_deductible.policy_level or 0) / len(
#                         excess_deductibles)
#                     ed.minimum = (ed.minimum or 0) + (excess_deductible.minimum or 0) / len(excess_deductibles)
#
#                 submission.deductibles.remove(excess_deductible)
#
#
# def mark_all_applicable_as_excess(submission: Submission, umbrella: Coverage):
#     excess_coverage = one_or_none(list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.coverages)))
#     umbrella_coverage = one_or_none(list(filter(lambda x: x.coverage.name == 'umbrella', submission.coverages)))
#
#     excess_deductible = one_or_none(
#         list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.deductibles)))
#     umbrella_deductible = one_or_none(list(filter(lambda x: x.coverage.name == 'umbrella', submission.deductibles)))
#
#     # If E&S is listed with other Excess-applicable coverages, add E&S premium to GL if GL is listed. If no GL, divide E&S premium among all other Excess-applicable coverages
#     if not excess_coverage:
#         mark_all_applicable_as(submission, CoverageType.EXCESS)
#         return
#
#     if umbrella_coverage:
#         add_coverage_premium(umbrella_coverage, excess_coverage)
#         submission.coverages.remove(excess_coverage)
#
#         if umbrella_deductible:
#             if excess_deductible:
#                 add_deductible_premium(umbrella_deductible, excess_deductible)
#                 submission.deductibles.remove(excess_deductible)
#
#         mark_all_applicable_as(submission, CoverageType.EXCESS)
#
#     else:
#         excess_coverages = list(
#             filter(lambda x: CoverageType.EXCESS in x.coverage.coverage_types, submission.coverages))
#
#         excess_deductibles = list(
#             filter(lambda x: CoverageType.EXCESS in x.coverage.coverage_types, submission.deductibles))
#
#         # If E&S is listed as a coverage and there are no other Excess-applicable coverages, add GL and mark it as Excess, and add premium from E&S to GL
#         if not excess_coverages:
#             excess_coverage.coverage = umbrella
#             excess_coverage.coverage_id = umbrella.id
#
#             if excess_deductible:
#                 excess_deductible.coverage_id = umbrella.id
#                 excess_coverage.coverage = umbrella
#
#         else:
#             for ec in excess_coverages:
#                 ec.quoted_premium = (ec.quoted_premium or 0) + (excess_coverage.quoted_premium or 0) / len(
#                     excess_coverages)
#                 ec.estimated_premium = (ec.estimated_premium or 0) + (excess_coverage.estimated_premium or 0) / len(
#                     excess_coverages)
#                 ec.bound_premium = (ec.bound_premium or 0) + (excess_coverage.bound_premium or 0) / len(
#                     excess_coverages)
#
#             submission.coverages.remove(excess_coverage)
#
#             if excess_deductible:
#                 for ed in excess_deductibles:
#                     ed.policy_limit = (ed.policy_limit or 0) + (excess_deductible.policy_limit or 0) / len(
#                         excess_deductibles)
#                     ed.policy_level = (ed.policy_level or 0) + (excess_deductible.policy_level or 0) / len(
#                         excess_deductibles)
#                     ed.minimum = (ed.minimum or 0) + (excess_deductible.minimum or 0) / len(excess_deductibles)
#
#                 submission.deductibles.remove(excess_deductible)
#
#
# def migrate_kis(session):
#     submissions = session.query(Submission).options(joinedload(Submission.coverages),
#                                                     joinedload(Submission.deductibles)).join(
#         User).filter(
#         User.organization_id == 7).all()
#
#     umbrella = session.query(Coverage).filter(Coverage.name == 'umbrella').filter(
#         Coverage.organization_id == 7).first()
#
#     for submission in submissions:
#         merge_duplicate_coverages(submission)
#         mark_all_applicable_as_excess(submission, umbrella)
#
#
# def migrate_arch(session):
#     # 1500 items
#     submissions = session.query(Submission).options(joinedload(Submission.coverages),
#                                                     joinedload(Submission.deductibles)).join(
#         User).filter(
#         User.organization_id == 10).all()
#
#     umbrella = session.query(Coverage).filter(Coverage.name == 'umbrella').filter(
#         Coverage.organization_id == 10).first()
#
#     for submission in submissions:
#         merge_duplicate_coverages(submission)
#         coverage_names = [sc.coverage.name for sc in submission.coverages]
#
#         # If (LOB = GL or Auto) AND (there isn't also E&S or Umbrella) then mark those LOBs as Primary
#         if (
#                 'businessAuto' in coverage_names) and 'excessAndSurplus' not in coverage_names and 'umbrella' not in coverage_names:
#             for sc in submission.coverages:
#                 if sc.coverage.name == 'businessAuto':
#                     sc.coverage_type = CoverageType.PRIMARY
#
#                     deductibles = list(filter(lambda x: x.coverage.name == sc.coverage.name, submission.deductibles))
#                     if deductibles:
#                         for deductible in deductibles:
#                             if deductible.coverage.name == 'businessAuto':
#                                 deductible.coverage_type = CoverageType.PRIMARY
#
#         # If the only LOBs are Umbrella and E&S just leave Umbrella and don't mark as Excess, but move any E&S premium to Umbrella
#         elif len(coverage_names) == 2 and 'excessAndSurplus' in coverage_names and 'umbrella' in coverage_names:
#             excess_coverage = list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.coverages))[0]
#             umbrella_coverage = list(filter(lambda x: x.coverage.name == 'umbrella', submission.coverages))[0]
#
#             excess_deductible = one_or_none(
#                 list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.deductibles)))
#             umbrella_deductible = one_or_none(
#                 list(filter(lambda x: x.coverage.name == 'umbrella', submission.deductibles)))
#
#             add_coverage_premium(umbrella_coverage, excess_coverage)
#             submission.coverages.remove(excess_coverage)
#
#             if excess_deductible:
#                 if umbrella_deductible:
#                     add_deductible_premium(umbrella_deductible, excess_deductible)
#
#                 submission.deductibles.remove(excess_deductible)
#
#         # Else mark all applicable coverages as Excess
#         else:
#             mark_all_applicable_as_excess(submission, umbrella)
#
#
# def migrate_nw(session):
#     # 18500 items
#     submissions = session.query(Submission).options(joinedload(Submission.coverages), joinedload(Submission.policies),
#                                                     joinedload(Submission.reports),
#                                                     joinedload(Submission.deductibles)).join(
#         User).filter(
#         User.organization_id == 6).all()
#
#     umbrella = session.query(Coverage).filter(Coverage.name == 'umbrella').filter(
#         Coverage.organization_id == 6).first()
#
#     underwriters = session.query(SubmissionUser).join(User).filter(User.organization_id == 6).all()
#
#     for submission in submissions:
#         merge_duplicate_coverages(submission)
#
#         if len(submission.reports) == 0:
#             continue
#
#         coverage_names = [sc.coverage.name for sc in submission.coverages]
#
#         # If only 1 policy ID on submission
#         if len(submission.policies) == 1:
#             # Ends in “A” = mark all applicable LOBs as Excess
#             if submission.policies[0].external_id.endswith('A') or submission.policies[0].external_id.endswith('a'):
#                 mark_all_applicable_as_excess(submission, umbrella)
#             # Ends in “C” = mark all applicable LOBs as Primary. If E&S is a coverage on these, remove it but preserve the premium on GL (add GL if not listed)
#             elif submission.policies[0].external_id.endswith('C') or submission.policies[0].external_id.endswith('c'):
#                 excess_coverage = one_or_none(
#                     list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.coverages)))
#                 umbrella_coverage = one_or_none(
#                     list(filter(lambda x: x.coverage.name == 'umbrella', submission.coverages)))
#
#                 excess_deductible = one_or_none(
#                     list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.deductibles)))
#                 umbrella_deductible = one_or_none(
#                     list(filter(lambda x: x.coverage.name == 'umbrella', submission.deductibles)))
#
#                 if excess_coverage:
#                     if not umbrella_coverage:
#                         umbrella_coverage = SubmissionCoverage(coverage_id=umbrella.id,
#                                                                coverage=umbrella,
#                                                                submission_id=submission.id)
#                         submission.coverages.append(umbrella_coverage)
#
#                     add_coverage_premium(umbrella_coverage, excess_coverage)
#                     submission.coverages.remove(excess_coverage)
#
#                     if excess_deductible:
#                         if umbrella_deductible:
#                             add_deductible_premium(umbrella_deductible, excess_deductible)
#                         submission.deductibles.remove(excess_deductible)
#
#                 mark_all_applicable_as(submission, CoverageType.PRIMARY)
#
#         else:
#             assigned_submission_users = list(filter(lambda x: x.submission_id == submission.id, underwriters))
#
#             primary_group = {254, 264, 257, 364}
#             excess_group = {155, 303, 266, 262, 255, 307, 261, 260, 263, 259, 253, 288, 363, 346, 275, 351, 347, 285,
#                             292, 286, 270, 293}
#
#             submission_users = {x.user_id for x in assigned_submission_users}
#
#             # If no policy ID, and all assigned/owner UWs are in the same groups below, use following UW mapping as a backup:
#             if len(submission.policies) == 0 and len(submission_users) > 0 and len(
#                     submission_users.intersection(primary_group)) == len(
#                 submission_users):
#                 mark_all_applicable_as(submission, CoverageType.PRIMARY)
#             elif len(submission.policies) == 0 and len(submission_users) > 0 and len(
#                     submission_users.intersection(excess_group)) == len(
#                 submission_users):
#                 mark_all_applicable_as_excess(submission, umbrella)
#
#             # If multiple policy IDs, or UWs from conflicting groups, or no UWs:
#             coverage_names = [sc.coverage.name for sc in submission.coverages]
#
#             # If E&S is a coverage, mark GL as Excess (add GL if necessary) and preserve E&S premium
#             if 'excessAndSurplus' in coverage_names:
#                 excess_coverage = \
#                     list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.coverages))[0]
#                 umbrella_coverage = one_or_none(
#                     list(filter(lambda x: x.coverage.name == 'umbrella', submission.coverages)))
#
#                 excess_deductible = one_or_none(
#                     list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.deductibles)))
#                 umbrella_deductible = one_or_none(
#                     list(filter(lambda x: x.coverage.name == 'umbrella', submission.deductibles)))
#
#                 if not umbrella_coverage:
#                     umbrella_coverage = SubmissionCoverage(coverage_id=umbrella.id, coverage=umbrella,
#                                                            submission_id=submission.id)
#                     submission.coverages.append(umbrella_coverage)
#
#                 add_coverage_premium(umbrella_coverage, excess_coverage)
#                 submission.coverages.remove(excess_coverage)
#
#                 for submission_coverage in submission.coverages:
#                     if len(submission_coverage.coverage.coverage_types) > 0 and submission_coverage.coverage_type is None:
#                         submission_coverage.coverage_type = CoverageType.PRIMARY
#
#                 for submission_deductible in submission.deductibles:
#                     if len(submission_deductible.coverage.coverage_types) > 0 and submission_deductible.coverage_type is None:
#                         submission_deductible.coverage_type = CoverageType.PRIMARY
#
#                 if umbrella_deductible:
#                     if excess_deductible:
#                         add_deductible_premium(umbrella_deductible, excess_deductible)
#                         submission.deductibles.remove(excess_deductible)
#
#             else:
#                 # Mark all other coverages as Primary
#                 for sc in submission.coverages:
#                     if CoverageType.PRIMARY in sc.coverage.coverage_types and sc.coverage_type is None:
#                         sc.coverage_type = CoverageType.PRIMARY
#                 for sd in submission.deductibles:
#                     if CoverageType.PRIMARY in sd.coverage.coverage_types and sd.coverage_type is None:
#                         sd.coverage_type = CoverageType.PRIMARY
#
#
# def migrate_all_other_organizations(session):
#     other_org_ids = [4, 2, 1, 3, 8, 9, 13]
#
#     submissions = session.query(Submission).options(joinedload(Submission.coverages),
#                                                     joinedload(Submission.deductibles)).join(
#         User).filter(
#         User.organization_id.in_(other_org_ids)).all()
#
#     # generalLiability = session.query(Coverage).filter(Coverage.name == 'generalLiability').filter(
#     #    Coverage.organization_id.in_(other_org_ids)).first()
#
#     for submission in submissions:
#         excess = list(filter(lambda x: x.coverage.name == 'excessAndSurplus', submission.coverages))
#         # If E&S is listed, mark all applicable coverages as Excess. Add E&S premium to GL if GL exists, otherwise divide among Excess-applicable coverages
#         if excess:
#             other_mark_all_applicable_as_excess(submission)
#         # If E&S is not listed, mark all applicable coverages as Primary
#         else:
#             mark_all_applicable_as(submission, CoverageType.PRIMARY)


def upgrade():
    pass


# def original_upgrade():
#     bind = op.get_bind()
#     session = orm.Session(bind=bind)
#
#     migrate_arch(session)
#     migrate_nw(session)
#     migrate_kis(session)
#     migrate_all_other_organizations(session)
#
#     session.commit()
#
#     conn = op.get_bind()
#     conn.execute('''
# UPDATE submission_coverages sc
# SET coverage_type = 'EXCESS'
# WHERE sc.id IN (
#     SELECT
#         isc.id
#     FROM submission_coverages isc
#     JOIN coverages c on isc.coverage_id = c.id
#     WHERE c.coverage_types::text[] && ARRAY['EXCESS']
#     AND c.organization_id IN (7, 10))
# AND sc.coverage_type IS NULL;
#
# UPDATE submission_deductibles sd
# SET coverage_type = 'EXCESS'
# WHERE sd.id IN (
#     SELECT
#         isd.id
#     FROM submission_deductibles isd
#     JOIN coverages c on isd.coverage_id = c.id
#     WHERE c.coverage_types::text[] && ARRAY['EXCESS']
#     AND c.organization_id IN (7, 10))
# AND sd.coverage_type IS NULL;
#
# UPDATE submission_coverages sc
# SET coverage_type = 'PRIMARY'
# WHERE sc.id IN (
#     SELECT
#         isc.id
#     FROM submission_coverages isc
#     JOIN coverages c on isc.coverage_id = c.id
#     WHERE c.coverage_types::text[] && ARRAY['PRIMARY']
#     AND c.organization_id IN (11, 12))
# AND sc.coverage_type IS NULL;
#
# UPDATE submission_deductibles sd
# SET coverage_type = 'PRIMARY'
# WHERE sd.id IN (
#     SELECT
#         isd.id
#     FROM submission_deductibles isd
#     JOIN coverages c on isd.coverage_id = c.id
#     WHERE c.coverage_types::text[] && ARRAY['PRIMARY']
#     AND c.organization_id IN (11, 12))
# AND sd.coverage_type IS NULL;
#
# DELETE FROM coverages where name IN (
#     'yacht',
#     'excessAndSurplus',
#     'miscLiability1',
#     'miscLiability2',
#     'motorCarriers'
# );
#
# DELETE FROM coverages WHERE name IN ('policyFee', 'terrorism') AND organization_id <> 7;
#     ''')


def downgrade():
    pass

"""Creates Kalepa machine user

Revision ID: 604d2b1510d6
Revises: aa86d4da25dd
Create Date: 2021-04-29 21:15:46.171388+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "604d2b1510d6"
down_revision = "aa86d4da25dd"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT INTO users (id, email, pass_hash, external_id, organization_id, role, name, photo_s3_file_path) 
    VALUES (DEFAULT, '<EMAIL>', null, 'a60kaWl3wSBt7lOVa7ST1cEVhS00OJxp', (select id from organization where name = '<PERSON><PERSON><PERSON>'), 'manager', '<PERSON><PERSON><PERSON>', null)
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("delete from users where email = '<EMAIL>'")

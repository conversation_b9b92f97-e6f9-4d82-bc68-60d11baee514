"""Enable loss <NAME_EMAIL> (user id 1445, org id = 34)

Revision ID: 67ef352beefa
Revises: 4787e11bc3fa
Create Date: 2023-02-23 01:01:42.662947+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "67ef352beefa"
down_revision = "4787e11bc3fa"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update settings set loss_runs_enabled=true where organization_id=34;
    """)


def downgrade():
    pass

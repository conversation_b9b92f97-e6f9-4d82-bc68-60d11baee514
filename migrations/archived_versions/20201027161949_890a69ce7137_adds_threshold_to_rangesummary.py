"""Adds threshold to RangeSummary

Revision ID: 890a69ce7137
Revises: 3d3a88ca9063
Create Date: 2020-10-27 16:19:49.959096+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "890a69ce7137"
down_revision = "3d3a88ca9063"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("range_summary", sa.Column("threshold", sa.String(length=30), nullable=True))


def downgrade():
    op.drop_column("range_summary", "threshold")

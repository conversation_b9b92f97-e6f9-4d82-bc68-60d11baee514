"""More dashboard settings - mike ward
Revision ID: 1e4cef127aed
Revises: e5aa0f5eec43
Create Date: 2022-11-23 15:53:28.481469+00:00
"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1e4cef127aed"
down_revision = "e5aa0f5eec43"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        UPDATE submissions
        SET lost_reasons=(
            ARRAY(
                    SELECT STRING_AGG(TRIM(JsonString::text, '"'), ', ')
                    FROM JSONB_ARRAY_ELEMENTS(stage_details -> 'lostReasons') JsonString
                )
            )
        WHERE stage_details ->> 'lostReasons' IS NOT NULL;
    """)


def downgrade():
    pass

"""Add is_enabled_for_grid_view to MetricPreference

Revision ID: 2b8bc0b4dd4c
Revises: b7785044dd49
Create Date: 2021-09-10 08:59:10.713240+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2b8bc0b4dd4c"
down_revision = "b7785044dd49"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "metric_preference", sa.Column("is_enabled_for_grid_view", sa.<PERSON>(), nullable=False, server_default="t")
    )
    op.create_index(
        op.f("ix_metric_preference_is_enabled_for_grid_view"),
        "metric_preference",
        ["is_enabled_for_grid_view"],
        unique=False,
    )
    conn = op.get_bind()
    conn.execute("""update metric_preference set is_enabled_for_grid_view = is_enabled;""")


def downgrade():
    op.drop_index(op.f("ix_metric_preference_is_enabled_for_grid_view"), table_name="metric_preference")
    op.drop_column("metric_preference", "is_enabled_for_grid_view")

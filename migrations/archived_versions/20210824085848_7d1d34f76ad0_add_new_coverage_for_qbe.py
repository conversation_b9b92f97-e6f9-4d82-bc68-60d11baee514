"""Add new coverage for qbe

Revision ID: 7d1d34f76ad0
Revises: b10350cea62b
Create Date: 2021-08-24 08:58:48.933046+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7d1d34f76ad0"
down_revision = "b10350cea62b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute(f"""
        insert into coverages values (
            uuid_generate_v4(),
            now(),
            null,
            'earthquake(EQSL)',
            'Earthquake (EQSL)',
            false,
            (select id from organization where name = 'QBE')
        ) ON CONFLICT DO NOTHING
    """)


def downgrade():
    pass

"""recover settings for Management Dashboard

Revision ID: 2eb7055aef21
Revises: 2eb7055aef24
Create Date: 2023-02-01 13:07:11.067340+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2eb7055aef21"
down_revision = "2eb7055aef24"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("settings", sa.Column("is_management_dashboard_enabled", sa.<PERSON>(), nullable=True))
    op.add_column("settings", sa.Column("is_underwriter_dashboard_enabled", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("settings", "is_management_dashboard_enabled")
    op.drop_column("settings", "is_underwriter_dashboard_enabled")

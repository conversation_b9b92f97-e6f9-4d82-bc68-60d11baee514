"""read only user

Revision ID: 98d4ad19855b
Revises: 207f7e5220eb
Create Date: 2022-09-12 10:24:45.572265+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "98d4ad19855b"
down_revision = "207f7e5220eb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("is_read_only_account", sa.<PERSON>(), nullable=True))
    op.execute("UPDATE users SET is_read_only_account = false;")
    op.execute("""
        UPDATE users SET is_read_only_account = true WHERE email IN (
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>');""")
    op.alter_column("users", "is_read_only_account", nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "is_read_only_account")
    # ### end Alembic commands ###

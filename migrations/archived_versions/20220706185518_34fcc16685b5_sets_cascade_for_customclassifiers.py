"""Sets CASCADE for CustomClassifiers

Revision ID: 34fcc16685b5
Revises: 9fb5cb05382e
Create Date: 2022-07-06 18:55:18.523792+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "34fcc16685b5"
down_revision = "9fb5cb05382e"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint(
        "customizable_classifier_test_ru_customizable_classifier_id_fkey",
        "customizable_classifier_test_runs",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "customizable_classifier_test_ru_customizable_classifier_id_fkey",
        "customizable_classifier_test_runs",
        "customizable_classifiers",
        ["customizable_classifier_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(
        "phrase_matching_classifier_phra_customizable_classifier_id_fkey",
        "phrase_matching_classifier_phrase",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "phrase_matching_classifier_phra_customizable_classifier_id_fkey",
        "phrase_matching_classifier_phrase",
        "customizable_classifiers",
        ["customizable_classifier_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_constraint(
        "test_run_classification_task_test_run_id_fkey", "test_run_classification_task", type_="foreignkey"
    )
    op.create_foreign_key(
        "test_run_classification_task_test_run_id_fkey",
        "test_run_classification_task",
        "customizable_classifier_test_runs",
        ["test_run_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade():
    op.drop_constraint(
        "test_run_classification_task_test_run_id_fkey", "test_run_classification_task", type_="foreignkey"
    )
    op.create_foreign_key(
        "test_run_classification_task_test_run_id_fkey",
        "test_run_classification_task",
        "customizable_classifier_test_runs",
        ["test_run_id"],
        ["id"],
    )
    op.drop_constraint(
        "phrase_matching_classifier_phra_customizable_classifier_id_fkey",
        "phrase_matching_classifier_phrase",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "phrase_matching_classifier_phra_customizable_classifier_id_fkey",
        "phrase_matching_classifier_phrase",
        "customizable_classifiers",
        ["customizable_classifier_id"],
        ["id"],
    )
    op.drop_constraint(
        "customizable_classifier_test_ru_customizable_classifier_id_fkey",
        "customizable_classifier_test_runs",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "customizable_classifier_test_ru_customizable_classifier_id_fkey",
        "customizable_classifier_test_runs",
        "customizable_classifiers",
        ["customizable_classifier_id"],
        ["id"],
    )

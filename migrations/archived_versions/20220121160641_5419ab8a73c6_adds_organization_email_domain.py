"""Adds organization.email_domain

Revision ID: 5419ab8a73c6
Revises: 3c6a445d5a1b
Create Date: 2022-01-21 16:06:41.994213+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5419ab8a73c6"
down_revision = "3c6a445d5a1b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("organization", sa.Column("email_domain", sa.String(), nullable=True))
    op.create_unique_constraint(None, "organization", ["email_domain"])

    conn = op.get_bind()
    conn.execute("update organization set email_domain = 'kalepa.com' where name = '<PERSON><PERSON><PERSON>';")
    conn.execute("update organization set email_domain = 'us.qbe.com' where name = 'QBE';")
    conn.execute("update organization set email_domain = 'nationwide.com' where name = 'Nationwide';")


def downgrade():
    op.drop_constraint(None, "organization", type_="unique")
    op.drop_column("organization", "email_domain")

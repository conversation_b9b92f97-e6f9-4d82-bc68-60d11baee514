"""emails normalized subject

Revision ID: ab318baeaab7
Revises: fd956007c6c0
Create Date: 2023-04-12 10:56:13.814636+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ab318baeaab7"
down_revision = "fd956007c6c0"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '900 s';")  # 15 min
        op.execute("""ALTER TABLE emails ADD COLUMN if not exists normalized_subject varchar;""")
        op.execute("""ALTER TABLE report_email_correspondence ADD COLUMN if not exists email_account varchar;""")
        op.execute("""
            WITH email_accounts AS (SELECT case u.organization_id
                                       when 6 then '<EMAIL>'
                                       when 10 then '<EMAIL>'
                                       when 33 then '<EMAIL>'
                                       else '<EMAIL>' end as email_account,
                                   r.correspondence_id
                            FROM reports_v2 r
                                     INNER JOIN users u on r.owner_id = u.id
                            WHERE r.correspondence_id is not null)
            UPDATE report_email_correspondence
            SET email_account = ea.email_account
            FROM email_accounts ea
            WHERE report_email_correspondence.id = ea.correspondence_id;
            """)
        op.execute("""
            UPDATE emails 
            SET normalized_subject = upper(
                regexp_replace(
                    regexp_replace(
                        trim(regexp_replace(email_subject, E'\u00a0', ' ', 'g')),
                        '^(fw:\\s*|re:\\s*|fwd:\\s*|\\(external\\)\\s*|\\[external\\]\\s*|\\d{9}[A,C]\\s+)+', '', 'i'
                    ),
                    '\\s+', ' ', 'g'
                )
            )
            """)
        op.execute("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_emails_email_account_normalized_subject 
            ON emails (email_account, normalized_subject);
            """)
        op.execute("""
            CREATE INDEX CONCURRENTLY IF NOT EXISTS ix_report_email_correspondence_email_account_thread_id 
            ON report_email_correspondence (email_account, thread_id);
            """)


def downgrade():
    op.drop_index("ix_emails_email_account_normalized_subject", table_name="emails")
    op.drop_index("ix_report_email_correspondence_email_account_thread_id", table_name="report_email_correspondence")
    op.drop_column("emails", "normalized_subject")
    op.drop_column("report_email_correspondence", "email_account")

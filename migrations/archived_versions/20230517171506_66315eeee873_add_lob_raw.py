"""add lob_raw

Revision ID: 66315eeee873
Revises: c5f48afa89ec
Create Date: 2023-05-17 17:15:06.588344+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "66315eeee873"
down_revision = "c5f48afa89ec"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""ALTER TABLE loss ADD COLUMN IF NOT EXISTS lob_raw VARCHAR;""")


def downgrade():
    op.execute("""ALTER TABLE loss DROP COLUMN IF EXISTS lob_raw;""")

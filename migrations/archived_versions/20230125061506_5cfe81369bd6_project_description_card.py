"""project description card

Revision ID: 5cfe81369bd6
Revises: aaaa20230124
Create Date: 2023-01-24 12:15:06.801384+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5cfe81369bd6"
down_revision = "aaaa20230124"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards set type = 'PROJECT_DESCRIPTION' where id = '55be9fb2-1f8b-456f-944d-65158faafb74';

INSERT INTO mode_rows(id, mode_id, position, title) VALUES
        ('a8c55d22-797b-44a4-a324-e77987a2831e', '2f622131-068c-4ca4-9066-99d7bb8436b8', 95, null);

INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
        ('1fe42da2-5463-48d2-a0c1-ed7167cabd2b', 'a8c55d22-797b-44a4-a324-e77987a2831e', 0, 12, null);

INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
        (
            'a15fbfb4-36e5-4904-a4fb-e5758d5efc87',
            '1fe42da2-5463-48d2-a0c1-ed7167cabd2b',
            null,
            10,
            'PROJECT_DESCRIPTION_HEADER',
            'project-description-header',
            null
        );
    """)


def downgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards set type = 'DEFAULT' where id = '55be9fb2-1f8b-456f-944d-65158faafb74';

DELETE FROM mode_cards where id = 'a15fbfb4-36e5-4904-a4fb-e5758d5efc87';
DELETE FROM mode_columns where id = '1fe42da2-5463-48d2-a0c1-ed7167cabd2b';
DELETE FROM mode_rows where id = 'a8c55d22-797b-44a4-a324-e77987a2831e';
    """)

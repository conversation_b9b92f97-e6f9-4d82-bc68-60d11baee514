"""Add description fields

Revision ID: eeb41f545fd8
Revises: f482315079d3
Create Date: 2023-03-27 18:02:21.072177+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "eeb41f545fd8"
down_revision = "f482315079d3"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("email_description", sa.String(), nullable=True))
    op.add_column("submissions", sa.Column("email_project_description", sa.String(), nullable=True))


def downgrade():
    op.drop_column("submissions", "email_project_description")
    op.drop_column("submissions", "email_description")

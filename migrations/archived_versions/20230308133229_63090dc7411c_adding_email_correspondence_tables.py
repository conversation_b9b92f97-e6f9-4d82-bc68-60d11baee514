"""Adding email correspondence tables

Revision ID: 63090dc7411c
Revises: f8d4ad19855b
Create Date: 2023-03-08 13:32:29.123679+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
from sqlalchemy.engine.reflection import Inspector
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "63090dc7411c"
down_revision = "f8d4ad19855b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    tables = inspector.get_table_names()
    if "report_email_correspondence" not in tables:
        op.create_table(
            "report_email_correspondence",
            sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
            sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
            sa.Column("thread_id", sa.String(), nullable=True),
            sa.PrimaryKeyConstraint("id"),
        )
        op.create_index(
            op.f("ix_report_email_correspondence_thread_id"), "report_email_correspondence", ["thread_id"], unique=False
        )
    if "emails" not in tables:
        op.create_table(
            "emails",
            sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
            sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
            sa.Column("message_id", sa.String(), nullable=True),
            sa.Column("correspondence_id", postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column("email_account", sa.String(), nullable=False),
            sa.Column("email_subject", sa.String(), nullable=True),
            sa.Column("email_from", sa.String(), nullable=True),
            sa.Column("email_to", sa.String(), nullable=True),
            sa.Column("email_cc", sa.ARRAY(sa.String()), nullable=True),
            sa.Column("email_attachments_count", sa.Integer(), nullable=True),
            sa.Column("attachments", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
            sa.ForeignKeyConstraint(
                ["correspondence_id"],
                ["report_email_correspondence.id"],
            ),
            sa.PrimaryKeyConstraint("id"),
        )
        op.create_index(op.f("ix_emails_correspondence_id"), "emails", ["correspondence_id"], unique=False)
        op.create_index(op.f("ix_emails_message_id"), "emails", ["message_id"], unique=False)
    columns = inspector.get_columns("reports_v2")
    if "correspondence_id" not in columns:
        op.add_column("reports_v2", sa.Column("correspondence_id", postgresql.UUID(as_uuid=True), nullable=True))
        op.create_index(op.f("ix_reports_v2_correspondence_id"), "reports_v2", ["correspondence_id"], unique=False)
        op.create_foreign_key(
            "reports_v2_correspondence_id_fkey",
            "reports_v2",
            "report_email_correspondence",
            ["correspondence_id"],
            ["id"],
        )


def downgrade():
    op.drop_constraint("reports_v2_correspondence_id_fkey", "reports_v2", type_="foreignkey")
    op.drop_index(op.f("ix_reports_v2_correspondence_id"), table_name="reports_v2")
    op.drop_column("reports_v2", "correspondence_id")
    op.drop_index(op.f("ix_emails_message_id"), table_name="emails")
    op.drop_index(op.f("ix_emails_correspondence_id"), table_name="emails")
    op.drop_table("emails")
    op.drop_index(op.f("ix_report_email_correspondence_thread_id"), table_name="report_email_correspondence")
    op.drop_table("report_email_correspondence")

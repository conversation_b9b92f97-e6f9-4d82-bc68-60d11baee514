"""fix agent with invalid email

Revision ID: ccadb5b99309
Revises: ac72139051a5
Create Date: 2023-03-21 13:24:47.103759+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ccadb5b99309"
down_revision = "ac72139051a5"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE brokers SET email = null WHERE email = '';
    """)


def downgrade():
    pass

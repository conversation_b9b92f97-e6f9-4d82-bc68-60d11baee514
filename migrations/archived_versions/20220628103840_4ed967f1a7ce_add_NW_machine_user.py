"""Adds NW machine user

Revision ID: 4ed967f1a7ce
Revises: 8426db504d8d
Create Date: 2022-06-28 10:38:40.641646+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4ed967f1a7ce"
down_revision = "8426db504d8d"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            insert into users values
            (default, '<EMAIL>', null, 'Bayd96nz3CFO7JQeYfe5bTLmvZngTpha', 6, 'manager', 'Nationwide', null, null, now(), null)
        """)


def downgrade():
    pass

"""Enables reply emails for all users

Revision ID: 4913ec26214f
Revises: 063dbabaca2f
Create Date: 2023-05-08 22:24:59.001632+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4913ec26214f"
down_revision = "063dbabaca2f"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("settings", "reply_emails_enabled")
    op.add_column("settings", sa.Column("reply_emails_enabled", sa.<PERSON>(), nullable=True, server_default="true"))


def downgrade():
    ...

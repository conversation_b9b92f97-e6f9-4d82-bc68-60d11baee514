"""Add HOMEPAGE to classifiers

Revision ID: 6b987796990e
Revises: 2e05bd743e5a
Create Date: 2022-06-01 17:01:15.041411+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6b987796990e"
down_revision = "2e05bd743e5a"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            update customizable_classifiers set should_analyze = should_analyze || '{"HOMEPAGE"}'
            where label != 'Dangerous objects in food' and NOT ('HOMEPAGE' = ANY(should_analyze))
        """)


def downgrade():
    pass

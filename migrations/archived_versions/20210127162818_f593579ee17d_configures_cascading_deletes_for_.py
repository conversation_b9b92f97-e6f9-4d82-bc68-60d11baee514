"""Configures cascading deletes for MetricConfigs as

Revision ID: f593579ee17d
Revises: f239ec8eeb8f
Create Date: 2021-01-27 16:28:18.480749+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "f593579ee17d"
down_revision = "f239ec8eeb8f"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint("categorical_data_summary_id_fkey", "categorical_data_summary", type_="foreignkey")
    op.create_foreign_key(None, "categorical_data_summary", "metric", ["id"], ["id"], ondelete="CASCADE")
    op.drop_constraint("employees_summary_id_fkey", "employees_summary", type_="foreignkey")
    op.create_foreign_key(None, "employees_summary", "metric", ["id"], ["id"], ondelete="CASCADE")
    op.drop_constraint("grade_summary_id_fkey", "grade_summary", type_="foreignkey")
    op.create_foreign_key(None, "grade_summary", "metric", ["id"], ["id"], ondelete="CASCADE")
    op.drop_constraint("list_summary_id_fkey", "list_summary", type_="foreignkey")
    op.create_foreign_key(None, "list_summary", "metric", ["id"], ["id"], ondelete="CASCADE")
    op.drop_constraint("mean_id_fkey", "mean", type_="foreignkey")
    op.create_foreign_key(None, "mean", "metric", ["id"], ["id"], ondelete="CASCADE")
    op.drop_constraint("range_summary_id_fkey", "range_summary", type_="foreignkey")
    op.create_foreign_key(None, "range_summary", "metric", ["id"], ["id"], ondelete="CASCADE")


def downgrade():
    op.drop_constraint(None, "range_summary", type_="foreignkey")
    op.create_foreign_key("range_summary_id_fkey", "range_summary", "metric", ["id"], ["id"])
    op.drop_constraint(None, "mean", type_="foreignkey")
    op.create_foreign_key("mean_id_fkey", "mean", "metric", ["id"], ["id"])
    op.drop_constraint(None, "list_summary", type_="foreignkey")
    op.create_foreign_key("list_summary_id_fkey", "list_summary", "metric", ["id"], ["id"])
    op.drop_constraint(None, "grade_summary", type_="foreignkey")
    op.create_foreign_key("grade_summary_id_fkey", "grade_summary", "metric", ["id"], ["id"])
    op.drop_constraint(None, "employees_summary", type_="foreignkey")
    op.create_foreign_key("employees_summary_id_fkey", "employees_summary", "metric", ["id"], ["id"])
    op.drop_constraint(None, "categorical_data_summary", type_="foreignkey")
    op.create_foreign_key("categorical_data_summary_id_fkey", "categorical_data_summary", "metric", ["id"], ["id"])

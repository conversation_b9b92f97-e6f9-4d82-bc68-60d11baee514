"""Added metric_config weight

Revision ID: 5ffc8346ef2c
Revises: 677855b51fc2
Create Date: 2021-08-31 10:28:50.524139+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5ffc8346ef2c"
down_revision = "677855b51fc2"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "org_metric_config",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("metric_name", sa.VARCHAR(), nullable=False),
        sa.Column("organization_id", postgresql.INTEGER(), nullable=False),
        sa.Column("weight", postgresql.DOUBLE_PRECISION(precision=53), nullable=False),
        sa.ForeignKeyConstraint(["organization_id"], ["organization.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_org_metric_config_organization_id"), "org_metric_config", ["organization_id"], unique=False
    )
    op.create_index(op.f("ix_org_metric_config_metric_name"), "org_metric_config", ["metric_name"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_org_metric_config_metric_name"), table_name="org_metric_config")
    op.drop_index(op.f("ix_org_metric_config_organization_id"), table_name="org_metric_config")
    op.drop_table("org_metric_config")

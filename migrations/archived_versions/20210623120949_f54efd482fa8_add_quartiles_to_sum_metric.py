"""Add quartiles to sum metric

Revision ID: f54efd482fa8
Revises: 467f9acdf330
Create Date: 2021-06-23 12:09:49.325877+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f54efd482fa8"
down_revision = "467f9acdf330"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("sum", sa.Column("quartiles", postgresql.ARRAY(sa.Float()), nullable=True))


def downgrade():
    op.drop_column("sum", "quartiles")

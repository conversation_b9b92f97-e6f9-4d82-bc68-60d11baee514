"""Extend submission stage enum

Revision ID: f1a8df027069
Revises: 8d30d9eb419b
Create Date: 2020-12-28 13:51:37.033708+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f1a8df027069"
down_revision = "8d30d9eb419b"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionstage ADD VALUE 'QUOTED_LOST'")
        op.execute("ALTER TYPE submissionstage ADD VALUE 'QUOTED_BOUND'")


def downgrade():
    pass

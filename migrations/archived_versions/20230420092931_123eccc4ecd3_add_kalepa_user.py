"""Add Kalepa user

Revision ID: 123eccc4ecd3
Revises: ddad2b85962c
Create Date: 2023-04-20 09:29:31.674916+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "123eccc4ecd3"
down_revision = "ddad2b85962c"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        UPDATE settings
        SET support_email = '<EMAIL>'
        WHERE organization_id = 9
    """)
    op.execute("""
        insert into users values
        (default, '<EMAIL>', null, 'auth0|643ff35fd80c9283931a80ce', 9, 'manager', '<EMAIL>', null, null, now(), null, false, null, false, null)
    """)


def downgrade():
    pass

"""remove duplicate policies

Revision ID: 90af694a01e4
Revises: 1acf694a01e4
Create Date: 2023-04-26 13:41:47.571339+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "90af694a01e4"
down_revision = "1acf694a01e4"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
        with duplicate_policies as (
        select external_id,
               submission_id,
               (array_agg(id order by created_at))[1] as first_policy_id
        from policy
        group by external_id, submission_id
        having count(*) > 1)
        delete from policy p
        using duplicate_policies d
        where p.external_id = d.external_id
          and p.submission_id = d.submission_id
          and p.id <> d.first_policy_id
        """)

        op.create_unique_constraint(None, "policy", ["external_id", "submission_id"])


def downgrade():
    pass

"""policy cleanup

Revision ID: 1be067e5b9ef
Revises: 16ec52c0eaef
Create Date: 2023-05-11 21:16:08.229999+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1be067e5b9ef"
down_revision = "16ec52c0eaef"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
            DELETE FROM policy;
        """)


def downgrade():
    pass

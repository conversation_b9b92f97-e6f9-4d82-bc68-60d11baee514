"""enforcement cases card

Revision ID: 4aacf6d06a2c
Revises: bdfb30ce754b
Create Date: 2023-02-02 15:06:49.333445+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4aacf6d06a2c"
down_revision = "bdfb30ce754b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
insert into mode_rows (id, created_at, mode_id, position, title)
values ('2ea7ae68-a221-4c36-86a5-e6685657c91b', current_timestamp, 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 62, 'Enforcement Cases');
insert into mode_columns (id, created_at, row_id, position, width, section_title)
values ('8d7e5379-ec90-4557-bc47-b6c8577bfdf2', current_timestamp, '2ea7ae68-a221-4c36-86a5-e6685657c91b', 0, 12, null);
insert into mode_cards (id, created_at, column_id, title, position, type, card_id, props)
values ('23530500-a4b6-4b3d-960d-585a198e903d', current_timestamp, '8d7e5379-ec90-4557-bc47-b6c8577bfdf2', null, 0, 'TABLE', 'enforcement-cases', '{"type":"ENFORCEMENT_CASES"}');
    """)


def downgrade():
    pass

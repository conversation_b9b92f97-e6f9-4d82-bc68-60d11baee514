"""add new table update_business_log

Revision ID: d030753f777d
Revises: a202505654ab
Create Date: 2022-12-14 18:30:27.088882+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d030753f777d"
down_revision = "a202505654ab"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "update_businesses_log",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("new_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("old_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=False),
        sa.Column("submission_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
        sa.Column("affected_tables", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade():
    op.drop_index(op.f("ix_update_businesses_log_new_id"), table_name="update_businesses_log")
    op.drop_table("update_businesses_log")

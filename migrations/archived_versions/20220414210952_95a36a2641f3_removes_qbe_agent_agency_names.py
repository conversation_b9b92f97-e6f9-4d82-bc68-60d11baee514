"""Removes QBE agent/agency names

Revision ID: 95a36a2641f3
Revises: cd2ba16fc134
Create Date: 2022-04-14 21:09:52.955455+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "95a36a2641f3"
down_revision = "cd2ba16fc134"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    delete from user_fields_review ufr where id in (
        select ufr.id
        from user_fields_review ufr
                 join submissions_reports sr on ufr.submission_id = sr.submission_id
                 join reports_v2 r on sr.report_id = r.id
                 join users u on r.owner_id = u.id
                 join organization o on u.organization_id = o.id
        where o.name = 'QBE'
          and ufr.field_key in ('agent/agentName', 'agent/agency')
    );
    """)


def downgrade():
    pass

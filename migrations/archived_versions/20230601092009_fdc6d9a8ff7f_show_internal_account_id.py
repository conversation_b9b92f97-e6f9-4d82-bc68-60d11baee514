"""show_internal_account_id

Revision ID: fdc6d9a8ff7f
Revises: 7e36c2259b8d
Create Date: 2023-05-29 19:20:09.752722+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fdc6d9a8ff7f"
down_revision = "7e36c2259b8d"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("settings", sa.Column("show_internal_account_id", sa.<PERSON>(), nullable=True))

    with op.get_context().autocommit_block():
        op.execute("UPDATE settings SET show_internal_account_id = TRUE WHERE organization_id = 3;")
        op.execute("UPDATE settings SET show_account_id = FALSE WHERE organization_id = 3;")


def downgrade():
    op.drop_column("settings", "show_internal_account_id")

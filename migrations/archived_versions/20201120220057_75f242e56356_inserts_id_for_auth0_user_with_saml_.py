"""Inserts ID for Auth0 user with SAML connector

Revision ID: 75f242e56356
Revises: cad174f99056
Create Date: 2020-11-20 22:00:57.773838+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "75f242e56356"
down_revision = "cad174f99056"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update users
    set external_id_saml = 'samlp|KALEPA-OKTA|<EMAIL>'
    where email = '<EMAIL>'
    """)


def downgrade():
    pass

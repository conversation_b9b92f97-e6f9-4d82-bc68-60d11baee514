"""ENG-7381 Update user account

Revision ID: bae6746a33a7
Revises: 8a73da7042bd
Create Date: 2022-02-22 11:02:28.466659+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bae6746a33a7"
down_revision = "8a73da7042bd"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("""
        DELETE FROM users
        WHERE
        email = '<EMAIL>' AND organization_id is NULL;
    """)
    conn.execute("""
        UPDATE users
        SET role = 'manager',
        external_id = 'google-apps|<EMAIL>'
        WHERE email = '<EMAIL>';
    """)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

"""Added Loss file_id

Revision ID: dd10843c13e8
Revises: 8d739fd6610a
Create Date: 2022-12-14 08:12:09.577569+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "dd10843c13e8"
down_revision = "8d739fd6610a"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '600 s';")
    op.add_column("loss", sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(op.f("ix_loss_file_id"), "loss", ["file_id"], unique=False)
    op.create_foreign_key(None, "loss", "files", ["file_id"], ["id"])


def downgrade():
    op.drop_index(op.f("ix_loss_file_id"), table_name="loss")
    op.drop_column("loss", "file_id")

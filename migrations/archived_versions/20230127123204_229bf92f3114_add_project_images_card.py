"""Add images card

Revision ID: 229bf92f3114
Revises: de58e5b7bbf3
Create Date: 2023-01-27 12:32:04.190132+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "229bf92f3114"
down_revision = "de58e5b7bbf3"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        INSERT INTO mode_rows(id, mode_id, position, title) VALUES
    ('feb2611c-7032-45bc-82c3-4b282c848b5d', '2f622131-068c-4ca4-9066-99d7bb8436b8', 190, 'Images');

    INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
    ('b3762dcc-630c-4e09-bc77-2756a253efdf', 'feb2611c-7032-45bc-82c3-4b282c848b5d', 0, 12, null);

    INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
    (
        '4d490b2d-2767-4f45-854d-f88e60cc6ec1',
        'b3762dcc-630c-4e09-bc77-2756a253efdf',
        'Images',
        10,
        'IMAGES_CARD',
        'images',
        '{"parentType": "BUSINESS", "businessSelect": "contractorProject"}'
    );
    """)


def downgrade():
    pass

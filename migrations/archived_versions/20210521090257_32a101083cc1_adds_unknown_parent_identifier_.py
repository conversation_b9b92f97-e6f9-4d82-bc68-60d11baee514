"""Adds unknown_parent_identifier column to FormFieldValue

Revision ID: 32a101083cc1
Revises: 1173c37e27f5
Create Date: 2021-05-21 09:02:57.486819-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "32a101083cc1"
down_revision = "1173c37e27f5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE parenttype ADD VALUE IF NOT EXISTS 'ARBITRARY_PARENT';""")
    op.add_column("form_field_value", sa.Column("arbitrary_parent_identifier", sa.String(), nullable=True))


def downgrade():
    op.drop_column("form_field_value", "arbitrary_parent_identifier")

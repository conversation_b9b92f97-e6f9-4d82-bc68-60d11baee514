"""Add images card

Revision ID: 159bf92f3114
Revises: e6c66ce448b7
Create Date: 2023-01-26 11:32:04.190132+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "159bf92f3114"
down_revision = "e6c66ce448b7"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards
        set props='{"parentType": "BUSINESS", "businessSelect": "generalContractor"}', type='IMAGES_CARD'
        where id = 'cd27fed1-6793-4e39-8e94-f14959f85273';

        update mode_cards
        set props='{"parentType": "BUSINESS", "businessSelect": "contractorProject"}', type='IMAGES_CARD'
        where id = '047f00cb-9d13-4e43-b10b-e45c1f04c55b';
    """)


def downgrade():
    pass

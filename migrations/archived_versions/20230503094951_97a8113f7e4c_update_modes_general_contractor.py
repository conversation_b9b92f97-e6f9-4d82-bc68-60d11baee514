"""Update General Contractor to Contractor

Revision ID: 97a8113f7e4c
Revises: 6254cee29e35
Create Date: 2023-05-03 09:49:51.119230+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "97a8113f7e4c"
down_revision = "6254cee29e35"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
            UPDATE mode_cards set title='Contractor' where title='General Contractor';
            UPDATE mode_rows set title='Contractor' where title='General Contractor';
        """)


def downgrade():
    pass

"""Fixes multi-premises osha-violations preview card

Revision ID: b95afccaee4a
Revises: f48c047a9c21
Create Date: 2023-04-23 22:14:30.081875+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "b95afccaee4a"
down_revision = "f48c047a9c21"
branch_labels = None
depends_on = None

new_props = (
    '{"highlights": [{"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}],'
    ' "label": "Legal Filings", "cardId": "legal-filings-card", "source": {"mapper": "numberOfItems", "source":'
    ' {"parentType": "BUSINESS", "sourceType": "DOCUMENT", "documentType": "LEGAL_FILING"}}, "noValuesLabel": "None'
    ' found", "redirectLinkLabel": "Go to Legal Filings"}, {"icons": [{"name": "warning", "color": "error",'
    ' "condition": {"min": 1, "type": "isInRange"}}], "label": "OSHA Violations", "cardId": "osha-violations",'
    ' "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType": "DOCUMENT",'
    ' "documentType": "OSHA_VIOLATION"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to OSHA Violations"},'
    ' {"icons": [{"name": "warning", "color": "error", "condition": {"min": 1, "type": "isInRange"}}], "label": "News",'
    ' "cardId": "news-card", "source": {"mapper": "numberOfItems", "source": {"parentType": "BUSINESS", "sourceType":'
    ' "DOCUMENT", "documentType": "NEWS"}}, "noValuesLabel": "None found", "redirectLinkLabel": "Go to News"}]}'
)
mode_card_preview_id = "3d65d00d-aa03-4b7d-ad26-84fa0c56e446"


def upgrade():
    conn = op.get_bind()
    conn.execute(f"UPDATE mode_card_previews SET props = '{new_props}' WHERE id = '{mode_card_preview_id}'")


def downgrade():
    ...

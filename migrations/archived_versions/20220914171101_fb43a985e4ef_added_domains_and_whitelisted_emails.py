"""Added domains and whitelisted emails

Revision ID: fb43a985e4ef
Revises: 98d4ad19855b
Create Date: 2022-09-14 17:11:01.302212+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fb43a985e4ef"
down_revision = "98d4ad19855b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    op.add_column("settings", sa.Column("email_domains", postgresql.ARRAY(sa.String()), nullable=True))
    op.add_column("settings", sa.Column("whitelisted_emails", postgresql.ARRAY(sa.String()), nullable=True))
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=1;")
    conn.execute("update settings set email_domains=array_append(email_domains,'us.er') where organization_id=1;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=2;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.com') where organization_id=3;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=3;")
    conn.execute("update settings set email_domains=array_append(email_domains,'us.qbe.com') where organization_id=5;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.com') where organization_id=5;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=5;")
    conn.execute(
        "update settings set email_domains=array_append(email_domains,'nationwide.com') where organization_id=6;"
    )
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=6;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.com') where organization_id=6;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.com') where organization_id=7;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=7;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.com') where organization_id=8;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=8;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.com') where organization_id=9;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=9;")
    conn.execute(
        "update settings set email_domains=array_append(email_domains,'archinsurance.com') where organization_id=10;"
    )
    conn.execute(
        "update settings set email_domains=array_append(email_domains,'archcapservices.com') where organization_id=10;"
    )
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.co') where organization_id=10;")
    conn.execute("update settings set email_domains=array_append(email_domains,'kalepa.com') where organization_id=10;")


def downgrade():
    op.drop_column("settings", "whitelisted_emails")
    op.drop_column("settings", "email_domains")

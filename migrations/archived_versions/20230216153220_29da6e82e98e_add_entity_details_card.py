"""Add entity details card

Revision ID: 29da6e82e98e
Revises: a8b2170aa29e
Create Date: 2023-02-16 15:32:20.981439+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "29da6e82e98e"
down_revision = "a8b2170aa29e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    insert into mode_rows (id, mode_id, position, is_collapsible, is_default_open, title) values
    ('6160b40e-76d0-4a9f-9c8e-ac12a64ad43d', 'a265715d-4411-4411-9fb0-e745695d8aa8', 190, false, true, null);
    
    insert into mode_columns (id, row_id, position, width) values
    ('35212633-c86c-42e0-b976-e4c74ce5c969', '6160b40e-76d0-4a9f-9c8e-ac12a64ad43d', 0, 12);
    
    insert into mode_cards (id, column_id, title, position, type, card_id, props)  values
    ('3ed3d7d1-7512-413c-808d-cb4ccd5fc33f', '35212633-c86c-42e0-b976-e4c74ce5c969', null, 10, 'ENTITY_DETAILS', 'entity-details', null);
    """)


def downgrade():
    pass

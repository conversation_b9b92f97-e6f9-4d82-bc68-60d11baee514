"""Add permits location card

Revision ID: 62e6654ec03d
Revises: e2900aa6a8dd
Create Date: 2023-01-17 07:50:40.233211+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "62e6654ec03d"
down_revision = "6b2a25f94ced"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        INSERT INTO mode_rows(id, mode_id, position, title) VALUES
        ('f537fc68-ff88-4929-aa0d-ab8d4edd5a18', '0da04e4f-b332-4fa3-9f89-08caa046fc24', 40, 'General Contractor');
        
        INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
        ('44ab64d9-4327-476a-a156-a10f08195696', 'f537fc68-ff88-4929-aa0d-ab8d4edd5a18', 0, 12, null);
        
        INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
        (
            '38571967-eb91-424b-b03a-b35d0d5e250d',
            '44ab64d9-4327-476a-a156-a10f08195696',
            'Permit locations', 
            30,
            'MAP_CARD',
            'permit-locations',
            '{"parentType": "BUSINESS", "markerSelect": "positionFromPremises", "businessSelect": "generalContractor", "documentType": "PERMIT", "height": 416}'
        );
        
        INSERT INTO mode_rows(id, mode_id, position, title) VALUES
        ('dd114578-3e7a-4821-b118-21ade1d2d097', '2f622131-068c-4ca4-9066-99d7bb8436b8', 40, 'General Contractor');
        
        INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
        ('4055ad70-5a24-4ef5-82dc-9aa346896c71', 'dd114578-3e7a-4821-b118-21ade1d2d097', 0, 12, null);
        
        INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
        (
            'bddd673c-2a6b-4b0b-a369-944f4afde8b1',
            '4055ad70-5a24-4ef5-82dc-9aa346896c71',
            'Permit locations', 
            30,
            'MAP_CARD',
            'permit-locations',
            '{"parentType": "BUSINESS", "markerSelect": "positionFromPremises", "businessSelect": "generalContractor", "documentType": "PERMIT", "height": 416}'
        );
        
        INSERT INTO mode_rows(id, mode_id, position, title) VALUES
        ('b14555bc-8fc0-415f-a2db-2b6a8e9f3b95', 'a265715d-4411-4411-9fb0-e745695d8aa8', 40, 'General Contractor');

        INSERT INTO mode_columns(id, row_id, position, width, section_title) VALUES
        ('1fc2cfc1-8836-47c2-82f7-334247f1f2bc', 'b14555bc-8fc0-415f-a2db-2b6a8e9f3b95', 0, 12, null);
        
        INSERT INTO mode_cards (id, column_id, title, position, type, card_id, props) VALUES
        (
            '73604af8-e376-44bf-af03-631a30a1288d',
            '1fc2cfc1-8836-47c2-82f7-334247f1f2bc',
            'Permit locations', 
            30,
            'MAP_CARD',
            'permit-locations',
            '{"parentType": "BUSINESS", "markerSelect": "positionFromPremises", "documentType": "PERMIT", "height": 416}'
        );
    """)


def downgrade():
    pass

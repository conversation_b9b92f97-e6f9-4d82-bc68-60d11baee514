"""Fixes contractor types and services metric configs
Revision ID: 11f41db9c025
Revises: 421832c5df4d
Create Date: 2022-01-11 23:24:48.505889+00:00
"""
from uuid import UUID, uuid4
import os

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "11f41db9c025"
down_revision = "421832c5df4d"
branch_labels = None
depends_on = None

ENV_TO_METRIC_GROUP_ID = {
    "dev": UUID("379bc9f4-470e-4129-835e-368f726969ae"),
    "stage": UUID("379bc9f4-470e-4129-835e-368f726969ae"),
    "prod": UUID("79a38bc2-f2d2-4a49-b428-00d27a55eacd"),
}

CONTRACTOR_SERVICE_DISPLAY_NAMES = [
    "Cabinetry",
    "Tile And Stone Work",
    "Concrete Construction",
    "Decorating",
    "Door And Window Installation",
    "Fence Construction",
    "Hvac Installation And Service",
    "Masonry",
    "Plumbing",
    "Sheet Metal Work",
    "Residential Siding Work",
    "Sign Installation And Repair",
    "Swimming Pool Construction",
    "Paving",
]

CONTRACTOR_TYPE_DISPLAY_NAMES = [
    "Architectural Services Contractor",
    "Glass And Glazing Contractor",
    "Electrical And Other Wiring Installation Contractor",
    "Tile And Terrazzo Contractor",
    "Structural Steel And Precast Concrete Contractor",
    "Site Preparation Contractor",
    "Siding Contractor",
    "Roofing Contractor",
    "Residential Building Construction Contractor",
    "Poured Concrete Foundation And Structure Contractor",
    "Plumbing Heating And Air Conditioning Contractor",
    "Painting And Wall Covering Contractor",
    "Other Foundation Structure And Building Exterior Contractor",
    "Masonry Contractor",
    "Landscaping Services Contractor",
    "Landscape Architectural Services Contractor",
    "Industrial Building Construction Contractor",
    "Framing Contractor",
    "Flooring Contractor",
    "Finish Carpentry Contractor",
    "Drywall And Insulation Contractor",
    "Commercial And Institutional Building Construction Contractor",
]


def upgrade():
    conn = op.get_bind()

    for display_name in CONTRACTOR_SERVICE_DISPLAY_NAMES:
        conn.execute(
            sa.text("""
            INSERT INTO metric_config (id, created_at, display_name, metric_type, metric_group_id, parent_metric) 
            VALUES (:id, DEFAULT, :display_name, 'CATEGORICAL_DATA_SUMMARY', :metric_group_id, 'Contractor Services') 
            ON CONFLICT DO NOTHING;
            """),
            id=uuid4(),
            display_name=display_name,
            metric_group_id=ENV_TO_METRIC_GROUP_ID[os.environ["KALEPA_ENV"]],
        )

    for display_name in CONTRACTOR_TYPE_DISPLAY_NAMES:
        conn.execute(
            sa.text("""
            INSERT INTO metric_config (id, created_at, display_name, metric_type, metric_group_id, parent_metric) 
            VALUES (:id, DEFAULT, :display_name, 'CATEGORICAL_DATA_SUMMARY', :metric_group_id, 'Contractor Types') 
            ON CONFLICT DO NOTHING;
            """),
            id=uuid4(),
            display_name=display_name,
            metric_group_id=ENV_TO_METRIC_GROUP_ID[os.environ["KALEPA_ENV"]],
        )


def downgrade():
    pass

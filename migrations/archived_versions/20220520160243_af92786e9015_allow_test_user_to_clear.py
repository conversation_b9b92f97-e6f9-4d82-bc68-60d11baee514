"""Allow test user to clear
Revision ID: af92786e9015
Revises: ac0cec4d3cce
Create Date: 2022-05-20 16:02:43.410854+00:00
"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "af92786e9015"
down_revision = "ac0cec4d3cce"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        # <EMAIL>
        op.execute("""
            INSERT INTO settings (id, created_at, updated_at, user_id, can_resolve_clearing_issues)
            VALUES (uuid_generate_v4(), now(), null, 17, true)
        """)


def downgrade():
    pass

"""redirect user id

Revision ID: bc318baeaab7
Revises: 6a5921309ab2
Create Date: 2023-04-04 10:56:13.814636+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bc318baeaab7"
down_revision = "6a5921309ab2"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("users", sa.Column("redirect_user_id", sa.Integer(), nullable=True))
    op.create_foreign_key("users_redirect_user_id_fkey", "users", "users", ["redirect_user_id"], ["id"])


def downgrade():
    op.drop_constraint("users_redirect_user_id_fkey", "users", type_="foreignkey")
    op.drop_column("users", "redirect_user_id")

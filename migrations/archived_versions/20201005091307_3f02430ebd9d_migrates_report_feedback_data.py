"""migrates report_feedback data

Revision ID: 3f02430ebd9d
Revises: 3c5175405eac
Create Date: 2020-10-05 09:13:07.487678+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3f02430ebd9d"
down_revision = "3c5175405eac"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        update report_feedback set submission_id=submissions_reports.submission_id FROM submissions_reports 
        JOIN reports on submissions_reports.report_id = reports.report_v2_id 
        WHERE report_feedback.report_id = reports.id
        """)


def downgrade():
    pass

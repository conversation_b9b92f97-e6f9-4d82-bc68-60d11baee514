"""Adds subscription table

Revision ID: 460d6c31ebaa
Revises: 21cdf0fbb61d
Create Date: 2020-09-06 22:59:32.454578+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "460d6c31ebaa"
down_revision = "21cdf0fbb61d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "subscription",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["report_id"], ["reports_v2.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_subscription_report_id"), "subscription", ["report_id"], unique=False)
    op.create_index(op.f("ix_subscription_user_id"), "subscription", ["user_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_subscription_user_id"), table_name="subscription")
    op.drop_index(op.f("ix_subscription_report_id"), table_name="subscription")
    op.drop_table("subscription")

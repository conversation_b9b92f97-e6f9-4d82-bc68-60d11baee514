"""Add copilot_component_paths

Revision ID: fcac4bcdce3a
Revises: eca7d5d56aab
Create Date: 2021-03-02 13:18:33.876472-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fcac4bcdce3a"
down_revision = "eca7d5d56aab"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "notebook_thread", sa.Column("copilot_component_paths", postgresql.ARRAY(sa.String(length=256)), nullable=True)
    )


def downgrade():
    op.drop_column("notebook_thread", "copilot_component_paths")

"""Add IFTA card

Revision ID: c0d768db02a5
Revises: a130292ec75e
Create Date: 2023-03-30 19:46:36.157033+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c0d768db02a5"
down_revision = "a130292ec75e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
insert into mode_cards (id, created_at, updated_at, column_id, title, position, type, card_id, props, tooltip)
values ('e1a18c5b-69e0-410f-9b1b-127e39415331', now(), null, '18507c45-920c-4f0d-944f-2b169df06ede', null, 15, 'IFTA_HEAT_MAP', 'fleet-operations-ifta-heat-map', null, null);
        """)


def downgrade():
    pass

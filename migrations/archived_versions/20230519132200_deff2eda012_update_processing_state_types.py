"""Add WAITING_FOR_BC

Revision ID: deff2eda012
Revises: ab0d45942ac5
Create Date: 2023-05-19 13:22:00.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

revision = "deff2eda012"
down_revision = "ab0d45942ac5"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(f"ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'BUSINESS_CONFIRMATION';")
        op.execute(f"ALTER TYPE fileprocessingstate ADD VALUE IF NOT EXISTS 'WAITING_FOR_BUSINESS_CONFIRMATION';")


def downgrade():
    pass

"""Clears invalid coverage values for the submission f7a964da-a25f-4e70-a994-97337a9eea1a

Revision ID: 4c35a0d43b34
Revises: 6e964804d177
Create Date: 2022-07-05 10:03:14.501168+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4c35a0d43b34"
down_revision = "6e964804d177"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            delete from submission_coverages
            where submission_coverages.submission_id = 'f7a964da-a25f-4e70-a994-97337a9eea1a'
            and submission_coverages.coverage_id = '6df35f6d-3a9e-40b5-a2c7-95b3096d2b33'
            and submission_coverages.created_at < '2022-06-30'
        """)


def downgrade():
    pass

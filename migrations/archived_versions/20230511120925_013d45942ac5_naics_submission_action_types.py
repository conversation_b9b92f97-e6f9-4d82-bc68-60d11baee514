"""NAICS submission action types

Revision ID: 013d45942ac5
Revises: 0e0162ce90ba
Create Date: 2023-05-11 12:09:25.257958+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "013d45942ac5"
down_revision = "0e0162ce90ba"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PRIMARY_NAICS_UPDATED';")
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'NAICS_VERIFIED';")


def downgrade():
    ...

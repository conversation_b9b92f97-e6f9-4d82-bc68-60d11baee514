"""Deletes non-SOV QBE files

Revision ID: 57253717eec8
Revises: 95a36a2641f3
Create Date: 2022-04-14 21:17:24.363930+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "57253717eec8"
down_revision = "95a36a2641f3"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    delete from files where id in (
        select f.id
        from files f
                 join submissions s on s.id = f.submission_id
                 join submissions_reports sr on s.id = sr.submission_id
                 join reports_v2 r on r.id = sr.report_id
                 join users u on r.owner_id = u.id
                 join organization o on u.organization_id = o.id
        where o.name = 'QBE'
          and f.file_type != 'SOV'
    );
    """)


def downgrade():
    pass

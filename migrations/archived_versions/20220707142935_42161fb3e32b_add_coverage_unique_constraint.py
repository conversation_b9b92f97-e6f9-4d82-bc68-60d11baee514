"""Adds an unique constraint on the submission coverage
42161fb3e32b_
Revision ID: 42161fb3e32b
Revises: 6ed1ae437e0e
Create Date: 2022-07-06 18:55:18.523792+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "42161fb3e32b"
down_revision = "6ed1ae437e0e"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("SET statement_timeout TO '600 s';")  # 10 min
    # Remove all the coverages that aren't quoted if there are some other quoted
    op.execute(f"""
        DELETE FROM submission_coverages sc1 USING submission_coverages sc2
        WHERE sc1.is_quoted = false AND sc2.is_quoted 
        AND sc1.submission_id  = sc2.submission_id AND sc1.coverage_id = sc2.coverage_id
    """)
    # Remove all of those that don't have the max value
    op.execute(f"""
        DELETE FROM submission_coverages sc1 USING submission_coverages sc2
        WHERE sc1.is_quoted = true AND sc1.quoted_premium < sc2.quoted_premium
        AND sc1.submission_id  = sc2.submission_id AND sc1.coverage_id = sc2.coverage_id
    """)
    op.execute(f"""
        DELETE FROM submission_coverages sc1 USING submission_coverages sc2
        WHERE sc1.estimated_premium < sc2.estimated_premium
        AND sc1.submission_id  = sc2.submission_id AND sc1.coverage_id = sc2.coverage_id
    """)
    # Clear all the rows that are not the latest one, but there might multiple with the same timestamp
    op.execute(f"""
        DELETE FROM submission_coverages sc1 USING submission_coverages sc2
        WHERE sc1.created_at < sc2.created_at
        AND sc1.submission_id  = sc2.submission_id AND sc1.coverage_id = sc2.coverage_id
    """)
    # Clear the same rows based on the lowest id
    op.execute(f"""
        DELETE FROM submission_coverages
        WHERE id IN (
            SELECT id FROM (
                SELECT
                    id,
                    ROW_NUMBER() OVER (PARTITION BY submission_id, coverage_id ORDER BY id ) AS row_num
                FROM submission_coverages
            ) t
            WHERE t.row_num > 1
        )
    """)

    op.create_unique_constraint(
        "uq_submission_coverages_coverage_id_submission_id", "submission_coverages", ["coverage_id", "submission_id"]
    )


def downgrade():
    op.drop_constraint("uq_submission_coverages_coverage_id_submission_id", "submission_coverages")

"""updates feedback business_id where applicable

Revision ID: d9cc7b198456
Revises: f08453a9616b
Create Date: 2020-10-08 09:47:57.570578+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d9cc7b198456"
down_revision = "f08453a9616b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""UPDATE feedback set business_id=object_id::uuid where object_type='business'""")


def downgrade():
    pass

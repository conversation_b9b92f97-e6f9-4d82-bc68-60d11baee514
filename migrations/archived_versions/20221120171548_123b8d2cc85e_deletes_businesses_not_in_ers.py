"""Deletes businesses that are not there in ERS from SubmissionBusinesses

Revision ID: 123b8d2cc85e
Revises: f3d4434e644b
Create Date: 2022-11-20 17:15:48.356031+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "123b8d2cc85e"
down_revision = "f3d4434e644b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # ENG-11791 - there are 2 businesses in CAPI that are not in ERS, we need to delete them from SubmissionBusinesses
    conn.execute(
        "DELETE FROM submission_businesses WHERE business_id IN"
        " ('aa8ee838-ca80-4875-964c-a4b2189a2dc2','a4133fd2-7e1f-4d50-b74c-1c828fc68b0f');"
    )


def downgrade():
    pass

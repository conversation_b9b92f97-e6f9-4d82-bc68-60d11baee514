"""adds is_muted field to subscriptions

Revision ID: eef36e6c6069
Revises: 460d6c31ebaa
Create Date: 2020-09-08 10:36:46.879694+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "eef36e6c6069"
down_revision = "2da825de5aee"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("subscription", sa.Column("is_muted", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("subscription", "is_muted")

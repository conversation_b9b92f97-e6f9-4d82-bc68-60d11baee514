"""reports v2 tables

Revision ID: 2fb0fa53cd54
Revises: c97f1acf2f32
Create Date: 2020-08-26 16:41:45.644026

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2fb0fa53cd54"
down_revision = "c97f1acf2f32"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("metric", sa.Column("report_v2_id", postgresql.UUID(), nullable=True))
    op.alter_column("metric", "report_id", existing_type=sa.INTEGER(), nullable=True)
    op.create_index(op.f("ix_metric_report_v2_id"), "metric", ["report_v2_id"], unique=False)
    op.create_foreign_key("reports_v2_metric_id_fkey", "metric", "reports_v2", ["report_v2_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("reports_v2_metric_id_fkey", "metric", type_="foreignkey")
    op.drop_index(op.f("ix_metric_report_v2_id"), table_name="metric")
    op.alter_column("metric", "report_id", existing_type=sa.INTEGER(), nullable=False)
    op.drop_column("metric", "report_v2_id")
    # ### end Alembic commands ###

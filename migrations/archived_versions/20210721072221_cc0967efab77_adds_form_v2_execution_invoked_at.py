"""Adds form_v2.execution_invoked_at

Revision ID: cc0967efab77
Revises: 9ae963dee9fc
Create Date: 2021-07-21 07:22:21.525368+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cc0967efab77"
down_revision = "9ae963dee9fc"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("form_v2", sa.Column("execution_started_at", sa.DateTime(), nullable=True))


def downgrade():
    op.drop_column("form_v2", "execution_started_at")

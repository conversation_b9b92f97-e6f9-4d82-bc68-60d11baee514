"""Migrate <NAME_EMAIL> (user id 1445)

Revision ID: 4787e11bc3fa
Revises: a45186063610
Create Date: 2023-02-23 01:01:42.662947+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "4787e11bc3fa"
down_revision = "a45186063610"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='2a90b391-147e-44d1-8e68-69a09e5002f0';
    update submissions set owner_id=1445 where id='be957f8d-f583-4f90-a2dc-35e5dee02d00';
    update report_permissions set grantee_user_id=1445 where report_id='2a90b391-147e-44d1-8e68-69a09e5002f0' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='be957f8d-f583-4f90-a2dc-35e5dee02d00' and user_id=3;

    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='5d1292ad-ce85-4c33-9850-b7548eacc509';
    update submissions set owner_id=1445 where id='8f7b7c69-d4a3-4d57-93ce-1b443790fc2f';
    update report_permissions set grantee_user_id=1445 where report_id='5d1292ad-ce85-4c33-9850-b7548eacc509' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='8f7b7c69-d4a3-4d57-93ce-1b443790fc2f' and user_id=3;

    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='fd08034f-62a5-4b8c-b18e-1d6d1761bdce';
    update submissions set owner_id=1445 where id='6b230128-bb1e-4528-9b5c-269fcfd9db4a';
    update report_permissions set grantee_user_id=1445 where report_id='fd08034f-62a5-4b8c-b18e-1d6d1761bdce' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='6b230128-bb1e-4528-9b5c-269fcfd9db4a' and user_id=3;

    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='f5246d27-d81a-4bc5-b494-8974ad9d3038';
    update submissions set owner_id=1445 where id='2a8a12d0-2289-468a-a028-ac733c0b9f4c';
    update report_permissions set grantee_user_id=1445 where report_id='f5246d27-d81a-4bc5-b494-8974ad9d3038' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='2a8a12d0-2289-468a-a028-ac733c0b9f4c' and user_id=3;

    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='d5c30dba-f20f-4e2f-8db2-a8341c882b63';
    update submissions set owner_id=1445 where id='d6fbd90b-10a8-4215-9e5c-f3950d40a21d';
    update report_permissions set grantee_user_id=1445 where report_id='d5c30dba-f20f-4e2f-8db2-a8341c882b63' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='d6fbd90b-10a8-4215-9e5c-f3950d40a21d' and user_id=3;

    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='455e1298-0c96-424a-92d9-d40d177f771e';
    update submissions set owner_id=1445 where id='658ff168-0e1b-4251-89d1-4ed32eead820';
    update report_permissions set grantee_user_id=1445 where report_id='455e1298-0c96-424a-92d9-d40d177f771e' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='658ff168-0e1b-4251-89d1-4ed32eead820' and user_id=3;

    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='af35fd5a-cb27-4422-8f16-c1df74fa490c';
    update submissions set owner_id=1445 where id='90a943ff-13be-4d14-bdbf-b485c054978a';
    update report_permissions set grantee_user_id=1445 where report_id='af35fd5a-cb27-4422-8f16-c1df74fa490c' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='90a943ff-13be-4d14-bdbf-b485c054978a' and user_id=3;

    update reports_v2 set report_bundle_id = null, owner_id=1445 where id='89096b3b-7330-4348-b33b-6ce34291c72e';
    update submissions set owner_id=1445 where id='a77339b3-e2c4-4e7b-9491-ad50333ac5e4';
    update report_permissions set grantee_user_id=1445 where report_id='89096b3b-7330-4348-b33b-6ce34291c72e' and permission_type='OWNER';
    update user_submission_stage set user_id=1445 where submission_id='a77339b3-e2c4-4e7b-9491-ad50333ac5e4' and user_id=3;
    """)


def downgrade():
    pass

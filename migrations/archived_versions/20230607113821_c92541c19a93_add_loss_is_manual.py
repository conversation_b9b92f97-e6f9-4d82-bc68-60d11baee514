"""Add loss.is_manual

Revision ID: c92541c19a93
Revises: 0c09949f98f1
Create Date: 2023-06-07 11:38:21.844260+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c92541c19a93"
down_revision = "0c09949f98f1"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("loss", sa.Column("is_manual", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("loss", "is_manual")

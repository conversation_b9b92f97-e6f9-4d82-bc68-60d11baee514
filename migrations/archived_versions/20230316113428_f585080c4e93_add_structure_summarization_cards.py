"""Add structure summarization cards

Revision ID: f585080c4e93
Revises: 7f77d535d52d
Create Date: 2023-03-16 11:34:28.478369+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f585080c4e93"
down_revision = "7f77d535d52d"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
-- ADD STRUCTURE INFO & SUMMARY CARDS
INSERT INTO mode_cards (column_id, id, position, card_id, type, title, props) VALUES
('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', 'f5e163c3-b2b3-4db5-8329-6377f0228e0d', 900, 'premises-structures-summary', 'STRUCTURES-SUMMARY', 'Structures summaries', null),
('0f5f7c8d-44bf-4abd-9fdb-67e87269cb35', '846ab1d2-29d0-49da-a079-b17f42eb67d6', 1000, 'premises-structures-info', 'STRUCTURES-INFO', 'Structure', null);
    """)


def downgrade():
    pass

"""Adding email body

Revision ID: 3c7161e2c801
Revises: 58001f14dab8
Create Date: 2023-03-14 16:15:56.177859+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3c7161e2c801"
down_revision = "58001f14dab8"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("emails", sa.Column("email_body", sa.String(), nullable=True))


def downgrade():
    op.drop_column("emails", "email_body")

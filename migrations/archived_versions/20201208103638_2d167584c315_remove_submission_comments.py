"""Remove submission comments

Revision ID: 2d167584c315
Revises: f354cdd42886
Create Date: 2020-12-08 10:36:38.956529+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2d167584c315"
down_revision = "f354cdd42886"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_comments_submission_id", table_name="comments")
    op.drop_index("ix_comments_user_id", table_name="comments")
    op.drop_table("comments")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "comments",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("submission_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column("body", sa.TEXT(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], name="comments_submission_id_fkey"),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], name="comments_user_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="comments_pkey"),
    )
    op.create_index("ix_comments_user_id", "comments", ["user_id"], unique=False)
    op.create_index("ix_comments_submission_id", "comments", ["submission_id"], unique=False)
    # ### end Alembic commands ###

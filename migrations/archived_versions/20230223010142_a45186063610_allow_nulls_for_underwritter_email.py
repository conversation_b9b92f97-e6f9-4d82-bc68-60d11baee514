"""Allow nulls for underwriter email

Revision ID: a45186063610
Revises: 3b5e5d1f9adf
Create Date: 2023-02-23 01:01:42.662947+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "a45186063610"
down_revision = "3b5e5d1f9adf"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
      alter table submission_sync alter column underwriter_email drop not null
    """)


def downgrade():
    pass

"""add report processing dependencies

Revision ID: 12ad23a3920d
Revises: 2615bdb44222
Create Date: 2023-04-27 11:05:25.662135+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "12ad23a3920d"
down_revision = "2615bdb44222"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "report_processing_dependency",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column("dependent_report_id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column("dependency_type", sa.Enum("SAME_ORG", "CROSS_ORG", name="report_dependency_type"), nullable=False),
        sa.ForeignKeyConstraint(["report_id"], ["reports_v2.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["dependent_report_id"], ["reports_v2.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )

    op.add_column("submissions", sa.Column("is_waiting_for_auto_verify", sa.Boolean(), nullable=True))
    op.add_column("submissions", sa.Column("verified_at", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submissions", "is_waiting_for_auto_verify")
    op.drop_column("submissions", "verified_at")
    op.drop_table("report_processing_dependency")
    # ### end Alembic commands ###

"""Add is_submitted_data

Revision ID: 42104ccc1c48
Revises: 11f41db9c025
Create Date: 2022-01-14 14:00:45.393925+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "42104ccc1c48"
down_revision = "5aef92b0c6e4"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric_preference", sa.Column("is_submitted_data", sa.<PERSON>(), nullable=True))
    op.create_index(
        op.f("ix_metric_preference_is_submitted_data"), "metric_preference", ["is_submitted_data"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_metric_preference_is_submitted_data"), table_name="metric_preference")
    op.drop_column("metric_preference", "is_submitted_data")

"""Make org_id not nullable

Revision ID: c8196393bf13
Revises: 2280381274ab
Create Date: 2023-05-12 08:49:40.276954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c8196393bf13"
down_revision = "2280381274ab"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    op.alter_column("reports_v2", "organization_id", existing_type=sa.INTEGER(), nullable=False)
    conn.execute("""
        CREATE OR REPLACE FUNCTION org_check_rp() R<PERSON>URNS trigger AS $org_check_rp$
            DECLARE
                org_id_for_report integer;
                org_id_for_user integer;
            BEGIN
                org_id_for_report := (select organization_id from reports_v2 r where r.id = NEW.report_id);
                org_id_for_user := (select organization_id from users where id = NEW.grantee_user_id);
                IF org_id_for_report != org_id_for_user THEN
                    RAISE EXCEPTION 'Organization id mismatch';
                END IF;
                RETURN NEW;
            END;
        $org_check_rp$ LANGUAGE plpgsql;
    """)


def downgrade():
    pass

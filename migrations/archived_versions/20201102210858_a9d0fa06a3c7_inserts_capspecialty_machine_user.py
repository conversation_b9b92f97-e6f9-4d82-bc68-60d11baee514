"""Inserts CapSpecialty machine user

Revision ID: a9d0fa06a3c7
Revises: 3b325d4cef01
Create Date: 2020-11-02 21:08:58.793838+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a9d0fa06a3c7"
down_revision = "3b325d4cef01"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT INTO users (id, email, pass_hash, external_id, organization_id, role, name, photo_s3_file_path) 
    VALUES (DEFAULT, '<EMAIL>', null, 'Pmzuk5yF8dcglm7suLYZB1tKT72V02vl', (select id from organization where name = 'CapSpecialty'), 'manager', 'CapSpecialty', null)
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    pass

"""Add is_archived flag to report

Revision ID: 43cfbc3ce16e
Revises: 350e426a9596
Create Date: 2021-04-23 14:42:45.374142-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "43cfbc3ce16e"
down_revision = "350e426a9596"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("reports_v2", sa.Column("is_archived", sa.<PERSON>(), nullable=True))
    op.create_index(op.f("ix_reports_v2_is_archived"), "reports_v2", ["is_archived"], unique=False)
    conn = op.get_bind()
    conn.execute("""UPDATE reports_v2 SET is_archived = FALSE WHERE is_archived IS NULL""")


def downgrade():
    op.drop_index(op.f("ix_reports_v2_is_archived"), table_name="reports_v2")
    op.drop_column("reports_v2", "is_archived")

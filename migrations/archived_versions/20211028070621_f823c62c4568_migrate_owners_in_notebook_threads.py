"""migrate owners in notebook threads

Revision ID: f823c62c4568
Revises: 4156b06f31a0
Create Date: 2021-10-28 07:06:21.736153+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f823c62c4568"
down_revision = "4156b06f31a0"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    update notebook_thread set dossier_component_paths='{facts[?(@.parent_type==''PREMISES''&@.fact_subtype?.id==''OWNER'')]}' where dossier_component_paths='{properties.0.owners.0.name}';
    """)


def downgrade():
    pass

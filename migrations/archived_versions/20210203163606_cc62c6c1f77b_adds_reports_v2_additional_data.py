"""Adds reports_v2.additional_data

Revision ID: cc62c6c1f77b
Revises: 87d0ee9bcf87
Create Date: 2021-02-03 16:36:06.058736+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "cc62c6c1f77b"
down_revision = "87d0ee9bcf87"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("reports_v2", sa.Column("additional_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade():
    op.drop_column("reports_v2", "additional_data")

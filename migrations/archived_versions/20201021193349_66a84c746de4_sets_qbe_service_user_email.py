"""Sets QBE service user email

Revision ID: 66a84c746de4
Revises: 0f9560128ed4
Create Date: 2020-10-21 19:33:49.662655+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "66a84c746de4"
down_revision = "0f9560128ed4"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("update users set email = '<EMAIL>' where name = 'QBE'")


def downgrade():
    pass

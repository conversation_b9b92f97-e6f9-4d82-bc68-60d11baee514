"""add label to GradeSummary

Revision ID: 99f35068e0ea
Revises: 59fddc4817e9
Create Date: 2020-09-03 11:46:28.996067

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "99f35068e0ea"
down_revision = "e302aef210e5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("grade_summary", sa.Column("label", sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("grade_summary", "label")
    # ### end Alembic commands ###

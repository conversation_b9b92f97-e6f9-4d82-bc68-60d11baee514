"""empty message

Revision ID: 7465489760ee
Revises: d346c97caa43
Create Date: 2020-10-03 20:11:29.376206+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
from sqlalchemy.schema import CreateSequence, DropSequence, Sequence
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7465489760ee"
down_revision = "d346c97caa43"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(CreateSequence(Sequence("serial_id_seq")))
    op.add_column(
        "submission_businesses",
        sa.Column("serial_id", sa.Integer(), server_default=sa.text("nextval('serial_id_seq')"), nullable=False),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submission_businesses", "serial_id")
    op.execute(DropSequence(Sequence("serial_id_seq")))
    # ### end Alembic commands ###

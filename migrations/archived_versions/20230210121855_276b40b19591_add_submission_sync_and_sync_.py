"""Add submission_sync and sync_configuration

Revision ID: 276b40b19591
Revises: a249c78c55b7
Create Date: 2023-02-10 12:18:55.054072+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "276b40b19591"
down_revision = "a249c78c55b7"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "submission_sync",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("policy_number", sa.String(), nullable=False),
        sa.Column("submission_name", sa.String(), nullable=False),
        sa.Column("underwriter_email", sa.String(), nullable=False),
        sa.Column("stage", postgresql.ENUM(name="submissionstage", create_type=False), nullable=False),
        sa.Column("received_date", sa.DateTime(), nullable=True),
        sa.Column("effective_date", sa.DateTime(), nullable=True),
        sa.Column("coverage_name", sa.String(), nullable=True),
        sa.Column("coverage_type", sa.Enum("PRIMARY", "EXCESS", name="coveragetype", native_enum=False), nullable=True),
        sa.Column("quoted_premium", sa.Float(), nullable=True),
        sa.Column("bound_premium", sa.Float(), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.Column("applied_changes", postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'[]'::jsonb")),
        sa.Column("pending_changes", postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'[]'::jsonb")),
        sa.Column("applied", sa.Boolean(), nullable=False, server_default=sa.text("false")),
        sa.Column("attempt", sa.Integer(), nullable=False, server_default=sa.text("0")),
        sa.Column("declined_date", sa.DateTime(), nullable=True),
        sa.Column("source", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["organization_id"], ["organization.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_sync_policy_number_organization_id"),
        "submission_sync",
        ["policy_number", "organization_id"],
        unique=True,
    )

    op.create_table(
        "sync_configuration",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.Column("configuration", postgresql.JSONB(astext_type=sa.Text()), server_default=sa.text("'{}'::jsonb")),
        sa.ForeignKeyConstraint(["organization_id"], ["organization.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )

    nw_config = (
        '{"max_attempts": 0, "submission_matcher_config": {"shell_owner_email": "<EMAIL>",'
        ' "duplicate_submissions": true, "create_shell_submissions": true, "shell_submissions_days_passed": 7,'
        ' "fuzzy_matching_level": 3, "original_max_days_old": 6, "original_min_days_old": 2, "name_match_max_days_old":'
        ' 3}, "handlers": [{"handler_type": "POLICY_HANDLER", "config": {}}, {"handler_type": "SUBMISSION_HANDLER",'
        ' "config": {}}, {"handler_type": "COVERAGE_HANDLER", "config": {"remove_non_matching_coverages": true}},'
        ' {"handler_type": "UNDERWRITER_HANDLER", "config": {"remove_previous_assignees": true,'
        ' "default_primary_naics": {"<EMAIL>": "NAICS_230000", "<EMAIL>": "NAICS_230000",'
        ' "<EMAIL>": "NAICS_230000", "<EMAIL>": "NAICS_230000",'
        ' "<EMAIL>": "NAICS_230000", "<EMAIL>": "NAICS_230000",'
        ' "<EMAIL>": "NAICS_230000", "<EMAIL>": "NAICS_230000",'
        ' "<EMAIL>": "NAICS_230000", "<EMAIL>": "NAICS_230000",'
        ' "<EMAIL>": "NAICS_230000", "<EMAIL>": "NAICS_230000",'
        ' "<EMAIL>": "NAICS_230000"}}}]}'
    )
    arch_config = (
        '{"max_attempts": 0, "submission_matcher_config": {"shell_owner_email": "<EMAIL>",'
        ' "duplicate_submissions": true, "create_shell_submissions": false, "shell_submissions_days_passed": 7,'
        ' "fuzzy_matching_level": 1, "original_max_days_old": 6, "original_min_days_old": 2, "name_match_max_days_old":'
        ' 3}, "handlers": [{"handler_type": "POLICY_HANDLER", "config": {}}, {"handler_type": "SUBMISSION_HANDLER",'
        ' "config": {}}, {"handler_type": "COVERAGE_HANDLER", "config": {"remove_non_matching_coverages": true}},'
        ' {"handler_type": "UNDERWRITER_HANDLER", "config": {"remove_previous_assignees": true,'
        ' "default_primary_naics": {}}}]}'
    )
    op.execute(f"""
    insert into sync_configuration (id, updated_at, organization_id, configuration) values (uuid_generate_v4(), null, 6, '{nw_config}');
    insert into sync_configuration (id, updated_at, organization_id, configuration) values (uuid_generate_v4(), null, 10, '{arch_config}');
    """)


def downgrade():
    op.drop_index(op.f("ix_submission_sync_policy_number_organization_id"), table_name="submission_sync")
    op.drop_table("submission_sync")
    op.drop_table("sync_configuration")

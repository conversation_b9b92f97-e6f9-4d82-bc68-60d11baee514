"""emails normalized subject fix

Revision ID: ca118baeaab7
Revises: 8b12ef6b90e1
Create Date: 2023-04-13 10:56:13.814636+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ca118baeaab7"
down_revision = "8b12ef6b90e1"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""
            UPDATE emails 
            SET normalized_subject = upper(normalized_subject)
            WHERE created_at > '2023-04-12';            
            """)


def downgrade():
    pass

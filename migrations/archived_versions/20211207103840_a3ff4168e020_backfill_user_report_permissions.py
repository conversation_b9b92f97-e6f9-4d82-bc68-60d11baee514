"""Backfill user report permissions

Revision ID: a3ff4168e020
Revises: 99ef7ecb91ab
Create Date: 2021-12-07 10:38:40.472801+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a3ff4168e020"
down_revision = "99ef7ecb91ab"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # Set default filter template for all QBE and Nationwide users to include the “owned by me” filter (only for those that havent yet).
    conn.execute("""
        insert into report_permissions (id, created_at, grantee_user_id, report_id, permission_type, message, is_org_permission)
            select
                uuid_generate_v4(),
                now(),
                u.id,
                r.id,
                r.organization_permission_level,
                null,
                true
            from (select * from reports_v2 where owner_id = (select id from users where email = '<EMAIL>') and organization_permission_level is not null) r,
                 (select * from users where organization_id = 5) u
            where u.id not in (select grantee_user_id from report_permissions where report_id = r.id);
    """)
    conn.execute("""
        insert into user_submission_stage(id, submission_id, user_id, stage)
            select uuid_generate_v4(), p.submission_id, p.grantee_user_id, 'JUST_IN'
            from (select submission_id, grantee_user_id from report_permissions rp join submissions_reports sr on rp.report_id = sr.report_id where
                grantee_user_id in (select id from users where organization_id = 5) and is_org_permission is true
            ) p ON CONFLICT DO NOTHING;
    """)
    conn.execute("""
        insert into report_permissions (id, created_at, grantee_user_id, report_id, permission_type, message, is_org_permission)
            select
                uuid_generate_v4(),
                now(),
                u.id,
                r.id,
                r.organization_permission_level,
                null,
                true
            from (select * from reports_v2 where owner_id = (select id from users where email = '<EMAIL>') and organization_permission_level is not null) r,
                 (select * from users where organization_id = 6) u
            where u.id not in (select grantee_user_id from report_permissions where report_id = r.id);
    """)
    conn.execute("""
        insert into user_submission_stage(id, submission_id, user_id, stage)
            select uuid_generate_v4(), p.submission_id, p.grantee_user_id, 'JUST_IN'
            from (select submission_id, grantee_user_id from report_permissions rp join submissions_reports sr on rp.report_id = sr.report_id where
                grantee_user_id in (select id from users where organization_id = 6) and is_org_permission is true
            ) p ON CONFLICT DO NOTHING;

    """)


def downgrade():
    pass

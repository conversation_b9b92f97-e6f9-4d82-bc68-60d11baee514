"""Switches occupancy to structures.building_type

Revision ID: e36b7f9ddd4e
Revises: fc3b23b61704
Create Date: 2021-08-24 16:57:10.073556+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e36b7f9ddd4e"
down_revision = "fc3b23b61704"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
        UPDATE form_field_source SET path = 'properties[0].structures[0].building_type' 
        where path = 'properties[0].structures[0].category'
        """)


def downgrade():
    pass

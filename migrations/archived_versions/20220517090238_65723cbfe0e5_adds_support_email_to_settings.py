"""adds support email to settings

Revision ID: 65723cbfe0e5
Revises: 71f154ed99dd
Create Date: 2022-05-17 09:02:38.492641+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "65723cbfe0e5"
down_revision = "71f154ed99dd"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("settings", sa.Column("support_email", sa.VARCHAR(length=200), nullable=True))
    with op.get_context().autocommit_block():
        # nationwide
        op.execute("update settings set support_email='<EMAIL>' where organization_id=6")


def downgrade():
    op.drop_column("settings", "support_email")

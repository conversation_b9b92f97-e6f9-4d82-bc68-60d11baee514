"""Adding initial_classifiied_file_type

Revision ID: g8c56h39rpg1
Revises: n9h43c98dgd2
Create Date: 2023-05-26 01:55:25.508526+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "g8c56h39rpg1"
down_revision = "n9h43c98dgd2"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("files", sa.Column("initial_classification_confidence", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("files", "initial_classification_confidence")

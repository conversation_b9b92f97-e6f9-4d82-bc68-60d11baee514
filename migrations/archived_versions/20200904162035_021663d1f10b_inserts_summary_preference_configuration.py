"""Inserts summary preference configuration

Revision ID: 021663d1f10b
Revises: 8dd74b798bc6
Create Date: 2020-09-04 16:20:35.910644+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "021663d1f10b"
down_revision = "8dd74b798bc6"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    INSERT
    INTO
    summary_preference(id, display_name, group_display_name, is_default)
    VALUES('2d6f20b7-f2cf-4246-b684-113cd7ca1ffe', 'Websites', 'General', true)
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    conn = op.get_bind()
    conn.execute("DELETE FROM summary_preference WHERE id='2d6f20b7-f2cf-4246-b684-113cd7ca1ffe';")

"""update mode cards for fleet

Revision ID: e7cfb2703188
Revises: 29da6e82e98e
Create Date: 2023-02-16 16:34:01.853024+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e7cfb2703188"
down_revision = "29da6e82e98e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards set title='Summary', type='VEHICLES_IN_SUBMISSION_SUMMARY' where card_id='vehicles-in-submission-summary';
        update mode_cards set title='', type='VEHICLES_IN_SUBMISSION_TABLE' where card_id='vehicles-table-Submission';
        update mode_cards set title='Summary', type='VEHICLES_IN_FMCSA_SUMMARY' where card_id='vehicles-in-fmcsa-summary';
        update mode_cards set title='', type='VEHICLES_IN_FMCSA_TABLE' where card_id='vehicles-table-fmcsa';
        delete from mode_cards where column_id='924f83bd-4615-459f-baf1-59296ad3a6ff';
        delete from mode_cards where column_id='28721610-265c-4851-b485-6e2cc17f23f9';
        delete from mode_columns where row_id='56d51d1e-b059-4ac0-8a3a-75c21e0fc89b';
        delete from mode_cards where column_id='e23ca5fa-54a9-47c6-8af4-0997177b691e';
        delete from mode_cards where column_id='6637d362-674b-4612-b057-fc823c8155f3';
        delete from mode_columns where row_id='b228174a-b530-4b42-9360-e4739249d757';
        delete from mode_rows where id='56d51d1e-b059-4ac0-8a3a-75c21e0fc89b';
        delete from mode_rows where id='b228174a-b530-4b42-9360-e4739249d757';
        update mode_rows set position=90 where id='f25a17e4-7e6e-4cb7-8985-f9bda201cff7';
        update mode_cards set type='VEHICLES_IN_SUBMISSION_TABLE' where card_id='vehicles-table-Submission';
        update mode_rows set position=100 where id='5004d050-2c63-440a-af4a-46d0317406ea';
        delete from mode_cards where column_id='faa65273-5387-408d-9ccf-996e6821ec0d';
        delete from mode_cards where column_id='cbd7bc1f-67f6-4422-a275-8bcf296e4406';
        delete from mode_columns where row_id='6bd47e2a-f295-4552-abf5-fb55e4d6ce9c';
        delete from mode_cards where column_id='c0b8ee42-b876-44d5-a8ce-85c88db45d65';
        delete from mode_cards where column_id='a5f1f24a-658b-4acf-b3dd-949879db44fb';
        delete from mode_columns where row_id='ca80f1dc-0d8f-4c11-be6b-cd5a66dc5bf3';
        delete from mode_rows where id='6bd47e2a-f295-4552-abf5-fb55e4d6ce9c';
        delete from mode_rows where id='ca80f1dc-0d8f-4c11-be6b-cd5a66dc5bf3';
        update mode_rows set position=110 where id='76001f00-c825-40eb-8b1f-a37654342ed5';
        update mode_cards set type='VEHICLES_IN_FMCSA_TABLE' where card_id='vehicles-table-fmcsa';
        update mode_rows set position=140 where id='9e284000-1ea8-44a6-845f-a8d6de129310';
        insert into mode_rows (id, created_at, mode_id, position, title) values ('8ef04fd2-9392-4b26-aa68-f6c87c6d63d1', current_timestamp, 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 120, 'Drivers in Submission');
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('41421401-6925-4bab-9775-00a92ab188cc', current_timestamp, '8ef04fd2-9392-4b26-aa68-f6c87c6d63d1', 0, 12, null);
        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('2da57e45-31ad-486f-b0ba-ceb8569ccbce', current_timestamp, '41421401-6925-4bab-9775-00a92ab188cc', 'Summary', 0, 'DRIVERS_IN_SUBMISSION_SUMMARY', 'drivers-in-submission-summary');
        insert into mode_rows (id, created_at, mode_id, position, title, is_collapsible, is_default_open) values ('447ac681-565d-4c36-ab95-ce3b945bbe3d', current_timestamp, 'a72774ed-b6c9-4845-a9dc-310fba8d4879', 130, null, false, true);
        insert into mode_columns (id, created_at, row_id, position, width, section_title) values ('bf28efa9-a396-4693-b7b1-84b836efd0fd', current_timestamp, '447ac681-565d-4c36-ab95-ce3b945bbe3d', 0, 12, null);
        insert into mode_cards (id, created_at, column_id, title, position, type, card_id) values ('9ea3afe8-1140-44fd-b8eb-efa8aefcf4cd', current_timestamp, 'bf28efa9-a396-4693-b7b1-84b836efd0fd', '', 0, 'DRIVERS_IN_SUBMISSION_TABLE', 'drivers-table-submission');
        update mode_cards set title='Loss run files' where card_id='loss-files-card';
    """)


def downgrade():
    pass

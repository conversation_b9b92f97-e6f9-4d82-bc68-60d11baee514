"""agency alias

Revision ID: ed5dcd346d18
Revises: 87a25edc9619
Create Date: 2023-06-12 17:47:16.033518+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ed5dcd346d18"
down_revision = "87a25edc9619"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("brokerages", sa.Column("aliases", postgresql.ARRAY(sa.String()), nullable=True))

    conn = op.get_bind()
    conn.execute(""" 
UPDATE brokerages SET aliases = ARRAY['& riding','brc ins','brown & riding insurance','brown and riding','maria luz carvajal'] WHERE name = 'Brown & Riding' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['& wilcox brokerage','& wilcox brokerage]<http://burnsandwilcoxbrokerage.com','brns & wilcox','burns & wilcox','burns & wilcox brokerage','burns & wilcox brokerage','burns & wilcox ltd'] WHERE name = 'burns & wilcox' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['a sattelite office of amwins los angeles','amw','amwins access insurance services, llc','amwins brokerage','amwins brokerage 31745','amwins brokerage insurance services','amwins brokerage llc','amwins brokerage of georgia, llc','amwins brokerage of new jersey','amwins brokerage of new jersey insurance services','amwins brokerage of new jersey, inc.','amwins brokerage of new york','amwins brokerage of new york, inc','amwins brokerage of pennsylvania','amwins brokerage of texas, inc.','amwins brokerage of the midwest','amwins brokerage of the midwest, llc','amwins group','amwins group & brokerage','amwins group, inc','amwins group, inc.','amwins insura','amwins insurance brokerage','amwins insurance brokerage llc','amwins insurance brokerage of california, llc','amwins insurance brokerage, llc','amwins insurance brokerage, llc amwins brokerage insurance services','amwins insurance brokerage, llc brokerage insurance services','amwins insurance brokers, llc','amwins of tennessee insurance services','avp amwins insurance brokerage, llc','property & casualty'] WHERE name = 'AMWINS' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['align general insurance'] WHERE name = 'Align General Insurance Agency LLC' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['alliant americas','lodi-alliant insurance services','newport beach-alliant insurance services','san diego- alliant insurance services','thousand oaks - alliant insurance svcs'] WHERE name = 'alliant insurance services' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['aon risk services inc of florida','aon risk services northeast, inc.'] WHERE name = 'aon risk services inc' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['arlington roe & company','arlington/roe'] WHERE name = 'arlington roe' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['arthur j gallagher & co'] WHERE name = 'gallagher' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['blake bartnick crc group','chase muder crc group','crc','crc atlanta','crc casualty','crc – atlanta','crc | casualty','crc atlanta','crc atlanta casualty','crc binding','crc charlotte','crc dallas (walnut hill','crc group—long island','crc group?long island','crc ins svcs','crc inside broke','crc insurance services','crc insurance services inc','crc insurance services, inc','crc insurance svcs','crc manhattan','crc services','crc transportation','crcgroup.com'] WHERE name = 'CRC GROUP' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['breckenridge'] WHERE name = 'Breckenridge Insurance Services, LLC' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['casualty risk placement services rps-chicago casualty','risk placement services','risk placement services rps atlanta alpharetta','risk placement services rps socal oc','risk placement services rps uniondale','risk placement services rps-atlanta','risk placement services rps-chicago casualty','risk placement services, inc','risk placement services, inc.','risk placement services, inc. boston','risk placement services, inc. rps atlanta','risk placement services, inc. | casualty','rp ins','rps cms','rps cowles & connell','rps excel','rps socal oc','rps uniondale','rps-denver','southern california risk placement services','southern risk placement services'] WHERE name = 'rps' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['casualty rt specialty, llc','casualty| rt-specialty, llc','casualty|rt specialty insurance services, llc','denver office rt specialty','harry rubenstein','r t specialty irvine','r-t specialty insurance services, llc','r-t specialty, llc','r-t specialty, llc casualty','r-t specialty, llc- new york','rt kansas city','rt spe','rt specialty casualty','rt specialty casualty and workers comp','rt specialty chicago','rt specialty dallas','rt specialty one premier plaza','rt specialty phoenix','rt specialty - buffalo','rt specialty - dallas','rt specialty - new york','rt specialty - phoenix','rt specialty – casualty','rt specialty denver','rt specialty insurance services, llc','rt specialty llc','rt specialty town pavilion','rt specialty- phoenix','rt specialty-casualty','rt specialty-casualty division','rt specialty, llc','rt specialty, llc ramundt casualty team','rt specialty|casualty','rt-specialty, llc','ryan specialty','team eckinger rt specialty','todd ballot and harry rubenstein','will huntington'] WHERE name = 'RT SPECIALTY' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['cbiz borden periman'] WHERE name = 'cbiz insurance services inc' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['colonial general','colonial general insurance agency'] WHERE name = 'Colonial General Insurance Agency, Inc' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['commercial insurance of texas'] WHERE name = 'Commercial Insurance' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['crouse & associates'] WHERE name = 'Crouse and Associates' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['hays companies','hays insurance services'] WHERE name = 'Hays Insurance Companies' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['hull northeast'] WHERE name = 'hull & company' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['ima, inc - wichita'] WHERE name = 'ima corp' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['insurance center agency - wooster'] WHERE name = 'insurance center inc' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['jen cap','jencap group','jencap specialty insurance','jencap specialty insurance services'] WHERE name = 'jencap' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['k & k insurance','k and k insurance','k and k insurance group','k&k insurance group inc','k&k insurance group, inc','k&k insurance group, inc.'] WHERE name = 'k&k insurance group' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['lat sestha rt specialty'] WHERE name = 'RT Specialty' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['liberty insurance services inc - chicago'] WHERE name = 'liberty mutual' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['maggie baker','margaret sharon','maryann stone'] WHERE name = 'CRC Group' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['marsh','marsh specialty finpro placement'] WHERE name = 'Marsh USA, Inc.' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['marsh & mclennan agency llc company'] WHERE name = 'marsh and mclennan agency' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['mini co'] WHERE name = 'MiniCo Insurance Agency' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['nationwide e & s/specialty'] WHERE name = 'nationwide' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['nbs insurance agency, inc'] WHERE name = 'NBS Insurance Agency' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['paragon insurance holdings','paragon specialty property'] WHERE name = 'paragon insurance' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['peachtree special risk brokers','peachtree special risk brokers, llc'] WHERE name = 'Peachtree Special Risk' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['professional risk solutions, one80 intermediaries'] WHERE name = 'Professional Risk Solutions' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['rla','rla insurance intermediaries','rla insurance intermediaries, llc'] WHERE name = 'RLA Insurance' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['rsg specialty','rsg specialty insurance services, llc','rsg specialty, llc, in california rsg specialty insurance services, llc'] WHERE name = 'RSG Specialty, LLC' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['u.s. risk','u.s. risk - orange county','u.s. risk llc – orange county, ca','u.s. risk orange county','us risk','us risk inc','usrisk','usrisk brokers ca','usrisk brokers dallas'] WHERE name = 'u.s. risk llc' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['usi ins srvcs llc','usi ins svcs c/l salt lake city','usi ins svcs, cl vero beach','usi insurance services','usi insurance services llc','usi insurance svcs','usi insurance svcs llc','usi midsuth'] WHERE name = 'usi insurance services, llc' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['w. brown & associates ca','w. brown & associates irvine'] WHERE name = 'w. brown & associates' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['willis towers watson northeast inc'] WHERE name = 'willis towers watson insurance' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['wtis new york','wtis san francisco'] WHERE name = 'wtis' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['xpt specialty'] WHERE name = 'xpt' AND organization_id = 6;
UPDATE brokerages SET aliases = ARRAY['xs brokers insurance agency inc','xs specialty brokerage'] WHERE name = 'xs brokers' AND organization_id = 6;
    """)


def downgrade():
    op.drop_column("brokerages", "aliases")

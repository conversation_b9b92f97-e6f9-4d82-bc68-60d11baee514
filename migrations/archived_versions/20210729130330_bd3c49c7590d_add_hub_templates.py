"""Add hub templates

Revision ID: bd3c49c7590d
Revises: cc0967efab77
Create Date: 2021-07-29 13:03:30.350425+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bd3c49c7590d"
down_revision = "cc0967efab77"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "hub_templates",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("is_default", sa.<PERSON>(), nullable=False),
        sa.Column("template", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_hub_templates_user_id"), "hub_templates", ["user_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_hub_templates_user_id"), table_name="hub_templates")
    op.drop_table("hub_templates")

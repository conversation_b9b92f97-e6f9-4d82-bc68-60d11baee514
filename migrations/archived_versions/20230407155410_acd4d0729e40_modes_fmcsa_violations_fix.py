"""FMCSA violations

Revision ID: acd4d0729e40
Revises: 3ejn77723h22
Create Date: 2023-04-07 15:54:10.781364+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "acd4d0729e40"
down_revision = "3ejn77723h22"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # create the mode
    conn.execute("""
        INSERT INTO mode_cards(id, column_id, position, type, card_id, title) VALUES
                ('540d28ee-4d9a-47fe-9719-9857532b48f1', '1e11d76a-8576-4196-a70c-30cbf787aac9', 2, 'TOTAL_FMCSA_VIOLATIONS', 'total-fmcsa-violations', 'FMCSA Violations')
                ON CONFLICT DO NOTHING;
                
        update mode_cards set title = 'Serious FMCSA Violations',
        tooltip = 'May not add up to total violations because not all are classified as Serious'
        where id = '3f40861f-46f2-4cd7-8c70-bb4b44ca2e7b';
    """)


def downgrade():
    pass

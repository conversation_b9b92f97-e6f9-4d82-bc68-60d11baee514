"""adds policy_id to submissions table

Revision ID: 80ff83566332
Revises: df2c4802cdbe
Create Date: 2020-10-30 08:44:08.532500-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "80ff83566332"
down_revision = "df2c4802cdbe"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("policy_id", sa.String(), nullable=True))


def downgrade():
    op.drop_column("submissions", "policy_id")

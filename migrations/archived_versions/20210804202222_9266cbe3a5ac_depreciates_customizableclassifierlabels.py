"""Depreciates CustomizableClassifierLabels

Revision ID: 9266cbe3a5ac
Revises: 45c717df01db
Create Date: 2021-08-04 20:22:22.962731+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9266cbe3a5ac"
down_revision = "45c717df01db"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index(
        "ix_customizable_classifier_labels_customizable_classifier_id", table_name="customizable_classifier_labels"
    )
    op.drop_index("ix_test_run_classification_task_label_id", table_name="test_run_classification_task")
    op.drop_constraint("ix_test_run_classification_task_label_id", "test_run_classification_task", type_="foreignkey")
    op.drop_table("customizable_classifier_labels")
    op.add_column(
        "test_run_classification_task", sa.Column("business_id", postgresql.UUID(as_uuid=True), nullable=True)
    )
    op.add_column(
        "test_run_classification_task",
        sa.Column("ground_truth_class", sa.Enum("NO", "YES", name="binaryclassificationclasstype"), nullable=True),
    )
    op.drop_column("test_run_classification_task", "label_id")


def downgrade():
    op.add_column(
        "test_run_classification_task", sa.Column("label_id", postgresql.UUID(), autoincrement=False, nullable=False)
    )
    op.create_foreign_key(
        "ix_test_run_classification_task_label_id",
        "test_run_classification_task",
        "customizable_classifier_labels",
        ["label_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_index(
        "ix_test_run_classification_task_label_id", "test_run_classification_task", ["label_id"], unique=False
    )
    op.drop_column("test_run_classification_task", "ground_truth_class")
    op.drop_column("test_run_classification_task", "business_id")
    op.create_table(
        "customizable_classifier_labels",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("customizable_classifier_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column("business_name", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("business_address", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("business_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.Column(
            "ground_truth_class",
            postgresql.ENUM("NO", "YES", name="binaryclassificationclasstype"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("is_excluded", sa.BOOLEAN(), autoincrement=False, nullable=True),
        sa.Column("comment", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(
            ["customizable_classifier_id"],
            ["customizable_classifiers.id"],
            name="customizable_classifier_labels_customizable_classifier_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="customizable_classifier_labels_pkey"),
    )
    op.create_index(
        "ix_customizable_classifier_labels_customizable_classifier_id",
        "customizable_classifier_labels",
        ["customizable_classifier_id"],
        unique=False,
    )

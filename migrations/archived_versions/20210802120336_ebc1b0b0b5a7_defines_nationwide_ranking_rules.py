"""Defines nationwide ranking rules

Revision ID: ebc1b0b0b5a7
Revises: 5bc8a7339026
Create Date: 2021-08-02 12:03:36.971362+00:00

"""
from datetime import datetime
from uuid import uuid4
import json

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ebc1b0b0b5a7"
down_revision = "5bc8a7339026"
branch_labels = None
depends_on = None

RULE_TEMPLATE = {
    "conditions": {
        "all": [
            {
                "name": "dossiers",
                "operator": "include_one_or_more_with",
                "value": {"conditions": {"all": [{"name": None, "operator": "is_true", "value": True}]}, "actions": []},
            }
        ]
    },
    "actions": [
        {"name": "increment_expected_value", "params": {"by": None}},
        {"name": "append_recommendation", "params": {"action": None}},
    ],
}

NATIONWIDE_RULES_PARAMS = [
    ("has_active_contractor_license", 2.666666667),
    ("airport_work", -1.3),
    ("asbestos_removal", "Soft Decline"),
    ("average_size_of_jobs", 1.0),
    ("bearing_walls", -2.0),
    ("boiler_work", -0.7),
    ("booms", -1.0),
    ("bridge_work", -2.0),
    ("burglar_alarm", 3.0),
    ("carpenter", 3.7),
    ("commercial_work", 5.0),
    ("cranes", -0.3),
    ("damage_restoration", -0.7),
    ("demolition", -1.3),
    ("demolition_as_primary_service", -2.0),
    ("digging", 1.3),
    ("directional_boring", -0.3),
    ("drilling", -0.3),
    ("driving_exposure", -0.3),
    ("drone", 0.0),
    ("drywall", 4.3),
    ("drywall_as_primary_service", 4.3),
    ("electric", 4.3),
    ("elevators_escalators", -0.3),
    ("excavation", 2.3),
    ("exterior_insulation_finishing_systems", "Soft Decline"),
    ("exterior_plumbing", 2.7),
    ("exterior_framing", 2.0),
    ("exterior_framing_as_primary_service", 2.0),
    ("fire_alarm", 1.3),
    ("fireproofing", 0.0),
    ("forklifts", 0.3),
    ("gas_mains", 0.0),
    ("gas_oil_work", "Soft Decline"),
    ("hazardous_materials", "Soft Decline"),
    ("hoists", 0.3),
    ("industrial_work", 3.0),
    ("ladders", 0.3),
    ("lead_abatement", "Soft Decline"),
    ("marine_work", "Soft Decline"),
    ("mold_abatement", "Soft Decline"),
    ("painting", 5.0),
    ("piling", -1.0),
    ("radon_abatement", "Soft Decline"),
    ("rail", -2.0),
    ("remodeling_work", 4.3),
    ("renovation_restoration", 4.3),
    ("residential_work", 0.0),
    ("road_or_street_work", 3.0),
    ("roofing", 0.7),
    ("roofing_as_primary_service", 0.3),
    ("sanding", 1.7),
    ("scaffolding", 0.0),
    ("scissor_lifts", 0.0),
    ("sewer", 2.5),
    ("solar", 1.0),
    ("solid_state_plastic_organic_insulation", 0.7),
    ("spray_painting", 1.0),
    ("sprayable_foam_insulation", 0.7),
    ("subcontractor", 2.0),
    ("traffic_signal_work", -1.3),
    ("tunnel_work", -3.0),
    ("underground_cable_installation", 1.7),
    ("underground_work", -0.3),
    ("waxing_floors", -1.3),
    ("welding_work", 0.7),
    ("work_in_altitude", 0.0),
    ("wrecking", 0.3),
]

recommendation_rule_query = sa.text("""
    INSERT INTO recommendation_rule(id, created_at, description, definition, owner_organization_id, 
    is_active, is_immutable) VALUES (:idx, :created_at, :description, :definition, 6, FALSE, FALSE)
    ON CONFLICT DO NOTHING;
    """)


def upgrade():
    conn = op.get_bind()
    conn.execute(
        "INSERT INTO organization(id, name, renewal_creation_interval) VALUES (6, 'Nationwide', '270 days') "
        "ON CONFLICT DO NOTHING"
    )
    for row in NATIONWIDE_RULES_PARAMS:
        variable_id, increment_expected_value = row
        action = "REFER"
        if increment_expected_value == "Soft Decline":
            increment_expected_value = 0
            action = "DECLINE"
        definition = dict(RULE_TEMPLATE)
        definition["conditions"]["all"][0]["value"]["conditions"]["all"][0]["name"] = variable_id
        definition["actions"][0]["params"]["by"] = increment_expected_value
        definition["actions"][1]["params"]["action"] = action
        conn.execute(
            recommendation_rule_query,
            idx=uuid4(),
            created_at=datetime.utcnow(),
            description=f'Ranking rule: {variable_id.replace("_", " ").title()}',
            definition=json.dumps(definition),
        )


def downgrade():
    pass

"""Reset deleted submissions

Revision ID: a10e1ae2f64a
Revises: 64b8cb8d2089
Create Date: 2022-12-08 17:41:37.575602+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a10e1ae2f64a"
down_revision = "64b8cb8d2089"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("""update reports_v2 set is_deleted = false where reports_v2.id in (
select reports_v2.id from reports_v2
inner join users u on reports_v2.owner_id = u.id
inner join organization o on u.organization_id = o.id
inner join submissions_reports sr on reports_v2.id = sr.report_id
where o.id =10 and is_deleted = true and date_trunc('day', reports_v2.updated_at) = '2022-12-06'
)""")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

"""Added spreadsheets table

Revision ID: 30a98384b2d3
Revises: 5419ab8a73c6
Create Date: 2020-01-24 09:41:13.662390+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "30a98384b2d3"
down_revision = "5419ab8a73c6"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "spreadsheets",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "form_type", postgresql.ENUM("CAT_REQUEST_FORM", "QCP_PROP_SPECS_FORM", name="formtype"), nullable=False
        ),
        sa.Column("json_value", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_spreadsheets_submission_id"), "spreadsheets", ["submission_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_spreadsheets_submission_id"), table_name="spreadsheets")
    op.drop_table("spreadsheets")

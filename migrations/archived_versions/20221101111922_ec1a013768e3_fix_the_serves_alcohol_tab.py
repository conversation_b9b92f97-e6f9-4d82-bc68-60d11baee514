"""Fix the serves alcohol tab

Revision ID: ec1a013768e3
Revises: 5bda90046db7
Create Date: 2022-11-01 11:19:22.330383+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "ec1a013768e3"
down_revision = "5bda90046db7"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        SET statement_timeout TO '600 s';
        update metric_preference
        set metric_group_name='General Liability'
        where display_name='Serves Alcohol' and metric_group_name='Property';
    """)


def downgrade():
    pass

"""Adds metric config ID

Revision ID: 4b48b983bd58
Revises: 2debbbb0c71b
Create Date: 2020-12-16 19:02:05.110460+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4b48b983bd58"
down_revision = "2debbbb0c71b"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("metric", sa.Column("metric_config_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(op.f("ix_metric_metric_config_id"), "metric", ["metric_config_id"], unique=False)
    op.create_foreign_key(None, "metric", "metric_config", ["metric_config_id"], ["id"])
    op.create_index(op.f("ix_metric_config_report_id"), "metric_config", ["report_id"], unique=False)
    op.create_foreign_key(None, "metric_config", "reports_v2", ["report_id"], ["id"])


def downgrade():
    op.drop_constraint(None, "metric_config", type_="foreignkey")
    op.drop_index(op.f("ix_metric_config_report_id"), table_name="metric_config")
    op.drop_constraint(None, "metric", type_="foreignkey")
    op.drop_index(op.f("ix_metric_metric_config_id"), table_name="metric")
    op.drop_column("metric", "metric_config_id")

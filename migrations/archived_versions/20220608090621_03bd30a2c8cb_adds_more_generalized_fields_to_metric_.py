"""Adds more generalized fields to metric data models

Revision ID: 03bd30a2c8cb
Revises: e8290e69ca98
Create Date: 2022-06-08 09:06:21.501168+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "03bd30a2c8cb"
down_revision = "e8290e69ca98"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "category_summary_parent",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("category_summary_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(["category_summary_id"], ["category_summary.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_category_summary_parent_category_summary_id"),
        "category_summary_parent",
        ["category_summary_id"],
        unique=False,
    )
    op.add_column(
        "date_range_summary", sa.Column("children_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True)
    )
    op.add_column("grade", sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.alter_column("grade", "business_id", existing_type=postgresql.UUID(), nullable=True)
    op.add_column("mean", sa.Column("children_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True))
    op.add_column("range", sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.alter_column("range", "business_id", existing_type=postgresql.UUID(), nullable=True)
    op.add_column("sum", sa.Column("children_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True))


def downgrade():
    op.drop_column("sum", "parent_ids")
    op.alter_column("range", "business_id", existing_type=postgresql.UUID(), nullable=False)
    op.drop_column("range", "parent_id")
    op.drop_column("mean", "parent_ids")
    op.alter_column("grade", "business_id", existing_type=postgresql.UUID(), nullable=False)
    op.drop_column("grade", "parent_id")
    op.drop_column("date_range_summary", "children_ids")
    op.drop_index(op.f("ix_category_summary_parent_category_summary_id"), table_name="category_summary_parent")
    op.drop_table("category_summary_parent")

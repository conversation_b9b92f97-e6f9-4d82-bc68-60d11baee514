"""Migrates recommendation to new table

Revision ID: f64a2a800c33
Revises: fe5ff064878a
Create Date: 2021-04-01 07:04:04.663310-04:00

"""
import datetime

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f64a2a800c33"
down_revision = "fe5ff064878a"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    results = conn.execute(f"""select submissions.id, created_at, expected_value, recommendation_action
            from submissions where recommendation_action is not NULL and submissions.id not in 
            (select distinct recommendations.submission_id from recommendations)""").fetchall()
    for row in results:
        submission_id = row[0]
        created_at = row[1]
        expected_value = row[2] if row[2] else "null"
        action = row[3]
        conn.execute(f"""INSERT INTO recommendations(id, created_at, expected_value, action, submission_id)
                      values ('{submission_id}', '{created_at}', {expected_value}, '{action}', '{submission_id}')""")
        conn.execute(f"""UPDATE recommendation_explanation SET recommendation_id = '{submission_id}' 
        where submission_id='{submission_id}'""")


def downgrade():
    pass

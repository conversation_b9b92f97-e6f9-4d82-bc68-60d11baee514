"""new groups

Revision ID: 1bab671b2b76
Revises: 53712d1b3993
Create Date: 2023-04-09 08:10:07.602227+00:00

"""
import json

from alembic import op
from sqlalchemy.dialects import postgresql
from static_common.enums.group import GroupID
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1bab671b2b76"
down_revision = "53712d1b3993"
branch_labels = None
depends_on = None

GROUPS = [
    {
        GroupID.LOCATION_RISKS: [
            "CATASTROPHIC_FLOOD_RISK",
            "COASTAL_STORM_SURGE_RISK",
            "FLOOD_RISK",
            "EARTHQUAKE_RISK",
            "HAIL_RISK",
            "LIGHTNING_RISK",
            "SINKHOLE_RISK",
            "SNOW_LOAD_RISK",
            "TORNADO_RISK",
            "WILDFIRE_RISK",
            "PROSTITUTION_RISK",
        ]
    },
    {
        GroupID.CRIME_CARD: [
            "ASSAULT_GRADE",
            "<PERSON><PERSON><PERSON>Y_GRADE",
            "<PERSON>URDER_GRADE",
            "RO<PERSON>ERY_GRADE",
            "BUR<PERSON><PERSON>RY_GRADE",
            "VEHICLE_THEFT_GRADE",
            "RAPE_GRADE",
            "DRUG_ALCOHOL_RELATED_DEATHS_GRADE",
        ]
    },
    {
        GroupID.FLEET_OPERATIONS: [
            "CARGO_CARRIED",
            "FLEET_OPERATION_STATES",
            "CARRIER_OPERATION_STATUS",
            "CARRIER_OPERATION",
            "OPERATION_CLASSIFICATION",
            "TRANSPORTATION_SAFETY_RATING",
            "TRANSPORTATION_REPORTED_OPERATIONS_AND_PERMITS",
            "NUMBER_OF_VEHICLES",
            "NUMBER_OF_REGISTERED_DRIVERS",
            "TRANSPORTATION_RISK_OF_TRANSPORT_WITHOUT_PERMISSION",
            "TRANSPORTATION_HAS_CONSUMER_COMPLAINTS",
            "IS_NEW_ENTRANT",
            "TRANSPORTATION_RISKY_NEW_ENTRANT",
        ]
    },
    {
        GroupID.DISTANCE_TO_HAZARDS: [
            "DISTANCE_TO_FIRE_STATION",
            "DISTANCE_TO_POLICE_STATION",
            "DISTANCE_TO_URGENT_CARE",
            "DISTANCE_TO_HOSPITAL",
            "DISTANCE_TO_FIRE_HYDRANT",
            "DISTANCE_TO_COAST",
            "DISTANCE_TO_TOXIC_FACILITY",
        ]
    },
    {
        GroupID.CATASTROPHIC_RISKS: [
            "CATASTROPHIC_FLOOD_RISK",
            "COASTAL_STORM_SURGE_RISK",
            "ELEVATION",
            "DISTANCE_TO_COAST",
            "FLOOD_RISK",
            "HAIL_RISK",
            "LIGHTNING_RISK",
            "DISTANCE_TO_TOXIC_FACILITY",
            "EARTHQUAKE_RISK",
            "WILDFIRE_RISK",
            "SNOW_LOAD_RISK",
            "FLOOD_ZONE",
            "SINKHOLE_RISK",
            "TORNADO_RISK",
        ]
    },
    {
        GroupID.BASIC_BENCHMARK: [
            "TRANSPORTATION_BASIC_BENCHMARK_UNSAFE_DRIVING",
            "TRANSPORTATION_BASIC_BENCHMARK_HOS_COMPLIANCE",
            "TRANSPORTATION_BASIC_BENCHMARK_VEHICLE_MAINTENANCE",
            "TRANSPORTATION_BASIC_BENCHMARK_DRUGS_ALCOHOL",
            "TRANSPORTATION_BASIC_BENCHMARK_DRIVER_FITNESS",
        ]
    },
]

update_query = """
        UPDATE mode_cards SET props = JSONB_SET(props, '{{facts}}',
                 ((props ->> 'facts')::jsonb - 'factSubtypeIds' || JSONB_BUILD_OBJECT('group', '{groupId}')))
        WHERE ((props ->> 'facts')::jsonb ->> 'factSubtypeIds')::jsonb @> '{factSubtypeIds}'::jsonb AND
            ((props ->> 'facts')::jsonb ->> 'factSubtypeIds')::jsonb <@ '{factSubtypeIds}'::jsonb
    """


def upgrade():
    conn = op.get_bind()
    for group, subtypes in enumerate(GROUPS):
        for group_id, subtype_ids in subtypes.items():
            query_params = {
                "groupId": group_id.value,
                "factSubtypeIds": json.dumps(subtype_ids),
            }
            conn.execute(statement=update_query.format(**query_params))


def downgrade():
    pass

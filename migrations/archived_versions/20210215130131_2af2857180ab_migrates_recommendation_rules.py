"""Migrates recommendation_rules

Revision ID: 2af2857180ab
Revises: 6e263933d2e5
Create Date: 2021-02-15 13:01:31.929183-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2af2857180ab"
down_revision = "6e263933d2e5"
branch_labels = None
depends_on = None

RULES_TO_MIGRATE = {
    "If wildfire values contains any of (D, E, F), refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "wildfire_values", "operator": "shares_at_least_one_element_with", "value": ["D", "E", "F"]}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If number of businesses in high hail risk greater than threshold of total number of businesses, refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "hail_values", "operator": "shares_at_least_one_element_with", "value": ["D", "F"]}]}, "actions": [{"name": "extract_value_from_dossier", "params": {"path": "location.hail"}}]}}]}, "actions": [{"name": "append_high_hail_risk_explanation", "params": {"path": "location.hail", "label": "Has High Hail Risk", "scalar": 0.01, "action": "REFER"}}]}""",
    "If industries contains any of (Hospitality, Restaurants & Nightlife, Real Estate) and crime on premises, refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "industries", "operator": "shares_at_least_one_element_with", "value": ["Hospitality", "Restaurants & Nightlife", "Real Estate"]}, {"name": "crime_in_business_insight", "operator": "contains", "value": "Yes"}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If industries contains Restaurants & Nightlife and open past 2AM, refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "industries", "operator": "shares_at_least_one_element_with", "value": ["Restaurants & Nightlife"]}, {"name": "open_past_2am", "operator": "is_true", "value": true}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If industries contains Restaurants & Nightlife and has security guards, refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "industries", "operator": "shares_at_least_one_element_with", "value": ["Restaurants & Nightlife"]}, {"name": "security_guards_insight", "operator": "contains", "value": "Yes"}]}}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If industries contains Real Estate and has low income unit count, refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "industries", "operator": "shares_at_least_one_element_with", "value": ["Real Estate"]}, {"name": "low_income_unit_count", "operator": "greater_than", "value": 0}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If industries contains Hospitality and has prostitution risk, refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "industries", "operator": "shares_at_least_one_element_with", "value": ["Hospitality"]}, {"name": "prostitution_risk", "operator": "shares_at_least_one_element_with", "value": ["Yes", "True"]}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If industries contains (Hospitality), refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "industries", "operator": "shares_at_least_one_element_with", "value": ["Hospitality"]}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If entertainment contains any of (Live Music, DJ), refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "live_entertainment", "operator": "shares_at_least_one_element_with", "value": ["Live Music", "DJ"]}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If business in high judicial risk jurisdiction, refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "judicial_risk_locations", "operator": "is_true", "value": true}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
    "If industry in (hotels, real estate, restaurant) and crime in (D, E, F), refer": """{"conditions": {"all": [{"name": "dossiers", "operator": "include_one_or_more_with", "value": {"conditions": {"all": [{"name": "industries", "operator": "shares_at_least_one_element_with", "value": ["Hospitality", "Restaurants & Nightlife", "Real Estate"]}, {"name": "crime_values", "operator": "shares_at_least_one_element_with", "value": ["D", "E", "F"]}]}, "actions": []}}]}, "actions": [{"name": "scale_expected_value", "params": {"scalar": 0.01}}, {"name": "append_recommendation", "params": {"action": "REFER"}}]}""",
}


def upgrade():
    conn = op.get_bind()
    for description in RULES_TO_MIGRATE:
        conn.execute(f"""UPDATE recommendation_rule SET definition = '{RULES_TO_MIGRATE[description]}'
                          where description = '{description}'""")


def downgrade():
    pass

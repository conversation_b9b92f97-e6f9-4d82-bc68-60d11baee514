"""adds decline email recipient address

Revision ID: 225fe264aeef
Revises: 518c821c5573
Create Date: 2023-01-20 20:44:08.972352+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "225fe264aeef"
down_revision = "518c821c5573"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("decline_email_recipient_address", sa.String(), nullable=True))


def downgrade():
    op.drop_column("submissions", "decline_email_recipient_address")

"""Add recommendation_v2_score to submission

Revision ID: 09a7055aef92
Revises: adb05a215a04
Create Date: 2023-04-16 13:07:11.067340+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "09a7055aef92"
down_revision = "adb05a215a04"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("recommendation_v2_score", sa.Integer(), nullable=True))


def downgrade():
    op.drop_column("submissions", "recommendation_v2_score")

"""Add QBE EDITOR permissions

Revision ID: e4e33ff992f1
Revises: c1bbbf8d2908
Create Date: 2021-09-01 13:53:00.373257+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e4e33ff992f1"
down_revision = "c1bbbf8d2908"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    # For each (qbe user, report) pair, create EDITOR permission unless there's existing permission for that pair.
    conn.execute("""
        insert into report_permissions (id, created_at, grantee_user_id, report_id, permission_type, message)
            select
                uuid_generate_v4(),
                now(),
                u.id,
                r.id,
                'EDITOR',
                null
            from (select * from reports_v2 where owner_id = (select id from users where email = '<EMAIL>')) r,
                 (select * from users where organization_id = 5) u
            where u.id not in (select grantee_user_id from report_permissions where report_id = r.id);
    """)


def downgrade():
    pass

"""Adds renewal_creation_date to submission

Revision ID: 2375a20bef5f
Revises: 856eac8aa4b5
Create Date: 2021-04-25 11:12:08.951041-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2375a20bef5f"
down_revision = "308577613568"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("renewal_creation_date", sa.DateTime(), nullable=True))
    op.create_index(
        op.f("ix_submissions_renewal_creation_date"), "submissions", ["renewal_creation_date"], unique=False
    )


def downgrade():
    op.drop_index(op.f("ix_submissions_renewal_creation_date"), table_name="submissions")
    op.drop_column("submissions", "renewal_creation_date")

"""LOAD_ADDITIONAL_FIRST_PARTY_DATA

Revision ID: 8c1adfc73006
Revises: 6b987796990e
Create Date: 2022-06-02 09:47:57.010409+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8c1adfc73006"
down_revision = "6b987796990e"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute(
            """ALTER TYPE copilotworkerexecutiontype ADD VALUE IF NOT EXISTS 'LOAD_ADDITIONAL_FIRST_PARTY_DATA';"""
        )
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'DRIVERS';""")
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'VEHICLES';""")


def downgrade():
    pass

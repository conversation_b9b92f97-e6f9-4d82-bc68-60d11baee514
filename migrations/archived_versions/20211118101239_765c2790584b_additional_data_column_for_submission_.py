"""additional data column for submission history

Revision ID: 765c2790584b
Revises: 1baf5f08c6f2
Create Date: 2021-11-18 10:12:39.965786+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "765c2790584b"
down_revision = "1baf5f08c6f2"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "submission_history", sa.Column("additional_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True)
    )


def downgrade():
    op.drop_column("submission_history", "additional_data")

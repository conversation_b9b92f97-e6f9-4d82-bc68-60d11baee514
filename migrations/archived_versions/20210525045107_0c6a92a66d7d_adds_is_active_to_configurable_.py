"""Adds is_active to configurable classifier

Revision ID: 0c6a92a66d7d
Revises: 2dbfb7ee5383
Create Date: 2021-05-25 04:51:07.993851-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0c6a92a66d7d"
down_revision = "2dbfb7ee5383"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("customizable_classifiers", sa.Column("is_active", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("customizable_classifiers", "is_active")

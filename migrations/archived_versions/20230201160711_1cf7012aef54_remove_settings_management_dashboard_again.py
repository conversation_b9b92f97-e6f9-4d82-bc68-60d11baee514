"""remove settings for Management Dashboard again

Revision ID: 1cf7012aef54
Revises: 2eb7055aef21
Create Date: 2023-02-01 16:07:11.067340+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1cf7012aef54"
down_revision = "2eb7055aef21"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("settings", "is_management_dashboard_enabled")
    op.drop_column("settings", "is_underwriter_dashboard_enabled")


def downgrade():
    op.add_column("settings", sa.Column("is_management_dashboard_enabled", sa.<PERSON>(), nullable=True))
    op.add_column("settings", sa.Column("is_underwriter_dashboard_enabled", sa.<PERSON>(), nullable=True))

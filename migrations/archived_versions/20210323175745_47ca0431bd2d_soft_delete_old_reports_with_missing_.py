"""Soft delete old reports with missing snapshots

Revision ID: 47ca0431bd2d
Revises: e7c7365688f3
Create Date: 2021-03-23 17:57:45.402632+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "47ca0431bd2d"
down_revision = "e7c7365688f3"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    UPDATE reports_v2
    SET is_deleted = true
    WHERE id IN (
                        '0c72e5c9-c86a-4b2c-bdc2-e4bc6fd16c64',
                        'a7574160-7dae-4651-b2bc-2baeb78f6a4a',
                        'b9ebbe04-fa1c-470f-8c3b-b49d00ad56c4',
                        'fe910943-be13-47ab-b1d4-3bc9637bba1b',
                        'ccf87f09-6daa-4bd9-b7c6-e5caf7797b0b',
                        '90c5876d-0925-43fb-a6e6-e61b28633436',
                        'e34f8efb-2888-4ffb-9c74-4fb39dada2f4',
                        '3bff8bd2-dd15-4676-9204-274cd2de76b4',
                        '95f7c3d4-7253-4b71-91c0-b3cd6adb8443',
                        '1d5710e7-87f5-4e67-bd86-d649c580b0e6',
                        '19c25755-5763-4413-b1fe-249ec1e71633',
                        'e09efdb9-7358-497b-9148-fedaa3e73d71',
                        'bdde15ce-87b6-41a7-8245-dfee7e8d18df',
                        '5460ce2f-102a-4485-bd00-18b5316b1c63',
                        '64b04648-64f3-404f-8102-94c592680be0',
                        'a4b5994d-f88e-489c-982c-fb00b0a4b3a3',
                        '56b823fa-f688-4e6e-8d17-73a27a84cbd0',
                        '8711ac71-a953-4bc1-b969-aae195bbc3f8',
                        'aafa7b8a-2ea1-420a-b625-9bffd00c54fd',
                        'cc6cf99a-7671-48b1-a764-e66de180832a',
                        '5ccb3cc5-349b-4250-a5ba-322a47a31f55',
                        '36a96fb2-3e58-440a-8606-4bc3f0f87fca',
                        'f48137b1-7910-4277-aa40-8e418cdbd205',
                        '7fa17040-fd87-4239-aad5-5d352b309757',
                        '83baf569-9d20-44d1-949c-e9b1f2bd23d7',
                        '203cab3d-ca44-45d7-9934-6d49d73c7fcd',
                        'b15c9fe0-9630-46c5-b2d9-2d9eb10e4a7b',
                        '7c7548ed-4920-4d9d-b0cc-ad4ad00eb8ef',
                        '95f593fd-ec83-4ec6-9861-09e1ce431a76',
                        'bdce8967-263f-418e-b5f2-675b0409e24b'
    )
    """)


def downgrade():
    op.execute("""
    UPDATE reports_v2
    SET is_deleted = false
    WHERE id IN (
                        '0c72e5c9-c86a-4b2c-bdc2-e4bc6fd16c64',
                        'a7574160-7dae-4651-b2bc-2baeb78f6a4a',
                        'b9ebbe04-fa1c-470f-8c3b-b49d00ad56c4',
                        'fe910943-be13-47ab-b1d4-3bc9637bba1b',
                        'ccf87f09-6daa-4bd9-b7c6-e5caf7797b0b',
                        '90c5876d-0925-43fb-a6e6-e61b28633436',
                        'e34f8efb-2888-4ffb-9c74-4fb39dada2f4',
                        '3bff8bd2-dd15-4676-9204-274cd2de76b4',
                        '95f7c3d4-7253-4b71-91c0-b3cd6adb8443',
                        '1d5710e7-87f5-4e67-bd86-d649c580b0e6',
                        '19c25755-5763-4413-b1fe-249ec1e71633',
                        'e09efdb9-7358-497b-9148-fedaa3e73d71',
                        'bdde15ce-87b6-41a7-8245-dfee7e8d18df',
                        '5460ce2f-102a-4485-bd00-18b5316b1c63',
                        '64b04648-64f3-404f-8102-94c592680be0',
                        'a4b5994d-f88e-489c-982c-fb00b0a4b3a3',
                        '56b823fa-f688-4e6e-8d17-73a27a84cbd0',
                        '8711ac71-a953-4bc1-b969-aae195bbc3f8',
                        'aafa7b8a-2ea1-420a-b625-9bffd00c54fd',
                        'cc6cf99a-7671-48b1-a764-e66de180832a',
                        '5ccb3cc5-349b-4250-a5ba-322a47a31f55',
                        '36a96fb2-3e58-440a-8606-4bc3f0f87fca',
                        'f48137b1-7910-4277-aa40-8e418cdbd205',
                        '7fa17040-fd87-4239-aad5-5d352b309757',
                        '83baf569-9d20-44d1-949c-e9b1f2bd23d7',
                        '203cab3d-ca44-45d7-9934-6d49d73c7fcd',
                        'b15c9fe0-9630-46c5-b2d9-2d9eb10e4a7b',
                        '7c7548ed-4920-4d9d-b0cc-ad4ad00eb8ef',
                        '95f593fd-ec83-4ec6-9861-09e1ce431a76',
                        'bdce8967-263f-418e-b5f2-675b0409e24b'
    )
    """)

"""add is_renewal flag to submission sync

Revision ID: df40ef3f5176
Revises: 1eb7055aef92
Create Date: 2023-04-12 11:53:58.519307+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "df40ef3f5176"
down_revision = "1eb7055aef92"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submission_sync", sa.Column("is_renewal", sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submission_sync", "is_renewal")
    # ### end Alembic commands ###

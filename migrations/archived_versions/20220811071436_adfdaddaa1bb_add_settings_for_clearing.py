"""Add settings for clearing

Revision ID: adfdaddaa1bb
Revises: 5e9e2f8395c2
Create Date: 2022-08-11 07:14:36.575148+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "adfdaddaa1bb"
down_revision = "5e9e2f8395c2"
branch_labels = None
depends_on = None


def upgrade():
    con = op.get_bind()
    tomek_w_id = list(con.execute("""select id from users where email = 'tomasz.<PERSON><PERSON><PERSON><PERSON><PERSON>@kalepa.com';"""))
    piotr_id = list(con.execute("""select id from users where email = '<EMAIL>';"""))

    if len(tomek_w_id) > 0:
        op.execute(f"""
            insert into settings (
                id,
                is_map_enabled_by_default,
                default_coverages,
                user_id,
                is_clarion_door_enabled,
                can_resolve_clearing_issues,
                is_clearing_enabled,
                should_be_notified_for_clearing_issues,
                support_email
            )
            values (
                '1c16e7e3-ef53-455e-b062-81c59a15235c', false, null, {tomek_w_id[0][0]}, null, false, true, false, null
            );
        """)
    if len(piotr_id) > 0:
        op.execute(f"""
            insert into settings (
                id,
                is_map_enabled_by_default,
                default_coverages,
                user_id,
                is_clarion_door_enabled,
                can_resolve_clearing_issues,
                is_clearing_enabled,
                should_be_notified_for_clearing_issues,
                support_email
            )
            values (
                'adea9f0a-8f72-4b78-b1a0-5ef9500bc6fc', false, null, {piotr_id[0][0]}, null, true, true, true, null
            );
        """)


def downgrade():
    op.execute("""
        delete from settings where id='1c16e7e3-ef53-455e-b062-81c59a15235c';
        delete from settings where id='adea9f0a-8f72-4b78-b1a0-5ef9500bc6fc';
    """)

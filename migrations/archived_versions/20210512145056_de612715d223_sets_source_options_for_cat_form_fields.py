"""Sets source options for CAT form fields

Revision ID: de612715d223
Revises: 439bcb3f0a65
Create Date: 2021-05-12 14:50:56.021740-04:00

"""


from datetime import datetime
from uuid import uuid4

from alembic import op
import sqlalchemy as sa

from copilot.models.types import SourceType

# revision identifiers, used by Alembic.
revision = "de612715d223"
down_revision = "439bcb3f0a65"
branch_labels = None
depends_on = None

SOURCE_OPTIONS = [
    ("Account Name", SourceType.SUBMISSION, "Account Name", "account_name"),
    ("Policy Number", SourceType.SUBMISSION, "Policy Number", "policy_id"),
    ("Policy Inception Date", SourceType.SUBMISSION, "Submission Effective Date", "proposed_effective_date"),
    ("Policy Expiration Date", SourceType.SUBMISSION, "Submission Policy Expiration Date", "policy_expiration_date"),
    ("# Locations", SourceType.SUBMISSION, "Number of Submission Businesses", "businesses.length()"),
    ("Underwriter", SourceType.USER, "Submission Owner Name", "name"),
    (
        "STREET ADDRESS",
        SourceType.SUBMISSION_BUSINESS,
        "Submission Business Address Line 1",
        "ers_data.premises.address_line_1",
    ),
    ("CITY", SourceType.SUBMISSION_BUSINESS, "Submission Business City", "ers_data.premises.city"),
    ("STATE", SourceType.SUBMISSION_BUSINESS, "Submission Business State", "ers_data.premises.state"),
    ("ZIP CODE", SourceType.SUBMISSION_BUSINESS, "Submission Business Postal Code", "ers_data.premises.postal_code"),
    (
        "CONSTRUCTION CLASS",
        SourceType.DOSSIER,
        "Construction Class (Kalepa Property Records)",
        "properties[0].structures[0].construction_type",
    ),
    (
        "OCCUPANCY TYPE",
        SourceType.DOSSIER,
        "Occuppancy Type (Kalepa Property Records)",
        "properties[0].structures[0].category",
    ),
    (
        "YEAR BUILT",
        SourceType.DOSSIER,
        "Year Built (Kalepa Property Records)",
        "properties[0].structures[0].year_built",
    ),
    (
        "# of STORIES",
        SourceType.DOSSIER,
        "Stories Count (Kalepa Property Records)",
        "properties[0].structures[0].stories_count",
    ),
    (
        "# of BLDGS",
        SourceType.DOSSIER,
        "Structures Count (Kalepa Property Records)",
        "properties[0].structure_total.count",
    ),
    (
        "SQUARE FOOTAGE",
        SourceType.DOSSIER,
        "Structures Count (Kalepa Property Records)",
        "properties[0].structure_total.sqft",
    ),
    ("BUILDING VALUE", SourceType.DOSSIER, "Building Value (Kalepa Property Records)", "properties[0].valuation.value"),
]

FIELD_DEFAULTS = [
    ("Priority Level", "Normal"),
    ("Policy Level Deductible", True),
    ("Location Level Deductible", False),
    ("Deductible Per Unit of Insurance", False),
]

FIELD_OPTIONS = [
    (
        "CONSTRUCTION CLASS",
        [
            "0 - Unknown",
            "1 - Frame",
            "2 - Joisted Masonry",
            "3 - Non-Combustible",
            "4 - Masonry Non-Combustible",
            "5 - Modified Fire Resistive",
            "6 - Fire Resistive",
            "7 - Heavy Timber Joisted Masonry",
            "8 - Superior Non-Combustible",
            "9 - Superior Masonry Non-Combustible",
            "10 - Conventional Bridges (<500 ft spans) w/ Multiple Simple Spans",
            "11 - Conventional Bridges (<500 ft spans) w/ Monolithic Spans",
            "12 - Major Bridges (>500 ft spans)",
            "13 - Underground Pipelines",
            "14 - Pipelines at Grade",
            "15 - Concrete Dams",
            "16 - Earthfill and Rockfill Dams",
            "17 - Alluvium Tunnels",
            "18 - Rock Tunnels",
            "19 - Cut & Cover Tunnels",
            "20 - Underground Liquid Storage Tanks",
            "21 - Underground Solid Storage Tanks",
            "22 - On Ground Liquid Storage Tanks",
            "23 - On Ground Solid Storage Tanks",
            "24 - Elevated Liquid Storage Tanks",
            "25 - Elevated Solid Storage Tanks",
            "26 - Railroads (Track & Bed)",
            "27 - Highways",
            "28 - Runways",
            "29 - High Industrial Masonry Chimneys",
            "30 - High Industrial Concrete Chimneys",
            "31 - High Industrial Steel Chimneys",
            "32 - Cranes",
            "33 - Conveyor Systems",
            "34 - Conventional (<100 ft) Electrical Transmission Line Towers",
            "35 - Major (>100 ft) Electrical Transmission Line Towers",
            "36 - Broadcast Towers",
            "37 - Observation Towers",
            "38 - Offshore Towers",
            "39 - Canals",
            "40 - Earth Retaining Structures (>20 ft high)",
            "41 - Waterfront Structures",
            "42 - Residential Equipment",
            "43 - Office Equipment",
            "44 - Electrical Equipment",
            "45 - Mechanical Equipment",
            "46 - High Technology/Lab Equipment",
            "47 - Trains, Trucks, Airplanes",
        ],
    ),
    (
        "OCCUPANCY TYPE",
        [
            "1 - Single Family Dwelling",
            "2 - Multi Family Dwelling - Apartments",
            "42 - Condo Homeowners Association",
            "43 - Condo Unit Owner",
            "3 - Hotels Size Unknown",
            "51 - Hotels Large",
            "52 - Hotels Small and Medium",
            "4 - Group Institutional Housing",
            "5 - Retail Trade",
            "6 - Wholesale Trade",
            "7 - Personal and Repair Services",
            "44 - Gasoline Service Station",
            "8 - Professional, Technical and Business Services",
            "9 - Health Care Service",
            "49 - Acute Care Service Hospitals",
            "50 - Acute Care Service Hospitals OSHPD CA",
            "10 - Entertainment and Recreation",
            "47 - Restaurants",
            "48 - Casinos",
            "11 - Parking",
            "12 - Heavy Fabrication and Assembly",
            "13 - Light Fabrication and Assembly",
            "14 - Food and Drugs Processing",
            "15 - Chemicals Processing",
            "16 - Metal and Minerals Processing",
            "17 - High Technology",
            "18 - Construction",
            "19 - Petroleum",
            "20 - Agriculture",
            "21 - Mining",
            "22 - Religion and Non Profit",
            "23 - General Federal and State Services",
            "24 - Emergency Response Services",
            "25 - Education - Other Than Higher",
            "54 - Education - Higher",
            "26 - Highway",
            "27 - Railroad",
            "28 - Air",
            "29 - Sea / Water Transportation",
            "30 - Electrical",
            "31 - Water Supply and Purification",
            "32 - Sanitary Sewer",
            "33 - Natural Gas",
            "34 - Telephone and Telegraph",
            "35 - Communication (Radio and TV)",
            "36 - Flood Control",
            "37 - General Commercial",
            "38 - General Industrial",
        ],
    ),
]


def upgrade():
    conn = op.get_bind()
    for row in SOURCE_OPTIONS:
        f_label, source_type, label, path = row
        form_field_id = conn.execute(f"""SELECT id from form_field where label = '{f_label}' 
                                     and form_definition_id = 'CAT MOD REQUEST'""").fetchone()[0]
        conn.execute(
            """
            INSERT INTO form_field_source(id, created_at, form_field_id, label, source_type, path)
            VALUES ({}, {}, {}, {}, {}, {})""".format(
                f"'{uuid4()}'",
                f"'{datetime.utcnow()}'",
                f"'{form_field_id}'",
                f"'{label}'",
                f"'{source_type.name}'",
                f"'{path}'",
            )
        )
    for row in FIELD_OPTIONS:
        label, options = row
        form_field_id = conn.execute(f"""SELECT id from form_field where label = '{label}' 
                                     and form_definition_id = 'CAT MOD REQUEST'""").fetchone()[0]
        conn.execute(
            """UPDATE form_field SET options = '{}' WHERE id = '{}'""".format(
                "{ " + ", ".join([f'"{el}"' for el in options]) + " }", form_field_id
            )
        )
    for row in FIELD_DEFAULTS:
        label, value = row
        form_field_id = conn.execute(f"""SELECT id from form_field where label = '{label}' 
                                     and form_definition_id = 'CAT MOD REQUEST'""").fetchone()[0]
        conn.execute(
            """
            INSERT INTO form_field_value(id, created_at, form_field_id, value_type, {})
            VALUES ({}, {}, {}, {}, {})""".format(
                "boolean_value" if type(value) == bool else "text_value",
                f"'{uuid4()}'",
                f"'{datetime.utcnow()}'",
                f"'{form_field_id}'",
                "'BOOLEAN'" if type(value) == bool else "'TEXT'",
                str(value).lower() if type(value) == bool else f"'{value}'",
            )
        )


def downgrade():
    pass

"""blasting work

Revision ID: 6254cee29e35
Revises: 7f8f61e57c15
Create Date: 2023-05-03 09:06:02.760485+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "6254cee29e35"
down_revision = "7f8f61e57c15"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
UPDATE mode_cards set props = '{
  "generalContractor": {
    "DEFAULT": {
      "factSubtypes": [
        {
          "id": "PROJECT_USE_OF_EIFS",
          "parentSubtypeIds": ["EXTERIOR_INSULATION_FINISHING_SYSTEMS"]
        },
        { "id": "PROJECT_SCAFFOLDING", "parentSubtypeIds": ["SCAFFOLDING"] },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"]
        },
        { "id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"] },
        { "id": "PROJECT_CRANE_WORK", "parentSubtypeIds": ["CRANES"] },
        { "id": "PROJECT_DEMOLITION_WORK", "parentSubtypeIds": ["DEMOLITION"] },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"]
        },
        { "parentSubtypeIds": ["BLASTING_OPERATIONS"] },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"]
        },
        { "id": "PROJECT_EXCAVATION_WORK" },
        { "id": "PROJECT_BELOW_GRADE" },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"]
        },
        { "id": "PROJECT_HEIGHT_IN_FT" },
        { "id": "PROJECT_HEIGHT_IN_STORIES" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES" }
      ]
    },
    "ELECTRICAL_CONTRACTOR": {
      "factSubtypes": [
        { "id": "PROJECT_ELECTRICAL_WORK" },
        { "id": "PROJECT_TRAFFIC_LIGHTING_SIGNALS_WORK" },
        { "id": "PROJECT_AIRPORT_WORK" },
        { "id": "PROJECT_BELOW_GRADE" },
        { "id": "PROJECT_DEPTH_OF_WORK" },
        { "id": "PROJECT_HEIGHT_IN_FT" },
        { "id": "PROJECT_HEIGHT_IN_STORIES" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES" }
      ]
    },
    "SITE_PREPARATION_PRACTICE": {
      "factSubtypes": [
        {
          "id": "PROJECT_DEMOLITION_WORK",
          "parentSubtypeIds": ["DEMOLITION"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_BLASTING_WORK",
          "parentSubtypeIds": ["BLASTING_OPERATIONS"]
        },
        { "id": "PROJECT_EXCAVATION_WORK", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL",
          "parentSubtypeIds": ["HAZARDOUS_MATERIALS", "RADON_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_MOLD_REMOVAL",
          "parentSubtypeIds": ["MOLD_ABATEMENT"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_GRADING_WORK" },
        { "id": "PROJECT_PILE_DRIVING_WORK" },
        { "id": "PROJECT_PAVING_WORK" },
        { "id": "PROJECT_DRILLING_WORK" },
        {
          "id": "PROJECT_SCAFFOLDING",
          "parentSubtypeIds": ["SCAFFOLDING"],
          "hiddenWhenNegative": true
        },
        {
          "id": "PROJECT_CRANE_WORK",
          "parentSubtypeIds": ["CRANES"],
          "hiddenWhenNegative": true
        },
        { "id": "PROJECT_BELOW_GRADE", "hiddenWhenNegative": true },
        {
          "id": "PROJECT_DEPTH_OF_WORK",
          "parentSubtypeIds": ["PROJECT_BELOW_GRADE"],
          "hiddenWhenNegative": true
        }
      ]
    },
    "HVAC_AND_PLUMBING_PRACTICE": {
      "factSubtypes": [
        {
          "id": "PROJECT_HVAC_WORK",
          "parentSubtypeIds": ["HVAC_INSTALLATION_AND_SERVICE"]
        },
        { "id": "PROJECT_PLUMBING_WORK", "parentSubtypeIds": ["PLUMBING"] },
        { "id": "PROJECT_BOILER_WORK" },
        { "id": "PROJECT_ELECTRICAL_WORK" },
        { "id": "PROJECT_GAS_LINE_WORK" },
        { "id": "PROJECT_SPRINKLER_SYSTEM_WORK" },
        { "id": "PROJECT_SEWER_WORK" },
        { "id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"] },
        { "id": "PROJECT_SCAFFOLDING", "parentSubtypeIds": ["SCAFFOLDING"] },
        { "id": "PROJECT_EXCAVATION_WORK" },
        { "id": "PROJECT_HEIGHT_IN_FT" },
        { "id": "PROJECT_HEIGHT_IN_STORIES" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES" }
      ]
    },
    "ROOFING_AND_SIDING_PRACTICE": {
      "factSubtypes": [
        { "id": "PROJECT_ROOF_WORK", "parentSubtypeIds": ["ROOFING_SERVICE"] },
        { "id": "PROJECT_SIDING_WORK" },
        { "id": "PROJECT_INSULATION_WORK" },
        { "id": "PROJECT_WATER_PROOFING_WORK" },
        { "id": "PROJECT_HEIGHT_IN_FT" },
        { "id": "PROJECT_HEIGHT_IN_STORIES" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_FT" },
        { "id": "PRACTICE_MAX_HEIGHT_OF_WORK_IN_STORIES" }
      ]
    }
  }
}'
WHERE id in ('ac664a5b-bdae-46c0-b0df-bb2ff504f210', '9f87d358-8a2b-4206-a32c-7a9194cb8612');
        """)


def downgrade():
    pass

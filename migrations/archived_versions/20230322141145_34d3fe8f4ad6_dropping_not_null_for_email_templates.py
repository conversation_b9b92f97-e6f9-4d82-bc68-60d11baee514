"""Dropping not null for email templates

Revision ID: 34d3fe8f4ad6
Revises: 0ce2b185a51b
Create Date: 2023-03-22 14:11:45.381313+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "34d3fe8f4ad6"
down_revision = "0ce2b185a51b"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("ALTER TABLE email_templates ALTER COLUMN external_template_id DROP NOT NULL;")
    op.execute("ALTER TABLE email_templates ALTER COLUMN external_version_id DROP NOT NULL;")


def downgrade():
    pass

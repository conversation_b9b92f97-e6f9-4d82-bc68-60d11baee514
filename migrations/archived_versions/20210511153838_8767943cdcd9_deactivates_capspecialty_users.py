"""Deactivates CapSpecialty users

Revision ID: 8767943cdcd9
Revises: 0b958702f64f
Create Date: 2021-05-11 15:38:38.931769+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8767943cdcd9"
down_revision = "0b958702f64f"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
    update users set email = 'defunct' || id || '@kalepa.co', organization_id = null 
    where id = 140 and email = '<EMAIL>'
    """)

    conn.execute("""
    update users set email = 'defunct' || id || '@kalepa.co', organization_id = null 
    where id in (106, 131) and email = '<EMAIL>'
    """)


def downgrade():
    pass

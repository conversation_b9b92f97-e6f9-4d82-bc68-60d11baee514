"""submission sync account id

Revision ID: 071d9a958390
Revises: eeb41f545fd8
Create Date: 2023-03-24 13:13:58.541284+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "071d9a958390"
down_revision = "eeb41f545fd8"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_sync", sa.Column("account_id", sa.String(), nullable=True))


def downgrade():
    op.drop_column("submission_sync", "account_id")

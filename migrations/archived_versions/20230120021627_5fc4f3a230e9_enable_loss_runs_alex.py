"""Enable Loss Runs Alex

Revision ID: 5fc4f3a230e9
Revises: aa2f2dbc4069
Create Date: 2023-01-20 02:16:27.574505+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5fc4f3a230e9"
down_revision = "aa2f2dbc4069"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    conn.execute("UPDATE settings SET loss_runs_enabled = true WHERE user_id =306;")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

"""Adds new type to FormSourceEnum and adds FactsAPI paths

Revision ID: 4e5c2946978d
Revises: fbe94746e907
Create Date: 2021-09-01 21:31:44.545077+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "4e5c2946978d"
down_revision = "fbe94746e907"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE sourcetype ADD VALUE IF NOT EXISTS 'FACTS';""")
    op.add_column("form_field_source", sa.Column("fact_subtype_id", sa.String(), nullable=True))
    conn = op.get_bind()
    conn.execute(
        """UPDATE form_field_source SET fact_subtype_id = 'STRUCTURES_COUNT' WHERE PATH='properties[0].structure_total.count'"""
    )
    conn.execute(
        """UPDATE form_field_source SET fact_subtype_id = 'BUILDING_SIZE' WHERE PATH='properties[0].structure_total.sqft'"""
    )
    conn.execute(
        """UPDATE form_field_source SET fact_subtype_id = 'BUILDING_TYPE' WHERE PATH='properties[0].structures[0].building_type'"""
    )
    conn.execute(
        """UPDATE form_field_source SET fact_subtype_id = 'BUILDING_CONSTRUCTION' WHERE PATH='properties[0].structures[0].construction_type'"""
    )
    conn.execute(
        """UPDATE form_field_source SET fact_subtype_id = 'YEAR_BUILT' WHERE PATH='properties[0].structures[0].year_built'"""
    )
    conn.execute(
        """UPDATE form_field_source SET fact_subtype_id = 'NUMBER_OF_STORIES' WHERE PATH='properties[0].structures[0].stories_count'"""
    )
    conn.execute(
        """UPDATE form_field_source SET fact_subtype_id = 'BUILDING_VALUE' WHERE PATH='properties[0].valuation.value'"""
    )

    conn.execute("""UPDATE form_field_source SET source_type = 'FACTS' WHERE source_type = 'DOSSIER'""")


def downgrade():
    op.drop_column("form_field_source", "fact_subtype_id")

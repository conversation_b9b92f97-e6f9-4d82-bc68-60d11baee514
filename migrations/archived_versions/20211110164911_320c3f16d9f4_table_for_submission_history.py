"""table for submission history

Revision ID: 320c3f16d9f4
Revises: 6915f90583a8
Create Date: 2021-11-10 16:49:11.342090+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "320c3f16d9f4"
down_revision = "6915f90583a8"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "submission_history",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("occurred_at", sa.DateTime(), nullable=True),
        sa.Column("submission_action_type", sa.Enum("ADDED", "DELETED", name="submissionactiontype"), nullable=False),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("parent_type", sa.Enum("SUBMISSION_BUSINESS", name="submissionparenttype"), nullable=False),
        sa.ForeignKeyConstraint(
            ["submission_id"],
            ["submissions.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_submission_history_submission_id"), "submission_history", ["submission_id"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_submission_history_submission_id"), table_name="submission_history")
    op.drop_table("submission_history")

"""Sets is_quoted in previous submission_coverage

Revision ID: 46dd7582ec2b
Revises: e573b520e17e
Create Date: 2021-01-14 06:35:35.007173-05:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "46dd7582ec2b"
down_revision = "e573b520e17e"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""UPDATE submission_coverages SET is_quoted=FALSE;""")


def downgrade():
    pass

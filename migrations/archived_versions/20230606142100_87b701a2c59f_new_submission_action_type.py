"""fix submission audit

Revision ID: 87b701a2c59f
Revises: 1ab701a2c59f
Create Date: 2023-06-06 10:00:00.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "87b701a2c59f"
down_revision = "1ab701a2c59f"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_BUSINESS_CONFIRMATION_COMPLETED';")


def downgrade():
    pass

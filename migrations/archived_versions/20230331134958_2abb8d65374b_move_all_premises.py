"""move all premises

Revision ID: 2abb8d65374b
Revises: 70318baeaab7
Create Date: 2023-03-31 13:49:58.838480+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2abb8d65374b"
down_revision = "70318baeaab7"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        UPDATE mode_elements set position = 30000 where id = 'bcbc607e-9c06-4326-a0fa-dd057b0f19fa';
        """)


def downgrade():
    pass

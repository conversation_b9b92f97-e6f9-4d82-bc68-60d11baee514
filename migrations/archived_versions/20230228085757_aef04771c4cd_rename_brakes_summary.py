"""rename brakes summary

Revision ID: aef04771c4cd
Revises: f2ac08e79153
Create Date: 2023-02-28 08:57:57.312343+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "aef04771c4cd"
down_revision = "f2ac08e79153"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("SET statement_timeout TO '1800 s';")

    # create the mode
    conn.execute("""
        UPDATE category_summary SET display_name = 'Brakes Maintenance Violations' WHERE display_name = 'Breaks Maintenance Violations';
        """)


def downgrade():
    pass

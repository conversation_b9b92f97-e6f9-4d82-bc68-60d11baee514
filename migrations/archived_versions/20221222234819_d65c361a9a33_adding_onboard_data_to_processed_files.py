"""Adding onboard data to processed files

Revision ID: d65c361a9a33
Revises: 866d48e1273b
Create Date: 2022-12-22 23:48:19.932899+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
from sqlalchemy.engine.reflection import Inspector
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d65c361a9a33"
down_revision = "866d48e1273b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    columns = inspector.get_columns("processed_files")
    if "onboarded_data" not in columns:
        op.add_column(
            "processed_files", sa.Column("onboarded_data", postgresql.JSONB(astext_type=sa.Text()), nullable=True)
        )


def downgrade():
    op.drop_column("processed_files", "onboarded_data")

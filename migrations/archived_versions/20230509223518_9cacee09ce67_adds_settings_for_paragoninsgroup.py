"""Adds settings for paragoninsgroup

Revision ID: 9cacee09ce67
Revises: b1d6347b4dab
Create Date: 2023-05-09 22:35:18.421750+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "9cacee09ce67"
down_revision = "b1d6347b4dab"
branch_labels = None
depends_on = None

show_management_dashboard_column = "show_management_dashboard"


def upgrade():
    op.execute(f"""
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        INSERT INTO settings (id, user_id, {show_management_dashboard_column}) VALUES
        (uuid_generate_v4(), (select id from users where email = '<EMAIL>'), true);
        """)


def downgrade():
    ...

"""Adjust the modes to new design

Revision ID: 61c15cb3fbab
Revises: d030753f777d
Create Date: 2022-12-15 20:20:16.312777+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "61c15cb3fbab"
down_revision = "d030753f777d"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("mode_cards", "title", existing_type=sa.VARCHAR(), nullable=True)
    op.add_column("mode_rows", sa.Column("elevation", sa.<PERSON>nteger(), nullable=True))
    op.add_column("mode_rows", sa.Column("is_collapsible", sa.<PERSON>(), nullable=True))
    op.add_column("mode_rows", sa.Column("is_default_open", sa.<PERSON>(), nullable=True))
    op.add_column("mode_rows", sa.Column("title", sa.String(), nullable=True))

    op.add_column("submissions", sa.Column("copilot_2_mode_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, "submissions", "modes", ["copilot_2_mode_id"], ["id"])


def downgrade():
    pass

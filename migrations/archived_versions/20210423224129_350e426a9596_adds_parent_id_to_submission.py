"""Adds parent_id to submission

Revision ID: 350e426a9596
Revises: 31346eb6c99d
Create Date: 2021-04-23 08:41:29.645505-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "350e426a9596"
down_revision = "628f44239a81"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("parent_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, "submissions", "submissions", ["parent_id"], ["id"])


def downgrade():
    op.drop_constraint(None, "submissions", type_="foreignkey")
    op.drop_column("submissions", "parent_id")

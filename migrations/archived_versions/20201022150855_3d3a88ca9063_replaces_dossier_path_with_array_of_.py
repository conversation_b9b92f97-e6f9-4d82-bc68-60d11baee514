"""replaces dossier_path with array of paths

Revision ID: 3d3a88ca9063
Revises: b4c4e198950c
Create Date: 2020-10-22 15:08:55.723540+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3d3a88ca9063"
down_revision = "b4c4e198950c"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "notebook_thread", sa.Column("dossier_component_paths", postgresql.ARRAY(sa.String(length=256)), nullable=True)
    )
    op.alter_column(
        "notebook_thread",
        "relates_to",
        existing_type=postgresql.ENUM(
            "SUBMISSION", "SUBMISSION_BUSINESS", "DOSSIER_COMPONENT", name="notebookthreadsubject"
        ),
        nullable=False,
    )
    op.drop_column("notebook_thread", "dossier_component_path")


def downgrade():
    op.add_column(
        "notebook_thread",
        sa.Column("dossier_component_path", sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    )
    op.alter_column(
        "notebook_thread",
        "relates_to",
        existing_type=postgresql.ENUM(
            "SUBMISSION", "SUBMISSION_BUSINESS", "DOSSIER_COMPONENT", name="notebookthreadsubject"
        ),
        nullable=True,
    )
    op.drop_column("notebook_thread", "dossier_component_paths")

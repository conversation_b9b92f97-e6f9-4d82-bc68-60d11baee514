"""mode-updates-contractor-project

Revision ID: f13b4b5a57e0
Revises: b60cab96f786
Create Date: 2023-02-13 16:50:18.803144+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f13b4b5a57e0"
down_revision = "b60cab96f786"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_cards set title=NULL where id = 'c0dc089c-ae2f-4063-aa9c-8ed8d9d5f28a';
        update mode_cards set title=NULL where id = 'b11cafe2-ddd3-4b36-81ca-5055d40c3c1a';
        update mode_cards set title=NULL where id = '5c2606ab-2313-49b8-a7a2-f30533ccf532';
        update mode_cards set title='Project Images' where id = '4d490b2d-2767-4f45-854d-f88e60cc6ec1';
        update mode_cards set type='FACTS', props = '{"generalContractor":{"DEFAULT":{"factSubtypes":[{"id":"YEARS_IN_BUSINESS"},{"id":"KNOWN_PERSONNEL"},{"id":"BBB_RATING"}]}}}' 
            where id = '3786a024-2929-4793-8448-dbf10ee2bb49';
        update mode_cards set type='FACTS', props = '{"generalContractor":{"DEFAULT":{"factSubtypes":[{"id":"TOTAL_SALES"},{"id":"PAYROLL"}]}}}' 
            where id = '2eb7e12b-694b-4ca1-a2f7-d9d080052106';
        update mode_cards set type='TABLE', title = NULL, props = '{"type":"PERMIT"}' 
            where id = 'a18a11e4-e8f1-47d6-b73b-514b01775fa5';    
        update mode_cards set type='SUBMISSION_BUSINESSES', props = '{"types":["OTHER_INSURED"]}' 
            where id = 'e7ad716f-ace9-402e-87c2-ae3ca2d8275d';
        
        -- temporarily set to other position to bypass constraint
        update mode_rows set position=123 WHERE id = '7c7ddb7a-b674-4693-8621-7a011528a8a3';
        
        update mode_rows set position=20 WHERE id = 'cf8fee73-349c-47a7-a622-615766135b48';
        update mode_rows set position=30 WHERE id = '6984e90e-c73a-44d0-8be4-250e853af384';
        update mode_rows set position=40 WHERE id = '7c7ddb7a-b674-4693-8621-7a011528a8a3';
        update mode_rows set position=45 WHERE id = '1786d8f7-13a0-4358-a9f2-923983911963';
            """)


def downgrade():
    pass

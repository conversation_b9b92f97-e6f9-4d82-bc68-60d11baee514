"""Add triggers

Revision ID: a65ac0838d9e
Revises: fd956007c6c0
Create Date: 2023-04-12 14:32:06.685769+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a65ac0838d9e"
down_revision = "fd956007c6c0"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Submission users
    triggers = conn.execute(
        "select tgname from pg_trigger where not tgisinternal and tgname = 'cross_org_check_su';"
    ).fetchall()
    already_exists = [x for x in triggers]
    if not already_exists:
        conn.execute("""
            CREATE FUNCTION cross_check_su() RETURNS trigger AS $cross_check_su$
                DECLARE
                    is_cross_org boolean;
                BEGIN
                    is_cross_org := (select cross_organization_access from users where id = NEW.user_id);
                    IF is_cross_org THEN
                        RAISE EXCEPTION 'Cross org users cannot be assigned to submissions';
                    END IF;
                    RETURN NEW;
                END;
            $cross_check_su$ LANGUAGE plpgsql;
            CREATE TRIGGER cross_org_check_su BEFORE INSERT OR UPDATE ON submissions_users FOR EACH ROW EXECUTE PROCEDURE cross_check_su();
        """)

    # Users
    triggers = conn.execute(
        "select tgname from pg_trigger where not tgisinternal and tgname = 'cross_check_users_trigger';"
    ).fetchall()
    already_exists = [x for x in triggers]
    if not already_exists:
        conn.execute("""
            CREATE FUNCTION cross_check_users() RETURNS trigger AS $cross_check_users$
                DECLARE
                    is_cross_org boolean;
                    has_reports boolean;
                    is_assigned boolean;
                    current_org_id integer;
                BEGIN
                    is_cross_org := NEW.cross_organization_access;
                    has_reports := EXISTS(SELECT 1 FROM reports_v2 WHERE owner_id = NEW.id);
                    is_assigned := EXISTS(SELECT 1 from submissions_users where user_id = NEW.id);
                    current_org_id := (SELECT organization_id FROM users WHERE id = NEW.id);
                    IF NEW.organization_id != current_org_id AND NOT is_cross_org THEN
                        RAISE EXCEPTION 'Only cross org users can change organization id';
                    END IF;
                    IF is_cross_org AND (has_reports OR is_assigned) THEN
                        RAISE EXCEPTION 'Cross org users cannot be assigned to any reports or own them';
                    END IF;
                    RETURN NEW;
                END;
            $cross_check_users$ LANGUAGE plpgsql;
            CREATE TRIGGER cross_check_users_trigger BEFORE INSERT OR UPDATE ON users FOR EACH ROW EXECUTE PROCEDURE cross_check_users();
        """)

    # Reports
    triggers = conn.execute(
        "select tgname from pg_trigger where not tgisinternal and tgname = 'cross_org_check_reports_trigger';"
    ).fetchall()
    already_exists = [x for x in triggers]
    if not already_exists:
        conn.execute("""
            CREATE FUNCTION cross_check_reports() RETURNS trigger AS $cross_check_reports$
                DECLARE
                    is_cross_org boolean;
                BEGIN
                    is_cross_org := (select cross_organization_access from users where id = NEW.owner_id);
                    IF is_cross_org THEN
                        RAISE EXCEPTION 'Cross org users cannot own reports or submissions';
                    END IF;
                    RETURN NEW;
                END;
            $cross_check_reports$ LANGUAGE plpgsql;
            CREATE TRIGGER cross_org_check_reports_trigger BEFORE INSERT OR UPDATE ON reports_v2 FOR EACH ROW EXECUTE PROCEDURE cross_check_reports();
            CREATE TRIGGER cross_org_check_reports_trigger BEFORE INSERT OR UPDATE ON submissions FOR EACH ROW EXECUTE PROCEDURE cross_check_reports();
        """)


def downgrade():
    pass

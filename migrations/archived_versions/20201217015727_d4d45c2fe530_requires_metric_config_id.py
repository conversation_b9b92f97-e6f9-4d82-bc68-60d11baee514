"""Requires metric_config_id

Revision ID: d4d45c2fe530
Revises: af5f96ff1db9
Create Date: 2020-12-17 01:57:27.051996+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d4d45c2fe530"
down_revision = "af5f96ff1db9"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column("metric", "metric_config_id", existing_type=postgresql.UUID(), nullable=False)


def downgrade():
    op.alter_column("metric", "metric_config_id", existing_type=postgresql.UUID(), nullable=True)

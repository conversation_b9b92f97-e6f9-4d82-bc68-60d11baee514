"""Make claimaint name nullable in loss

Revision ID: d164d0a80202
Revises: 5bda90046db7
Create Date: 2022-10-31 12:34:25.117336+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "d164d0a80202"
down_revision = "5bda90046db7"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("loss", "claimant_name", existing_type=sa.VARCHAR(), nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

"""Adds recommendation condition explanation

Revision ID: 66742d6e7547
Revises: f7995874e7e1
Create Date: 2021-03-10 17:51:22.251593-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "66742d6e7547"
down_revision = "f7995874e7e1"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "recommendation_condition_explanation",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("text", sa.String(length=2048), nullable=True),
        sa.Column("icon_name", sa.String(length=128), nullable=True),
        sa.Column("explanation_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("condition_variable", sa.String(length=128), nullable=False),
        sa.Column("condition_operator", sa.String(length=128), nullable=False),
        sa.Column("condition_value", sa.String(length=128), nullable=False),
        sa.Column("triggered_submission_business_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
        sa.Column("triggered_values", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(["explanation_id"], ["recommendation_explanation.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_recommendation_condition_explanation_explanation_id"),
        "recommendation_condition_explanation",
        ["explanation_id"],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f("ix_recommendation_condition_explanation_explanation_id"),
        table_name="recommendation_condition_explanation",
    )
    op.drop_table("recommendation_condition_explanation")

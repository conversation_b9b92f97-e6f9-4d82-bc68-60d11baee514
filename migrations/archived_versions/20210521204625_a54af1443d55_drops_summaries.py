"""Drops summaries

Revision ID: a54af1443d55
Revises: 453ebac3617d
Create Date: 2021-05-21 20:46:25.312135+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a54af1443d55"
down_revision = "453ebac3617d"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("ix_summaries_report_id", table_name="summaries")
    op.drop_index("ix_summaries_report_v2_id", table_name="summaries")
    op.drop_table("summaries")


def downgrade():
    op.create_table(
        "summaries",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("report_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("summary", postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
        sa.Column("report_v2_id", postgresql.UUID(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(["report_id"], ["reports.id"], name="summaries_report_id_fkey"),
        sa.ForeignKeyConstraint(["report_v2_id"], ["reports_v2.id"], name="summaries_report_v2_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="summaries_pkey"),
    )
    op.create_index("ix_summaries_report_v2_id", "summaries", ["report_v2_id"], unique=True)
    op.create_index("ix_summaries_report_id", "summaries", ["report_id"], unique=True)

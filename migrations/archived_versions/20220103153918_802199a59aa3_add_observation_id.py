"""Add observation_id

Revision ID: 802199a59aa3
Revises: 133d0b28a9b3
Create Date: 2022-01-03 15:39:18.931167+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "802199a59aa3"
down_revision = "133d0b28a9b3"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "submission_business_field_values", sa.Column("observation_id", postgresql.UUID(as_uuid=True), nullable=True)
    )


def downgrade():
    op.drop_column("submission_business_field_values", "observation_id")

"""cross organization access

Revision ID: f8d4ad19855b
Revises: 02caac078dcb
Create Date: 2023-03-10 18:27:35.472265+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f8d4ad19855b"
down_revision = "02caac078dcb"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("cross_organization_access", sa.<PERSON>(), nullable=True))
    op.execute("UPDATE users SET cross_organization_access = false;")
    op.alter_column("users", "cross_organization_access", nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "cross_organization_access")
    # ### end Alembic commands ###

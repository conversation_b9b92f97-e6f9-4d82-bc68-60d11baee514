"""Invokes contractor insights for engineering industries

Revision ID: 5b1ea33bb506
Revises: 735fe7071154
Create Date: 2022-02-17 12:39:40.678637+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5b1ea33bb506"
down_revision = "735fe7071154"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""
    UPDATE customizable_classifiers set is_applicable_to = is_applicable_to || '{"Construction & Engineering"}'
    WHERE 'Contractors'=ANY(is_applicable_to) AND 'Construction & Engineering' != ANY(is_applicable_to);
    """)


def downgrade():
    pass

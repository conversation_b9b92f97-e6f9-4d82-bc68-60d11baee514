"""Adds metric type

Revision ID: 8dd74b798bc6
Revises: 12dfbedcc867
Create Date: 2020-09-04 16:15:37.321800+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8dd74b798bc6"
down_revision = "12dfbedcc867"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE metrictype ADD VALUE IF NOT EXISTS 'LIST_SUMMARY';""")


def downgrade():
    pass

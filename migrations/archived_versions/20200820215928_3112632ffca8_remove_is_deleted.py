"""empty message

Revision ID: 3112632ffca8
Revises: 50e82255f80b
Create Date: 2020-08-20 21:59:28.296007

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3112632ffca8"
down_revision = "50e82255f80b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("submission_businesses", "is_deleted")
    op.drop_column("submissions", "is_deleted")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("submissions", sa.Column("is_deleted", sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column("submission_businesses", sa.Column("is_deleted", sa.BOOLEAN(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###

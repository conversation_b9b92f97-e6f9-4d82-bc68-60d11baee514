"""Removes report ID from metric

Revision ID: 0273bffccdb3
Revises: 4b48b983bd58
Create Date: 2020-12-16 19:04:25.425214+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "0273bffccdb3"
down_revision = "4b48b983bd58"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("ix_metric_report_id", table_name="metric")
    op.drop_constraint("metric_report_id_fkey", "metric", type_="foreignkey")
    op.drop_column("metric", "report_id")


def downgrade():
    op.add_column("metric", sa.Column("report_id", sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key("metric_report_id_fkey", "metric", "reports", ["report_id"], ["id"], ondelete="CASCADE")
    op.create_index("ix_metric_report_id", "metric", ["report_id"], unique=False)

"""Removes submission_business.is_deleted

Revision ID: c3dc56ca8a50
Revises: 414b8d2cc98c
Create Date: 2021-08-11 17:20:15.284043+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c3dc56ca8a50"
down_revision = "414b8d2cc98c"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("submission_businesses", "is_deleted")


def downgrade():
    op.add_column("submission_businesses", sa.Column("is_deleted", sa.BOOLEAN(), autoincrement=False, nullable=True))

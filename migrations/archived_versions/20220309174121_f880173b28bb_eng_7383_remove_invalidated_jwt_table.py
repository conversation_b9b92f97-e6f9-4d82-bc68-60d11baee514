"""eng_7383_remove_invalidated_jwt_table

Revision ID: f880173b28bb
Revises: 2d19363ff1c2
Create Date: 2022-03-09 17:41:21.400306+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "f880173b28bb"
down_revision = "2d19363ff1c2"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table("invalidated_jwt")


def downgrade():
    op.create_table(
        "invalidated_jwt",
        sa.Column("jwt", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint("jwt", name="invalidated_jwt_pkey"),
    )

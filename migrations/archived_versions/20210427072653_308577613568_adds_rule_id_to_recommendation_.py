"""Adds rule_id to recommendation explanation

Revision ID: ************
Revises: 856eac8aa4b5
Create Date: 2021-04-27 07:26:53.646219-04:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "************"
down_revision = "856eac8aa4b5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("recommendation_explanation", sa.Column("rule_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_index(
        op.f("ix_recommendation_explanation_rule_id"), "recommendation_explanation", ["rule_id"], unique=False
    )
    op.create_foreign_key(
        "recommendation_explanation_rule_id_fk",
        "recommendation_explanation",
        "recommendation_rule",
        ["rule_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade():
    op.drop_constraint("recommendation_explanation_rule_id_fk", "recommendation_explanation", type_="foreignkey")
    op.drop_index(op.f("ix_recommendation_explanation_rule_id"), table_name="recommendation_explanation")
    op.drop_column("recommendation_explanation", "rule_id")

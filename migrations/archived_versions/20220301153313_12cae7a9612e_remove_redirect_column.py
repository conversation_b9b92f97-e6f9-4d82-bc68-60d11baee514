"""Remove redirect column

Revision ID: 12cae7a9612e
Revises: eb646ddc4049
Create Date: 2022-03-01 15:33:13.729954+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "12cae7a9612e"
down_revision = "eb646ddc4049"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column("users", "signout_redirection_url")


def downgrade():
    op.add_column("users", sa.Column("signout_redirection_url", sa.VARCHAR(), autoincrement=False, nullable=True))

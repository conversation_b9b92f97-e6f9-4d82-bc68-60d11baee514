"""Add sensible cache table

Revision ID: 16ec52c0eaef
Revises: 013d45942ac5
Create Date: 2023-05-11 12:17:23.214329+00:00

"""

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "16ec52c0eaef"
down_revision = "013d45942ac5"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "sensible_document_response_cache",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("response", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("file_ids", postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True),
        sa.Column("configurations", postgresql.ARRAY(sa.String(length=256)), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_sensible_document_response_cache_created_at"),
        "sensible_document_response_cache",
        ["created_at"],
    )

    op.create_index(
        op.f("ix_sensible_document_response_cache_configurations"),
        "sensible_document_response_cache",
        ["configurations"],
        postgresql_using="gin",
    )


def downgrade():
    op.drop_table("sensible_document_response_cache")

"""Extends SubmissionCoverage

Revision ID: e573b520e17e
Revises: e1020beb0c52
Create Date: 2021-01-13 15:49:51.866814-05:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e573b520e17e"
down_revision = "e1020beb0c52"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submission_coverages", sa.Column("is_quoted", sa.<PERSON>(), nullable=True))
    op.add_column("submission_coverages", sa.Column("quoted_premium", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("submission_coverages", "quoted_premium")
    op.drop_column("submission_coverages", "is_quoted")

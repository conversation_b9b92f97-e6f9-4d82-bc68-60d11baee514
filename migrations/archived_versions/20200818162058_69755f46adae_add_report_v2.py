"""empty message

Revision ID: 69755f46adae
Revises: 4af3aca4f090
Create Date: 2020-08-18 16:20:58.833955

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "69755f46adae"
down_revision = "4af3aca4f090"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "coverages",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("name", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "reports_v2",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "submissions",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("name", sa.String(), nullable=True),
        sa.Column("owner_id", sa.Integer(), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=True),
        sa.Column(
            "stage",
            sa.Enum(
                "JUST_IN",
                "ON_MY_PLATE",
                "WAITING_FOR_OTHERS",
                "QUOTED",
                "COMPLETED",
                "DECLINED",
                name="submissionstage",
            ),
            nullable=True,
        ),
        sa.Column("proposed_effective_date", sa.DateTime(), nullable=True),
        sa.Column("notebook", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("acord_125", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_submissions_owner_id"), "submissions", ["owner_id"], unique=False)
    op.create_table(
        "submission_businesses",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("requested_name", sa.String(), nullable=True),
        sa.Column("requested_address", sa.String(), nullable=True),
        sa.Column("requested_phone_number", sa.String(), nullable=True),
        sa.Column("requested_industries", sa.ARRAY(sa.String()), nullable=True),
        sa.Column("business_id", postgresql.UUID(), nullable=True),
        sa.Column("is_user_confirmed", sa.Boolean(), nullable=True),
        sa.Column("aliases", sa.ARRAY(sa.String()), nullable=True),
        sa.Column("selected_alias", sa.String(), nullable=True),
        sa.Column("is_deleted", sa.Boolean(), nullable=True),
        sa.Column("submission_id", postgresql.UUID(), nullable=False),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_businesses_business_id"), "submission_businesses", ["business_id"], unique=False
    )
    op.create_index(
        op.f("ix_submission_businesses_submission_id"), "submission_businesses", ["submission_id"], unique=False
    )
    op.create_table(
        "submission_coverages",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("coverage_id", postgresql.UUID(), nullable=False),
        sa.Column("estimated_premium", sa.Float(), nullable=True),
        sa.Column("submission_id", postgresql.UUID(), nullable=False),
        sa.ForeignKeyConstraint(["coverage_id"], ["coverages.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_submission_coverages_coverage_id"), "submission_coverages", ["coverage_id"], unique=False)
    op.create_index(
        op.f("ix_submission_coverages_submission_id"), "submission_coverages", ["submission_id"], unique=False
    )
    op.create_table(
        "submission_stage_changes",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("introduced_at", sa.DateTime(), nullable=True),
        sa.Column(
            "stage",
            sa.Enum(
                "JUST_IN",
                "ON_MY_PLATE",
                "WAITING_FOR_OTHERS",
                "QUOTED",
                "COMPLETED",
                "DECLINED",
                name="submissionstage",
            ),
            nullable=True,
        ),
        sa.Column("submission_id", postgresql.UUID(), nullable=False),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_submission_stage_changes_submission_id"), "submission_stage_changes", ["submission_id"], unique=False
    )
    op.add_column("report_summary_preference", sa.Column("submission_id", postgresql.UUID(), nullable=True))
    op.create_index(
        op.f("ix_report_summary_preference_submission_id"), "report_summary_preference", ["submission_id"], unique=False
    )
    op.create_foreign_key(None, "report_summary_preference", "submissions", ["submission_id"], ["id"])
    op.add_column("summaries", sa.Column("submission_id", postgresql.UUID(), nullable=True))
    op.create_index(op.f("ix_summaries_submission_id"), "summaries", ["submission_id"], unique=True)
    op.create_foreign_key(None, "summaries", "submissions", ["submission_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "summaries", type_="foreignkey")
    op.drop_index(op.f("ix_summaries_submission_id"), table_name="summaries")
    op.drop_column("summaries", "submission_id")
    op.drop_constraint(None, "report_summary_preference", type_="foreignkey")
    op.drop_index(op.f("ix_report_summary_preference_submission_id"), table_name="report_summary_preference")
    op.drop_column("report_summary_preference", "submission_id")
    op.drop_index(op.f("ix_submission_stage_changes_submission_id"), table_name="submission_stage_changes")
    op.drop_table("submission_stage_changes")
    op.drop_index(op.f("ix_submission_coverages_submission_id"), table_name="submission_coverages")
    op.drop_index(op.f("ix_submission_coverages_coverage_id"), table_name="submission_coverages")
    op.drop_table("submission_coverages")
    op.drop_index(op.f("ix_submission_businesses_submission_id"), table_name="submission_businesses")
    op.drop_index(op.f("ix_submission_businesses_business_id"), table_name="submission_businesses")
    op.drop_table("submission_businesses")
    op.drop_index(op.f("ix_submissions_owner_id"), table_name="submissions")
    op.drop_table("submissions")
    op.drop_table("reports_v2")
    op.drop_table("coverages")
    # ### end Alembic commands ###

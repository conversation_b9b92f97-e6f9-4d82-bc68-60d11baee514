from typing import List
import json
import os

from events_common.kalepa_events import KalepaEvents
import boto3

from test_utils.test_utils import DictToDotNotation

KalepaEventsSqsQueueName = "events-sqs"
RuleEnabled = "ENABLED"


class FakeAws:
    """
    Provides helper utility methods for setting up Fake AWS env (moto) for particular test cases.
    Reusable by both unit and BDD tests.
    """

    def __init__(self) -> None:
        aws_endpoint_url = os.environ.get("AWS_ENDPOINT_URL")
        self.env = os.environ.get("KALEPA_ENV", "dev")
        self.sqs = boto3.client("sqs", endpoint_url=aws_endpoint_url)
        self.ssm = boto3.client("ssm", endpoint_url=aws_endpoint_url)

    def prepare_env_for_testing_kalepa_domain_events(self) -> None:
        self.sqs_queue_for_kalepa_events()
        try:
            kalepa_events_queue_str = f'"kalepa_events_queue_name": "{self.kalepa_events_sqs_url}"'
            self.ssm.put_parameter(
                Name="/kalepa-config/dev",
                Description="Kalepa system config",
                Value='{"events":{' + kalepa_events_queue_str + "}}",
                Type="String",
                DataType="text",
            )
        except self.ssm.exceptions.ParameterAlreadyExists:
            pass

    def clear_env_for_testing_kalepa_domain_events(self) -> None:
        try:
            queue_url = self.sqs.get_queue_url(QueueName=KalepaEventsSqsQueueName)
            self.sqs.purge_queue(QueueUrl=queue_url["QueueUrl"])
        except self.sqs.exceptions.QueueDoesNotExist:
            pass

    def sqs_queue(self, queue_name: str) -> str:
        try:
            queue_url = self.sqs.get_queue_url(QueueName=queue_name)
            return queue_url["QueueUrl"]
        except self.sqs.exceptions.QueueDoesNotExist:
            statement = [
                {
                    "Effect": "Allow",
                    "Principal": {"Service": "events.amazonaws.com"},
                    "Action": "sqs:SendMessage",
                    "Resource": "*",
                }
            ]
            policy = json.dumps({"Version": "2012-10-17", "Statement": [statement]})
            queue = self.sqs.create_queue(QueueName=queue_name, Attributes={"Policy": policy})
            return queue["QueueUrl"]

    def sqs_queue_for_kalepa_events(self) -> None:
        self.kalepa_events_sqs_url = self.sqs_queue(KalepaEventsSqsQueueName)

    def assert_kalepa_domain_events_have_been_sent(self, expected_event_types: list[KalepaEvents]):
        sqs_messages = self.receive_sqs_messages(self.kalepa_events_sqs_url)

        messages = sqs_messages["Messages"]
        assert len(messages) == len(
            expected_event_types
        ), f"Expected exactly {len(expected_event_types)} message in SQS queue but got {len(messages)}!"

        for message, expected_event_type in zip(messages, expected_event_types):
            body_as_json: str = message["Body"]
            body = DictToDotNotation.wrap_dict(json.loads(body_as_json))
            assert body.event_type == expected_event_type, f"KalepaEvent message type is not {expected_event_type}"

    def receive_sqs_messages(self, url: str):
        sqs_messages = self.sqs.receive_message(QueueUrl=url, MaxNumberOfMessages=10, WaitTimeSeconds=1)
        assert "Messages" in sqs_messages, "No 'Messages' in SQS queue!"
        return sqs_messages

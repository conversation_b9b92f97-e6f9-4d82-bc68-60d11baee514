from __future__ import annotations


class DictToDotNotation:
    """
    Small helper class that takes Python dictionary
    and creates object that allows to get properites using "dot" notation.
    For example, for this input: {'field': 'val', 'field2': {'field3': 'val'}}
    will create object which can be used like:
    obj.field (returning 'val')
    obj.field2.field3 (returning 'val')
    """

    @staticmethod
    def wrap_dict(dict_instance: dict) -> DictToDotNotation:
        dict_to_dot = DictToDotNotation()
        for k, v in dict_instance.items():
            if type(v) is dict:
                v = DictToDotNotation.wrap_dict(v)
            setattr(dict_to_dot, DictToDotNotation.sanitize_key(k), v)
        return dict_to_dot

    @staticmethod
    def sanitize_key(key: str):
        return key.replace("-", "_")

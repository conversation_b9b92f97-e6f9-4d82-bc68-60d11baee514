Feature: Synchronize submissions with status reports

  Scenario: After loading sync requests we are trying to synchronize them
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|414ad8ac03f3360113ca2f4d" that belongs to organization with id "57"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "A report to be matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "111111111A",
        "stage": "QUOTED",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "A report to be matched",
        "received_date": "2023-02-05T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312
          }
        ],
        "matcher_data": {
          "named_insured": "random company",
          "named_insured_address": "random address"
        },
        "quoted_date": "2022-02-05T12:25:17",
        "first_seen": "2022-01-01T12:25:17"
      },
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "111111111A",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "A report to be matched",
        "received_date": "2023-02-06T12:25:17",
        "bound_date": "2022-04-05T12:25:17",
        "first_seen": "2022-02-02T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "dadb3ace-3ff0-2d62-55b9-1548b717b547"
    When we run the synchronization for ids "dadb3ace-3ff0-2d62-55b9-1548b717b547" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should have quoted_date "2022-02-05T12:25:17" and bound_date "2022-04-05T12:25:17"
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED_BOUND" and it should have quoted premium of "3212.0"
    When we run the synchronization for ids "dadb3ace-3ff0-2d62-55b9-1548b717b547" and organization "36"
    Then we should "1" response already synchronized without match result
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "111111111A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "A report to be matched",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "dadb3ace-3ff0-2d62-55b9-1548b717b547"
    When we run the synchronization for ids "dadb3ace-3ff0-2d62-55b9-1548b717b547" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED_BOUND" and it should have quoted premium of "321.0"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "122222222A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "A report to be matched",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "903b3e8f-adf0-e3bf-085f-d9ba1dc1a126"
    When we run the synchronization for ids "903b3e8f-adf0-e3bf-085f-d9ba1dc1a126" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "True" and is_shell "False"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And the name should contain the policy id "122222222A"
    When we GET the original submission
    Then the name should contain the policy id "111111111A"
    When we POST report
    """
    {
      "name": "A report to be matched",
      "user_email": "<EMAIL>",
      "businesses": [
        {"requested_name": "Pizza", "requested_address": "NYC"}
      ],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty",
      "external_id": "521341145A"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "133333333A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "A report to be matched",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "02ad9c8d-2620-ff86-f7c4-f7697ea69160"
    When we run the synchronization for ids "02ad9c8d-2620-ff86-f7c4-f7697ea69160" and organization "36"
    Then we should "1" response with matching result type "MULTIPLE_MATCHES" without synchronization errors
    And the matching result should have is_duplicate "True" and is_shell "False"
    When we GET the matched submission
    And we verify the submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And the name should contain the policy id "133333333A"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": null,
        "quoted_premium": 321,
        "policy_number": "133333333A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": ".Report. to be",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "02ad9c8d-2620-ff86-f7c4-f7697ea69160"
    When we run the synchronization for ids "02ad9c8d-2620-ff86-f7c4-f7697ea69160" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "False"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And the submission should have "1" coverage with name "liability"
    And the name should contain the policy id "133333333A"

  Scenario: Load sync requests that should create shell submissions
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "Too old to be matched",
        "user_email": "<EMAIL>",
        "businesses": [
        {"requested_name": "Pizza", "requested_address": "NYC"},
        {"requested_name": "Spaghetti", "requested_address": "NYC"}],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we update created date of the submission to be older than "10950" days
    And we verify the submission
    When we load synchronization requests
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "555555555A",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Too old to be matched",
        "received_date": "2023-01-05T00:00:00"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "f3ff5116-00c7-37d8-d11a-95fdb25497dc"
    When we run the synchronization for ids "f3ff5116-00c7-37d8-d11a-95fdb25497dc" and organization "36"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED_BOUND" and it should have quoted premium of "321.0"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "166666666A",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Cannot be matched",
        "received_date": "3023-02-05T12:25:17"
      }
    ]
    """
    # setting the received date way in the future just so we dont get failing behave tests in gh actions
    Then we should receive "1" request(s) with ids "d10a5401-08c5-3f96-e684-0b9f01ee3d5c"
    When we run the synchronization for ids "d10a5401-08c5-3f96-e684-0b9f01ee3d5c" and organization "36"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors

  Scenario: Load sync requests that should match a shell submission
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "Shell submission to be matched",
        "user_email": "<EMAIL>",
        "businesses": [],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "177777777A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Shell submission to be matched",
        "coverage_type": "EXCESS",
        "received_date": "2023-02-05T12:25:17",
        "account_id": "nw_account_id_123"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "ce288593-8eef-7959-b347-0f28e41832a4"
    When we run the synchronization for ids "ce288593-8eef-7959-b347-0f28e41832a4" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED" and it should have quoted premium of "321.0"
    And the submission should have account id set to "nw_account_id_123"

  Scenario: We should not match synchronization requests from different organization
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Report that belongs to arch",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S1111111",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Report that belongs to arch",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "b5a6d370-0aff-6a9b-6525-98bcaecfa682"
    When we run the synchronization for ids "b5a6d370-0aff-6a9b-6525-98bcaecfa682" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED" and it should have quoted premium of "43212.0"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "188888888A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Report that belongs to arch",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "35586fcc-cde0-0fb6-08eb-fae39d9c9e88"
    When we run the synchronization for ids "35586fcc-cde0-0fb6-08eb-fae39d9c9e88" and organization "36"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED" and it should have quoted premium of "321.0"
    And the matching result should have is_duplicate "False" and is_shell "True"

  Scenario: We should skip submissions which are in the middle of pds processing
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Incomplete PDS report",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we update the processing state of the submission to "NOT_STARTED"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "S1111121",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "Incomplete PDS report",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "8ac5f215-7a69-17dd-f2e3-17d0c8b21356"
    When we run the synchronization for ids "8ac5f215-7a69-17dd-f2e3-17d0c8b21356" and organization "10"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors
    When we update the processing state of the submission to "COMPLETED"
    And we verify the submission
    And we run the synchronization for ids "8ac5f215-7a69-17dd-f2e3-17d0c8b21356" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors


  Scenario: After loading and synchronizing requests, we delete the matched report and load new sync requests
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a user with email "<EMAIL>" and external id "auth0|11aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Report to be matched that will be deleted",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "16" days
    And we verify the submission
    When we load synchronization requests with updated received_date "14" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "125179311A",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "Report to be matched that will be deleted",
        "received_date": "2023-02-06T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "c6d6cd8b-50e6-6749-7766-68d0099499be"
    When we run the synchronization for ids "c6d6cd8b-50e6-6749-7766-68d0099499be" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    And we DELETE the associated report
    When we load synchronization requests
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "125179311A",
        "stage": "EXPIRED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 422312,
        "submission_name": "Report to be matched that will be deleted",
        "received_date": "2023-02-06T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "c6d6cd8b-50e6-6749-7766-68d0099499be"
    When we run the synchronization for ids "c6d6cd8b-50e6-6749-7766-68d0099499be" and organization "10"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "125179311A",
        "stage": "ON_MY_PLATE",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 422312,
        "submission_name": "Report to be matched that will be deleted",
        "received_date": "2023-02-06T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "c6d6cd8b-50e6-6749-7766-68d0099499be"
    When we run the synchronization for ids "c6d6cd8b-50e6-6749-7766-68d0099499be" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And its stage should be "EXPIRED" and it should have quoted premium of "3212.0"

  Scenario: After loading sync requests we are trying to synchronize multiple at a time
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Potato company",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Potatoes", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we set created date of the submission to "2023-02-04T12:25:17"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "111111111C",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Potato company",
        "received_date": "2023-02-05T12:25:17"
      },
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "111112111C",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "Potato company",
        "received_date": "2023-02-06T12:25:17"
      }
    ]
    """
    Then we should receive "2" request(s) with ids "6134aa10-d89b-60db-1008-25bb045cdecd,c2106409-931c-4367-6190-1913043a6fcc"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "111112111C",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "Potato company",
        "received_date": "2023-02-06T12:25:17"
      }
    ]
    """
    Then we should receive "0" request(s)
    When we load synchronization requests
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "111112111C",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43421,
        "submission_name": "Potato company",
        "received_date": "2023-02-06T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s)
    When we run the synchronization for ids "6134aa10-d89b-60db-1008-25bb045cdecd,c2106409-931c-4367-6190-1913043a6fcc" and organization "36"
    Then we should "2" response with different submission ids
    When we GET the matched submissions
    Then each submission should have "1" client ids

  Scenario: After loading sync requests we are creating a shell and a duplicate
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we load synchronization requests
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "CLIENT_ID_1",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "POTATO COMPANY REPORT THAT SHOULD NOT BE MATCHED AND BE A SHELL",
        "received_date": "2023-02-05T12:25:17"
      },
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "CLIENT_ID_2",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "POTATO COMPANY REPORT THAT SHOULD NOT BE MATCHED AND BE A SHELL",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "2" request(s) with ids "33a6bf06-2552-7579-7f48-fe6c882b7f4d,f779f805-9ce0-5547-a22a-13914f40b9e2"
    When we run the synchronization for ids "33a6bf06-2552-7579-7f48-fe6c882b7f4d,f779f805-9ce0-5547-a22a-13914f40b9e2" and organization "10"
    Then we should "2" response with different submission ids
    When we GET the matched submissions
    Then each submission should have "1" client ids

  Scenario: After loading sync requests we are matching a submission in still in pds flow
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Submission in pds processing",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Potatoes", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And set auto processed of the submission to True and processing state to "ENTITY_MAPPING"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "123321123C",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Submission in pds processing",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "50c634fc-81eb-c13e-69e5-2afac95eb8c1"
    When we run the synchronization for ids "50c634fc-81eb-c13e-69e5-2afac95eb8c1" and organization "36"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors

  Scenario: Load sync requests for arch and check if the underwriter is correctly assigned
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a user with email "<EMAIL>" and external id "auth0|11aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Another report that belongs to arch",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S1234567",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Another report that belongs to arch",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "86e3a6a5-2265-da05-7814-da9ec14106e2"
    When we run the synchronization for ids "86e3a6a5-2265-da05-7814-da9ec14106e2" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED_BOUND" and it should have quoted premium of "43212.0"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S1234567",
        "stage": "ON_MY_PLATE",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Another report that belongs to arch",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "86e3a6a5-2265-da05-7814-da9ec14106e2"
    When we run the synchronization for ids "86e3a6a5-2265-da05-7814-da9ec14106e2" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "ON_MY_PLATE" and it should have quoted premium of "43212.0"
    And the matching result should have is_duplicate "False" and is_shell "False"

  Scenario: Load sync requests, but run only the last change
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "A second shell submission to be matched",
        "user_email": "<EMAIL>",
        "businesses": [],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "111785962A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": " second shell submission to be matched",
        "coverage_type": "EXCESS",
        "received_date": "2023-02-05T12:25:17",
        "account_id": "nw_account_id_123"
      },
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "111785962A",
        "stage": "ON_MY_PLATE",
        "coverage_name": "liability",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": " second shell submission to be matched",
        "coverage_type": "EXCESS",
        "received_date": "2023-02-05T12:25:17",
        "account_id": "nw_account_id_123"
      },

      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "111785962A",
        "stage": "DECLINED",
        "coverage_name": "liability",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": " second shell submission to be matched",
        "coverage_type": "EXCESS",
        "received_date": "2023-02-05T12:25:17",
        "account_id": "nw_account_id_123"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "0a7ac375-d221-14d2-e934-208c268a575c"
    When we run the synchronization for ids "0a7ac375-d221-14d2-e934-208c268a575c" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And the submission should have "1" coverage with name "liability"
    And its stage should be "DECLINED" and it should have quoted premium of "321.0"
    And the submission should have account id set to "nw_account_id_123"

  Scenario: Load historical sync requests and try to synchronize them
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "Historical submission to be matched",
        "user_email": "<EMAIL>",
        "businesses": [
        {"requested_name": "Pizza", "requested_address": "NYC"},
        {"requested_name": "Spaghetti", "requested_address": "NYC"}],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we set created date of the submission to "2022-08-24T10:10:08.549849"
    And we verify the submission
    When we load synchronization requests
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "111456123A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Historical submission to be matched",
        "coverage_type": "EXCESS",
        "received_date": "2023-02-05T12:25:17",
        "account_id": "nw_account_id_123"
      },
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "111987123A",
        "stage": "ON_MY_PLATE",
        "coverage_name": "liability",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Historical submission to be matched",
        "coverage_type": "EXCESS",
        "received_date": "2022-08-24T12:34:21",
        "account_id": "nw_account_id_123"
      }
    ]
    """
    Then we should receive "2" request(s) with ids "5c7babf2-82a7-ee82-85df-2aafcb1b3431,b2c0bbf6-beae-d5a7-f146-72df74942a78"
    When we run the synchronization for ids "b2c0bbf6-beae-d5a7-f146-72df74942a78" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And the submission should have "1" coverage with name "liability"
    And its stage should be "ON_MY_PLATE" and it should have quoted premium of "321.0"
    And the submission should have account id set to "nw_account_id_123"
    When we run the synchronization for ids "5c7babf2-82a7-ee82-85df-2aafcb1b3431" and organization "36"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED" and it should have quoted premium of "321.0"
    And the submission should have account id set to "nw_account_id_123"

  Scenario: Reverse match a report
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "A Reverse . matching report mat",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "143216541A",
        "stage": "ON_MY_PLATE",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "A reverse matching report match",
        "received_date": "2023-02-06T12:25:17",
        "contractor_submission_type": "PRACTICE",
        "project_insurance_type": "OWNERS_INTEREST",
        "primary_state": "NY",
        "policy_status": "Active",
        "policy_expiration_date": "2024-02-06T00:00:00"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b"
    When we run the synchronization for ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we schedule to resync sync requests with ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b"
    When we run the synchronization for ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    Then no handlers should have run
    When we GET the matched submission
    And we assign the underwriter with email "<EMAIL>"
    And we PATCH the matched submission
    """
    {
      "proposed_effective_date": "2022-06-05T12:25:17",
      "received_date": "2022-02-06T12:25:17"
    }
    """
    And we PATCH the coverage of the matched submission
    """
    {
      "quoted_premium": 3212222,
      "bound_premium": 110000
    }
    """
    When we schedule to resync sync requests with ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b"
    And we run the synchronization for ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the following handlers should have run: "SUBMISSION_HANDLER,COVERAGE_HANDLER"
    When we GET the matched submission
    Then one of the underwrites should have an email "<EMAIL>" with source "MANUAL"
    And one of the coverages should have name: "liability", type: "PRIMARY", quoted_premium: "3212", bound_premium: "43422", limit: "null", attachment_point: "null"
    When we PATCH the matched submission
    """
    {
      "contractor_submission_type": "PROJECT",
      "project_insurance_type": "WRAP_UP"
    }
    """
    When we schedule to resync sync requests with ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b"
    And we run the synchronization for ids "5fec5437-a9c6-798e-0a62-7dc88c76fb3b" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the following handlers should have run: "SUBMISSION_HANDLER"
    When we GET the matched submission
    Then it should have the following values
    """
    {
      "contractor_submission_type": "PRACTICE",
      "project_insurance_type": "OWNERS_INTEREST",
      "primary_state": "NY",
      "policy_status": "Active",
      "policy_expiration_date": "2024-02-06T00:00:00"
    }
    """


  Scenario: Filter matched reports by brokerage and broker
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Report to be matched by brokerage and broker",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Charles Xavier",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "Report to be matched by brokerage and broker",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Erik Lehnsherr",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "Report to be matched by brokerage and broker",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Dark Batman",
      "brokerage": "AmWINS"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S2512653",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Report to be matched by brokerage and broker",
        "received_date": "2023-02-05T12:25:17",
        "broker": "Charles Xavier",
        "brokerage": "RT Specialty"
      },
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S2512636",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Report to be matched by brokerage and broker",
        "received_date": "2023-02-05T12:25:17",
        "broker": "Erik Lehnsher",
        "brokerage": "RT Specialty"
      }
    ]
    """
    Then we should receive "2" request(s) with ids "d4d7de13-6952-36c1-5f82-c4f2efc49ea8,efdc5405-d9e0-39ca-8b59-0e439a7b6fd4"
    When we run the synchronization for ids "d4d7de13-6952-36c1-5f82-c4f2efc49ea8" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    # spaces seem to be problematic
    Then the submission's broker name should be "Charles_Xavier"
    When we run the synchronization for ids "efdc5405-d9e0-39ca-8b59-0e439a7b6fd4" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    # spaces seem to be problematic
    Then the submission's broker name should be "Erik_Lehnsherr"

  Scenario: We should return multiple matches for arch
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Report that should be multiple matches",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "Report that should be multiple matches",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "S1321121",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "Report that should be multiple matches",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "c197a28e-4a77-8567-0072-8079c909445c"
    When we run the synchronization for ids "c197a28e-4a77-8567-0072-8079c909445c" and organization "10"
    Then we should "1" response with matching result type "MULTIPLE_MATCHES" without synchronization errors

  Scenario: The broker and brokerage should affect the matching
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Report that should be matched by name and by broker and brokerage",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Peter Parker",
      "brokerage": "The Daily Bugle"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "Should be matched by name and by broker",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Clark Kent",
      "brokerage": "Daily Planet"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "S2721121",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "broker": "Peter Parker",
        "brokerage": "The Daily Bugle",
        "submission_name": "Should be matched by name and by broker",
        "received_date": "2023-02-05T12:25:17"
      },
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "S2721122",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "broker": "Clark Kent",
        "brokerage": "Daily Planet",
        "submission_name": "Should be matched by name and by broker",
        "received_date": "2023-02-05T12:25:17"
      },
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "S2754781",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "broker": "Clark Kent",
        "brokerage": "Daily Planet",
        "submission_name": "Sync request that will be deleted",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "3" request(s) with ids "4cf5a8aa-b5fb-14f0-86d7-add7165f52c7,2f4baf98-907f-a97d-60df-a777bf35eb08,c457d491-6153-453c-1e0b-79e93c7e96c8"
    When we run the synchronization for ids "4cf5a8aa-b5fb-14f0-86d7-add7165f52c7" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    # this should be true even if the second report has the same name as the sync request
    Then the submission's broker name should be "Peter_Parker"
    When we run the synchronization for ids "2f4baf98-907f-a97d-60df-a777bf35eb08" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission's broker name should be "Clark_Kent"
    When we delete the sync requests with client_ids "S2754781" from organization with id "10"
    Then the server should respond with the HTTP status code "202"
    When we run the synchronization for ids "c457d491-6153-453c-1e0b-79e93c7e96c8" and organization "10"
    Then we should "1" response with matching result type "DELETED" without synchronization errors

  Scenario: Reports in clearing that should be skipped
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "[ARCH CLEARING]Report that is cleared and should be skipped",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Charles Xavier",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "S3251235",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "Report that is cleared and should be skipped",
        "received_date": "2023-02-06T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "af676fb7-0a01-e0dd-1b83-3c5081f32771"
    When we run the synchronization for ids "af676fb7-0a01-e0dd-1b83-3c5081f32771" and organization "10"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors

  @skip
  Scenario: Load and run sync for admiral
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360213ca2f4d" that belongs to organization with id "49"
    Given we have a user with email "<EMAIL>" and external id "auth0|321ad8ac03f3360113ca2f4d" that belongs to organization with id "49"
    Given we have a user with email "<EMAIL>" and external id "auth0|123ad8ac03f3360113ca2f4d" that belongs to organization with id "49"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Admiral report that will be matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Charles Xavier",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "Another Admiral report that is going to be matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Charles Xavier",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "16" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "3251235",
        "stage": "QUOTED",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Admiral report that will be matched",
        "received_date": "2023-02-06T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": null
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "6eeb60f5-ca61-2984-8081-95a3d30b9e40"
    When we load synchronization requests with updated received_date "14" days back
    """
    [
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "6651235",
        "stage": "QUOTED",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Another Admiral report that is going to be matched",
        "received_date": "2023-02-06T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": null
          },
          {
            "coverage_name": "business",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": null
          }
        ]
      },
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "6651235",
        "stage": "QUOTED",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Another Admiral report that is going to be matched",
        "received_date": "2023-02-06T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 3212,
            "bound_premium": null
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "ea1beb99-b3ba-2533-3fb4-059dbfae726f"
    When we run the synchronization for ids "6eeb60f5-ca61-2984-8081-95a3d30b9e40" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverages
    And one of the coverages should have name: "liability", type: "PRIMARY", quoted_premium: "3212", bound_premium: "null", limit: "null", attachment_point: "null"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "3251235",
        "stage": "QUOTED_BOUND",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Admiral report that will be matched",
        "received_date": "2023-02-06T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": null,
            "bound_premium": 8932
          },
          {
            "coverage_name": "businessAuto",
            "coverage_type": "PRIMARY",
            "quoted_premium": null,
            "bound_premium": 345
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "6eeb60f5-ca61-2984-8081-95a3d30b9e40"
    When we run the synchronization for ids "6eeb60f5-ca61-2984-8081-95a3d30b9e40" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "2" coverages
    And one of the coverages should have name: "liability", type: "EXCESS", quoted_premium: "null", bound_premium: "8932", limit: "null", attachment_point: "null"
    And one of the coverages should have name: "businessAuto", type: "PRIMARY", quoted_premium: "null", bound_premium: "345", limit: "null", attachment_point: "null"
    When we run the synchronization for ids "ea1beb99-b3ba-2533-3fb4-059dbfae726f" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverages
    And one of the coverages should have name: "liability", type: "EXCESS", quoted_premium: "3212", bound_premium: "null", limit: "null", attachment_point: "null"

  Scenario: Load sync requests with direct match only
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360213ca2f4d" that belongs to organization with id "49"
    Given we have a user with email "<EMAIL>" and external id "auth0|321ad8ac03f3360113ca2f4d" that belongs to organization with id "49"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    And we have the coverages for organization "49"
    When we POST report
    """
    {
      "name": "A new Report that will not be matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    And we append requested coverage with coverage name: "businessAuto", coverage type: "PRIMARY", limit: "5000"
    And we append requested coverage with coverage name: "liability", coverage type: "PRIMARY", limit: "5000"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "A5193721",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for admiral",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "A new Report that will not be matched",
        "received_date": "2023-02-05T12:25:17",
        "direct_match_only": true,
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "3677bfdd-835b-4d05-55b7-2ad496501b55"
    When we run the synchronization for ids "3677bfdd-835b-4d05-55b7-2ad496501b55" and organization "49"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors
    When we add client submission ID "A5193721"
    When we run the synchronization for ids "3677bfdd-835b-4d05-55b7-2ad496501b55" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then its stage should be "QUOTED" and it should have quoted premium of "432132.0"
    And the submission should have "1" coverages
    And one of the coverages should have name: "liability", type: "PRIMARY", quoted_premium: "432132", bound_premium: "3212312", limit: "5000", attachment_point: "null"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": null,
        "quoted_premium": 432132,
        "policy_number": "A5EOY4001",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for admiral",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "A new Report that will not be matched",
        "received_date": "2023-02-05T12:25:17",
        "direct_match_only": false,
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "36db77fb-7cd1-e051-449e-e049105fb25c"
    When we run the synchronization for ids "36db77fb-7cd1-e051-449e-e049105fb25c" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "True" and is_shell "False"
    When we GET the matched submission
    And one of the underwrites should have an email "<EMAIL>"


  @skip
  Scenario: Load sync requests and match submissions before the cutoff date. First should match, second should be a shell
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|66bc7088f72538f51da5e4c3" that belongs to organization with id "58"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360213ca2f4d" that belongs to organization with id "49"
    Given we have a user with email "<EMAIL>" and external id "auth0|321ad8ac03f3360113ca2f2d" that belongs to organization with id "58"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    And we have the coverages for organization "58"
    When we POST report
    """
    {
      "name": "An old report to be matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we set created date of the submission to "2023-02-02T00:00:00"
    And we verify the submission
    And we set the stage of the submission to be "INDICATED"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": null,
        "quoted_premium": 432132,
        "policy_number": "04S1EE008",
        "stage": "ON_MY_PLATE",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for conifer",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "An old report to be matched",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false
      }
    ]
    """
    Then we should receive "1" request(s) with ids "39872a54-a18d-6705-015b-1194bee620a3"
    When we run the synchronization for ids "39872a54-a18d-6705-015b-1194bee620a3" and organization "58"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "False"
    When we GET the matched submission
    Then its stage should be "INDICATED" and it should have quoted premium of "432132.0"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "05AM7D001",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for conifer",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "An old report to be matched",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false
      }
    ]
    """
    Then we should receive "1" request(s) with ids "ff377f43-8c64-d25e-0a6a-4724bc88103b"
    When we run the synchronization for ids "ff377f43-8c64-d25e-0a6a-4724bc88103b" and organization "58"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"

  Scenario: Load sync requests for Bowhead and match them by business name
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03ff360213ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|3774d8ac03f3360113ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|3774d8ac03cba60113ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    And we have the coverages for organization "54"
    When we POST report
    """
    {
      "name": "A bowhead report to be matched",
      "user_email": "<EMAIL>",
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we set created date of the submission to "2023-02-02T00:00:00"
    And we add submission businesses
    """
    [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Neptunes Parlour", "requested_address": "NYC", "named_insured": "FIRST_NAMED_INSURED"}
    ]
    """
    And we assign the underwriter with email "<EMAIL>"
    And we assign the underwriter with email "<EMAIL>"
    And we verify the submission
    When we load synchronization requests
    """
    [
      {
        "organization_id": 54,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "01534254",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for bowhead",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "Neptunes Parlour",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false,
        "matcher_data": {
          "named_insured": "Neptunes Parlour",
          "named_insured_address": "14 W Martin St, Raleigh, NORTH CAROLINA 27601",
          "additional_data": {
            "address_line_1": "14 W Martin St",
            "city": "Raleigh",
            "state": "NC",
            "postal_code": "27601"
          }
        }
      }
    ]
    """
    Then we should receive "1" request(s) with ids "856c72a7-ec2a-9cc8-d5b9-5abf849fb5b1"
    When we run the synchronization for ids "856c72a7-ec2a-9cc8-d5b9-5abf849fb5b1" and organization "54"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "False"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>" with source "SYNC"
    When we GET the premises of the submission
    Then the submission should have "1" premises
    And one of the premises should have the following properties
    """
    {
      "address": "14 W Martin St, Raleigh, NORTH CAROLINA 27601",
      "named_insured": "Neptunes Parlour",
      "organization_id": 54,
      "submission_premises_type": "SYNC",
      "additional_data": {
        "address_line_1": "14 W Martin St",
        "city": "Raleigh",
        "state": "NC",
        "postal_code": "27601"
      }
    }
    """

  @skip
  Scenario: Load sync requests for Admiral with different received date that should be coverage splits
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360213ca2f4d" that belongs to organization with id "49"
    Given we have a user with email "<EMAIL>" and external id "auth0|321ad8ac03f3360113ca2f4d" that belongs to organization with id "49"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    And we have the coverages for organization "49"
    When we POST report
    """
    {
      "name": "Admiral report to be matched and split",
      "user_email": "<EMAIL>",
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we set created date of the submission to "2023-07-02T00:00:00"
    And we add submission businesses
    """
    [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Neptunes Parlour", "requested_address": "NYC", "named_insured": "FIRST_NAMED_INSURED"}
    ]
    """
    And we verify the submission
    When we load synchronization requests
    """
    [
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "05CPLC001",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for admiral",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Admiral report to be matched and split",
        "received_date": "2023-07-02T00:00:00",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 20000
          }
        ],
        "broker": "Charles Xavier",
        "brokerage": "RT Specialty"
      },
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "A5CPLC001",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for admiral",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Admiral report to be matched and split",
        "received_date": "2023-08-02T00:00:00",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 20000,
            "bound_premium": 10000,
            "limit": 1000,
            "attachment_point": 2000
          }
        ],
        "broker": "Charles Xavier",
        "brokerage": "RT Specialty"
      },
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "B5CPLC001",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for admiral",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Admiral report to be matched and split",
        "received_date": "2023-08-02T00:00:00",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 20000,
            "limit": 1000,
            "attachment_point": 2000
          }
        ],
        "broker": "Wade Wilson",
        "brokerage": "AMwins"
      }
    ]
    """
    Then we should receive "3" request(s) with ids "03adc721-724e-10fb-b55f-eab296ae2430,53cd06b1-f1c4-c63e-e347-665e7293b51b,4f74872a-6dff-c4a4-2bed-d719a139e6c4"
    When we run the synchronization for ids "03adc721-724e-10fb-b55f-eab296ae2430" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "False"
    When we run the synchronization for ids "53cd06b1-f1c4-c63e-e347-665e7293b51b" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "True" and is_shell "False"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverages
    And one of the coverages should have name: "liability", type: "EXCESS", quoted_premium: "20000", bound_premium: "10000", limit: "1000", attachment_point: "2000"
    When we run the synchronization for ids "4f74872a-6dff-c4a4-2bed-d719a139e6c4" and organization "49"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"

  Scenario: Load sync for NW and match them by client_id
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|332df8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|578ab8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "Too old to be matched",
        "user_email": "<EMAIL>",
        "businesses": [],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we verify the submission
    And we update created date of the submission to be older than "10950" days
    And we add client submission ID "53213543A"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 6,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "53213543A",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Too old to be matched",
        "received_date": "2023-01-05T12:25:17"
      },
      {
        "organization_id": 6,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 20000,
        "policy_number": "53213543A",
        "coverage_name": "liability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 10000,
        "submission_name": "Too old to be matched",
        "received_date": "2023-01-05T12:25:17",
        "client_stage_id": 59
      }
    ]
    """
    Then we should receive "1" request(s) with ids "400ed859-be8f-bcfe-4631-708790c90f18"
    When we run the synchronization for ids "400ed859-be8f-bcfe-4631-708790c90f18" and organization "6"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    And the submission changes result before the sync should have "0" coverages and "1" coverages after the sync
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED_BOUND" and it should have quoted premium of "20000"

  Scenario: We should match a renewal to a submission created at a later date
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we load synchronization requests with updated received_date "180" days back and effective_date "61" days ahead
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S74628129",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "is_renewal": true,
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Report that will be later matched by Arch",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "38f77cd2-492a-7385-3f70-d5783fa3f807"
    When we run the synchronization for ids "38f77cd2-492a-7385-3f70-d5783fa3f807" and organization "10"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors
    When we load synchronization requests with updated received_date "180" days back and effective_date "59" days ahead
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S74628129",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "is_renewal": true,
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Report that will be later matched by Arch",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "38f77cd2-492a-7385-3f70-d5783fa3f807"
    When we run the synchronization for ids "38f77cd2-492a-7385-3f70-d5783fa3f807" and organization "10"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    When we POST report
    """
    {
      "name": "Report that will be later matched by Arch",
      "user_email": "<EMAIL>",
      "broker": "Charles Xavier",
      "brokerage": "RT Specialty"
    }
    """
    And we add submission businesses
    """
    [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Neptunes Parlour", "requested_address": "NYC", "named_insured": "FIRST_NAMED_INSURED"}
    ]
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "180" days back and effective_date "59" days ahead
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 4321222,
        "policy_number": "S74628129",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "is_renewal": true,
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "Report that will be later matched by Arch",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "38f77cd2-492a-7385-3f70-d5783fa3f807"
    When we run the synchronization for ids "38f77cd2-492a-7385-3f70-d5783fa3f807" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "False"
    And the previously matched submission should be deleted
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED_BOUND" and it should have quoted premium of "4321222.0"

  Scenario: Unmatched sync requests should contain close matches
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "First report that is going to be closely matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "Second report that is going to be closely matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "Third report that is going to be closely matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 3212,
        "policy_number": "54612355A",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 43422,
        "submission_name": "report to be closely matched",
        "received_date": "2023-02-06T12:25:17",
        "bound_date": "2022-04-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "ad8cf114-a1fb-6e19-7c01-5ff4e7ef74f0"
    When we run the synchronization for ids "ad8cf114-a1fb-6e19-7c01-5ff4e7ef74f0" and organization "36"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors
    And the response should contain "3" close matches

  Scenario: We should not run relation handler if not needed
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "New report that will not be matched, and it is going to be a renewal",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST a related report
    """
    {
      "name": "Old report that should not be matched, but it is going to be renewed",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty",
      "client_id": "*********",
      "primary_naics_code": "NAICS_331222",
      "relation": {
        "type": "RENEWAL",
        "confidence": 0.7,
        "source": "AUTO",
        "is_active": false
      }
    }
    """
    When we POST a related report
    """
    {
      "name": "Another Old report that should not be matched, but it is going to be renewed",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty",
      "relation": {
        "type": "RENEWAL",
        "confidence": 1.0,
        "source": "AUTO",
        "is_active": true
      }
    }
    """
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "*********",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "New report that will not be matched, and it is going to be a renewal",
        "received_date": "2023-02-05T12:25:17",
        "client_id_being_renewed": "*********"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "152cb0f4-e9b8-9570-a245-87349546b95a"
    When we run the synchronization for ids "152cb0f4-e9b8-9570-a245-87349546b95a" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the following handlers should have run: "SUBMISSION_HANDLER,UNDERWRITER_HANDLER,COVERAGE_HANDLER,RENEWAL_RELATION_HANDLER"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "QUOTED" and it should have quoted premium of "43212.0"
    And the submission should have the following properties
    """
    {
      "primary_naics_code": "NAICS_331222",
      "is_naics_verified": true
    }
    """
    When we GET the "RENEWAL" relations for the matched submission
    Then we should receive "2" relation(s)
    And the active relation should have confidence "1.0" and source "SYNC"
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "*********",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "New report that will not be matched, and it is going to be a renewal",
        "received_date": "2023-02-05T12:25:17",
        "client_id_being_renewed": "*********"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "152cb0f4-e9b8-9570-a245-87349546b95a"
    When we run the synchronization for ids "152cb0f4-e9b8-9570-a245-87349546b95a" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the following handlers should have run: "SUBMISSION_HANDLER"

  Scenario: We should match a standout submission
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "CONSIGLI CONSTRUCTION CO INC, PROJECT: THE APOLLO THEATER FOUNDATION PROJECT",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we POST report
    """
    {
      "name": "ELECTRONIC ARTS INC.",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "2" days back
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43212,
        "policy_number": "S4652356",
        "stage": "QUOTED",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 32123,
        "submission_name": "The Apollo Theater Foundation",
        "received_date": "2023-02-05T12:25:17"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "6fff487d-c2d7-471a-c9b6-8866448ccb4e"
    When we run the synchronization for ids "6fff487d-c2d7-471a-c9b6-8866448ccb4e" and organization "10"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should have the following properties
    """
    {
      "name": "CONSIGLI CONSTRUCTION CO INC, PROJECT: THE APOLLO THEATER FOUNDATION PROJECT"
    }
    """
    And its stage should be "QUOTED" and it should have quoted premium of "43212.0"

  Scenario: Load sync requests for NW ML and sync the submission
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|414ad8ac03f3360113ca2f4d" that belongs to organization with id "57"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "Too old to be matched for NW ML organization",
        "user_email": "<EMAIL>",
        "businesses": [
        {"requested_name": "Pizza", "requested_address": "NYC"},
        {"requested_name": "Spaghetti", "requested_address": "NYC"}],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we update created date of the submission to be older than "10950" days
    And we verify the submission
    When we load synchronization requests
    """
    [
      {
        "organization_id": 57,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 321,
        "policy_number": "32105681",
        "stage": "QUOTED_BOUND",
        "coverage_name": "fiduciaryLiability",
        "coverage_type": "EXCESS",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Too old to be matched for NW ML organization",
        "received_date": "2023-01-05T00:00:00",
        "premium": 1000,
        "expired_premium": 500
      }
    ]
    """
    Then we should receive "1" request(s) with ids "c1ec2dac-c2c9-2059-60a5-2e31bcf61277"
    When we run the synchronization for ids "c1ec2dac-c2c9-2059-60a5-2e31bcf61277" and organization "57"
    Then we should "1" response with matching result type "UNMATCHED" without synchronization errors

  @skip
  Scenario: Load sync requests for Bishop-Conifer and sync the submission
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|66bc7088f72538f51da5e4c4" that belongs to organization with id "58"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|414ad8ac03f3360113ca2f4d" that belongs to organization with id "57"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "Conifer report to be matched",
        "user_email": "<EMAIL>",
        "businesses": [
        {"requested_name": "Pizza", "requested_address": "NYC"},
        {"requested_name": "Spaghetti", "requested_address": "NYC"}],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we update created date of the submission to be older than "2" days
    And we set the following report properties
    """
    {
      "email_subject": "FW: Conifer report to be matched QUOTE1234"
    }
    """
    And we verify the submission
    When we load synchronization requests with updated received_date "2" days back
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": null,
        "quoted_premium": 321,
        "policy_number": "derived-12345678",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liquorLiability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Report to be matched",
        "received_date": "2023-01-05T00:00:00",
        "premium": 1000,
        "expired_premium": 500,
        "sync_identifiers": {
          "quote_numbers": ["QUOTE1234", "QUOTE1235"],
          "policy_numbers": ["POLICY1234", "POLICY1235"]
        }
      }
    ]
    """
    Then we should receive "1" request(s) with ids "6ddd7cf7-9eeb-754a-86f0-eedc017150cb"
    When we run the synchronization for ids "6ddd7cf7-9eeb-754a-86f0-eedc017150cb" and organization "58"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we load synchronization requests with updated received_date "2" days back
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": null,
        "quoted_premium": 321,
        "policy_number": "derived-12345678",
        "stage": "QUOTED_BOUND",
        "coverage_name": "liquorLiability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Report to be matched",
        "received_date": "2023-01-05T00:00:00",
        "premium": 1000,
        "expired_premium": 500,
        "sync_identifiers": {
          "quote_numbers": ["QUOTE1234", "QUOTE1236"],
          "policy_numbers": ["POLICY1234", "POLICY1235"]
        }
      }
    ]
    """
    Then we should receive "1" request(s) with ids "6ddd7cf7-9eeb-754a-86f0-eedc017150cb"
    When we run the synchronization for ids "6ddd7cf7-9eeb-754a-86f0-eedc017150cb" and organization "58"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should have the following properties
    """
    {
      "name": "Conifer report to be matched"
    }
    """
    And its stage should be "QUOTED_BOUND" and it should have quoted premium of "321.0"
    And the submission should have the following identifiers
    """
    {
      "quote_number": "QUOTE1234, QUOTE1235, QUOTE1236",
      "policy_number": "POLICY1234, POLICY1235"
    }
    """

  @skip
  Scenario: Load sync requests for Bishop-Conifer and sync the requests with different coverages
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|66bc7088f72538f51da5e4c4" that belongs to organization with id "58"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|414ad8ac03f3360113ca2f4d" that belongs to organization with id "57"
    Given we have set organization groups "Hospitality,Main" for organization with id "58"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
      """
      {
        "name": "Another Bishop-Conifer report to be matched",
        "user_email": "<EMAIL>",
        "businesses": [
        {"requested_name": "Pizza", "requested_address": "NYC"},
        {"requested_name": "Spaghetti", "requested_address": "NYC"}],
        "broker": "Amazing Spiderman",
        "brokerage": "RT Specialty"
      }
      """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "2" days back
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": null,
        "policy_number": "derived-11111111",
        "stage": "QUOTED_BOUND",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Another Bishop-Conifer report to be matched",
        "received_date": "2023-01-05T00:00:00",
        "coverages": [
          {
            "coverage_name": "property",
            "coverage_type": "EXCESS",
            "quoted_premium": 1000.0,
            "bound_premium": 2000.0
          },
          {
            "coverage_name": "workersComp",
            "coverage_type": "PRIMARY",
            "quoted_premium": 2000.0,
            "bound_premium": 1000.0
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "0f60488f-5b4d-c20a-99f6-e983ba9af1dc"
    When we run the synchronization for ids "0f60488f-5b4d-c20a-99f6-e983ba9af1dc" and organization "58"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED_BOUND"
    }
    """
    And one of the coverages should have name: "property", type: "EXCESS", quoted_premium: "1000", bound_premium: "2000", limit: "null", attachment_point: "null"
    And one of the coverages should have name: "workersComp", type: "PRIMARY", quoted_premium: "2000", bound_premium: "1000", limit: "null", attachment_point: "null"
    When we load synchronization requests with updated received_date "2" days back
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": null,
        "policy_number": "derived-11111111",
        "stage": "QUOTED_BOUND",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Another Bishop-Conifer report to be matched",
        "received_date": "2023-01-05T00:00:00",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 3000.0,
            "bound_premium": 4000.0,
            "keep_other_coverage": true
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "0f60488f-5b4d-c20a-99f6-e983ba9af1dc"
    When we run the synchronization for ids "0f60488f-5b4d-c20a-99f6-e983ba9af1dc" and organization "58"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should have "3" coverages
    And one of the coverages should have name: "liability", type: "EXCESS", quoted_premium: "3000", bound_premium: "4000", limit: "null", attachment_point: "null"
    # this will enable us to test the coverage type change.
    # simulating a scenario where we are trying to sync a submission that already has coverages
    And we delete client submission ID "derived-11111111" for the matched submission
    When we load synchronization requests with updated received_date "2" days back
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": null,
        "policy_number": "derived-11111112",
        "stage": "QUOTED_BOUND",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Another Bishop-Conifer report to be matched",
        "received_date": "2023-01-05T00:00:00",
        "coverage_type": "PRIMARY",
        "sync_report": {
          "org_group": "Hospitality"
        }
      }
    ]
    """
    # all coverages should be primary type
    Then we should receive "1" request(s) with ids "dcd42c7e-ea1d-1155-8cac-c788a3a8cf75"
    When we run the synchronization for ids "dcd42c7e-ea1d-1155-8cac-c788a3a8cf75" and organization "58"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should have "3" coverages
    And one of the coverages should have name: "liability", type: "PRIMARY", quoted_premium: "3000", bound_premium: "4000", limit: "null", attachment_point: "null"
    And one of the coverages should have name: "property", type: "PRIMARY", quoted_premium: "1000", bound_premium: "2000", limit: "null", attachment_point: "null"
    And one of the coverages should have name: "workersComp", type: "PRIMARY", quoted_premium: "2000", bound_premium: "1000", limit: "null", attachment_point: "null"
    When we GET the matched report for the submission
    Then the report should have the following properties
    """
    {
      "org_group": "Hospitality"
    }
    """

  Scenario: Create a report and try and match it by an identifier
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360213ca2f4d" that belongs to organization with id "49"
    Given we have a user with email "<EMAIL>" and external id "auth0|321ad8ac03f3360113ca2f4d" that belongs to organization with id "49"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Admiral report that will be matched directly by an identifier (quote number)",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Charles Xavier",
      "brokerage": "RT Specialty",
      "additional_identifiers": [
        {
          "type": "quote_number",
          "identifier": "quote_ADMIRAL_1234"
        },
        {
          "type": "policy_number",
          "identifier": "policy_ADMIRAL_1234"
        }
      ]
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "2" days back
    """
    [
      {
        "organization_id": 49,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "A5FFRX001",
        "stage": "QUOTED_BOUND",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 1000,
        "submission_name": "Admiral report that will be matched directly by an identifier",
        "received_date": "2023-01-05T00:00:00",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": null
          }
        ],
        "sync_identifiers": {
          "quote_numbers": ["QUOTE_ADMIRAL_1234"],
          "policy_numbers": ["POLICY_ADMIRAL_1234"]
        }
      }
    ]
    """
    Then we should receive "1" request(s) with ids "53ba0b33-2603-a3bd-d8a6-90b52dbfb565"
    When we run the synchronization for ids "53ba0b33-2603-a3bd-d8a6-90b52dbfb565" and organization "49"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matcher type should be "SUBMISSION_IDENTIFIER_MATCHER"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"

  Scenario: Load sync requests for test organization and test multiple features
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|66bc7088f72538f51da5e4c4" that belongs to organization with id "58"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|414ad8ac03f3360113ca2f4d" that belongs to organization with id "57"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Kalepa test report that will be matched with eff. date",
      "user_email": "<EMAIL>",
      "businesses": [
        {"requested_name": "Pizza", "requested_address": "NYC"},
        {"requested_name": "Spaghetti", "requested_address": "NYC"}
      ],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty",
      "proposed_effective_date": "2020-01-01"
    }
    """
    And we verify the submission
    When we load synchronization requests
    """
    [
      {
        "organization_id": 3,
        "declined_date": null,
        "underwriter_email": null,
        "policy_number": "kalepa_test_1",
        "stage": "QUOTED_BOUND",
        "source": "Copilot submissions",
        "effective_date": "2020-01-10T00:00:00",
        "submission_name": "Kalepa test report that will be matched with effective date",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 1000.0,
            "bound_premium": 2000.0
          }
        ]
      },
      {
        "organization_id": 3,
        "declined_date": null,
        "underwriter_email": null,
        "policy_number": "kalepa_test_2",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions",
        "effective_date": "2020-01-10T00:00:00",
        "submission_name": "Kalepa test report that will be matched with effective date",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 1000.0,
            "bound_premium": 2000.0
          }
        ]
      },
      {
        "organization_id": 3,
        "declined_date": null,
        "underwriter_email": null,
        "policy_number": "kalepa_test_3",
        "stage": "QUOTED",
        "source": "Copilot submissions",
        "effective_date": "2021-01-10T00:00:00",
        "submission_name": "Kalepa test report for which we will create a shell",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 1000.0,
            "bound_premium": 2000.0
          }
        ]
      }
    ]
    """
    Then we should receive "3" request(s) with ids "fe6e410d-a27d-74c5-ca1d-e003c1bbe2ad,c1e4e5fd-0cec-5306-75d1-b9886b52261d,0051926e-ae69-0135-eed3-2f78aa51e546"
    When we run the synchronization for ids "fe6e410d-a27d-74c5-ca1d-e003c1bbe2ad" and organization "3"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED_BOUND"
    }
    """
    When we run the synchronization for ids "c1e4e5fd-0cec-5306-75d1-b9886b52261d" and organization "3"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matcher type should be "RELATED_POLICY_MATCHER"
    And the matching result should have is_duplicate "True" and is_shell "False"
    When we GET the matched submission
    Then the submission should have the following properties
    """
    {
      "stage": "ON_MY_PLATE"
    }
    """
    When we run the synchronization for ids "0051926e-ae69-0135-eed3-2f78aa51e546" and organization "3"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then the submission should have the following properties
    """
    {
      "stage": "QUOTED"
    }
    """

  Scenario: Matching collisions should be handled with matching sync run
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|50aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|40aad8ac03f3360113ca2f4d" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|414ad8ac03f3360113ca2f4d" that belongs to organization with id "57"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "A report that should be matched because it is Nikola's awesome boat company",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "Mistake",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "A report that shouldn't be matched because it isn't Nikola's awesome boat company",
        "received_date": "2023-02-05T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312
          }
        ],
        "quoted_date": "2022-02-05T12:25:17",
        "first_seen": "2022-01-01T12:25:17"
      },
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "Coverage split 1",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "A report that should be matched because it is Nikola's awesome boat company",
        "received_date": "2023-02-05T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312
          }
        ],
        "quoted_date": "2022-02-05T12:25:17",
        "first_seen": "2022-01-01T12:25:17"
      },
      {
        "organization_id": 36,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "Coverage split 2",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "A report that should be matched because it is Nikola's awesome boat company",
        "received_date": "2023-02-05T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312
          }
        ],
        "quoted_date": "2022-02-05T12:25:17",
        "first_seen": "2022-01-01T12:25:17"
      }
    ]
    """
    Then we should receive "3" request(s) with ids "475315d3-fec3-0763-6195-ca90f0dfc4de,478560b5-a856-a37b-5962-fda10dc7dc1c,06aba95e-d53c-c1c6-a693-51e167c66996"
    When we run the synchronization for ids "475315d3-fec3-0763-6195-ca90f0dfc4de" and organization "36"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    When we GET the matched submission
    # it should be an incorrect match
    Then the submission should have the following properties
    """
    {
      "name": "A report that should be matched because it is Nikola's awesome boat company"
    }
    """
    When we remove the client submission ID "Mistake" for the matched submission
    And we run the synchronization for ids "475315d3-fec3-0763-6195-ca90f0dfc4de,478560b5-a856-a37b-5962-fda10dc7dc1c,06aba95e-d53c-c1c6-a693-51e167c66996" and organization "36"
    Then we should receive "3" responses
    """
    {
      "475315d3-fec3-0763-6195-ca90f0dfc4de": {
        "matching_error": false,
        "synchronization_error": false,
        "match_result": {
          "match_type": "UNMATCHED"
        }
      },
      "478560b5-a856-a37b-5962-fda10dc7dc1c": {
        "matching_error": false,
        "synchronization_error": false,
        "match_result": {
          "match_type": "EXACT_MATCH"
        }
      },
      "06aba95e-d53c-c1c6-a693-51e167c66996": {
        "matching_error": false,
        "synchronization_error": false,
        "match_result": {
          "match_type": "EXACT_MATCH"
        }
      }
    }
    """

  Scenario: Running the sync for Paragon
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|6435b2bfabbd8c3c88df564a" that belongs to organization with id "37"
    Given we have a user with email "<EMAIL>" and external id "auth0|111128ac03f3360113ca2f4d" that belongs to organization with id "37"
    Given we have a successfully authenticated User "<EMAIL>" with internal id
    And we have an API client
    Given "<EMAIL>" is part of user group "PSP E3" for organization with id "37"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Paragon report that is already synced and should not be matched",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we add client submission ID "111111111"
    And we verify the submission
    When we POST report
    """
    {
      "name": "Another Paragon that should not be matched with json client id",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we add client submission ID
    """
    {"ims_insured_guid": "806a2bfe-a396-4bc3-a90b-5345e48f70ae", "ims_submission_id": "9a6dd1ad-cd89-47dd-8ea4-7a18106ab928", "ims_quote_guids": {"Workers Compensation": "323e658f-1e78-4b08-948e-a7d6f7574410"}, "ims_control_numbers": {"Workers Compensation": 111111112}}
    """
    And we verify the submission
    When we POST report
    """
    {
      "name": "Another Paragon that should not be matched with json client id that is renewing 111111112",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Pizza", "requested_address": "NYC"},
      {"requested_name": "Spaghetti", "requested_address": "NYC"}],
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we update created date of the submission to be older than "2" days
    And we add client submission ID
    """
    {"ims_insured_guid": "806a2bfe-a396-4bc3-a90b-5345e48f70ae", "ims_submission_id": "9a6dd1ad-cd89-47dd-8ea4-7a18106ab928", "ims_quote_guids": {"Workers Compensation": "323e658f-1e78-4b08-948e-a7d6f7574410"}, "ims_control_numbers": {"Workers Compensation": 111111113}}
    """
    And we verify the submission
    When we load synchronization requests with updated received_date "0" days back
    """
    [
      {
        "organization_id": 37,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "111111111",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions",
        "effective_date": "2025-06-05T12:25:17",
        "submission_name": "Matched by client id",
        "received_date": "2023-02-05T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312
          }
        ],
        "quoted_date": "2022-02-05T12:25:17",
        "first_seen": "2022-01-01T12:25:17"
      },
      {
        "organization_id": 37,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "111111113",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions",
        "effective_date": "2025-06-05T12:25:17",
        "submission_name": "Matched by client id",
        "received_date": "2023-02-05T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312
          }
        ],
        "quoted_date": "2022-02-05T12:25:17",
        "first_seen": "2022-01-01T12:25:17",
        "client_id_being_renewed": "111111112"
      },
      {
        "organization_id": 37,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "111111114",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions",
        "effective_date": "2025-06-05T12:25:17",
        "submission_name": "Should be a sync shell",
        "received_date": "2023-02-05T12:25:17",
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "PRIMARY",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312
          }
        ],
        "quoted_date": "2022-02-05T12:25:17",
        "first_seen": "2022-01-01T12:25:17",
        "sync_report": {
          "org_group": "PARAGON_PSP_E3"
        }
      }
    ]
    """
    Then we should receive "3" request(s) with ids "72a0b78d-29b2-bedc-dd89-6eef6a1d5537,8c9366c2-5d6d-8258-92b8-369aacb6447f,c34c26c3-7050-ccdc-0e5d-80d367731bfa"
    When we run the synchronization for ids "72a0b78d-29b2-bedc-dd89-6eef6a1d5537" and organization "37"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And no handlers should have run
    When we run the synchronization for ids "8c9366c2-5d6d-8258-92b8-369aacb6447f" and organization "37"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    # only the renewal handler should have run
    And the following handlers should have run: "RENEWAL_RELATION_HANDLER"
    When we GET the matched submission
    When we GET the "RENEWAL" relations for the matched submission
    Then we should receive "1" relation(s)
    And the active relation should have confidence "1.0" and source "SYNC"
    When we run the synchronization for ids "c34c26c3-7050-ccdc-0e5d-80d367731bfa" and organization "37"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>"
    And the submission should have "1" coverage with name "liability"
    And its stage should be "ON_MY_PLATE" and it should have quoted premium of "3212.0"

  Scenario: We should create a shell for Conifer with broker and brokerage
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|66bc7088f72538f51da5e4c3" that belongs to organization with id "58"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360213ca2f4d" that belongs to organization with id "49"
    Given we have a user with email "<EMAIL>" and external id "auth0|321ad8ac03f3360113ca2f2d" that belongs to organization with id "58"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we POST report
    """
    {
      "name": "Report by Arch that shouldn't be matched, but it's purpose is to create the broker/brokerage combo",
      "user_email": "<EMAIL>",
      "businesses": [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Report", "requested_address": "NYC"}],
      "broker": "Peter Parker",
      "brokerage": "The Daily Bugle"
    }
    """
    When we load synchronization requests
    """
    [
      {
        "organization_id": 58,
        "declined_date": null,
        "underwriter_email": null,
        "quoted_premium": 432132,
        "policy_number": "000000000A",
        "stage": "ON_MY_PLATE",
        "coverage_name": "liability",
        "coverage_type": "PRIMARY",
        "source": "Copilot submissions for conifer",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "Report that will not be matched, but a shell will be created instead",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false,
        "broker": "Peter Parker",
        "brokerage": "The Daily Bugle"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "a8366aab-607c-a9c2-8b38-b6d44e4f6368"
    When we run the synchronization for ids "a8366aab-607c-a9c2-8b38-b6d44e4f6368" and organization "58"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then its stage should be "ON_MY_PLATE" and it should have quoted premium of "432132.0"
    And the submission should have the following properties
    """
    {
      "broker": {
        "name": "Peter Parker"
      },
      "brokerage": {
          "name": "The Daily Bugle"
      }
    }
    """

  Scenario: We should match a sync request and then replace it with another
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03ff360213ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|3774d8ac03f3360113ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|3774d8ac03cba60113ca2f4d" that belongs to organization with id "54"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    And we have the coverages for organization "54"
    When we POST report
    """
    {
      "name": "A Bowhead report that will be matched and then have its client id replaced",
      "user_email": "<EMAIL>",
      "broker": "Amazing Spiderman",
      "brokerage": "RT Specialty"
    }
    """
    And we set created date of the submission to "2023-02-02T00:00:00"
    And we add submission businesses
    """
    [
      {"requested_name": "Incomplete", "requested_address": "NYC"},
      {"requested_name": "Neptunes Parlour", "requested_address": "NYC", "named_insured": "FIRST_NAMED_INSURED"}
    ]
    """
    And we assign the underwriter with email "<EMAIL>"
    And we assign the underwriter with email "<EMAIL>"
    And we verify the submission
    When we load synchronization requests
    """
    [
      {
        "organization_id": 54,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "4652341",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for bowhead",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "A Bowhead report that will be matched and then have its client id replaced",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false
      }
    ]
    """
    Then we should receive "1" request(s) with ids "e9d3138c-8367-7b49-90cd-d2aef1620bab"
    When we run the synchronization for ids "e9d3138c-8367-7b49-90cd-d2aef1620bab" and organization "54"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "False"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And one of the underwrites should have an email "<EMAIL>" with source "SYNC"
    And the submission should have the following client ids
    """
    ["4652341"]
    """
    When we load synchronization requests
    """
    [
      {
        "organization_id": 54,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "Replacing-4652341",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for bowhead",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "A Bowhead report that will be matched and then have its client id replaced",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false,
        "replaces_client_id": "4652341"
      }
    ]
    """
    Then we should receive "1" request(s) with ids "96f3c285-81c9-2f5f-6dac-928a66df13d4"
    And when we GET submission_sync with id "e9d3138c-8367-7b49-90cd-d2aef1620bab" it should have the following properties
    """
    {
      "replaced_by_client_id": "Replacing-4652341"
    }
    """
    When we run the synchronization for ids "96f3c285-81c9-2f5f-6dac-928a66df13d4" and organization "54"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors

  Scenario: Load sync requests for Secura with coverage status
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|4d3ea9db63425e0eb7f98abe" that belongs to organization with id "3"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03f3360113ca2f4d" that belongs to organization with id "6"
    Given we have a user with email "<EMAIL>" and external id "auth0|643520d83d6899c16732a357" that belongs to organization with id "36"
    Given we have a user with email "<EMAIL>" and external id "auth0|60aad8ac03ff360213ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|3774d8ac03f3360113ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|3774d8ac03cba60113ca2f4d" that belongs to organization with id "54"
    Given we have a user with email "<EMAIL>" and external id "auth0|123456ac03cba60113ca2f4d" that belongs to organization with id "64"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    And we have the coverages for organization "64"
    When we load synchronization requests
    """
    [
      {
        "organization_id": 64,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "secura_id_1",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for SECURA",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Secura report that will be a shell, but will have its coverages go through stages",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false,
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312,
            "stage": "ON_MY_PLATE"
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "0e70430e-b7fc-3951-c860-9f220498f292"
    When we run the synchronization for ids "0e70430e-b7fc-3951-c860-9f220498f292" and organization "64"
    Then we should "1" response with matching result type "SHELL_SUBMISSION" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    Then the submission should be assigned to "1" underwriters
    And the submission should have one coverage with the following properties
    """
    {
      "coverage.name": "liability",
      "coverage_type": "EXCESS",
      "quoted_premium": 3212,
      "bound_premium": 43422,
      "limit": 1234,
      "attachment_point": 4312,
      "stage": "ON_MY_PLATE"
    }
    """
    When we load synchronization requests
    """
    [
      {
        "organization_id": 64,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "secura_id_1",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for SECURA",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Secura report that will be a shell, but will have its coverages go through stages",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false,
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312,
            "stage": "QUOTED_BOUND"
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "0e70430e-b7fc-3951-c860-9f220498f292"
    When we run the synchronization for ids "0e70430e-b7fc-3951-c860-9f220498f292" and organization "64"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    And the submission should have one coverage with the following properties
    """
    {
      "coverage.name": "liability",
      "coverage_type": "EXCESS",
      "quoted_premium": 3212,
      "bound_premium": 43422,
      "limit": 1234,
      "attachment_point": 4312,
      "stage": "QUOTED_BOUND"
    }
    """
    When we load synchronization requests
    """
    [
      {
        "organization_id": 64,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "policy_number": "secura_id_1",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for SECURA",
        "effective_date": "2023-06-05T12:25:17",
        "submission_name": "Secura report that will be a shell, but will have its coverages go through stages",
        "received_date": "2023-02-02T12:25:17",
        "direct_match_only": false,
        "coverages": [
          {
            "coverage_name": "liability",
            "coverage_type": "EXCESS",
            "quoted_premium": 3212,
            "bound_premium": 43422,
            "limit": 1234,
            "attachment_point": 4312,
            "stage": "QUOTED"
          }
        ]
      }
    ]
    """
    Then we should receive "1" request(s) with ids "0e70430e-b7fc-3951-c860-9f220498f292"
    When we run the synchronization for ids "0e70430e-b7fc-3951-c860-9f220498f292" and organization "64"
    Then we should "1" response with matching result type "EXACT_MATCH" without synchronization errors
    And the matching result should have is_duplicate "False" and is_shell "True"
    When we GET the matched submission
    And the submission should have one coverage with the following properties
    """
    {
      "coverage.name": "liability",
      "coverage_type": "EXCESS",
      "quoted_premium": 3212,
      "bound_premium": 43422,
      "limit": 1234,
      "attachment_point": 4312,
      "stage": "QUOTED_BOUND"
    }
    """

  Scenario: Load sync requests for Arch for which duplicates will be created
    Given we have an internal API client
    Given we have a user with email "<EMAIL>" and external id "auth0|30aad8ac03f3360113ca2f4d" that belongs to organization with id "10"
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we load synchronization requests
    """
    [
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 432132,
        "policy_number": "arch_client_id_1",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "Arch report that is going to be matched and then have duplicates created from it",
        "received_date": "2023-06-02T12:25:17",
        "direct_match_only": false
      },
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43213,
        "policy_number": "arch_client_id_2",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "Arch report that is going to be matched and then have duplicates created from it",
        "received_date": "2023-06-02T12:25:17",
        "direct_match_only": false
      },
      {
        "organization_id": 10,
        "declined_date": null,
        "underwriter_email": "<EMAIL>",
        "quoted_premium": 43213,
        "policy_number": "arch_client_id_3",
        "stage": "ON_MY_PLATE",
        "source": "Copilot submissions for Arch",
        "effective_date": "2023-06-05T12:25:17",
        "bound_premium": 3212312,
        "submission_name": "Arch report that is going to be matched and then have duplicates created from it",
        "received_date": "2023-06-02T12:25:17",
        "direct_match_only": false
      }
    ]
    """
    Then we should receive "3" request(s)
    When we run the synchronization for ids "05cee92a-dbcd-361d-d8dd-24de526ff093,de4cdea9-89c6-6f4a-291d-73c7326b2813,1baed3f0-dcc3-9acb-8348-1e32793a0e53" and organization "10"
    Then we should "3" response with different submission ids
    When we GET the matched submissions
    Then each submission should have "1" client ids

import os

from behave import then, when
from copilot_client_v3.models.file_patch import FilePatch
from static_common.enums.origin import Origin
import requests


@when('we create the submission from file "{file_name:S}" for the User "{user_email:S}" and PDS flag')
def step_impl(context, file_name, user_email):
    with open(f"tests/data/{file_name}", "rb") as f:
        file = f.read()
        files = {
            "file": (file_name, file),
            "owner_email": (None, user_email),
        }
        payload = {"pds": True, "origin": Origin.API.value}
        create_report(context, files=files, payload=payload)


@when(
    'we update file type to "{file_type:S}" and classification to "{classification:S}" and processing state to'
    ' "{processing_state:S}"'
)
def step_impl(context, file_type, classification, processing_state):
    submission = context.submission
    file_id = submission.files[0].id

    file_patch = FilePatch(processing_state=processing_state, file_type=file_type, classification=classification)
    context.file = context.client.update_file(submission_id=submission.id, id=file_id, file_patch=file_patch)


@then('file is in "{processing_state:S}" state and classification is "{classification:S}"')
def step_impl(context, processing_state, classification):
    assert context.file.processing_state == processing_state
    assert context.file.classification == classification


@then('we GET the Report for User "{user_id:d}" with report name "{report_name:S}"')
def step_impl(context, user_id, report_name):
    reports_envelope = context.client.get_reports_by_user(user_id, name=report_name)
    context.reports_envelope = reports_envelope
    assert len(reports_envelope.reports) > 0


def create_report(context, payload=None, files=None):
    headers = context.internal_client.api_client.default_headers
    url = os.environ.get("COPILOT_API_URL", "http://app:5000/api/v3.0")
    response = requests.post(url + "/reports", files=files, headers=headers, data=payload)
    assert response.status_code == 201
    response_json = response.json()
    assert len(response_json["submissions"]) > 0

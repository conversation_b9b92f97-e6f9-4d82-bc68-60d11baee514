Feature: User and Organization Settings

  Scenario: Create and update Settings for User
     Given we have a successfully authenticated User "<EMAIL>"
      And we have an API client
      When we POST Settings
      """
      {
         "user_id": 20,
         "is_map_enabled_by_default": true
      }
      """
      Then the Settings should be created
      When we POST Settings
      """
      {
         "user_id": 20,
         "is_map_enabled_by_default": true
      }
      """
      Then it should throw error "409"
      When we PATCH Settings
      """
      {
         "is_map_enabled_by_default": false
      }
      """
      Then the Settings should be updated

Feature: Submission execution events

  Scenario: GET execution events for submission
    Given we have a successfully authenticated User "<EMAIL>"
    And we have an API client
    When we GET execution events for submission with id "4ceab4ac-d108-47b4-9d62-1f9d644cc994"
    Then there should be "0" execution events
    When we POST an execution event for submission with id "4ceab4ac-d108-47b4-9d62-1f9d644cc994"
      """
      {
        "submission_id": "4ceab4ac-d108-47b4-9d62-1f9d644cc994",
        "submission_business_id": "3efe75d0-3162-459e-afa4-001fac24ee45",
        "execution_type": "RESOLVE_BUSINESSES",
        "event_type": "STARTED",
        "occurred_at": "2020-11-04T23:48:13.168042+00:00",
        "execution_id": "96e8e0e6-94b2-4855-b23a-ce1061085172"
      }
      """
    And we GET execution events for submission with id "4ceab4ac-d108-47b4-9d62-1f9d644cc994"
    Then there should be one "STARTED" execution event of type "RESOLVE_BUSINESSES" with execution id "96e8e0e6-94b2-4855-b23a-ce1061085172"

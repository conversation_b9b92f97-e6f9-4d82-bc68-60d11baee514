name: copilot_api
services:
  #############################
  ## APPLICATION SERVICES
  #############################
  app:
    image: "${NEW_IMAGE:-kalepa/copilot_api:local}"
    volumes:
      - "./:/app"
    depends_on:
      - moto-server
      - db
    ports:
      - "5000:5000"
    command:
      [
        "/bin/bash",
        "-c",
        "/app/ci/behave-entrypoint.sh"
      ]
    env_file: .env.ci
    environment:
      AWS_ENDPOINT_URL: "http://moto-server:7777"
      TESTING_SCOPE: "True"
      INCLUDE_ID_IN_GET_UPLOAD_URL: "True"
      INCLUDE_CLIENT_TYPE_AND_TAGS_IN_EXTERNAL_API: "True"


  #############################
  ## DATABASES
  #############################
  db: &db-service
    image: "${DB_IMAGE:-922426152114.dkr.ecr.us-east-1.amazonaws.com/copilot-test-db:20241019-1-warm}"
    ports:
      - "5432:5432"
    command: postgres -c max_connections=600 -c fsync=off -c full_page_writes=off -c synchronous_commit=off -c wal_buffers=262143 -c shared_buffers=131072 -c work_mem=65536 -c maintenance_work_mem=262144 -c effective_io_concurrency=12 -c wal_level=minimal -c max_wal_senders=0 -c checkpoint_timeout=60min -c max_wal_size=32GB -c min_wal_size=1024MB -c checkpoint_completion_target=0.9 -c autovacuum=off -c temp_buffers=65536 -c log_destination=stderr -c logging_collector=off -c log_statement=none -c log_min_duration_statement=-1 -c log_connections=off -c effective_cache_size=2048MB -c max_parallel_workers_per_gather=4 -c max_worker_processes=12 -c max_parallel_workers=12

  db-integration-tests:
    <<: *db-service
    ports:
      - "0.0.0.0:5435:5432"
    environment:
      POSTGRES_USER: "copilot"
      POSTGRES_PASSWORD: "copilot"
      POSTGRES_DB: "copilotapi"

  #############################
  ## TEST SERVICES
  #############################
  pytest-unit:
    image: "${NEW_IMAGE:-kalepa/copilot_api:local}"
    command:
      [
        "/bin/bash",
        "-c",
        "python -X faulthandler -m pytest --durations=15 tests/unit"
      ]
    env_file: .env.ci

  pytest-integration:
    image: "${NEW_IMAGE:-kalepa/copilot_api:local}"
    volumes:
      - "./:/app"
    depends_on:
      - db-integration-tests
    command:
      [
        "/bin/bash",
        "-c",
        "/app/ci/run-integration-tests.sh"
      ]
    env_file: .env.ci
    environment:
      DB_CONFIG_SECRET_HOST: db-integration-tests
      TEST_DB_HOST: db-integration-tests
      TEST_DB_PORT: 5432
      TEST_DB_NAME: copilotapi
      TEST_DB_USER: copilot
      TEST_DB_PASSWORD: copilot
      TEST_DB_TEMPLATE_DB: copilotapi
      SQLALCHEMY_TEST_COPILOT_DB_URI: "******************************************************/copilotapi"

  behave:
    image: "${NEW_IMAGE:-kalepa/copilot_api:local}"
    volumes:
      - "./:/app"
    depends_on:
      - app
    command:
      [
        "/bin/bash",
        "-c",
        "/app/wait-for app:5000 -t 90 -- behave"
      ]
    env_file: .env.ci
    environment:
      PYTHONPATHPREPEND: "/app/generated_clients/"
      AWS_ENDPOINT_URL: "http://moto-server:7777"
      TESTING_SCOPE: "True"

  #############################
  ## HELPER SERVICES
  #############################
  moto-server:
    image: "${NEW_IMAGE:-kalepa/copilot_api:local}"
    ports:
      - "7777:7777"
    command:
      [
        "/bin/bash",
        "-c",
        "moto_server -H 0.0.0.0 -p 7777"
      ]

  #############################
  ## MIGRATION ONE-OFFS
  #############################
  migrate-db:
    image: "${NEW_IMAGE:-kalepa/copilot_api:local}"
    depends_on:
      - db
    volumes:
      - "./:/app"
    command:
      [
        "/bin/bash",
        "-c",
        "export SKIP_DDTRACE=1 && ( find / -type d -name \"ddtrace*\" -exec rm -rf {} \\; 2>/dev/null || true ) && /app/wait-for db:5432 -t 90 -- flask db upgrade -d /app/migrations heads"
      ]
    env_file: .env.ci
    environment:
        IS_MIGRATION_ONLY_RUN: "True"

  migrate-db-integration-tests:
    image: "${NEW_IMAGE:-kalepa/copilot_api:local}"
    depends_on:
      - db-integration-tests
    volumes:
      - "./:/app"
    command:
      [
        "/bin/bash",
        "-c",
        "export SKIP_DDTRACE=1 && ( find / -type d -name \"ddtrace*\" -exec rm -rf {} \\; 2>/dev/null || true ) && /app/wait-for db-integration-tests:5432 -t 90 -- flask db upgrade -d /app/migrations heads"
      ]
    env_file: .env.ci
    environment:
        DB_CONFIG_SECRET_HOST: db-integration-tests
        DB_CONFIG_SECRET_PORT: 5432
        IS_MIGRATION_ONLY_RUN: "True"

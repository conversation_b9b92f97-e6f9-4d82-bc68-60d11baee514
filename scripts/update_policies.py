from dataclasses import dataclass
from typing import List
import csv
import logging

from copilot_client_v3 import ApiClient, Configuration, DefaultApi, Policy

logging.basicConfig(level=logging.INFO)

dev_config = {"copilot_v3": "http://copilot-api.prod.int:8080/api/v3.0"}

config = dev_config

configuration = Configuration(config["copilot_v3"])
api_client = ApiClient(configuration=configuration)
copilot_api = DefaultApi(api_client)

NW_ORGANIZATION_ID = 6


@dataclass
class UpdateRow:
    submission_id: str
    policy_id: str


def __read_update_policies() -> list[UpdateRow]:
    with open("./data/to_remove.csv") as csv_file:
        csv_reader = csv.reader(csv_file)

        rows = []
        for row in csv_reader:
            rows.append(UpdateRow(submission_id=row[0], policy_id=row[1]))

    return rows


def __read_update_policies_test() -> list[UpdateRow]:
    s = """1525ee38-fc85-41c6-9f84-82e7860479a7,104571077A
           cfead9f1-59b5-4722-a097-931e261005b2,104488904C
           c569635d-ed62-44f5-b6e7-79b32d43417d,104574681A"""

    csv_reader = csv.reader(s.splitlines())
    rows = []
    for row in csv_reader:
        rows.append(UpdateRow(submission_id=row[0].strip(), policy_id=row[1].strip()))

    return rows


def update_policies():
    rows = __read_update_policies()

    for row in rows:
        logging.info(f"Updating submissionId={row.submission_id} policyId={row.policy_id}")

        copilot_api.put_policy(
            NW_ORGANIZATION_ID,
            row.policy_id,
            policy=Policy(
                external_id=row.policy_id, organization_id=NW_ORGANIZATION_ID, submission_id=row.submission_id
            ),
        )


if __name__ == "__main__":
    update_policies()

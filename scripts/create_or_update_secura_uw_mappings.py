# manage.py (or helper_scripts.py)
from dataclasses import dataclass
from datetime import datetime
from urllib.parse import urlparse
import csv
import os
import re
import time

from llm_common.clients import LLMClient
from llm_common.models.llm_model import LLMModel
from llm_common.models.llm_request_params import GPTRequest<PERSON>arams
from openpyxl import load_workbook
from rapidfuzz import fuzz, process
from static_common.enums.organization import ExistingOrganizations
from static_common.models.openai import ChatCompletionPrompt
from static_common.schemas.openai import ChatCompletionPromptSchema
import pandas as pd
import requests

from copilot.app import app
from copilot.init.db_initializer import (
    attach_dynamic_password_provider_for_new_connections,
)
from copilot.models import Brokerage, Submission, User, db
from copilot.models.secura_uw_mappings import SecuraUWMapping


@dataclass
class SecuraRowData:
    agency_code: str
    master_code: str
    lob: str
    underwriter: str
    agency_name: str
    agency_city: str
    agency_state: str
    agency_phone: str


SECURA_ORG_ID = ExistingOrganizations.SECURA.value
SECURA_EXCEL_PATH = "/Users/<USER>/Downloads/Secura_Agency UW_Mapping 2025.xlsx"
AGENCY_CODE_INDEX = 0
MASTER_CODE_INDEX = 1
LOB_INDEX = 3
UNDERWRITER_INDEX = 4
AGENCY_NAME_INDEX = 5
AGENCY_CITY_INDEX = 6
AGENCY_STATE_INDEX = 7
AGENCY_PHONE_INDEX = 8

# For serper.dev
INPUT_EXCEL_FILE = "/Users/<USER>/Downloads/agency_domains_serper.xlsx"
OUTPUT_EXCEL_FILE = "/Users/<USER>/Downloads/agency_domains_serper_filled.xlsx"
AGENCY_NAME_COL = "Agency Name"
CITY_COL = "City"
STATE_COL = "State"
# Optional: Add phone number column if you want to include it in the search query
# PHONE_COL = 'phone number'
SERPER_API_KEY = os.environ["SERPER_API_KEY"]


def _create_or_update_secura_uw_mappings(use_brokerage_name_fuzzy_matching: bool = False):
    secura_excel_rows = _load_secura_excel()

    with app.app_context():
        attach_dynamic_password_provider_for_new_connections(db.engine)

        # Fetch existing Kalepa mappings and underwriters for Secura
        kalepa_existing_secura_mappings = (
            db.session.query(SecuraUWMapping).execution_options(bypass_filter_required=True).all()
        )
        kalepa_existing_secura_mappings_by_logical_key = {
            (m.secura_excel_lob_raw, m.secura_excel_underwriter_raw, m.secura_excel_agency_name_raw): m
            for m in kalepa_existing_secura_mappings
        }
        kalepa_existing_secura_uws = User.query.filter(User.organization_id == SECURA_ORG_ID).all()
        kalepa_existing_secura_uws_by_normalized_name = {
            _normalize_string(uw.name, remove_single_letter_middle=True): uw for uw in kalepa_existing_secura_uws
        }

        if use_brokerage_name_fuzzy_matching:
            fuzzy_matched_kalepa_brokerages = _fuzzy_match_to_kalepa_brokerages(secura_excel_rows)

        count = 0
        count_of_rows_to_process = len(secura_excel_rows)
        for row_data in secura_excel_rows:
            # Excel rows define a unique key as a triple (lob, underwriter, agency_name)
            logical_key = (row_data.lob, row_data.underwriter, row_data.agency_name)

            kalepa_db_row = _fetch_existing_or_create_new_kalepa_mapping(
                kalepa_existing_secura_mappings_by_logical_key, logical_key, row_data
            )
            # Update normalized fields regardless (to keep them fresh)
            kalepa_db_row.normalized_secura_underwriter = _normalize_string(
                row_data.underwriter, remove_single_letter_middle=True
            )
            kalepa_db_row.normalized_secura_agency_name = _normalize_string(row_data.agency_name)
            kalepa_db_row.normalized_secura_agency_phone = _normalize_string(
                row_data.agency_phone, remove_whitespaces=True
            )

            # Overwrite is_manual_override = False each time (as stated)
            kalepa_db_row.is_manual_override = False

            # If we found a matched underwriter, set its ID, else None
            matched_uw = kalepa_existing_secura_uws_by_normalized_name.get(kalepa_db_row.normalized_secura_underwriter)
            if matched_uw:
                kalepa_db_row.mapped_kalepa_user_id = matched_uw.id
            else:
                kalepa_db_row.mapped_kalepa_user_id = None

            if use_brokerage_name_fuzzy_matching:
                _fill_fuzzy_matched_brokerages(fuzzy_matched_kalepa_brokerages, kalepa_db_row, logical_key, row_data)

            count += 1
            print(f"Processed {count}/{count_of_rows_to_process} Secura excel rows...")

        db.session.commit()


def _fill_fuzzy_matched_brokerages(fuzzy_matched_kalepa_brokerages, kalepa_db_row, logical_key, row_data):
    excel_row, fuzzy_matched_kalepa_brokerage, match_score = fuzzy_matched_kalepa_brokerages[logical_key]
    # If we found a matched brokerage, set it, else None
    if fuzzy_matched_kalepa_brokerage:
        print(
            f"Matched '{row_data.agency_name}' -> Brokerage '{fuzzy_matched_kalepa_brokerage.name}' with score"
            f" {match_score}"
        )
        kalepa_db_row.mapped_kalepa_brokerage_id = fuzzy_matched_kalepa_brokerage.id
    else:
        kalepa_db_row.mapped_kalepa_brokerage_id = None


def _fuzzy_match_to_kalepa_brokerages(secura_excel_rows):
    kalepa_all_brokerages = db.session.query(Brokerage).execution_options(bypass_filter_required=True).all()

    # Build dict for RapidFuzz (normalized -> Brokerage), for speedy lookups
    kalepa_brokerages_dict = {}
    for brokerage in kalepa_all_brokerages:
        brokerage_name_normalized = _normalize_string(brokerage.name or "")
        kalepa_brokerages_dict[brokerage_name_normalized] = brokerage
        if brokerage.aliases:
            for alias in brokerage.aliases:
                brokerage_alias_normalized = _normalize_string(alias)
                kalepa_brokerages_dict[brokerage_alias_normalized] = brokerage

    all_choices = list(kalepa_brokerages_dict.keys())
    FUZZY_MATCH_THRESHOLD = 95

    # For each Secura Excel row, find a best match from all_choices being Kalepa brokerage names and aliases
    results = {}
    for row_data in secura_excel_rows:
        logical_key = (row_data.lob, row_data.underwriter, row_data.agency_name)
        row_agency_norm = _normalize_string(row_data.agency_name)
        match = process.extractOne(row_agency_norm, all_choices, scorer=fuzz.ratio, score_cutoff=FUZZY_MATCH_THRESHOLD)

        if match:
            best_choice_str, best_score, _ = match
            best_brokerage = kalepa_brokerages_dict[best_choice_str]
            results[logical_key] = (row_data, best_brokerage, best_score)
        else:
            results[logical_key] = (row_data, None, None)
    return results


def _fetch_existing_or_create_new_kalepa_mapping(kalepa_existing_secura_mappings_by_logical_key, logical_key, row_data):
    kalepa_db_row = kalepa_existing_secura_mappings_by_logical_key.get(logical_key)
    if not kalepa_db_row:
        kalepa_db_row = SecuraUWMapping(
            secura_excel_agency_code_raw=row_data.agency_code,
            secura_excel_master_code_raw=row_data.master_code,
            secura_excel_lob_raw=row_data.lob,
            secura_excel_underwriter_raw=row_data.underwriter,
            secura_excel_agency_name_raw=row_data.agency_name,
            secura_excel_agency_city_raw=row_data.agency_city,
            secura_excel_agency_state_raw=row_data.agency_state,
            secura_excel_agency_phone_raw=row_data.agency_phone,
        )
        db.session.add(kalepa_db_row)
    return kalepa_db_row


def _load_secura_excel():
    wb = load_workbook(SECURA_EXCEL_PATH)
    ws = wb.active
    secura_excel_rows = _load_secura_mappings_excel(ws)
    return secura_excel_rows


def _normalize_string(s: str, remove_whitespaces: bool = False, remove_single_letter_middle: bool = False) -> str:
    s = s.lower()
    # Remove punctuation (commas, dots, dashes, etc.)
    s = re.sub(r"[^\w\s]", "", s)
    # Replace multiple spaces with single space
    s = re.sub(r"\s+", " ", s)
    s = s.strip()

    # Optionally remove single-letter middle tokens e.g. "Marc A McVay" -> "marc mcvay"
    if remove_single_letter_middle:
        tokens = s.split(" ")
        # Only remove single-letter tokens if there are at least 3 tokens
        if len(tokens) > 2:
            # Keep the first and last token as-is,
            # remove any single-letter tokens in the middle
            first = tokens[0]
            middle = [tok for tok in tokens[1:-1] if len(tok) != 1]
            last = tokens[-1]
            tokens = [first] + middle + [last]
        s = " ".join(tokens)

    # Optionally remove all remaining whitespaces, making it a single string
    if remove_whitespaces:
        s = s.replace(" ", "")

    return s


def _load_secura_mappings_excel(excel_rows):
    rows = []
    # Row 1 is empy, row 2 is header
    for row in excel_rows.iter_rows(min_row=3, values_only=True):
        if row[LOB_INDEX] is None:
            # Some additional description rows in Excel = skip them
            continue
        agency_code = row[AGENCY_CODE_INDEX]
        master_code = row[MASTER_CODE_INDEX]
        lob = row[LOB_INDEX]
        underwriter = row[UNDERWRITER_INDEX]
        agency_name = row[AGENCY_NAME_INDEX]
        agency_city = row[AGENCY_CITY_INDEX]
        agency_state = row[AGENCY_STATE_INDEX]
        agency_phone = row[AGENCY_PHONE_INDEX]

        rows.append(
            SecuraRowData(
                agency_code, master_code, lob, underwriter, agency_name, agency_city, agency_state, agency_phone
            )
        )
    return rows


def _create_llm_prompt_for_domains(
    company_name: str, company_city: str, company_state: str, company_phone: str
) -> ChatCompletionPrompt:
    instruction = """
        I will be feeding you with US based Insurance company names. They can be abbreviated or may have some slight errors. I will also provide US state and city this company is based. And its phone.
        I need you to find me domain name of this company. BUT AND THIS IS REALLY CRITICAL - do not guess domain names. I need to you to either provide me domain name you have in your database or tell me that you don't know.

        Example:
        Company: WARBURTON INS SVCS INC
        City: TEMPE
        State: AZ
        Phone: (*************

        I expect you to provide me domain name in response in format "Domain: found_domain" or "I don't know" if you can't find it.
        Don't add anything else to the response, keep it to only 2 possible choices I provided.
        """
    user_request = f"""
    Company: {company_name}
    City: {company_city}
    State: {company_state}
    Phone: {company_phone}
    """
    prompt_sequence = {
        "messages": [{"role": "system", "content": instruction}, {"role": "user", "content": user_request}]
    }
    return ChatCompletionPromptSchema().load(prompt_sequence)


def _get_first_N_unique_secura_agencies(secura_rows, n: int = 100):
    seen = set()
    unique_agencies = []

    for row in secura_rows:
        key = (row.agency_name, row.agency_city, row.agency_state, row.agency_phone)
        if key not in seen:
            seen.add(key)
            unique_agencies.append(key)
            if n >= 0 and len(unique_agencies) == n:
                break
    return unique_agencies


def _some_fun_with_llm_domains():
    secura_excel_rows = _load_secura_excel()
    unique_agencies = _get_first_N_unique_secura_agencies(secura_excel_rows, n=-1)

    results = []

    all_count = len(unique_agencies)
    count = 0
    llm_client = LLMClient(context_params=None)
    for agency_name, agency_city, agency_state, agency_phone in unique_agencies:
        chat_prompt = _create_llm_prompt_for_domains(
            company_name=agency_name, company_city=agency_city, company_state=agency_state, company_phone=agency_phone
        )
        response = llm_client.get_llm_response(
            params=[GPTRequestParams(model=LLMModel.OPENAI_GPT_45, max_tokens=256)],
            prompt=chat_prompt,
        )
        if response.startswith("Domain: "):
            domain = response[len("Domain: ") :].strip()
        elif "I don't know" in response:
            domain = None
        else:
            domain = None

        results.append(
            {
                "Agency Name": agency_name,
                "City": agency_city,
                "State": agency_state,
                "Phone": agency_phone,
                "Domain": domain,
                "Verified": None,
            }
        )
        count += 1
        print(f"Processed {count}/{all_count} Secura excel rows...")

    df_results = pd.DataFrame(results)
    df_results.to_excel("agency_domains.xlsx", index=False)


def _get_domain_from_serper(api_key, query):
    """
    Performs a Google search using Serper API and returns the domain of the first organic result.
    """
    if not api_key:
        print("ERROR: SERPER_API_KEY not found. Set it in .env or environment variables.")
        return None

    search_url = "https://google.serper.dev/search"
    payload = {"q": query, "num": 5}  # Requesting top 5 results increases chance of finding the right one
    headers = {"X-API-KEY": api_key, "Content-Type": "application/json"}

    try:
        response = requests.post(search_url, headers=headers, json=payload, timeout=15)
        response.raise_for_status()
        results = response.json()

        # Try to find the most relevant domain from organic results
        if "organic" in results and len(results["organic"]) > 0:
            for result in results["organic"]:
                if "link" in result:
                    try:
                        parsed_url = urlparse(result["link"])
                        domain = parsed_url.netloc
                        # Basic cleaning: remove 'www.' if present
                        if domain.startswith("www."):
                            domain = domain[4:]
                        # Basic check: avoid common unrelated domains if possible (customize if needed)
                        # if any(x in domain for x in ['facebook.com', 'linkedin.com', 'yelp.com', 'mapquest.com']):
                        #      continue
                        return domain  # Return the first plausible domain
                    except Exception as e:
                        print(f"  - Warning: Could not parse URL {result.get('link', 'N/A')}: {e}")
                        continue
            print(f"  - Warning: No parseable links found in organic results for query: {query}")
            return "No Organic Links Found"
        else:
            print(f"  - Warning: No organic results found for query: {query}")
            return "No Organic Results"

    except requests.exceptions.Timeout:
        print(f"  - Error: Request timed out for query: {query}")
        return "Request Timeout"
    except requests.exceptions.RequestException as e:
        print(f"  - Error during Serper request for query '{query}': {e}")
        if hasattr(e, "response") and e.response is not None:
            print(f"  - Response status: {e.response.status_code}")
            print(f"  - Response text: {e.response.text[:200]}...")
        return "API Error"
    except Exception as e:
        print(f"  - An unexpected error occurred for query '{query}': {e}")
        return "Unexpected Error"


def _search_domain_via_serper_dev():
    if not SERPER_API_KEY:
        print("Script cannot run without a SERPER_API_KEY.")
        exit()

    print(f"Reading data from {INPUT_EXCEL_FILE}...")
    try:
        df = pd.read_excel(INPUT_EXCEL_FILE)
        print(f"Found {len(df)} agencies.")
    except FileNotFoundError:
        print(f"ERROR: Input file '{INPUT_EXCEL_FILE}' not found. Make sure it's in the same directory.")
        exit()
    except Exception as e:
        print(f"ERROR: Could not read Excel file: {e}")
        exit()

    # Verify required columns exist
    required_cols = [AGENCY_NAME_COL, CITY_COL, STATE_COL]
    if not all(col in df.columns for col in required_cols):
        print(f"ERROR: Input file must contain columns: {', '.join(required_cols)}")
        print(f"Found columns: {list(df.columns)}")
        exit()

    found_domains = []
    print("Starting domain lookup (this may take a while)...")

    for index, row in df.iterrows():
        agency_name = row[AGENCY_NAME_COL]
        city = row[CITY_COL]
        state = row[STATE_COL]
        # phone = row.get(PHONE_COL, '') # Get phone if column exists, otherwise empty string

        # Construct a specific search query
        # You can experiment with this query for better results
        search_query = f"{agency_name} {city} {state}"
        # If you have phone number:
        # search_query = f'"{agency_name}" {city} {state} phone {phone} official website'

        print(f"\nProcessing row {index + 1}/{len(df)}: {agency_name}, {city}, {state}")
        print(f"  - Query: {search_query}")

        domain = _get_domain_from_serper(SERPER_API_KEY, search_query)

        if domain:
            print(f"  - Found domain: {domain}")
            found_domains.append(domain)
        else:
            print("  - Domain not found or error occurred.")
            found_domains.append("Not Found / Error")

        # --- IMPORTANT: Add a delay to avoid hitting API rate limits ---
        time.sleep(0.2)

    df["Website Domain"] = found_domains

    print(f"\nSaving results to {OUTPUT_EXCEL_FILE}...")
    try:
        df.to_excel(OUTPUT_EXCEL_FILE, index=False)
        print("Done!")
    except Exception as e:
        print(f"ERROR: Could not save Excel file: {e}")


def _fill_domains_from_secura_domains_csv():
    csv_lookup = {}
    csv_file_path = "/Users/<USER>/Downloads/Agency email domain data.csv"
    with open(csv_file_path, encoding="utf-8", newline="") as f:
        reader = csv.DictReader(f)
        for row in reader:
            state = row["State"].strip() if row["State"] else None
            agency_name = row["Agency Name"].strip() if row["Agency Name"] else None
            code = row["Code"].strip() if row["Code"] else None
            email_domain = row["Email Domains"].strip() if row["Email Domains"] else None

            # Build the tuple key for matching - this identifies matching rows in the database
            key = (code, agency_name, state)
            csv_lookup[key] = email_domain

    now_utc = datetime.utcnow()
    rows_to_update = []
    with app.app_context():
        attach_dynamic_password_provider_for_new_connections(db.engine)
        kalepa_existing_secura_mappings = (
            db.session.query(SecuraUWMapping).execution_options(bypass_filter_required=True).all()
        )
        for mapping in kalepa_existing_secura_mappings:
            # Build the same tuple to check in csv_lookup
            # Remove leading zeros
            code_raw = (mapping.secura_excel_agency_code_raw or "").strip()
            code_no_leading_zeros = code_raw.lstrip("0")
            key = (
                code_no_leading_zeros,
                (mapping.secura_excel_agency_name_raw or "").strip(),
                (mapping.secura_excel_agency_state_raw or "").strip(),
            )

            if key in csv_lookup:
                domain = csv_lookup[key]
                if domain:
                    mapping.agency_domains = [domain]
                    mapping.agency_domains_updated_at = now_utc
                    rows_to_update.append(mapping)

        if rows_to_update:
            db.session.bulk_save_objects(rows_to_update)
            db.session.commit()
            print(f"Updated {len(rows_to_update)} rows.")
        else:
            print("No rows matched, no updates performed.")


if __name__ == "__main__":
    pass
    # _search_domain_via_serper_dev()
    # _some_fun_with_llm_domains()
    # _create_or_update_secura_uw_mappings(use_brokerage_name_fuzzy_matching=False)
    # _fill_domains_from_secura_domains_csv()

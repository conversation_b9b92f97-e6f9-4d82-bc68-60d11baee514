type: object
properties:
  username:
    type: string
    description: Paragon Underwriter's username.
    nullable: true
  email:
    type: string
    format: email
    description: Paragon Underwriter's email.
    nullable: true
  kalepa_user_id:
    type: integer
    description: the ID of the User in Kalepa 'users' table.
    nullable: true
  ims_underwriter_id:
    type: integer
    description: the ID of the User in Paragon IMS system
    nullable: true
  ims_underwriter_guid:
    type: string
    format: uuid
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
    description: the GUID of the User in Paragon IMS system
    nullable: true
  first_name:
    type: string
    description: Paragon Underwriter's first name.
    nullable: true
  last_name:
    type: string
    description: Paragon Underwriter's last name.
    nullable: true
  title:
    type: string
    description: Paragon Underwriter's title.
    nullable: true
  office:
    type: string
    description: Paragon Underwriter's office.
    nullable: true
  phone:
    type: string
    description: Paragon Underwriter's phone.
    nullable: true
  fax:
    type: string
    description: Paragon Underwriter's fax.
    nullable: true
  mobile:
    type: string
    description: Paragon Underwriter's mobile.
    nullable: true

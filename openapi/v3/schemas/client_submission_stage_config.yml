type: object
properties:
  id:
    type: integer
  organization_id:
    type: integer
  client_stage:
    type: string
  tags:
    type: array
    items:
      type: string
  tag_labels:
    type: array
    items:
      type: string
  with_comment:
    type: boolean
    nullable: true
  not_selectable:
    type: boolean
    nullable: true
  copilot_stage:
    type: string
    enum:
      - "INDICATED"
      - "ON_MY_PLATE"
      - "DECLINED"
      - "COMPLETED"
      - "QUOTED"
      - "WAITING_FOR_OTHERS"
      - "QUOTED_LOST"
      - "QUOTED_BOUND"
      - "CLEARING_ISSUE"
      - "BLOCKED"
      - "EXPIRED"
      - "CANCELED"

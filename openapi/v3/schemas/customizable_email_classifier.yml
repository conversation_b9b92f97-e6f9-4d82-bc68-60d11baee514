description: Email classifier based on customizable classifier
allOf:
  - $ref: '../../v3.yml#/components/schemas/EmailClassifier'
  - type: object
    properties:
      customizable_classifier_id:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
        nullable: true
      customizable_classifier:
        nullable: true
        allOf:
          - $ref: '../../v3.yml#/components/schemas/CustomizableClassifier'

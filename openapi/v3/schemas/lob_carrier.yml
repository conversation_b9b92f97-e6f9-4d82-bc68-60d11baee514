type: object
allOf:
  - $ref: "../../v3.yml#/components/schemas/CreatedAt"
  - $ref: "../../v3.yml#/components/schemas/UpdatedAt"
properties:
  id:
    type: string
    format: uuid
    description: the ID of the item.
    nullable: true
  lob_raw:
    type: string
    description: the raw line of business value in the mapping
    nullable: false
  carrier:
    type: string
    description: carrier used in the mapping.
    nullable: true
  line_of_business:
    type: string
    description: line of business to be used for the provided lob_raw and carrier.
    nullable: false

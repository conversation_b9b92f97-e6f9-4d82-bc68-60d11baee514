type: object
properties:
  id:
    type: string
    format: uuid
  autoconfirmed_business_rejected:
    type: boolean
    nullable: true
  rejected_due_to_building_number:
    type: boolean
    nullable: true
  source_of_resolution:
    type: string
    nullable: true
  fallback_to_premises_profile:
    type: string
    nullable: true
  candidate_score:
    type: number
    format: float
    nullable: true
  candidate_id:
    type: string
    nullable: true
  names:
    type: array
    items:
      type: string
    nullable: true
  resolution_info:
    type: object
    additionalProperties: true
    nullable: true
  fallback_to_shell_entity:
    type: boolean
    nullable: true
  classification:
    type: string
    nullable: true
  requested_name:
    type: string
    nullable: true
  requested_address:
    type: string
    nullable: true
  llm_autoconfirmation_rejected:
    type: boolean
    nullable: true
  llm_autoconfirmation_rejected_reason:
    type: string
    nullable: true

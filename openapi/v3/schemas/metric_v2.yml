type: object
properties:
  submission_business_id:
    type: string
    format: uuid
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
    nullable: true
  execution_id:
    type: string
    format: uuid
  metric_type:
    type: string
    nullable: true
  name:
    type: string
    nullable: true
  has_structures_data:
    type: boolean
    nullable: true
  filtering_mode:
    type: string
  parent_id:
    type: string
    format: uuid
    nullable: true
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
  parent_type:
    type: string
    nullable: true
    example: 'BUSINESS'
    enum:
      - BUSINESS
      - PREMISES
      - ORGANIZATION
      - SUBMISSION
      - REPORT
      - VEHICLE
      - EQUIPMENT
      - STRUCTURE
      - DRIVER
      - PRODUCT
      - PERSON
      - ERISA_PLAN
  children_type:
    type: string
    nullable: true
    default: 'BUSINESS'
  summary_config_id:
    type: string
    nullable: true
    example: 'HAS_DELIVERY_SUMMARY_CONFIG'
  sources:
    type: array
    items:
      $ref: "../../v3.yml#/components/schemas/MetricSource"
    nullable: true
  metric_group_name:
    type: string
    nullable: true
    readOnly: true
    example: 'Fleet'
    description: group name used to group metrics into tabs
  business_ids:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
      nullable: true
  children_ids:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
      nullable: true
  values:
    type: array
    nullable: true
    items:
      type: string
      nullable: true
  quintile_distribution:
    type: array
    nullable: true
    items:
      type: object
      properties:
        business_ids:
          type: array
          items:
            type: string
            format: uuid
            pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
            nullable: true
        parent_ids:
          type: array
          items:
            type: string
            format: uuid
            nullable: true
            pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
        values:
          type: array
          items:
            type: number
            format: float
            nullable: true
        label:
          type: string
        frequency:
          type: integer
          minimum: 0
        percentage:
          type: number
          format: float
          minimum: 0
          maximum: 1.0
  interquintile_numbers:
    type: array
    nullable: true
    items:
      type: integer
      nullable: true
  quintiles:
    type: array
    nullable: true
    items:
      type: number
      format: double
  date_range_quintiles:
    type: array
    nullable: true
    items:
      $ref: '../../v3.yml#/components/schemas/DateRangeDistributionCategory'
  mean:
    type: number
    format: double
    nullable: true
  sum:
    type: number
    format: double
    nullable: true
  min:
    type: number
    format: double
    nullable: true
  max:
    type: number
    format: double
    nullable: true
  earliest:
    type: string
    format: date-time
    nullable: true
  latest:
    type: string
    format: date-time
    nullable: true
  filtered_minimums:
    type: array
    nullable: true
    items:
      type: number
      format: double
      nullable: true 
  filtered_maximums:
    type: array
    nullable: true
    items:
      type: number
      format: double
      nullable: true
  average_min_max:
    type: number
    format: double
    nullable: true
  computed_units:
    type: string
    nullable: true
    enum:
      - "ACRES"
      - "SQUARE FEET"
      - "FEET"
      - "STORIES"
      - "YEARS"
      - "USD"
      - "MILES"
      - "PERCENTAGE"
      - "EMPLOYEES"
      - "TECHNICIANS"
      - "STRUCTURES"
      - "DRIVERS"
      - "VEHICLES"
      - "SPRINKLERS"
      - "SPACES"
      - "STARS"
      - "PROJECTS"
      - "UNITS"
      - "ROOMS"
      - "OTHER"
      - "CONTRACTORS"
  list_item_type:
    type: string
    nullable: true
  list_item_values:
    type: array
    nullable: true
    items:
      type: string
      nullable: true
  threshold:
    type: string
    nullable: true
  inside_range_label:
    type: string
    nullable: true
  outside_range_label:
    type: string
    nullable: true
  inside_range:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      nullable: true
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
  outside_range:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      nullable: true
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
  unknown_range:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      nullable: true
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
  range_distribution:
    type: array
    nullable: true
    items:
      type: object
      properties:
        category:
          type: string
        business_ids:
          type: array
          items:
            type: string
            format: uuid
            nullable: true
            pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
        parent_ids:
          type: array
          items:
            type: string
            format: uuid
            nullable: true
            pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
        frequency:
          type: integer
          minimum: 0
        percentage:
          type: number
          format: float
          minimum: 0
          maximum: 1.0
  score:
    type: number
    format: double
    nullable: true
  label:
    type: string
    nullable: true
  grade_distribution:
    nullable: true
    type: array
    items:
      type: object
      properties:
        grade:
          type: string
        business_ids:
          type: array
          items:
            type: string
            format: uuid
            nullable: true
            pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
        frequency:
          type: integer
          minimum: 0
        percentage:
          type: number
          format: float
          minimum: 0
          maximum: 1.0
  category_summaries:
    type: array
    nullable: true
    items:
      type: object
      properties:
        display_name:
          type: string
        frequency:
          type: integer
          minimum: 0
        percentage:
          type: number
          minimum: 0
        business_ids:
          type: array
          items:
            type: string
            format: uuid
            nullable: true
            pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
        parent_types:
          type: array
          items:
            type: string
        parent_ids:
          type: array
          items:
            type: string
            format: uuid
            nullable: true
            pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'

type: object
allOf:
  - $ref: '../../v3.yml#/components/schemas/ID'
  - $ref: '../../v3.yml#/components/schemas/CreatedAt'
  - $ref: '../../v3.yml#/components/schemas/UpdatedAt'
properties:
    jurisdiction:
      type: string
      nullable: false
    date:
      type: string
      format: date-time
      nullable: false
    total_miles:
      type: number
      nullable: true
    taxable_gallons:
      type: number
      nullable: true
    file_id:
      type: string
      format: uuid
      nullable: false
    submission_id:
      type: string
      format: uuid
      nullable: false
    matched_total:
      type: boolean
      nullable: true
    evidence:
      type: array
      items:
        type: object
      description: evidence in PDFEvidence format
      nullable: true

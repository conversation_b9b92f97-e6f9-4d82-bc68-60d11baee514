type: object
properties:
  business_id:
    type: string
    format: uuid
  premises_id:
    type: string
    format: uuid
    nullable: true
  name:
    type: string
  address:
    type: string
    nullable: true
  latitude:
    type: number
    nullable: true
  longitude:
    type: number
    nullable: true
  industries:
    type: array
    nullable: true
    items:
      type: string
  is_closed:
    type: boolean
    nullable: true
  closed_at:
    type: string
    format: date-time
    nullable: true
  other_addresses:
    type: array
    nullable: true
    items:
      type: string
  identifiers:
    type: array
    items:
      $ref: '../../v3.yml#/components/schemas/ERSExternalIdentifier'
  relations_to:
    type: array
    items:
      $ref: '../../v3.yml#/components/schemas/ERSEntitiesRelation'
  relations_from:
    type: array
    items:
      $ref: '../../v3.yml#/components/schemas/ERSEntitiesRelation'

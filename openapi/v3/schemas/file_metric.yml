type: object
allOf:
  - $ref: '../../v3.yml#/components/schemas/ID'
  - $ref: '../../v3.yml#/components/schemas/CreatedAt'
  - $ref: '../../v3.yml#/components/schemas/UpdatedAt'
properties:
  file_id:
    type: string
    format: uuid
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
    nullable: false
  metric_name:
    type: string
    nullable: false
  metric_value:
    type: number
    nullable: false
  score_calculation:
    type: object
    nullable: true

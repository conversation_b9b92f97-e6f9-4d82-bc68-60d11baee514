type: object
allOf:
  - $ref: "../../v3.yml#/components/schemas/CreatedAt"
  - $ref: "../../v3.yml#/components/schemas/UpdatedAt"
properties:
  id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
  submission_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
  question:
    type: string
    nullable: false
  answer:
    type: string
    nullable: true

type: object
properties:
  type:
    # This field contains source_type_id of a fact
    # we do not define it as an enum to don't have to update the schema when new source is added
    type: string
    nullable: false
  properties:
    # This field cointains source properties - different for each type
    # e.g. WEB_INGESTED will have URL
    #      FIRST_PARTY will have submission_id, file_id, etc.
    type: object
    nullable: true

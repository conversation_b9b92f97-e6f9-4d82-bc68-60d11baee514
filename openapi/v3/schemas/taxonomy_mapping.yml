type: object
allOf:
  - $ref: '../../v3.yml#/components/schemas/ID'
  - $ref: '../../v3.yml#/components/schemas/CreatedAt'
  - $ref: '../../v3.yml#/components/schemas/UpdatedAt'
properties:
  mapping_type:
    type: string
    enum:
      - "NAICS_TO_SIC"
      - "NAICS_TO_SIC5"
      - "SIC_TO_NAICS"
      - "SIC5_TO_NAICS"
      - "NAICS_TO_GL"
      - "GL_TO_NAICS"
  code:
    type: string
  mapped_codes:
    type: array
    items:
      type: string
    minItems: 1
  description:
    $ref: '../../v3.yml#/components/schemas/TaxonomyDescription'
  mapped_codes_descriptions:
    type: array
    items:
      $ref: '../../v3.yml#/components/schemas/TaxonomyDescription'

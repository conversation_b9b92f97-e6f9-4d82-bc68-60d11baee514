post:
  x-openapi-router-controller: copilot.v3.controllers.temp_migrate_brokerages
  operationId: temp_fix_user_brokerages
  responses:
    "200":
      description: the Brokerage Employees were merged successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/BrokerageEmployee"
    "400":
      description: the request is invalid.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

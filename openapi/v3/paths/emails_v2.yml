post:
  x-openapi-router-controller: copilot.v3.controllers.emails
  operationId: store_email_v2
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/EmailRequest"
  responses:
    "201":
      description: the message was stored successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/EmailResponse"
    "400":
      description: the request is invalid
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred

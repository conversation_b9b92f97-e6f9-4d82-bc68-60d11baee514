parameters:
  - $ref: '../../v3.yml#/components/parameters/submission_idParam'
get:
  x-openapi-router-controller: copilot.v3.controllers.metadata
  operationId: get_submission_metadata
  responses:
    "200":
      description: Submission information about whats available
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/SubmissionMetadata"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the request is invalid.
    "500":
      description: an unexpected error occurred.

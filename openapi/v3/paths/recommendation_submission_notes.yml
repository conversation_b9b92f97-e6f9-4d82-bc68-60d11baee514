post:
  x-openapi-router-controller: copilot.v3.controllers.submission_notes
  operationId: add_recommendation_submission_notes
  requestBody:
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: "../../v3.yml#/components/schemas/RecommendationSubmissionNoteRequest"
  responses:
    "201":
      description: Recommendation notes have been created
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

parameters:
  - in: path
    name: submission_id
    required: true
    schema:
      type: string
      format: uuid
  - in: query
    name: file_classification
    required: true
    schema:
      type: string
get:
  x-openapi-router-controller: copilot.v3.controllers.processed_data
  operationId: get_submission_processed_data
  responses:
    "200":
      description: Get processed data for all files in submission with given classification.
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "../../v3.yml#/components/schemas/ProcessedFilePD"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

get:
  x-openapi-router-controller: copilot.v3.controllers.submission_assignment
  parameters:
    - in: query
      name: exclude_assigned
      required: false
      schema:
        type: boolean
      description: true if only reports that are not assigned should be returned, false otherwise.
  operationId: get_assignment_queue_count
  responses:
    "200":
      description: The queue count was retrieved successfully.
      content:
        application/json:
          schema:
            properties:
              queue_count:
                type: number
                format: integer
              specialists_needed:
                type: number
                format: float
    "400":
      description: the request is invalid.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

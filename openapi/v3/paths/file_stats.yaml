get:
  x-openapi-router-controller: copilot.v3.controllers.files
  operationId: get_file_stats
  parameters:
    - name: organization_id
      in: query
      description: the Organization ID of the files.
      required: true
      schema:
        type: integer
    - name: created_after
      in: query
      description: Filter for Created At >=
      required: false
      schema:
        type: string
        format: date-time
  responses:
    "200":
      description: the Stats over files were retrieved successfully
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/FileStats"
    "400":
      description: the request is invalid
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: organization was not found
    "500":
      description: an unexpected error occurred

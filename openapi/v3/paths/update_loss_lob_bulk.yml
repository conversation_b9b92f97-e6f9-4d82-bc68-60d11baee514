post:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  parameters:
    - $ref: '../../v3.yml#/components/parameters/submission_idParam'
  operationId: update_loss_lob_bulk
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/UpdateLossLobBulkRequest"
  responses:
    "200":
      description: the loss lobs were updated
    "400":
      description: the request is invalid
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: some losses were not found
    "409":
      description: Conflict when updating loss lobs
    "500":
      description: an unexpected error occurred

get:
  x-openapi-router-controller: copilot.v3.controllers.organizations
  operationId: get_all_organizations
  responses:
    "200":
      description: Organizations were retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/Organizations"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

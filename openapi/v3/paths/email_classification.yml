parameters:
  - $ref: '../../v3.yml#/components/parameters/idParam'
delete:
  x-openapi-router-controller: copilot.v3.controllers.emails
  operationId: delete_email_classification
  responses:
      "204":
        description: The classification was deleted successfully.
      "400":
        description: the request is invalid.
      "403":
        $ref: "../../v3.yml#/components/responses/UnauthorizedError"
      "500":
        description: an unexpected error occurred.
patch:
  x-openapi-router-controller: copilot.v3.controllers.emails
  operationId: update_email_classification
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/EmailClassification"
  responses:
    "200":
      description: The email classification was updated successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/EmailClassification"
    "400":
      description: the request is invalid.
    "500":
      description: an unexpected error occurred.

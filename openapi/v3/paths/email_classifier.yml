parameters:
  - $ref: '../../v3.yml#/components/parameters/idParam'
get:
  x-openapi-router-controller: copilot.v3.controllers.email_classification
  operationId: get_email_classifier
  responses:
      "200":
        description: The classifier was retrieved successfully.
        content:
          application/json:
            schema:
              $ref: "../../v3.yml#/components/schemas/EmailClassifier"
      "400":
        description: the request is invalid.
      "403":
        $ref: "../../v3.yml#/components/responses/UnauthorizedError"
      "500":
        description: an unexpected error occurred.
patch:
  x-openapi-router-controller: copilot.v3.controllers.email_classification
  operationId: update_email_classifier
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/EmailClassifier"
  responses:
    "200":
      description: The email classifier was updated successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/EmailClassifier"
    "400":
      description: the request is invalid.
    "500":
      description: an unexpected error occurred.
delete:
    x-openapi-router-controller: copilot.v3.controllers.email_classification
    operationId: delete_email_classifier
    responses:
        "204":
          description: the email classifier was deleted successfully.
        "400":
          description: the request is invalid.
        "500":
          description: an unexpected error occurred.

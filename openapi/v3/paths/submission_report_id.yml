parameters:
  - in: path
    name: id
    required: true
    schema:
      type: string
      format: uuid
get:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: get_submission_report_id
  responses:
    "200":
      description: The Submission was retrieved successfuly.
      content:
        application/json:
          schema:
            type: string
    "400":
      description: the request is invalid.
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

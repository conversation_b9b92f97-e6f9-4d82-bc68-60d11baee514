post:
  x-openapi-router-controller: copilot.v3.controllers.submissions_sync
  operationId: get_matching_results
  requestBody:
    description: Run matching only for given submission ids for a given configuration
    content:
      application/json:
        schema:
          $ref: '../../v3.yml#/components/schemas/MatcherDryRunRequest'
  responses:
    "200":
      description: Matching finished
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "../../v3.yml#/components/schemas/MatcherDryRunResult"
    "400":
      description: the request is invalid.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

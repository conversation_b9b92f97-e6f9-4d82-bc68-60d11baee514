put:
  x-openapi-router-controller: copilot.v3.controllers.users
  operationId: create_user_if_not_present
  requestBody:
    description: create User if not present or sync with external id - return user's metadata in both scenarios
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/UserCreateIfNotPresent"
  responses:
    "200":
      description: User was present and was only updated - return user's metadata
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/UserMetadata"
    "201":
      description: New user has been created - return new user's metadata
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/UserMetadata"
    "400":
      description: the request is invalid.
    "500":
      description: an unexpected error occurred.
get:
  x-openapi-router-controller: copilot.v3.controllers.user
  operationId: get_user_by_attributes
  parameters:
    - name: email
      in: query
      description: the User's email address.
      required: false
      schema:
        type: string
        format: email
      example: '<EMAIL>'
  responses:
    "200":
      description: The User was retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/User"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

parameters:
  - $ref: '../../v3.yml#/components/parameters/idParam'
patch:
  x-openapi-router-controller: copilot.v3.controllers.processed_files
  operationId: update_processed_file_resolution_data_row
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/PFResolutionDataRowUpdate"
  responses:
    "204":
      description: The Processed File was updated successfully.
    "404":
      description: The Processed File was not found.
    "400":
      description: the request is invalid.
    "500":
      description: an unexpected error occurred.

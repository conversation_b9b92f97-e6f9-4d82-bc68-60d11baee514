get:
  x-openapi-router-controller: copilot.v3.controllers.workers_comp_experience
  operationId: get_class_codes
  responses:
    "200":
      description: Workers comp state rating information class codes.
      content:
        application/json:
          schema:
            type: object
            properties:
              NCCI:
                type: object
              WCIRB:
                type: object

    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the request is invalid.
    "500":
      description: an unexpected error occurred.

parameters:
  - $ref: '../../v3.yml#/components/parameters/experiment_idParam'
put:
  x-openapi-router-controller: copilot.v3.controllers.experiments
  operationId: update_experiment_definition
  requestBody:
    description: Update experiment definition
    content:
      application/json:
        schema:
          $ref: '../../v3.yml#/components/schemas/ExperimentDefinitionUpdateRequest'
  responses:
    "200":
      description: Single experiment definition
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/ExperimentDefinition"
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
get:
  x-openapi-router-controller: copilot.v3.controllers.experiments
  operationId: get_experiment_definition
  responses:
    "200":
      description: Single experiment definition
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/ExperimentDefinition"
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

parameters:
  - in: path
    name: id
    required: true
    schema:
      type: string
      format: uuid
get:
  parameters:
    - in: query
      name: expand
      required: false
      schema:
        type: array
        items:
          type: string
    - in: query
      name: enrich_with_ers_data
      required: false
      schema:
        type: boolean
        default: false
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: get_submission_by_id
  responses:
    "200":
      description: The Submission was retrieved successfuly.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/Submission"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
patch:
  parameters:
    - $ref: '../../v3.yml#/components/parameters/callerContext'
    - in: query
      name: enrich_with_ers_data
      required: false
      schema:
        type: boolean
        default: false
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: update_submission
  requestBody:
    description: the Submission.
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/Submission"
  responses:
    "200":
      description: The Submission was updated successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/Submission"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
delete:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: delete_submission
  responses:
    "200":
      description: The Submission was deleted successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/Submission"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

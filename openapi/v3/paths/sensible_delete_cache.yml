delete:
  parameters:
    - in: query
      name: document_type
      required: false
      schema:
        type: string
        nullable: true
        default: null
      example: acord_forms
      description: Sensible document type
    - in: query
      name: config_name
      required: false
      schema:
        type: string
        nullable: true
        default: null
      example: acord_125_2014_12
      description: The specific configuration for the sensible document type
  x-openapi-router-controller: copilot.v3.controllers.sensible
  operationId: delete_cache
  responses:
    "200":
      description: Sensible configuration metadata refreshed.
    "500":
      description: An unexpected error occurred.

get:
  parameters:
    - in: query
      name: start_date
      required: false
      schema:
        type: string
        nullable: true
        format: date-time
      example: "2023-02-10 21:09:39.613390 +00:00"
      description: Start date of the requests
    - in: query
      name: end_date
      required: false
      schema:
        type: string
        nullable: true
        format: date-time
      example: "2023-02-10 21:09:39.613390 +00:00"
      description: Start date of the requests
  x-openapi-router-controller: copilot.v3.controllers.sensible
  operationId: get_extractions
  responses:
    "200":
      description: The Extractions were retrieved successfully.
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "../../v3.yml#/components/schemas/SensibleExtraction"
    "500":
      description: an unexpected error occurred.

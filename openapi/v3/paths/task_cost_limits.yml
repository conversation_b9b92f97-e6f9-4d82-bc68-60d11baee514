get:
  x-openapi-router-controller: copilot.v3.controllers.task_cost_limits
  operationId: get_task_cost_limits
  summary: Get task cost limits
  description: Retrieve task cost limits with optional filtering.
  parameters:
    - in: query
      name: organization_id
      schema:
        type: integer
      required: false
      description: Filter by organization ID (also returns null organization limits)
    - in: query
      name: task_definition_group
      schema:
        type: string
      required: false
      description: Filter by task definition group (also returns null group limits)
    - in: query
      name: for_test_run
      schema:
        type: boolean
        default: false
      required: false
      description: Filter by test run status
  responses:
    "200":
      description: Task cost limits retrieved successfully
      content:
        application/json:
          schema:
            type: object
            properties:
              task_cost_limits:
                type: array
                items:
                  $ref: "../schemas/task_cost_limit.yml"
    "400":
      description: Bad request
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: An unexpected error occurred
post:
  x-openapi-router-controller: copilot.v3.controllers.task_cost_limits
  operationId: create_task_cost_limit
  summary: Create a new task cost limit
  description: Create a new task cost limit with the provided configuration.
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../schemas/task_cost_limit.yml"
  responses:
    "201":
      description: Task cost limit created successfully
      content:
        application/json:
          schema:
            $ref: "../schemas/task_cost_limit.yml"
    "400":
      description: Bad request
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: Organization not found
    "500":
      description: An unexpected error occurred 

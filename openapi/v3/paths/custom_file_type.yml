post:
  x-openapi-router-controller: copilot.v3.controllers.custom_file_type
  operationId: create_or_update_custom_file_type
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/CustomFileType"
  responses:
    "201":
      description: Custom file type was created successfully
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/CustomFileType"
    "409":
      description: Custom file type already exists and is enabled
    "500":
      description: an unexpected error occurred.

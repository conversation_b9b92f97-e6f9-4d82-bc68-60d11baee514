put:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: put_loss_policy
  parameters:
    - $ref: '../../v3.yml#/components/parameters/submission_idParam'
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/PutLossPolicyRequest"
  responses:
    "200":
      description: the loss policy was updated
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/LossPolicy"
    "201":
      description: the loss policy was created
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/LossPolicy"
    "400":
      description: the request is invalid
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: file or submission was not found
    "500":
      description: an unexpected error occurred

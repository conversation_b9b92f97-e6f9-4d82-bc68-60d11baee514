post:
  x-openapi-router-controller: copilot.v3.controllers.files
  operationId: update_file_label_studio_status
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/FilePageProcessedRequest"
  responses:
    "200":
      description: The File Page processing status was updated.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/FilePageProcessedResponse"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "422":
      description: the request was well-formed, but the server refused to process it.
    "500":
      description: an unexpected error occurred.

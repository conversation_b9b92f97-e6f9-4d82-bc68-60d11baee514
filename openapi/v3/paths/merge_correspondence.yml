parameters:
  - in: query
    name: from_id
    required: true
    schema:
      type: string
      format: uuid
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
  - in: query
    name: to_id
    required: true
    schema:
      type: string
      format: uuid
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
post:
  x-openapi-router-controller: copilot.v3.controllers.clearing
  operationId: merge_correspondence
  responses:
    "204":
      description: merged successfully
    "400":
      description: the request is invalid
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: submission was not found
    "500":
      description: an unexpected error occurred

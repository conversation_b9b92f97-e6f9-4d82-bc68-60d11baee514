parameters:
  - in: path
    name: id
    required: true
    schema:
      type: string
      format: uuid
get:
  parameters:
    - in: query
      name: expand
      required: false
      schema:
        type: array
        items:
          type: string
    - in: query
      name: enrich_with_ers_data
      required: false
      schema:
        type: boolean
        default: false
    - in: query
      name: call_origin
      description: The name of the downstream service (caller/origin) that is calling this (upstream) CAPI service
      required: false
      schema:
        type: string
      example: 'ers'
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: get_submission_lite
  responses:
    "200":
      description: The Submission was retrieved successfuly.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/Submission"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

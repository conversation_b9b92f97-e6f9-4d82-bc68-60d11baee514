parameters:
  - in: path
    name: task_definition_id
    required: true
    schema:
      type: string
      format: uuid
post:
  x-openapi-router-controller: copilot.v3.controllers.tasks
  operationId: create_task_definition_metrics
  requestBody:
    required: false
    content:
      application/json:
        schema:
          type: object
          nullable: true
          properties:
            lookback_hours:
              type: integer
  responses:
    "204":
      description: The task definition metrics created successfully.
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

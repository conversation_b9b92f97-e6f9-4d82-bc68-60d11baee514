parameters:
  - $ref: '../../v3.yml#/components/parameters/file_idParam'
  - $ref: '../../v3.yml#/components/parameters/page_numParam'
get:
  x-openapi-router-controller: copilot.v3.controllers.files
  operationId: get_label_studio_file_page
  responses:
    "200":
      description: The File Pages object for the given File ID and Page number.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/FilePageLabelStudio"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "422":
      description: the request was well-formed, but the server refused to process it.
    "500":
      description: an unexpected error occurred.

parameters:
  - $ref: '../../v3.yml#/components/parameters/idParam'
post:
  x-openapi-router-controller: copilot.v3.controllers.emails
  operationId: update_email_classification_labels
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/EmailClassificationLabelsBulkUpdateRequest"
  responses:
    "200":
      description: Email updated successfully.
      content:
        application/json:
          schema:
            $ref: '../../v3.yml#/components/schemas/Email'
    "400":
      description: the request is invalid.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

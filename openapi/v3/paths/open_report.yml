parameters:
  - $ref: '../../v3.yml#/components/parameters/userIdParam'
  - $ref: '../../v3.yml#/components/parameters/report_idParam'
post:
  x-openapi-router-controller: copilot.v3.controllers.users
  operationId: add_user_open_report
  responses:
    "204":
      description: The report was saved as open successfully.
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
delete:
  x-openapi-router-controller: copilot.v3.controllers.users
  operationId: delete_user_open_report
  responses:
    "204":
      description: The reports was removed from open reports.
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

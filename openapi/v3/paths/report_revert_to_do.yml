parameters:
  - $ref: '../../v3.yml#/components/parameters/idParam'
post:
  x-openapi-router-controller: copilot.v3.controllers.reports
  operationId: revert_to_data_onboarding
  responses:
    "204":
      description: Report reverted to DO.
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the specified Report was not found.
    "500":
      description: an unexpected error occurred.

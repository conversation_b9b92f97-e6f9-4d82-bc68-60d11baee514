parameters:
  - $ref: "../../v3.yml#/components/parameters/idParam"
put:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: upsert_user_submission_notifications
  requestBody:
    description: Success
    content:
      application/json:
        schema:
          type: object
          properties:
            seen_emails:
              type: integer
            seen_notes:
              type: integer
  responses:
    "200":
      description: Success
      content:
        application/json:
          schema:
            type: object
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
get:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: get_users_submission_notifications
  responses:
    "200":
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              seen_emails:
                type: integer
              seen_notes:
                type: integer
              email_count:
                type: integer
              notes_count:
                type: integer
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

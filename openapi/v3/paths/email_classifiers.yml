get:
  x-openapi-router-controller: copilot.v3.controllers.email_classification
  operationId: get_email_classifiers
  responses:
    "200":
      description: Get all email classifiers for current user.
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '../../v3.yml#/components/schemas/EmailClassifier'
    "400":
      description: the request is invalid.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.
post:
  x-openapi-router-controller: copilot.v3.controllers.email_classification
  operationId: create_email_classifier
  requestBody:
    content:
      application/json:
        schema:
          $ref: '../../v3.yml#/components/schemas/EmailClassifier'
  responses:
    "201":
      description: The classifier was created.
      content:
        application/json:
          schema:
            $ref: '../../v3.yml#/components/schemas/EmailClassifier'
    "400":
      description: the request is invalid.
    "500":
      description: an unexpected error occurred.

parameters:
  - $ref: "../../v3.yml#/components/parameters/idParam"
get:
  x-openapi-router-controller: copilot.v3.controllers.broker_group
  operationId: get_broker_group
  responses:
    "200":
      description: The Broker Group was retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/BrokerGroup"
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: resource not found
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.
patch:
  x-openapi-router-controller: copilot.v3.controllers.broker_group
  operationId: update_broker_group
  requestBody:
    description: Broker Group to update
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/BrokerGroupPatch"
  responses:
    "200":
      description: The Broker Group was updated successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/BrokerGroup"
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: resource not found
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.
post:
  parameters:
    - name: organization_id
      in: query
      description: The organization ID to create the Broker Group for.
      required: false
      schema:
        type: integer
    - name: user_id
      in: query
      description: The organization ID to create the Broker Group for.
      required: false
      schema:
        type: integer
  x-openapi-router-controller: copilot.v3.controllers.broker_group
  operationId: update_broker_group_mappings
  requestBody:
    description: Broker Group to update
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/UpdateBrokerGroupMappings"
  responses:
    "200":
      description: The Broker Group was updated successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/BrokerGroup"
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: resource not found
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.
delete:
  x-openapi-router-controller: copilot.v3.controllers.broker_group
  operationId: delete_broker_group
  responses:
    "204":
      description: The Broker Group has been deleted.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: resource not found
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.

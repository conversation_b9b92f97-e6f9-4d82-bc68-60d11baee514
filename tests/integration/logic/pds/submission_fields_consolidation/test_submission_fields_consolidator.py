from collections.abc import Callable
from unittest.mock import MagicMock
import datetime

from freezegun import freeze_time
from static_common.enums.entity import EntityInformation
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.models.submission_level_data import SourceDetails
import pytest

from copilot.logic.pds.submission_fields_consolidation.effective_date_consolidator import (
    EffectiveDateConsolidator,
)
from copilot.logic.pds.submission_fields_consolidation.submission_fields_consolidator import (
    SubmissionFieldsConsolidationContext,
    TakeFirstValueConsolidator,
)
from copilot.models import db
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from tests.integration.coverages.fixtures import set_up_current_user
from tests.integration.factories import file_fixture, organization_fixture
from tests.integration.logic.pds.submission_fields_consolidation.consolidation_fixtures import (
    create_submission_level_extracted_data,
    file,
)

used_fixtures = (file, create_submission_level_extracted_data)


@pytest.mark.parametrize(
    (
        "value_l, source_details_l, file_types_l, file_type_order, reverse_order, sources_to_skip,"
        " sources_to_prioritize, consolidated_value"
    ),
    [
        (
            ["1", "2", "3"],
            [SourceDetails.WEBSITE, SourceDetails.GENERATED, SourceDetails.GENERATED],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            None,  # file_type_order
            False,  # reverse_order
            None,  # sources_to_skip
            None,  # sources_to_prioritize
            "2",  # consolidated_value
        ),
        (
            ["1", "2", "3"],
            [SourceDetails.WEBSITE, SourceDetails.GENERATED, SourceDetails.GENERATED],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            None,  # file_type_order
            True,  # reverse_order
            None,  # sources_to_skip
            None,  # sources_to_prioritize
            "3",  # consolidated_value
        ),
        (
            ["1", "2", "3"],
            [SourceDetails.API, SourceDetails.GENERATED, SourceDetails.GENERATED],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            None,  # file_type_order
            False,  # reverse_order
            None,  # sources_to_skip
            None,  # sources_to_prioritize
            "1",  # consolidated_value
        ),
        (
            ["1", "2", "3"],
            [SourceDetails.WEBSITE, SourceDetails.GENERATED, SourceDetails.GENERATED],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            None,  # file_type_order
            False,  # reverse_order
            None,  # sources_to_skip
            [SourceDetails.WEBSITE],  # sources_to_prioritize
            "1",  # consolidated_value
        ),
        (
            ["1", "2", "3"],
            [SourceDetails.GENERATED, SourceDetails.WEBSITE, SourceDetails.GENERATED],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            None,  # file_type_order
            False,  # reverse_order
            [SourceDetails.WEBSITE],  # sources_to_skip
            None,  # sources_to_prioritize
            "1",  # consolidated_value
        ),
        (
            ["1", "2", "3"],
            [SourceDetails.API, SourceDetails.WEBSITE, SourceDetails.API],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            {FileType.SUPPLEMENTAL_FORM: 0, FileType.ACORD_FORM: 1},  # file_type_order
            False,  # reverse_order
            None,  # sources_to_skip
            None,  # sources_to_prioritize
            "3",  # consolidated_value
        ),
    ],
)
def test_take_first_value_consolidator(
    app_context,
    set_up_current_user,
    submission,
    create_submission_level_extracted_data: Callable[[], SubmissionLevelExtractedData],
    value_l: list[str],
    source_details_l: list[SourceDetails],
    file_types_l: list[FileType],
    file_type_order: dict[FileType, int] | None,
    reverse_order: bool,
    sources_to_skip: list[SourceDetails] | None,
    sources_to_prioritize: list[SourceDetails] | None,
    consolidated_value,
):
    # GIVEN
    consolidator = TakeFirstValueConsolidator(
        context=SubmissionFieldsConsolidationContext(
            submission=submission,
            extracted_data_items={},
            extracted_data_collections={},
            consolidation_process=MagicMock(),
        ),
        file_type_order=file_type_order,
        reverse_order=reverse_order,
        sources_to_skip=sources_to_skip,
        sources_to_prioritize=sources_to_prioritize,
    )

    files = [file_fixture(file_type=file_type, submission_id=submission.id) for file_type in file_types_l]
    db.session.commit()

    data = [
        create_submission_level_extracted_data(
            value=value,
            source_details=source_details,
            entity_information=EntityInformation.BROKER_NAME,
            overwritten_file_id=file.id,
            is_valid=True,
        )
        for value, source_details, file in zip(value_l, source_details_l, files)
    ]

    # WHEN
    consolidator.consolidate(data)

    # THEN
    assert consolidator.context.extracted_data_items[EntityInformation.BROKER_NAME][0] == consolidated_value


@freeze_time("2025-03-03")
@pytest.mark.parametrize(
    "value_l, file_types_l, org_id, consolidated_value",
    [
        (
            # future dates are present, take one nearest to the submission creation date
            ["2025-03-04", "2025-04-04", "2025-02-04"],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            1,
            datetime.datetime(2025, 3, 4),  # consolidated_value
        ),
        (
            # no explicit future dates, they will be adjusted to be in the future and the nearest one will be taken
            ["2024-03-04", "2024-04-04", "2024-02-04"],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            1,
            datetime.datetime(2025, 3, 4),  # consolidated_value
        ),
        (
            # no future dates, they will NOT be adjusted to be in the future, we'll pick according to file order
            ["2024-03-04", "2024-04-04", "2024-02-04"],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            ExistingOrganizations.CNA.value,
            datetime.datetime(2024, 4, 4),  # consolidated_value
        ),
        (
            # only one correct date, it will be taken
            ["ASAP", "125435", "2024-02-04"],
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SUPPLEMENTAL_FORM],
            1,
            datetime.datetime(2025, 2, 4),  # consolidated_value
        ),
        (
            # no correct values
            ["ASAP", "125435"],
            [FileType.EMAIL, FileType.ACORD_FORM],
            1,
            None,  # consolidated_value
        ),
    ],
)
def test_eff_date_consolidator(
    app_context,
    set_up_current_user,
    submission,
    create_submission_level_extracted_data: Callable[[], SubmissionLevelExtractedData],
    value_l: list[str],
    file_types_l: list[FileType],
    org_id: int,
    consolidated_value,
):
    organization_fixture(id=org_id)
    db.session.commit()

    # GIVEN
    submission.created_at = datetime.datetime(2025, 3, 3)
    submission.report.organization_id = org_id
    consolidator = EffectiveDateConsolidator(
        context=SubmissionFieldsConsolidationContext(
            submission=submission,
            extracted_data_items={},
            extracted_data_collections={},
            consolidation_process=MagicMock(),
        ),
    )

    files = [file_fixture(file_type=file_type, submission_id=submission.id) for file_type in file_types_l]
    db.session.commit()

    data = [
        create_submission_level_extracted_data(
            value=value,
            entity_information=EntityInformation.POLICY_EFFECTIVE_START_DATE,
            source_details=SourceDetails.GENERATED,
            overwritten_file_id=file.id,
            is_valid=True,
        )
        for value, file in zip(value_l, files)
    ]

    # WHEN
    consolidator.consolidate(data)

    # THEN
    if consolidated_value:
        assert (
            consolidator.context.extracted_data_items[EntityInformation.POLICY_EFFECTIVE_START_DATE][0]
            == consolidated_value
        )
    else:
        assert consolidator.context.extracted_data_items.get(EntityInformation.POLICY_EFFECTIVE_START_DATE) is None

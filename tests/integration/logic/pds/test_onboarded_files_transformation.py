from collections import Counter
from pathlib import Path
from typing import Any, Dict, List
from uuid import UUID, uuid4
import json

from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.insurance import ProjectInsuranceType
from static_common.enums.parent import ParentType
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.models.business_resolution_data import BusinessResolutionData
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
)
from static_common.models.first_party import FirstPartyFieldEntity
from static_common.schemas.business_resolution_data import BusinessResolutionDataSchema
from static_common.schemas.file_onboarding import (
    LeanOnboardedFileSchema,
    OnboardedFileSchema,
)
import pytest

from copilot.logic.onboarded_files_transformation import (
    MergeDataRequest,
    _extract_aliases,
    _load_files_and_data,
    _map_entity_information_to_entity_idx,
    enhance_with_business_resolution_data,
    merge_onboarded_data,
    resolve_parent_type_n_id,
)
from copilot.logic.pds.project_insurance_utils import (
    _get_main_project_entity,
    _get_project_insurance_type_from_classification,
    _get_project_insurance_type_from_email,
)
from copilot.models import SubmissionBusiness
from copilot.models.files import ProcessedFile

CURRENT_DIR = Path(__file__).parent

file1_id = UUID("f8d4f728-e0ed-4b16-a03f-8f3a105f7366")
file2_id = UUID("a2514186-e8d3-40d9-b1ee-931ae3bb8e43")


@pytest.fixture()
def processed_files() -> list:
    file1_onboarded_data = {
        "fields": [
            {
                "name": "Field1",
                "value_type": "TEXT",
                "values": [
                    {"value": "V1", "entity_idx": 0, "file_idx": 0},
                    {"value": "", "entity_idx": 1, "file_idx": 0},
                ],
            },
            {
                "name": "Field2",
                "value_type": "TEXT",
                "values": [
                    {"value": "", "entity_idx": 0, "file_idx": 0},
                    {"value": "V2", "entity_idx": 1, "file_idx": 0},
                ],
            },
            {
                "name": "Field3",
                "value_type": "TEXT",
                "values": [
                    {"value": "V3", "entity_idx": 0, "file_idx": 0},
                    {"value": "V3", "entity_idx": 0, "file_idx": 0},
                    {"value": "V3", "entity_idx": 1, "file_idx": 0},
                ],
            },
        ],
        "entity_information": [
            {
                "name": "Name",
                "value_type": "TEXT",
                "values": [
                    {"value": "business 1", "entity_idx": 0, "file_idx": 0},
                    {"value": "business 2", "entity_idx": 1, "file_idx": 0},
                ],
            }
        ],
        "entities": [
            {"type": "Business", "id": "business 1"},
            {"type": "Business", "id": "business 2"},
        ],
        "files": [file1_id],
    }

    file2_onboarded_data = {
        "fields": [
            {
                "name": "Field1",
                "value_type": "TEXT",
                "values": [
                    {"value": "V1", "entity_idx": 0, "file_idx": 0},
                    {"value": "", "entity_idx": 0, "file_idx": 0},
                ],
            },
            {
                "name": "Field2",
                "value_type": "TEXT",
                "values": [
                    {"value": "", "entity_idx": 0, "file_idx": 0},
                ],
            },
            {
                "name": "Field3",
                "value_type": "TEXT",
                "values": [
                    {"value": "V5", "entity_idx": 0, "file_idx": 0},
                ],
            },
            {
                "name": "Field4",
                "value_type": "TEXT",
                "values": [
                    {"value": "V6", "entity_idx": 0, "file_idx": 0},
                ],
            },
        ],
        "entity_information": [
            {
                "name": "Name",
                "value_type": "TEXT",
                "values": [
                    {"value": "business1", "entity_idx": 0, "file_idx": 0},
                ],
            },
            {
                "name": "Name",
                "value_type": "TEXT",
                "values": [
                    {"value": "business 1", "entity_idx": 0, "file_idx": 0},
                ],
            },
        ],
        "entities": [{"type": "Business", "id": "business 1"}],
        "files": [file2_id],
    }

    return [
        (
            ProcessedFile(file_id=file1_id, onboarded_data=LeanOnboardedFileSchema().load(file1_onboarded_data)),
            ClassificationDocumentType.SOV_PDF,
        ),
        (
            ProcessedFile(file_id=file2_id, onboarded_data=LeanOnboardedFileSchema().load(file2_onboarded_data)),
            ClassificationDocumentType.ACORD_125,
        ),
    ]


@pytest.fixture
def merged_data() -> dict[str, Any]:
    with open("tests/data/parsing/onboarded_files/merged_data_with_dummy_entity.json") as file:
        return json.load(file)


@pytest.fixture
def onboarded_drivers_file() -> OnboardedFile:
    with open("tests/data/parsing/onboarded_files/onboarded_drivers_file.json") as file:
        return OnboardedFileSchema().load(json.load(file))


@pytest.fixture
def onboarded_vehicles_file() -> OnboardedFile:
    with open("tests/data/parsing/onboarded_files/onboarded_vehicles_file.json") as file:
        return OnboardedFileSchema().load(json.load(file))


def test_merge_onboarded_data(processed_files: list) -> None:
    request = [
        MergeDataRequest(file_id=file.file_id, data=file.onboarded_data, classification=classification)
        for file, classification in processed_files
    ]
    merged_data = merge_onboarded_data(
        files=request, if_submission_data=True, drop_empty_values=True, allow_multiple_observations=True
    )
    assert len(merged_data.entities) == 2
    assert len(merged_data.files) == 2
    assert len(merged_data.entity_information[0].values) == 4

    assert merged_data.entity_information[0].values[0].value == "business 1"
    assert merged_data.entity_information[0].values[0].file_idx == 0
    assert merged_data.entity_information[0].values[1].value == "business 1"  # bc of allow_multiple_observations = True
    assert merged_data.entity_information[0].values[1].file_idx == 1
    assert merged_data.entity_information[0].values[2].value == "business1"
    assert merged_data.entity_information[0].values[2].file_idx == 1
    assert merged_data.entity_information[0].values[3].value == "business 2"
    assert merged_data.entity_information[0].values[3].file_idx == 0

    assert len(merged_data.fields) == 4
    values_for_field1 = next(x.values for x in merged_data.fields if x.name == "Field1")
    assert len(values_for_field1) == 3
    assert any(x for x in values_for_field1 if x.value == "V1" and x.entity_idx == 0)
    assert any(x for x in values_for_field1 if x.value == "" and x.entity_idx == 1)

    values_for_field2 = next(x.values for x in merged_data.fields if x.name == "Field2")
    assert len(values_for_field2) == 2

    values_for_field3 = next(x.values for x in merged_data.fields if x.name == "Field3")
    assert len(values_for_field3) == 4

    values_for_field4 = next(x.values for x in merged_data.fields if x.name == "Field4")
    assert len(values_for_field4) == 1


def test_merge_onboarded_data_merge_values(processed_files: list) -> None:
    request = [
        MergeDataRequest(file_id=file.file_id, data=file.onboarded_data, classification=classification)
        for file, classification in processed_files
    ]
    merged_data = merge_onboarded_data(request, True, True, True)

    values_for_field3 = next(x.values for x in merged_data.fields if x.name == "Field3")
    assert len(values_for_field3) == 2


def test_merge_onboarded_data_with_business_resolution_null_entity_id(processed_files: list) -> None:
    processed_files[0][0].business_resolution_data = [
        BusinessResolutionData(entity_idx=0, entity_id=None),
        BusinessResolutionData(entity_idx=1, entity_id=None),
    ]
    processed_files[1][0].business_resolution_data = [BusinessResolutionData(entity_idx=0, entity_id=None)]
    request = [
        MergeDataRequest(file_id=file.file_id, data=file.onboarded_data, classification=classification)
        for file, classification in processed_files
    ]
    merged_data = merge_onboarded_data(request, True, True, True)

    assert len(merged_data.entities) == 2
    assert sum(1 for field in merged_data.entity_information for value in field.values) == 3
    assert sum(1 for field in merged_data.fields for value in field.values) == 7


@pytest.fixture
def processed_files_with_business_resolution(processed_files: list) -> list[ProcessedFile]:
    ers_id = uuid4()
    processed_files[0][0].onboarded_data.entities[0].id = "business 11"
    business_resolution_data = {
        "resolution_data": [
            {
                "entity_idx": 0,
                "entity_id": ers_id,
            },
            {
                "entity_idx": 1,
                "entity_id": None,
            },
        ]
    }

    processed_files[0][0].business_resolution_data = business_resolution_data
    processed_files[1][0].business_resolution_data = [BusinessResolutionData(entity_idx=0, entity_id=ers_id)]
    return processed_files


def test_merge_onboarded_data_with_business_resolution_data(
    app_context, processed_files_with_business_resolution: list
) -> None:
    for file, _ in processed_files_with_business_resolution:
        enhance_with_business_resolution_data(file, data_field="onboarded_data")
    request = [
        MergeDataRequest(file_id=file.file_id, data=file.onboarded_data, classification=classification)
        for file, classification in processed_files_with_business_resolution
    ]
    merged_data = merge_onboarded_data(request, True, True, True)
    assert len(merged_data.entities) == 2
    assert sum(1 for field in merged_data.entity_information for value in field.values) == 4
    assert sum(1 for field in merged_data.fields for value in field.values) == 7


def test_enhance_with_business_resolution_data_fni(app_context, processed_files_with_business_resolution: list) -> None:
    processed_files_with_business_resolution[0][0].onboarded_data.entities[
        0
    ].entity_named_insured = SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
    processed_files_with_business_resolution[0][0].onboarded_data.entities[
        1
    ].entity_named_insured = SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
    processed_files_with_business_resolution[0][0].business_resolution_data["resolution_data"][0]["is_fni"] = True
    processed_files_with_business_resolution[0][0].business_resolution_data["resolution_data"][1]["is_fni"] = False
    enhance_with_business_resolution_data(processed_files_with_business_resolution[0][0], data_field="onboarded_data")
    assert processed_files_with_business_resolution[0][0].onboarded_data.entities[0].is_fni is True
    assert processed_files_with_business_resolution[0][0].onboarded_data.entities[1].is_fni is False

    # Check that after the entity was resolved, we still update entity_named_insured field
    processed_files_with_business_resolution[0][0].business_resolution_data["resolution_data"][0]["is_fni"] = False
    enhance_with_business_resolution_data(processed_files_with_business_resolution[0][0], data_field="onboarded_data")
    assert processed_files_with_business_resolution[0][0].onboarded_data.entities[0].is_fni is False


def test_enhance_with_business_resolution_data_entity_role(
    app_context, processed_files_with_business_resolution: list
) -> None:
    processed_files_with_business_resolution[0][0].onboarded_data.entities[
        0
    ].entity_role = SubmissionBusinessEntityRole.PROJECT
    processed_files_with_business_resolution[0][0].onboarded_data.entities[1].entity_role = None
    processed_files_with_business_resolution[0][0].business_resolution_data["resolution_data"][0]["entity_role"] = None
    processed_files_with_business_resolution[0][0].business_resolution_data["resolution_data"][1][
        "entity_role"
    ] = SubmissionBusinessEntityRole.PROJECT
    enhance_with_business_resolution_data(processed_files_with_business_resolution[0][0], data_field="onboarded_data")
    assert processed_files_with_business_resolution[0][0].onboarded_data.entities[0].entity_role is None
    assert (
        processed_files_with_business_resolution[0][0].onboarded_data.entities[1].entity_role
        is SubmissionBusinessEntityRole.PROJECT
    )

    processed_files_with_business_resolution[0][0].business_resolution_data["resolution_data"][1][
        "entity_role"
    ] = SubmissionBusinessEntityRole.GENERAL_CONTRACTOR
    enhance_with_business_resolution_data(processed_files_with_business_resolution[0][0], data_field="onboarded_data")
    assert (
        processed_files_with_business_resolution[0][0].onboarded_data.entities[1].entity_role
        == SubmissionBusinessEntityRole.GENERAL_CONTRACTOR
    )


@pytest.fixture
def business_resolution_data_without_idx() -> BusinessResolutionData:
    return BusinessResolutionData(
        entity_idx=None,
        requested_name="Test business name no entity_idx",
        requested_address="Test business address no entity_idx",
        entity_id=uuid4(),
    )


@pytest.fixture
def processed_files_with_business_resolution_2(processed_files, business_resolution_data_without_idx):
    processed_files[1][0].onboarded_data.entities[0].id = "business 11"
    business_resolution_data = [
        BusinessResolutionData(
            entity_idx=0,
            requested_name="Test business name",
            requested_address="Test business address",
            entity_id=uuid4(),
        ),
        business_resolution_data_without_idx,
    ]

    processed_files[1][0].business_resolution_data = {
        "resolution_data": BusinessResolutionDataSchema().dump(business_resolution_data, many=True)
    }
    return processed_files[1][0]


def test_enhance_with_business_resolution_data_with_extra_entities(
    app_context,
    processed_files_with_business_resolution_2: ProcessedFile,
) -> None:
    onboarded_data = processed_files_with_business_resolution_2.onboarded_data

    business_resolution_data = BusinessResolutionDataSchema().load(
        processed_files_with_business_resolution_2.business_resolution_data, many=True
    )
    assert len([brd for brd in business_resolution_data if brd.entity_idx is not None]) == 1

    enhance_with_business_resolution_data(processed_files_with_business_resolution_2, data_field="onboarded_data")

    business_resolution_data = BusinessResolutionDataSchema().load(
        processed_files_with_business_resolution_2.business_resolution_data, many=True
    )
    assert len([brd for brd in business_resolution_data if brd.entity_idx is not None]) == 2

    assert len(onboarded_data.entities) == 2
    assert sum(len(f.values) for f in onboarded_data.entity_information if f.name == "Address") == 2
    assert sum(len(f.values) for f in onboarded_data.entity_information if f.name == "Name") == 3
    assert "test business name no entity_idx test business address no entity_idx" in [
        e.remote_id for e in onboarded_data.entities
    ]


def test_merge_onboarded_data_when_adding_dummy_entity(processed_files: list, merged_data: dict) -> None:
    processed_files[1][0].onboarded_data.entity_information[0].values.append(
        ResolvedDataValue(value="primary insured", entity_idx=None, file_idx=0)
    )
    processed_files[1][0].onboarded_data.fields[3].values.append(
        ResolvedDataValue(value="V7", entity_idx=None, file_idx=0)
    )
    request = [
        MergeDataRequest(file_id=file.file_id, data=file.onboarded_data, classification=classification)
        for file, classification in processed_files
    ]
    generated_merged_data = merge_onboarded_data(request, True, True)
    assert OnboardedFileSchema().dump(generated_merged_data) == merged_data


@pytest.fixture
def merge_data_requests() -> list[MergeDataRequest]:
    data1 = {
        "files": [str(file1_id)],
        "entities": [
            {
                "type": "Business",
                "id": "business 1",
                "entity_named_insured": "FIRST_NAMED_INSURED",
            },
            {
                "type": "Business",
                "id": "business 2",
            },
            {
                "type": "Business",
                "id": "business 3",
            },
        ],
    }
    data2 = {
        "files": [str(file2_id)],
        "entities": [
            {
                "type": "Business",
                "id": "business 1",
            },
            {
                "type": "Primary Insured",
                "id": "business 2",
            },
            {
                "type": "Business",
                "id": "business 3",
            },
        ],
    }
    m1 = MergeDataRequest(
        file_id=file1_id, data=LeanOnboardedFileSchema().load(data1), classification=ClassificationDocumentType.SOV_PDF
    )
    m2 = MergeDataRequest(
        file_id=file2_id,
        data=LeanOnboardedFileSchema().load(data2),
        classification=ClassificationDocumentType.ACORD_125,
    )
    return [m1, m2]


def test_load_entity_files_and_data(merge_data_requests: list[MergeDataRequest]) -> None:
    file_ids, data = _load_files_and_data(merge_data_requests)
    assert len(file_ids) == 2
    assert file1_id in file_ids
    assert file2_id in file_ids
    assert len(data) == 2
    assert file1_id in data
    assert file2_id in data


def test_resolve_parent_type_n_id_when_entity_is_none():
    entity = None
    field = ResolvedDataField(
        name="Inventory",
        values=[],
        value_type=FieldType.INTEGER,
        fact_subtype_id=FactSubtypeID.INVENTORY,
    )
    assert resolve_parent_type_n_id(entity, ClassificationDocumentType.ACORD_125, field) == (
        ParentType.SUBMISSION,
        None,
    )


def test_sov_business_parent_type_set_to_premises():
    entity = FirstPartyFieldEntity(parent_type=ParentType.BUSINESS)
    field = ResolvedDataField(
        name="Property Description",
        values=[],
        value_type=FieldType.TEXT,
        fact_subtype_id=FactSubtypeID.PROPERTY_DESCRIPTION,
    )
    assert resolve_parent_type_n_id(entity, ClassificationDocumentType.SOV, field) == (ParentType.PREMISES, None)


def test_get_main_project_entity_single():
    submissionBusinesses: list[SubmissionBusiness] = [
        SubmissionBusiness(entity_role=None),
        SubmissionBusiness(entity_role=SubmissionBusinessEntityRole.PROJECT),
    ]

    result = _get_main_project_entity(submissionBusinesses)

    assert result == submissionBusinesses[1]


def test_get_main_project_entity_with_named_insured():
    submissionBusinesses: list[SubmissionBusiness] = [
        SubmissionBusiness(entity_role=None),
        SubmissionBusiness(entity_role=SubmissionBusinessEntityRole.PROJECT),
        SubmissionBusiness(
            entity_role=SubmissionBusinessEntityRole.PROJECT,
            named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        ),
        SubmissionBusiness(entity_role=SubmissionBusinessEntityRole.PROJECT),
    ]

    result = _get_main_project_entity(submissionBusinesses)

    assert result == submissionBusinesses[2]


def test_get_main_project_entity_none():
    submissionBusinesses: list[SubmissionBusiness] = [
        SubmissionBusiness(entity_role=None),
        SubmissionBusiness(entity_role=SubmissionBusinessEntityRole.GENERAL_CONTRACTOR),
        SubmissionBusiness(
            entity_role=SubmissionBusinessEntityRole.BUYER,
            named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        ),
        SubmissionBusiness(entity_role=SubmissionBusinessEntityRole.SELLER),
    ]

    result = _get_main_project_entity(submissionBusinesses)

    assert result == None


def test_get_project_insurance_type_none():
    uuid1 = uuid4()
    file_id_to_classification: dict[UUID, ClassificationDocumentType] = {uuid1: ClassificationDocumentType.SOV}

    assert _get_project_insurance_type_from_classification(file_id_to_classification) == None


def test_get_project_insurance_type_classification():
    uuid1 = uuid4()
    file_id_to_classification: dict[UUID, ClassificationDocumentType] = {
        uuid1: ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION
    }

    assert (
        _get_project_insurance_type_from_classification(file_id_to_classification)
        == ProjectInsuranceType.PROJECT_SPECIFIC
    )


def test_get_project_insurance_type_conflicting():
    uuid1 = uuid4()
    uuid2 = uuid4()
    file_id_to_classification: dict[UUID, ClassificationDocumentType] = {
        uuid1: ClassificationDocumentType.PROJECT_SPECIFIC_OWNER_GC_SUPPLEMENTAL_APPLICATION,
        uuid2: ClassificationDocumentType.PROJECT_WRAP_UP_SUPPLEMENTAL_APPLICATION_EDITABLE_DOC,
    }

    assert _get_project_insurance_type_from_classification(file_id_to_classification) == None


def test_get_project_insurance_type_from_email():
    processed_data = OnboardedFile(
        entity_information=[
            ResolvedDataField(
                name=EntityInformation.PROJECT_INSURANCE_TYPE.value,
                value_type=FieldType.TEXT,
                values=[ResolvedDataValue(value=ProjectInsuranceType.PROJECT_SPECIFIC.value)],
            )
        ]
    )

    assert _get_project_insurance_type_from_email(processed_data) == ProjectInsuranceType.PROJECT_SPECIFIC


def test_get_project_insurance_type_from_email_none():
    processed_data = OnboardedFile(entity_information=[])
    assert _get_project_insurance_type_from_email(processed_data) == None


def test_merge_takes_into_consideration_fact_subtype(onboarded_drivers_file, onboarded_vehicles_file):
    request = [
        MergeDataRequest(
            file_id=uuid4(), data=onboarded_drivers_file, classification=ClassificationDocumentType.DRIVERS
        ),
        MergeDataRequest(
            file_id=uuid4(), data=onboarded_vehicles_file, classification=ClassificationDocumentType.VEHICLES
        ),
    ]
    merged_data = merge_onboarded_data(request, True, True)
    # Ensure we don't merge the field State from the driver file with the field State from the vehicle file
    assert len([field for field in merged_data.fields if field.name == "State"]) == 2


@pytest.fixture
def two_vehicles_processed_data():
    with open("tests/data/data_onboarding/two_vehicles_processed_data.json") as file:
        return OnboardedFileSchema().load(json.load(file))


@pytest.fixture
def four_vehicles_processed_data():
    with open("tests/data/data_onboarding/four_vehicles_processed_data.json") as file:
        return OnboardedFileSchema().load(json.load(file))


def test_merge_do_not_dedup_vin(two_vehicles_processed_data, four_vehicles_processed_data):
    request = [
        MergeDataRequest(
            file_id=uuid4(), data=two_vehicles_processed_data, classification=ClassificationDocumentType.VEHICLES
        ),
        MergeDataRequest(
            file_id=uuid4(), data=four_vehicles_processed_data, classification=ClassificationDocumentType.VEHICLES
        ),
    ]
    merged_data = merge_onboarded_data(request, True, True)
    # Ensure we don't merge the field State from the driver file with the field State from the vehicle file
    assert len(merged_data.entities) == 4
    assert merged_data.fields[0].name == "VIN"
    assert len(merged_data.fields[0].values) == 6
    assert merged_data.fields[1].name == "Make"
    assert len(merged_data.fields[1].values) == 4


@pytest.fixture
def processed_files_with_observed_value():
    file1_onboarded_data = {
        "fields": [],
        "entity_information": [
            {
                "name": "Name",
                "value_type": "TEXT",
                "values": [
                    {
                        "value": "Monahan-pacific Construction",
                        "entity_idx": 0,
                    },
                ],
            },
            {
                "name": "Address",
                "value_type": "TEXT",
                "values": [
                    {
                        "value": (
                            "1101 5TH AVE, SAN RAFAEL, CA 94901, US (1101 Fifth Avenue Suite 300, San Rafael, CA 94901)"
                        ),
                        "entity_idx": 0,
                        "observed_value": "1101 Fifth Avenue Suite 300, San Rafael, CA 94901",
                    },
                ],
            },
        ],
        "entities": [{"type": "Business", "id": "business 1"}],
        "files": [file1_id],
    }

    file2_onboarded_data = {
        "fields": [],
        "entity_information": [
            {
                "name": "Name",
                "value_type": "TEXT",
                "values": [
                    {
                        "value": "Monahan-pacific Construction (Monahan Pacific Corporation)",
                        "entity_idx": 0,
                        "observed_value": "Monahan Pacific Corporation",
                    },
                ],
            },
            {
                "name": "Address",
                "value_type": "TEXT",
                "values": [
                    {
                        "value": (
                            "1101 5TH AVE, SAN RAFAEL, CA 94901, US (1101 Fifth Avenue Suite 300, San Rafael, CA 94901)"
                        ),
                        "entity_idx": 0,
                        "observed_value": "1101 Fifth Avenue Suite 300, San Rafael, CA 94901",
                    },
                ],
            },
        ],
        "entities": [{"type": "Business", "id": "business 1"}],
        "files": [file2_id],
    }

    return [
        (
            ProcessedFile(file_id=file1_id, onboarded_data=LeanOnboardedFileSchema().load(file1_onboarded_data)),
            ClassificationDocumentType.SOV_PDF,
        ),
        (
            ProcessedFile(file_id=file2_id, onboarded_data=LeanOnboardedFileSchema().load(file2_onboarded_data)),
            ClassificationDocumentType.EMAIL,
        ),
    ]


def test_merge_onboarded_data_keeps_requested_name(processed_files_with_observed_value: list) -> None:
    request = [
        MergeDataRequest(file_id=file.file_id, data=file.onboarded_data, classification=classification)
        for file, classification in processed_files_with_observed_value
    ]
    merged_data = merge_onboarded_data(request, True, True)
    assert len(merged_data.entities) == 1
    assert merged_data.entity_information[1].name == "Name"
    assert len(merged_data.entity_information[1].values) == 2
    assert merged_data.entity_information[1].values[1].observed_value


@pytest.fixture
def processed_data_with_relations(
    four_vehicles_processed_data,
) -> list[tuple[ProcessedFile, ClassificationDocumentType]]:
    with open("tests/data/processed_data/with_relationships.json") as file:
        data = LeanOnboardedFileSchema().loads(file.read())
    return [
        (
            ProcessedFile(file_id=uuid4(), processed_data=four_vehicles_processed_data),
            ClassificationDocumentType.VEHICLES,
        ),
        (
            ProcessedFile(file_id=uuid4(), processed_data=data),
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION,
        ),
    ]


def test_merge_data_with_relations(processed_data_with_relations: list) -> None:
    request = [
        MergeDataRequest(file_id=file.file_id, data=file.processed_data, classification=classification)
        for file, classification in processed_data_with_relations
    ]
    merged_data = merge_onboarded_data(request, True, True)
    len(merged_data.entities)
    assert len(merged_data.entities) == 26
    assert len(merged_data.entity_information) == 3
    assert len(merged_data.fields) == 2
    relationship_type_field = next(f for f in merged_data.entity_information if f.name == "Entity Relationship Type")
    assert all(v.value == "Owner" for v in relationship_type_field.values)
    assert len(relationship_type_field.values) == 19
    relations = {(v.entity_idx, v.related_entity_idx) for v in relationship_type_field.values}
    the_cantina_trust_idx = next(idx for idx, e in enumerate(merged_data.entities) if e.id == "the_cantina_trust")
    cantina_corporation_idx = next(idx for idx, e in enumerate(merged_data.entities) if e.id == "cantina_corporation")
    jout_hospitality_llc_idx = next(idx for idx, e in enumerate(merged_data.entities) if e.id == "jout_hospitality_llc")
    assert (the_cantina_trust_idx, jout_hospitality_llc_idx) in relations
    assert (cantina_corporation_idx, jout_hospitality_llc_idx) in relations
    assert sum(1 for relation in relations if relation[0] == jout_hospitality_llc_idx) == 9


def test_extracting_aliases():
    obd = """
    {
        "files": [],
        "fields": [],
        "entities": [
            {
                "id": "75b43574-75bf-420a-ad74-ea8c127dc2ca",
                "type": "Business",
                "resolved": true,
                "remote_id": "aspire at 610 8900 lakes at 610 drive, houston, tx 77054"
            }
        ],
        "additional_data": {},
        "entity_information": [
            {
                "name": "Name",
                "values": [
                    {
                        "value": "Aspire at 610",
                        "entity_idx": 0,
                        "observed_value": "Aspire at 610"
                    }
                ],
                "value_type": "TEXT"
            },
            {
                "name": "Address",
                "values": [
                    {
                        "value": "8900 LAKES AT 610TH DR, HOUSTON, TX 77054, US (8900 Lakes at 610 Drive, Houston, TX 77054)",
                        "entity_idx": 0,
                        "observed_value": "8900 Lakes at 610 Drive, Houston, TX 77054"
                    }
                ],
                "value_type": "TEXT"
            },
            {
                "name": "Name Aliases",
                "values": [
                    {
                        "value": "Aspire 610 LLC",
                        "entity_idx": 0,
                        "observed_value": "['Aspire 610 LLC']"
                    },
                    {
                        "value": "Centerline Corporate Partners XX L.P., DBM Holdings, LLC",
                        "entity_idx": 0,
                        "observed_value": "['Centerline Corporate Partners XX L.P.', 'DBM Holdings, LLC']"
                    }
                ],
                "value_type": "TEXT_ARRAY"
            }
        ]
    }
    """
    entity_information_for_idx = _map_entity_information_to_entity_idx(LeanOnboardedFileSchema().loads(obd))[0]
    aliases = _extract_aliases(entity_information_for_idx, 0, None)

    assert set(aliases) == {"Aspire 610 LLC", "Centerline Corporate Partners XX L.P.", "DBM Holdings, LLC"}

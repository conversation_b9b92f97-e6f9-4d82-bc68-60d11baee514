from typing import List
from unittest.mock import MagicMock
from uuid import UUID, uuid4

from static_common.enums.file_type import FileType
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.models.address import Address
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
)
from static_common.models.paragon import (
    ImsControlNumber,
    ImsCoverage,
    ImsQuoteGuid,
    ParagonClientSubmissionId,
)
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
from static_common.schemas.paragon import ParagonClientSubmissionIdSchema
import flask
import pytest

from copilot.clients.ers_v3 import ERSClientV3
from copilot.logic.paragon import (
    IMS_COVERAGE_MAPPING,
    assign_paragon_client_id,
    determine_ims_coverages_for_es_submission,
    extract_fni_requested_data_from_submission_files,
    get_ims_search_input,
    handle_coverages,
)
from copilot.models import Submission, db
from copilot.models.reports import SubmissionClientId, SubmissionCoverage
from copilot.models.types import CoverageType, FieldType
from tests.integration.factories import (
    coverage_fixture,
    file_fixture,
    organization_fixture,
    processed_file_fixture,
    report_and_submission_fixture,
    submission_business_fixture,
    submission_coverage_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj

paragon_schema = ParagonClientSubmissionIdSchema()
onboarded_file_schema = LeanOnboardedFileSchema()


@pytest.fixture
def submission() -> Submission:
    organization_fixture(id=37)
    user_fixture(organization_id=37)
    _, submission = report_and_submission_fixture(organization_id=37)
    db.session.commit()
    return submission


@pytest.fixture
def paragon_client_id() -> ParagonClientSubmissionId:
    return ParagonClientSubmissionId(
        ims_insured_guid=uuid4(),
        ims_submission_id=uuid4(),
        ims_quote_guids=[
            ImsQuoteGuid(uuid4(), ImsCoverage.CASUALTY_PRIMARY),
            ImsQuoteGuid(uuid4(), ImsCoverage.CASUALTY_EXCESS_SUPPORTED),
            ImsQuoteGuid(uuid4(), ImsCoverage.AUTO_DEALERS_BREAKDOWN),
            ImsQuoteGuid(uuid4(), ImsCoverage.FALLS_LAKE_AUTO_DEALERS_PACKAGE),
        ],
        ims_control_numbers=[
            ImsControlNumber(1, ImsCoverage.CASUALTY_PRIMARY),
            ImsControlNumber(2, ImsCoverage.CASUALTY_EXCESS_SUPPORTED),
            ImsControlNumber(3, ImsCoverage.AUTO_DEALERS_BREAKDOWN),
            ImsControlNumber(4, ImsCoverage.FALLS_LAKE_AUTO_DEALERS_PACKAGE),
        ],
    )


def test_assign_paragon_client_id(app_context, paragon_client_id, submission):
    assert len(SubmissionClientId.query.all()) == 0

    assign_paragon_client_id(submission.id, paragon_client_id, indexes=[0, 3], client_id_source=None)

    submission_client_ids: list[SubmissionClientId] = SubmissionClientId.query.all()
    assert len(submission_client_ids) == 1

    sci = submission_client_ids[0]
    client_id: ParagonClientSubmissionId = paragon_schema.loads(sci.client_submission_id)
    assert client_id.ims_insured_guid == paragon_client_id.ims_insured_guid
    assert len(client_id.ims_quote_guids) == 2
    assert client_id.ims_quote_guids[0].guid == paragon_client_id.ims_quote_guids[0].guid
    assert client_id.ims_quote_guids[1].guid == paragon_client_id.ims_quote_guids[3].guid
    assert len(client_id.ims_control_numbers) == 2
    assert client_id.ims_control_numbers[0].control_number == paragon_client_id.ims_control_numbers[0].control_number
    assert client_id.ims_control_numbers[1].control_number == paragon_client_id.ims_control_numbers[3].control_number

    assign_paragon_client_id(submission.id, paragon_client_id, indexes=[0], client_id_source=None)
    submission_client_ids: list[SubmissionClientId] = SubmissionClientId.query.all()
    assert len(submission_client_ids) == 1
    assert len(client_id.ims_quote_guids) == 2
    assert client_id.ims_quote_guids[0].guid == paragon_client_id.ims_quote_guids[0].guid
    assert len(client_id.ims_control_numbers) == 2
    assert client_id.ims_control_numbers[0].control_number == paragon_client_id.ims_control_numbers[0].control_number


def test_handle_coverages(app_context, submission, paragon_client_id, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=lambda x: True,
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )
    IMS_COVERAGE_MAPPING[ImsCoverage.AUTO_DEALERS_BREAKDOWN] = {
        "name": "businessAuto",
        "type": None,
    }
    IMS_COVERAGE_MAPPING[ImsCoverage.FALLS_LAKE_AUTO_DEALERS_PACKAGE] = {
        "name": "package",
        "type": None,
    }

    c1 = coverage_fixture(
        name="liability",
        display_name="Liability",
        coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS],
        organization_id=37,
    )
    c2 = coverage_fixture(name="businessAuto", display_name="Business Auto", organization_id=37)
    coverage_fixture(name="package", display_name="Package", organization_id=37)
    c4 = coverage_fixture(
        name="managementLiability",
        display_name="Management Liability",
        coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS],
        organization_id=37,
    )

    sc1 = submission_coverage_fixture(
        id=UUID("00000000-0000-0000-0000-000000000000"),
        coverage_id=c1.id,
        submission_id=submission.id,
        coverage_type=CoverageType.PRIMARY,
    )
    submission_coverage_fixture(
        id=UUID("00000000-0000-0000-0000-000000000001"),
        coverage_id=c1.id,
        submission_id=submission.id,
        coverage_type=CoverageType.EXCESS,
    )
    sc3 = submission_coverage_fixture(
        id=UUID("00000000-0000-0000-0000-000000000002"), coverage_id=c2.id, submission_id=submission.id
    )
    sc4 = submission_coverage_fixture(
        id=UUID("00000000-0000-0000-0000-000000000003"),
        coverage_id=c4.id,
        submission_id=submission.id,
        coverage_type=CoverageType.PRIMARY,
    )
    db.session.commit()

    sub_coverages: list[SubmissionCoverage] = SubmissionCoverage.query.order_by(SubmissionCoverage.id).all()
    assert len(sub_coverages) == 4

    ims_quotes = paragon_client_id.ims_quote_guids
    del ims_quotes[1]
    handle_coverages(submission, ims_quotes)

    sub_coverages: list[SubmissionCoverage] = SubmissionCoverage.query.order_by(SubmissionCoverage.id).all()
    assert len(sub_coverages) == 4
    assert sub_coverages[0].id == sc1.id
    assert sub_coverages[1].id == sc3.id
    assert sub_coverages[2].id == sc4.id
    assert sub_coverages[3].coverage.name == "package"
    assert sub_coverages[3].coverage_type is None


def _get_extract_fni_test_data() -> list[tuple[list[tuple[FileType, OnboardedFile]], str, str]]:
    def _create_onboarded_file_with_fni_data(name: str, address: str) -> OnboardedFile:
        return OnboardedFile(
            entities=[
                SubmissionEntity(entity_named_insured=SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED),
                SubmissionEntity(entity_named_insured=None),
                SubmissionEntity(entity_named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED),
            ],
            entity_information=[
                ResolvedDataField(
                    name="Address",
                    values=[
                        ResolvedDataValue(
                            value="661 Blanding Blvd. Ste. 510, 514, 516 & 518, Orange, Park, FL 320703",
                            entity_idx=None,
                        ),
                        ResolvedDataValue(value="440 Bellevue Way NE, Bellevue, WA 98004", entity_idx=1),
                        ResolvedDataValue(value=address, entity_idx=2),
                        ResolvedDataValue(value="1234 Main St., Anytown, USA 12345", entity_idx=0),
                    ],
                    value_type=FieldType.TEXT,
                ),
                ResolvedDataField(
                    name="Name",
                    values=[
                        ResolvedDataValue(value="ABC Company", entity_idx=None),
                        ResolvedDataValue(value="XYZ Company", entity_idx=1),
                        ResolvedDataValue(value=name, entity_idx=2),
                        ResolvedDataValue(value="123 Company", entity_idx=0),
                    ],
                    value_type=FieldType.TEXT,
                ),
            ],
        )

    # Basic extraction
    first_test_case = (
        [
            (
                FileType.EMAIL,
                _create_onboarded_file_with_fni_data(
                    name="John Doe", address="351 Zoo Parkway, Jacksonville, FL 32226"
                ),
            )
        ],
        "John Doe",
        "351 Zoo Parkway, Jacksonville, FL 32226",
    )

    # Prioritize EMAIL
    second_test_case = (
        [
            (
                FileType.ACORD_FORM,
                _create_onboarded_file_with_fni_data(name="Jane Doe", address="1234 Main St., Anytown, USA 12345"),
            ),
            (
                FileType.EMAIL,
                _create_onboarded_file_with_fni_data(
                    name="John Snow", address="351 Zoo Parkway, Jacksonville, FL 32226"
                ),
            ),
        ],
        "John Snow",
        "351 Zoo Parkway, Jacksonville, FL 32226",
    )

    # Prioritize ACORD if EMAIL is empty
    third_test_case = (
        [
            (
                FileType.ACORD_FORM,
                _create_onboarded_file_with_fni_data(name="Jane Doe", address="1234 Main St., Anytown, USA 12345"),
            ),
            (FileType.EMAIL, _create_onboarded_file_with_fni_data(name="", address="")),
        ],
        "Jane Doe",
        "1234 Main St., Anytown, USA 12345",
    )

    return [first_test_case, second_test_case, third_test_case]


@pytest.mark.parametrize("fake_files, expected_name, expected_address", _get_extract_fni_test_data())
def test_extract_fni_requested_data(
    app_context, fake_files: list[tuple[FileType, OnboardedFile]], expected_name: str, expected_address: str
):
    organization_fixture()
    user_fixture()

    rep, sub = report_and_submission_fixture()
    for file_type, onboarded_file in fake_files:
        file = file_fixture(file_type=file_type, submission_id=sub.id)
        processed_file_fixture(file_id=file.id, processed_data=onboarded_file_schema.dump(onboarded_file))

    db.session.commit()

    name, address = extract_fni_requested_data_from_submission_files(sub)
    assert name == expected_name
    assert address == expected_address


def test_determine_ims_coverages_for_es_submission(app_context):
    organization_fixture()
    user_fixture()

    logger_mock = MagicMock()

    rep, sub = report_and_submission_fixture()
    cov = coverage_fixture(name="liability", display_name="Liability")

    db.session.commit()

    # No coverages on the sub = fallback to GL + supported XS
    ims_coverages = determine_ims_coverages_for_es_submission(sub, logger_mock)
    assert ims_coverages == [ImsCoverage.CASUALTY_PRIMARY, ImsCoverage.CASUALTY_EXCESS_SUPPORTED]

    sub_cov = submission_coverage_fixture(coverage_id=cov.id, submission_id=sub.id, coverage_type=CoverageType.PRIMARY)

    db.session.commit()

    # Only primary = GL
    ims_coverages = determine_ims_coverages_for_es_submission(sub, logger_mock)
    assert ims_coverages == [ImsCoverage.CASUALTY_PRIMARY]

    sub_cov.coverage_type = CoverageType.EXCESS

    db.session.commit()

    # Only excess = Unsupported XS
    ims_coverages = determine_ims_coverages_for_es_submission(sub, logger_mock)
    assert ims_coverages == [ImsCoverage.CASUALTY_EXCESS_UNSUPPORTED]

    submission_coverage_fixture(coverage_id=cov.id, submission_id=sub.id, coverage_type=CoverageType.PRIMARY)

    db.session.commit()

    # Both primary and excess = GL + supported XS
    ims_coverages = determine_ims_coverages_for_es_submission(sub, logger_mock)
    assert ims_coverages == [ImsCoverage.CASUALTY_PRIMARY, ImsCoverage.CASUALTY_EXCESS_SUPPORTED]


def test_get_ims_search_input(app_context, mocker):
    organization_fixture()
    user_fixture()

    report, sub = report_and_submission_fixture()
    submission_business_fixture(
        submission_id=sub.id,
        business_id=uuid4(),
        named_insured="FIRST_NAMED_INSURED",
        requested_name="Staybridge Suites West Des",
        requested_address="6905 Lake Drive, West Des Moines, IA 50266, USA",
    )
    db.session.commit()

    entity_mock = MagicMock()

    legal_name = MagicMock()
    legal_name.type = "LEGAL_NAME"
    legal_name.value = "XYZ Corporation"

    dba_name = MagicMock()
    dba_name.type = "DBA_NAME"
    dba_name.value = "ABC Corporation"

    premises = MagicMock()
    premises.city = "WINSTON SALEM"
    premises.postal_code = "27106"
    premises.state = "NC"
    premises.address_line_1 = "123 MAIN ST"
    premises.address_line_2 = "SUITE 100"

    entity_premises = MagicMock()
    entity_premises.type = "PHYSICAL_ADDRESS"
    entity_premises.premises = premises

    entity_mock.names = [
        legal_name,
        dba_name,
    ]
    entity_mock.premises = [entity_premises]

    mocker.patch("flask.current_app.ers_client_v3.get_entity_if_exists", return_value=entity_mock)

    ims_search_input = get_ims_search_input(sub)

    assert ims_search_input.requested_name == "Staybridge Suites West Des"
    assert ims_search_input.requested_address == Address(
        city="WEST DES MOINES",
        zip_code="50266",
        state="IA",
        address_line_1="6905 LAKE DR",
    )

    assert ims_search_input.legal_name == "XYZ Corporation"
    assert ims_search_input.dba_name == "ABC Corporation"
    assert ims_search_input.all_names == ["XYZ Corporation", "ABC Corporation"]

    assert len(ims_search_input.entity_addresses) == 1
    assert ims_search_input.entity_addresses[0] == Address(
        city="WINSTON SALEM",
        zip_code="27106",
        state="NC",
        address_line_1="123 MAIN ST",
        address_line_2="SUITE 100",
    )

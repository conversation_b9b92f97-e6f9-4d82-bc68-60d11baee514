from dataclasses import dataclass
from unittest.mock import MagicMock
import pickle
import unittest

from common.clients.arch_api_client import ArchApiClient
from common.clients.smtp import SMTPNotificationsClient
from events_common.model.types import <PERSON>lepaEvents, SenderEvent
from sendgrid import Bcc, Cc, Mail, ReplyTo
from static_common.enums.organization import ExistingOrganizations
import flask
import pytest

from copilot.logic.report_emailing import send_email_to_broker
from copilot.models import (
    Brokerage,
    BrokerageEmployee,
    Organization,
    ReportV2,
    Submission,
    User,
    db,
)
from copilot.models.emails import Email
from copilot.models.types import EmailTemplateType, ReportTriageResult
from copilot.notifications.sendgrid_notification_client import (
    SendgridNotificationsClient,
)
from copilot.notifications.submission_notification_handler_v2 import (
    NotificationHandlerV2,
)
from tests.conftest import app_context
from tests.integration.factories import (
    brokerage_employee_fixture,
    brokerage_fixture,
    email_fixture,
    email_template_fixture,
    organization_fixture,
    report_fixture,
    submission_fixture,
    user_fixture,
)

mock_notifications_client = MagicMock(SendgridNotificationsClient)
mock_nationwide_smtp_notifications_client = MagicMock(SMTPNotificationsClient)
mock_notifications_client.send = MagicMock(return_value=None)
arch_api_mock = MagicMock(spec=ArchApiClient)
arch_api_mock.send_email = MagicMock(return_value=True)
notifications_handler = NotificationHandlerV2(
    env="test",
    sendgrid_notifications_client=mock_notifications_client,
    nationwide_smtp_notifications_client=mock_nationwide_smtp_notifications_client,
    arch_api_client=arch_api_mock,
)


@dataclass
class BaseEmailingTestData:
    organization: Organization
    user: User
    email: Email

    brokerage: Brokerage
    brokerage_contact: BrokerageEmployee
    submission_broker: BrokerageEmployee

    report: ReportV2
    submission: Submission

    def set_arch_organization(self):
        arch_organization = organization_fixture(id=ExistingOrganizations.Arch.value)

        # Create user in arch organization
        user = user_fixture(name="Sybil", email="<EMAIL>", organization_id=arch_organization.id)
        self.user = user

        db.session.commit()

        self.report.owner_id = user.id
        self.report.organization_id = arch_organization.id
        self.organization = arch_organization

    def set_admiral_organization(self):
        admiral_organization = organization_fixture(id=ExistingOrganizations.AdmiralInsuranceGroup.value)

        # Create user in arch organization
        user = user_fixture(name="Sybil", email="<EMAIL>", organization_id=admiral_organization.id)
        self.user = user

        db.session.commit()

        self.report.owner_id = user.id
        self.report.organization_id = admiral_organization.id
        self.organization = admiral_organization

    def set_dummy_underwriter(self):
        self.user.email = "<EMAIL>"

    def set_report_email_as_paragon_pds(self):
        self.email.email_account = "<EMAIL>"


@pytest.fixture
def base_emailing_data(app_context) -> BaseEmailingTestData:
    org = organization_fixture()
    user = user_fixture(name="Sybil", email="<EMAIL>")
    email = email_fixture()

    brokerage = brokerage_fixture()

    brokerage_contact = brokerage_employee_fixture(brokerage_id=brokerage.id)
    brokerage_contact.name = "John Smith"
    brokerage_contact.email = "<EMAIL>"

    submission_broker = brokerage_employee_fixture(brokerage_id=brokerage.id)
    submission_broker.name = "Marian Adams"
    submission_broker.email = "<EMAIL>"

    report = report_fixture(organization_id=org.id, owner_id=user.id, correspondence_id=email.correspondence_id)
    submission = submission_fixture(
        report=report,
        brokerage_contact_id=brokerage_contact.id,
        brokerage_id=brokerage.id,
        name="Flowers Inc.",
        broker_id=submission_broker.id,
    )

    app_context.is_prod = True

    return BaseEmailingTestData(
        organization=org,
        user=user,
        email=email,
        brokerage=brokerage,
        brokerage_contact=brokerage_contact,
        submission_broker=submission_broker,
        report=report,
        submission=submission,
    )


def _example_email_template_html_content() -> str:
    return """
        <p>Hi {{ broker_name }},</p>
        <p>Given your business has negative income, we've decided not to insure it</p>
        <p>Thanks,</p>
        <p>Przem</p>
    """


def assert_sendgrid_mail(
    copilot_email: Email,
    assert_from_name: str | None = None,
    assert_from_email: str | None = None,
    assert_reply_to: list[ReplyTo] | None = None,
    assert_cc: list[Cc] | None = None,
    assert_bcc: list[Bcc] | None = None,
):
    sendgrid_mail: Mail = pickle.loads(copilot_email.sendgrid_data.data)

    if assert_from_name is not None:
        assert sendgrid_mail.from_email.name == assert_from_name, "Sending assertion failed for from name"

    if assert_from_email is not None:
        assert sendgrid_mail.from_email.email == assert_from_email, "Sendgrid assertion failed for from email"

    if assert_reply_to is not None:
        assert {(reply_to.email, reply_to.name) for reply_to in sendgrid_mail.reply_to_list} == {
            (reply_to.email, reply_to.name) for reply_to in assert_reply_to
        }, "Sendgrid assertion failed for reply to"

    if assert_cc is not None:
        assert len(sendgrid_mail.personalizations) == 1

        assert {(cc["email"], cc["name"]) for cc in sendgrid_mail.personalizations[0].ccs} == {
            (cc.email, cc.name) for cc in assert_cc
        }, "Sendgrid assertion failed for CC"

    if assert_bcc is not None:
        assert len(sendgrid_mail.personalizations) == 1

        assert {(bcc["email"], bcc["name"]) for bcc in sendgrid_mail.personalizations[0].bccs} == {
            (bcc.email, bcc.name) for bcc in assert_bcc
        }, "Sendgrind assertion failed for BCC"


def test_send_inactive_broker_decline(app_context, base_emailing_data: BaseEmailingTestData):
    assert db.session.query(Email).count() == 1

    email_template_fixture(
        type=EmailTemplateType.INACTIVE_BROKER_DECLINE,
        owner_id=base_emailing_data.user.id,
        html_content=_example_email_template_html_content(),
    )

    db.session.commit()

    flask.current_app.notifications_handler_v2 = notifications_handler
    arch_api_mock.reset_mock()

    was_mail_sent, _ = send_email_to_broker(
        base_emailing_data.report,
        base_emailing_data.user,
        ReportTriageResult.DECLINE,
        ["Report has inactive broker assigned"],
    )

    assert was_mail_sent is True

    app_context.lambda_client.invoke_sender_lambda.assert_called_once()
    sender_lambda_payload: SenderEvent = app_context.lambda_client.invoke_sender_lambda.call_args[0][0]

    assert sender_lambda_payload.event_type == KalepaEvents.SUBMISSION_EMAIL_REQUESTED

    db_emails = db.session.query(Email).all()

    assert len(db_emails) == 2
    assert {email.email_to for email in db_emails} == {"test@co", "<EMAIL>"}

    broker_email: Email = next(email for email in db_emails if email.email_to == "<EMAIL>")
    assert str(sender_lambda_payload.additional_data["email_id"]) == str(broker_email.id)

    # We send the email to the brokerage contact, but we mention submission broker name in the body
    assert "<p>Hi Marian Adams,</p>" in broker_email.email_body
    assert "Flowers Inc. - Inactive Broker" == broker_email.email_subject
    assert broker_email.email_from == "<EMAIL>"

    assert broker_email.email_reply_to == "<EMAIL>"

    assert_sendgrid_mail(
        broker_email,
        assert_from_name="Sybil via Copilot",
        assert_from_email="<EMAIL>",
        assert_reply_to=[ReplyTo(email="<EMAIL>", name="Sybil")],
        assert_cc=[
            Cc(name="Marian Adams", email="<EMAIL>"),
            Cc(name="Sybil", email="<EMAIL>"),
        ],
    )


def test_send_inactive_broker_decline_for_arch_should_update_cc_and_from(
    app_context, base_emailing_data: BaseEmailingTestData
):
    base_emailing_data.set_arch_organization()

    assert db.session.query(Email).count() == 1

    email_template_fixture(
        type=EmailTemplateType.INACTIVE_BROKER_DECLINE,
        owner_id=base_emailing_data.user.id,
        html_content=_example_email_template_html_content(),
    )

    db.session.commit()

    flask.current_app.notifications_handler_v2 = notifications_handler
    arch_api_mock.reset_mock()

    was_mail_sent, _ = send_email_to_broker(
        base_emailing_data.report,
        base_emailing_data.user,
        ReportTriageResult.DECLINE,
        ["Report has inactive broker assigned"],
    )

    assert was_mail_sent is True

    app_context.lambda_client.invoke_sender_lambda.assert_called_once()
    sender_lambda_payload: SenderEvent = app_context.lambda_client.invoke_sender_lambda.call_args[0][0]

    assert sender_lambda_payload.event_type == KalepaEvents.SUBMISSION_EMAIL_REQUESTED

    db_emails = db.session.query(Email).all()

    assert len(db_emails) == 2
    assert {email.email_to for email in db_emails} == {"test@co", "<EMAIL>"}

    broker_email: Email = next(email for email in db_emails if email.email_to == "<EMAIL>")
    assert str(sender_lambda_payload.additional_data["email_id"]) == str(broker_email.id)

    # We sent the email to the brokerage contact, but we mention submission broker name in the body
    assert "<p>Hi Marian Adams,</p>" in broker_email.email_body
    assert "Flowers Inc. - Inactive Broker" == broker_email.email_subject

    # Arch sends email from user directly
    assert broker_email.email_from == "<EMAIL>"
    assert broker_email.email_reply_to == "<EMAIL>"

    assert_sendgrid_mail(
        broker_email,
        assert_from_name="Sybil",  # Arch sends email from user directly
        assert_from_email="<EMAIL>",
        assert_reply_to=[ReplyTo(email="<EMAIL>", name="Sybil")],
        assert_cc=[
            Cc(name="Sybil", email="<EMAIL>"),  # Arch adds user to CC
            Cc(name="Marian Adams", email="<EMAIL>"),
            Cc(
                name="Kalepa Copilot", email="<EMAIL>"
            ),  # We add Kalepa to CC because we can't BCC with Arch API
        ],
    )


def test_send_inactive_broker_decline_for_admiral_should_update_bcc(
    app_context, base_emailing_data: BaseEmailingTestData
):
    base_emailing_data.set_admiral_organization()

    assert db.session.query(Email).count() == 1

    email_template_fixture(
        type=EmailTemplateType.INACTIVE_BROKER_DECLINE,
        owner_id=base_emailing_data.user.id,
        html_content=_example_email_template_html_content(),
    )

    db.session.commit()

    flask.current_app.notifications_handler_v2 = notifications_handler
    arch_api_mock.reset_mock()

    was_mail_sent, _ = send_email_to_broker(
        base_emailing_data.report,
        base_emailing_data.user,
        ReportTriageResult.DECLINE,
        ["Report has inactive broker assigned"],
    )

    assert was_mail_sent is True

    app_context.lambda_client.invoke_sender_lambda.assert_called_once()
    sender_lambda_payload: SenderEvent = app_context.lambda_client.invoke_sender_lambda.call_args[0][0]

    assert sender_lambda_payload.event_type == KalepaEvents.SUBMISSION_EMAIL_REQUESTED

    db_emails = db.session.query(Email).all()

    assert len(db_emails) == 2
    assert {email.email_to for email in db_emails} == {"test@co", "<EMAIL>"}

    broker_email: Email = next(email for email in db_emails if email.email_to == "<EMAIL>")
    assert str(sender_lambda_payload.additional_data["email_id"]) == str(broker_email.id)

    # We sent the email to the brokerage contact, but we mention submission broker name in the body
    assert "<p>Hi Marian Adams,</p>" in broker_email.email_body
    assert "Flowers Inc. - Inactive Broker" == broker_email.email_subject

    assert broker_email.email_from == "<EMAIL>"
    assert broker_email.email_reply_to == "<EMAIL>"

    assert_sendgrid_mail(
        broker_email,
        assert_from_name="Sybil via Copilot",
        assert_from_email="<EMAIL>",
        assert_reply_to=[ReplyTo(email="<EMAIL>", name="Sybil")],
        assert_cc=[
            Cc(name="Marian Adams", email="<EMAIL>"),
            Cc(name="Sybil", email="<EMAIL>"),
        ],
        assert_bcc=[
            Bcc(name="<EMAIL>", email="<EMAIL>"),
        ],
    )


def test_send_clearing_accepted_by_paragon_dummy_underwriter(app_context, base_emailing_data: BaseEmailingTestData):
    base_emailing_data.set_dummy_underwriter()

    assert db.session.query(Email).count() == 1

    email_template_fixture(
        type=EmailTemplateType.CLEARING_ACCEPTED_NO_UW,
        owner_id=base_emailing_data.user.id,
        html_content=_example_email_template_html_content(),
    )

    db.session.commit()

    flask.current_app.notifications_handler_v2 = notifications_handler
    arch_api_mock.reset_mock()

    was_mail_sent, _ = send_email_to_broker(
        base_emailing_data.report,
        base_emailing_data.user,
        ReportTriageResult.ACCEPT,
        ["This is good business", "Explanationx123", "Wownice"],
    )

    assert was_mail_sent is True

    app_context.lambda_client.invoke_sender_lambda.assert_called_once()
    sender_lambda_payload: SenderEvent = app_context.lambda_client.invoke_sender_lambda.call_args[0][0]

    assert sender_lambda_payload.event_type == KalepaEvents.SUBMISSION_EMAIL_REQUESTED

    db_emails = db.session.query(Email).all()

    assert len(db_emails) == 2
    assert {email.email_to for email in db_emails} == {"test@co", "<EMAIL>"}

    broker_email: Email = next(email for email in db_emails if email.email_to == "<EMAIL>")
    assert str(sender_lambda_payload.additional_data["email_id"]) == str(broker_email.id)

    # We sent the email to the brokerage contact, but we mention submission broker name in the body
    assert "<p>Hi Marian Adams,</p>" in broker_email.email_body
    assert "Flowers Inc. - Cleared" == broker_email.email_subject

    assert broker_email.email_from == "<EMAIL>"
    assert broker_email.email_reply_to == "<EMAIL>"

    assert_sendgrid_mail(
        broker_email,
        assert_from_name="Sybil via Copilot",
        assert_from_email="<EMAIL>",
        assert_reply_to=[ReplyTo(email="<EMAIL>", name="Sybil")],
        assert_cc=[
            Cc(name="Marian Adams", email="<EMAIL>"),
            Cc(name="Sybil", email="<EMAIL>"),
        ],
        assert_bcc=[Bcc(name="<EMAIL>", email="<EMAIL>")],
    )

from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.models.file_onboarding import Acord126TransientData, OnboardedFile
from static_common.schemas.file_onboarding import OnboardedFileSchema

from copilot.logic.acords import (
    consolidate_acord_data,
    consolidate_acord_fni_name,
    update_excess_coverage_limit_from_acords,
)
from copilot.models import Submission, db
from copilot.models.files import AcordEntityManager, File, ProcessedFile
from copilot.models.reports import Coverage, SubmissionCoverage
from copilot.models.types import CoverageType
from tests.integration.factories import (
    coverage_fixture,
    organization_fixture,
    report_and_submission_fixture,
    submission_coverage_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj, always_has_permission, insert_test_data

onboarded_file_schema = OnboardedFileSchema()


def _insert_processed_acords(json_data_file: str) -> Submission:
    organization = organization_fixture()
    user = user_fixture()
    _, submission = report_and_submission_fixture()
    ids = {
        "organization_id": organization.id,
        "user_id": user.id,
        "submission_id": submission.id,
    }
    insert_test_data(json_data_file, ids)
    db.session.commit()
    return submission


def test_consolidate_acord_data_126_transient(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )
    submission = _insert_processed_acords("tests/data/acord_entity_manager/consolidate_acords_with_transient.json")

    consolidated, _ = consolidate_acord_data(submission)

    assert consolidated

    acord_126_pf: ProcessedFile = ProcessedFile.query.filter(
        ProcessedFile.file.has(classification=ClassificationDocumentType.ACORD_126)
    ).one_or_none()

    transient_data: Acord126TransientData = OnboardedFileSchema().load(acord_126_pf.processed_data).transient_data

    for hazard in transient_data.schedule_of_hazards:
        assert hazard.requested_name
        assert hazard.requested_address


def test_consolidate_acord_fni_name_real_data(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )
    _insert_processed_acords("tests/data/data_consolidation/consolidate_fni_name_data.json")
    acords = File.query.all()

    consolidated = consolidate_acord_fni_name(acords)

    assert consolidated

    acord_125 = File.query.filter(File.classification == ClassificationDocumentType.ACORD_125).one_or_none()
    assert acord_125

    entities = AcordEntityManager().extract_entities_from_file(acord_125)
    assert entities[0].is_fni
    assert entities[0].name
    assert entities[0].address


def test_update_excess_coverage_limit_from_acords(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            email="<EMAIL>",
            is_being_impersonated=False,
        ),
    )

    submission = _insert_processed_acords("tests/data/submission_coverages/processed_acords_131.json")
    coverage = coverage_fixture(name=Coverage.ExistingNames.Liability)
    coverage2 = coverage_fixture(name=Coverage.ExistingNames.BusinessAuto)
    submission_coverage = submission_coverage_fixture(
        submission_id=submission.id, coverage_id=coverage.id, coverage_type=CoverageType.EXCESS
    )
    submission_coverage2 = submission_coverage_fixture(
        submission_id=submission.id, coverage_id=coverage2.id, coverage_type=CoverageType.EXCESS
    )
    submission.coverages.append(submission_coverage)
    submission.coverages.append(submission_coverage2)
    db.session.commit()

    update_excess_coverage_limit_from_acords(submission.id)

    sc: SubmissionCoverage = SubmissionCoverage.query.get(submission_coverage.id)
    assert sc.limit is None

    processed_files = ProcessedFile.query.all()
    assert len(processed_files) == 2

    processed_files[0].processed_data["transient_data"] = {
        "named_insured": "Sentry Asset Management",
        "policy_information": [
            {
                "umbrella": True,
                "occurrence": True,
                "limits_occurrence": 5000000.0,
                "limits_general_aggregate": 5000000.0,
            }
        ],
        "file_classification": "ACORD_131",
    }
    processed_files[1].processed_data["transient_data"] = {
        "named_insured": "Sentry Asset Management",
        "policy_information": [
            {
                "excess": True,
                "occurrence": True,
                "limits_occurrence": 2000000.0,
                "limits_general_aggregate": 4000000.0,
            }
        ],
        "file_classification": "ACORD_131",
    }
    for pf in processed_files:
        # needed so that changes are detected
        pf.processed_data = onboarded_file_schema.dump(onboarded_file_schema.load(pf.processed_data))
    db.session.commit()

    update_excess_coverage_limit_from_acords(submission.id)
    sc: SubmissionCoverage = SubmissionCoverage.query.get(submission_coverage.id)
    assert sc.limit == 4000000.0

from datetime import date
import uuid

from static_common.enums.task_model_processing_type import TaskModelProcessingType
import pytest

from copilot.logic.tasks.task_dataset import (
    _get_boolean_prediction_result,
    _get_not_boolean_prediction_result,
    _get_raw_value,
    complete_task_test_run,
    init_task_for_dataset_input,
)
from copilot.models import db
from copilot.models.task_dataset import TaskDatasetGroundTruth, TaskDatasetModelOutcome
from copilot.models.tasks import Task
from copilot.models.types import PredictionResult, TaskDatasetExecutionStatus
from tests.integration.factories import (
    organization_fixture,
    report_and_submission_fixture,
    task_dataset_execution_fixture,
    task_dataset_fixture,
    task_dataset_ground_truth_fixture,
    task_dataset_input_fixture,
    task_dataset_model_outcome_fixture,
    task_definition_fixture,
    task_definition_model_fixture,
    task_execution_fixture,
    task_fixture,
    task_model_fixture,
    user_fixture,
)


@pytest.fixture
def setup_task_dataset_environment():
    """Create the test environment with task datasets and related models."""
    # Create organization and user
    organization = organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    # Create task definition and models
    task_definition = task_definition_fixture(
        name="Test Task Definition",
        code="test_classification",
        llm_task_description="Test classification task",
    )

    # Create task models
    execution_model = task_model_fixture(
        name="Classification Model",
        type="LLM_PROMPT",
        llm_model="gpt-4",
        processing_type=TaskModelProcessingType.CLASSIFICATION.value,
    )
    validation_model = task_model_fixture(
        name="Validation Model",
        type="LLM_PROMPT",
        llm_model="claude-3-sonnet",
    )

    # Create task definition models
    task_def_execution_model = task_definition_model_fixture(
        task_definition_id=task_definition.id,
        task_model_id=execution_model.id,
        validation_task_model_id=validation_model.id,
        order=0,
        is_always_run=True,
    )

    # Create task dataset
    dataset_inputs_id = uuid.uuid4()
    task_dataset = task_dataset_fixture(
        description="Test classification dataset",
        organization_id=organization.id,
        processing_type=TaskModelProcessingType.CLASSIFICATION.value,
        output_type="BOOLEAN",
        dataset_inputs_id=dataset_inputs_id,
    )

    task_dataset_execution = task_dataset_execution_fixture(
        task_dataset_id=task_dataset.id,
        task_definition_id=task_definition.id,
        status=TaskDatasetExecutionStatus.PENDING.value,
        execution_arn="execution_arn",
    )

    # Create task dataset inputs with the same dataset_inputs_id to establish the group relationship
    inputs = []
    for i in range(3):
        task_dataset_input = task_dataset_input_fixture(
            dataset_inputs_id=dataset_inputs_id,
            organization_id=organization.id,
            submission_id=submission.id,
            input={"question": f"Test question {i}", "context": f"Test context {i}"},
        )
        inputs.append(task_dataset_input)
    db.session.commit()

    # Create ground truths
    ground_truths = []
    for i, input_item in enumerate(inputs):
        # First two are True, last one is False
        is_true = i < 2
        ground_truth = task_dataset_ground_truth_fixture(
            task_dataset_id=task_dataset.id,
            task_dataset_input_id=input_item.id,
            value={"value": is_true},
        )
        ground_truths.append(ground_truth)

    db.session.commit()

    return {
        "organization": organization,
        "user": user,
        "task_definition": task_definition,
        "execution_model": execution_model,
        "validation_model": validation_model,
        "task_dataset": task_dataset,
        "inputs": inputs,
        "ground_truths": ground_truths,
        "task_dataset_execution": task_dataset_execution,
        "submission": submission,
    }


def test_init_task_for_dataset_input(app_context, setup_task_dataset_environment):
    """Test that initializing a task from a dataset input works correctly."""
    env = setup_task_dataset_environment
    task_dataset_input = env["inputs"][0]

    # Create a task
    task = task_fixture(
        task_definition_id=env["task_definition"].id,
        submission_id=env["submission"].id,
        context={
            "task_dataset_input_id": str(task_dataset_input.id),
            "task_dataset_execution_id": str(env["task_dataset_execution"].id),
        },
        input=None,  # Input should be populated from dataset input
    )
    db.session.commit()

    # Call the function to initialize the task
    init_task_for_dataset_input(task)
    db.session.commit()

    # Verify the task was properly initialized
    updated_task = Task.query.get(task.id)
    assert updated_task.input == task_dataset_input.input
    assert updated_task.is_valid_input is True
    assert updated_task.is_test_run is True


def test_complete_task_test_run(app_context, setup_task_dataset_environment):
    """Test that completing a task test run creates proper model outcomes."""
    env = setup_task_dataset_environment
    task_dataset_execution = env["task_dataset_execution"]
    task_dataset_input = env["inputs"][0]

    # Create a task
    task = task_fixture(
        task_definition_id=env["task_definition"].id,
        submission_id=env["submission"].id,
        context={
            "task_dataset_input_id": str(task_dataset_input.id),
            "task_dataset_execution_id": str(task_dataset_execution.id),
        },
        input=task_dataset_input.input,
        is_valid_input=True,
        is_test_run=True,
        is_valid_output=True,
        processed_output={"value": True},  # Matches ground truth
    )

    # Create task executions
    execution = task_execution_fixture(
        task_id=task.id,
        task_model_id=env["execution_model"].id,
        output={"value": True},
        output_evaluation={"is_valid_output": True},
        processed_output={"value": True},
        used_input_tokens=100,
        used_output_tokens=20,
        processing_time=2,
    )

    validation_execution = task_execution_fixture(
        task_id=task.id,
        task_model_id=env["validation_model"].id,
        output={"is_valid": True},
        output_evaluation={"is_valid_output": True},
        processed_output={"is_valid": True},
        used_input_tokens=120,
        used_output_tokens=10,
        processing_time=1,
        is_validation_run=True,
        validated_task_execution_id=execution.id,
    )

    task.output_task_execution_id = execution.id
    db.session.commit()

    # Call the function to complete the task test run
    complete_task_test_run(task)
    db.session.commit()

    # Verify model outcomes were created
    model_outcomes = TaskDatasetModelOutcome.query.filter(
        TaskDatasetModelOutcome.task_dataset_input_id == task_dataset_input.id
    ).all()

    # Should have 2 outcomes: one for the model and one overall
    assert len(model_outcomes) == 2

    # Check execution model outcome
    execution_outcome = next((o for o in model_outcomes if o.task_model_id == env["execution_model"].id), None)
    assert execution_outcome is not None
    assert execution_outcome.is_valid_output is True
    assert execution_outcome.output == {"value": True}
    assert execution_outcome.input_tokens == 100
    assert execution_outcome.output_tokens == 20
    assert execution_outcome.validation_input_tokens == 120
    assert execution_outcome.validation_output_tokens == 10
    assert execution_outcome.processing_time == 3  # Sum of both executions
    assert execution_outcome.prediction_result == PredictionResult.TRUE_POSITIVE

    # Check overall outcome
    overall_outcome = next((o for o in model_outcomes if o.task_model_id is None), None)
    assert overall_outcome is not None
    assert overall_outcome.is_valid_output is True
    assert overall_outcome.output == {"value": True}
    assert overall_outcome.input_tokens == 100
    assert overall_outcome.output_tokens == 20
    assert overall_outcome.validation_input_tokens == 120
    assert overall_outcome.validation_output_tokens == 10
    assert overall_outcome.processing_time == 3
    assert overall_outcome.prediction_result == PredictionResult.TRUE_POSITIVE


def test_complete_task_test_run_false_positive(app_context, setup_task_dataset_environment):
    """Test completing a task test run with a false positive result."""
    env = setup_task_dataset_environment
    task_dataset_execution = env["task_dataset_execution"]
    task_dataset_input = env["inputs"][2]  # Using the input with False ground truth

    # Create a task
    task = task_fixture(
        task_definition_id=env["task_definition"].id,
        submission_id=env["submission"].id,
        context={
            "task_dataset_input_id": str(task_dataset_input.id),
            "task_dataset_execution_id": str(task_dataset_execution.id),
        },
        input=task_dataset_input.input,
        is_valid_input=True,
        is_test_run=True,
        is_valid_output=True,
        processed_output={"value": True},  # Should be false according to ground truth
    )

    # Create task execution
    execution = task_execution_fixture(
        task_id=task.id,
        task_model_id=env["execution_model"].id,
        output={"value": True},
        output_evaluation={"is_valid_output": True},
        processed_output={"value": True},
        used_input_tokens=100,
        used_output_tokens=20,
        processing_time=2,
    )

    task.output_task_execution_id = execution.id
    db.session.commit()

    # Call the function to complete the task test run
    complete_task_test_run(task)
    db.session.commit()

    # Verify model outcomes were created
    model_outcomes = TaskDatasetModelOutcome.query.filter(
        TaskDatasetModelOutcome.task_dataset_input_id == task_dataset_input.id
    ).all()

    # Check execution model outcome
    execution_outcome = next((o for o in model_outcomes if o.task_model_id == env["execution_model"].id), None)
    assert execution_outcome is not None
    assert execution_outcome.prediction_result == PredictionResult.FALSE_POSITIVE


def test_prediction_result_helpers(app_context):
    """Test the helper functions for calculating prediction results."""
    # Test boolean prediction results
    ground_truth = TaskDatasetGroundTruth(value={"value": True})
    model_outcome = TaskDatasetModelOutcome(output={"value": True})
    result = _get_boolean_prediction_result(ground_truth, model_outcome)
    assert result == PredictionResult.TRUE_POSITIVE

    ground_truth = TaskDatasetGroundTruth(value={"value": True})
    model_outcome = TaskDatasetModelOutcome(output={"value": False})
    result = _get_boolean_prediction_result(ground_truth, model_outcome)
    assert result == PredictionResult.FALSE_NEGATIVE

    ground_truth = TaskDatasetGroundTruth(value={"value": False})
    model_outcome = TaskDatasetModelOutcome(output={"value": True})
    result = _get_boolean_prediction_result(ground_truth, model_outcome)
    assert result == PredictionResult.FALSE_POSITIVE

    ground_truth = TaskDatasetGroundTruth(value={"value": False})
    model_outcome = TaskDatasetModelOutcome(output={"value": False})
    result = _get_boolean_prediction_result(ground_truth, model_outcome)
    assert result == PredictionResult.TRUE_NEGATIVE

    # Test numeric prediction results
    ground_truth = TaskDatasetGroundTruth(value={"value": 42})
    model_outcome = TaskDatasetModelOutcome(output={"value": 42})
    result = _get_not_boolean_prediction_result(ground_truth, model_outcome, float)
    assert result == PredictionResult.TRUE_POSITIVE

    ground_truth = TaskDatasetGroundTruth(value={"value": 42})
    model_outcome = TaskDatasetModelOutcome(output={"value": 24})
    result = _get_not_boolean_prediction_result(ground_truth, model_outcome, float)
    assert result == PredictionResult.FALSE_POSITIVE

    # Test date prediction results
    ground_truth = TaskDatasetGroundTruth(value={"value": "2023-01-01"})
    model_outcome = TaskDatasetModelOutcome(output={"value": "2023-01-01"})
    result = _get_not_boolean_prediction_result(ground_truth, model_outcome, date)
    assert result == PredictionResult.TRUE_POSITIVE

    # Test _get_raw_value function
    assert _get_raw_value({"value": 42}, float) == 42.0
    assert _get_raw_value({"value": "42"}, float) == 42.0
    assert _get_raw_value({"value": "2023-01-01"}, date) == date(2023, 1, 1)
    assert _get_raw_value({"value": "not a number"}, float) is None
    assert _get_raw_value(None, float) is None

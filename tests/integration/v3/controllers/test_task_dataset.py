from datetime import datetime
from http import HTTPStatus
from unittest.mock import Magic<PERSON><PERSON>
import uuid

import pytest

from copilot.models import db
from copilot.models.task_dataset import (
    TaskDataset,
    TaskDatasetExecution,
    TaskDatasetGroundTruth,
    TaskDatasetInput,
)
from copilot.models.task_metrics import TaskDefinitionMetrics
from copilot.models.tasks import Task
from copilot.models.types import PredictionR<PERSON>ult, TaskDatasetExecutionStatus
from copilot.v3.controllers.task_dataset.task_dataset import (
    cancel_task_dataset_execution,
    complete_task_dataset_execution,
    create_dataset_with_inputs_and_ground_truths,
    init_task_dataset_execution,
)
from copilot.v3.controllers.task_dataset.task_dataset_ground_truth import (
    upsert_dataset_ground_truth,
)
from copilot.v3.controllers.task_dataset.task_dataset_input import upsert_dataset_input
from tests.integration.factories import (
    file_fixture,
    organization_fixture,
    report_and_submission_fixture,
    task_dataset_execution_fixture,
    task_dataset_fixture,
    task_dataset_ground_truth_fixture,
    task_dataset_input_fixture,
    task_dataset_model_outcome_fixture,
    task_definition_fixture,
    task_definition_model_fixture,
    task_execution_fixture,
    task_fixture,
    task_model_fixture,
    user_fixture,
)


@pytest.fixture
def setup_task_dataset_execution_environment():
    """Create the test environment with task datasets, inputs, and related models."""
    # Create organization
    organization = organization_fixture()

    # Create task definition
    task_definition = task_definition_fixture(
        name="Test Task Definition",
        code="test_classification",
        llm_task_description="Test classification task",
    )

    # Create task models
    model_1 = task_model_fixture(
        name="Dataset Primary Model",
        type="LLM_PROMPT",
        llm_model="gpt-4",
    )

    task_definition_model_fixture(task_definition_id=task_definition.id, task_model_id=model_1.id)

    # Create task dataset
    dataset_inputs_id = uuid.uuid4()
    task_dataset = task_dataset_fixture(
        description="Test dataset for execution",
        organization_id=organization.id,
        processing_type="CLASSIFICATION",
        output_type="BOOLEAN",
        dataset_inputs_id=dataset_inputs_id,
    )

    # Create task dataset inputs with associated ground truths
    inputs = []
    ground_truths = []
    for i in range(10):
        # Create input
        input_item = task_dataset_input_fixture(
            dataset_inputs_id=dataset_inputs_id,
            input={"question": f"Test question {i}", "context": f"Test context {i}"},
        )
        inputs.append(input_item)

        # Create ground truth (5 true, 5 false)
        ground_truth = task_dataset_ground_truth_fixture(
            task_dataset_id=task_dataset.id,
            task_dataset_input_id=input_item.id,
            value={"value": i < 5},
        )

    db.session.commit()

    return {
        "organization": organization,
        "task_definition": task_definition,
        "task_model": model_1,
        "task_dataset": task_dataset,
        "inputs": inputs,
    }


def test_init_task_dataset_execution(app_context, setup_task_dataset_execution_environment):
    """Test initializing a task dataset execution."""
    env = setup_task_dataset_execution_environment
    task_dataset_id = str(env["task_dataset"].id)
    task_definition_id = str(env["task_definition"].id)
    execution_arn = "arn:aws:states:us-east-1:123456789012:execution:state-machine:execution-1234"

    body = {
        "task_dataset_id": task_dataset_id,
        "task_definition_id": task_definition_id,
        "execution_arn": execution_arn,
    }
    # Call the function to initialize the task dataset execution
    result, status_code = init_task_dataset_execution(body)

    # Verify the response
    assert status_code == 201
    assert result["task_definition_code"] == env["task_definition"].code
    assert "task_dataset_execution_id" in result
    assert "task_dataset_input_ids" in result
    assert len(result["task_dataset_input_ids"]) == 10

    # Verify database state
    task_dataset_execution = TaskDatasetExecution.query.get(result["task_dataset_execution_id"])
    assert task_dataset_execution is not None
    assert task_dataset_execution.task_dataset_id == uuid.UUID(task_dataset_id)
    assert task_dataset_execution.task_definition_id == uuid.UUID(task_definition_id)
    assert task_dataset_execution.execution_arn == execution_arn
    assert task_dataset_execution.status == TaskDatasetExecutionStatus.PENDING

    with pytest.raises(Exception) as excinfo:
        init_task_dataset_execution(body)

    assert "409" in str(excinfo.value) or "already exists" in str(excinfo.value)


def test_cancel_task_dataset_execution(app_context, setup_task_dataset_execution_environment):
    """Test canceling a task dataset execution."""
    env = setup_task_dataset_execution_environment

    # Create a pending task dataset execution
    task_dataset_execution = task_dataset_execution_fixture(
        task_dataset_id=env["task_dataset"].id,
        task_definition_id=env["task_definition"].id,
        execution_arn="arn:aws:states:us-east-1:123456789012:execution:state-machine:execution-1234",
        status=TaskDatasetExecutionStatus.PENDING,
    )
    db.session.commit()

    cancel_task_dataset_execution(task_dataset_execution.id)

    # Verify database state
    updated_execution = TaskDatasetExecution.query.get(task_dataset_execution.id)
    assert updated_execution.status == TaskDatasetExecutionStatus.CANCELLED


def test_complete_task_dataset_execution(app_context, setup_task_dataset_execution_environment, monkeypatch):
    """Test completing a task dataset execution."""
    env = setup_task_dataset_execution_environment
    execution_arn = "arn:aws:states:us-east-1:123456789012:execution:state-machine:execution-1234"

    # Create a pending task dataset execution
    task_dataset_execution = task_dataset_execution_fixture(
        task_dataset_id=env["task_dataset"].id,
        task_definition_id=env["task_definition"].id,
        execution_arn="arn:aws:states:us-east-1:123456789012:execution:state-machine:execution-1234",
        status=TaskDatasetExecutionStatus.PENDING,
    )
    db.session.commit()

    # Create tasks linked to this execution
    tasks = []
    for i, input_item in enumerate(env["inputs"]):
        task = task_fixture(
            task_definition_id=task_dataset_execution.task_definition_id,
            task_dataset_execution_id=task_dataset_execution.id,
            context={
                "task_dataset_input_id": str(input_item.id),
                "task_dataset_id": str(task_dataset_execution.task_dataset_id),
            },
            input=input_item.input,
            is_valid_input=True,
            is_valid_output=True,
            is_test_run=True,
            # First 6 predictions match ground truth (4 TP, 2 TN)
            # Last 4 don't match (2 FP, 2 FN)
            processed_output={"value": i < 4 or (i >= 5 and i < 7)},
        )
        tasks.append(task)

        # Create task execution
        execution = task_execution_fixture(
            task_id=task.id,
            task_model_id=env["task_model"].id,
            processing_time=i + 1,  # Different processing times
            used_input_tokens=100,
            used_output_tokens=20,
            output_evaluation={"is_valid_output": True},
        )

        # Set it as the output execution
        task.output_task_execution_id = execution.id

        # Create model outcome
        model_outcome = task_dataset_model_outcome_fixture(
            task_dataset_input_id=input_item.id,
            task_model_id=env["task_model"].id,
            task_definition_id=task_dataset_execution.task_definition_id,
            output={"value": i < 4 or (i >= 5 and i < 7)},
            is_valid_output=True,
            processing_time=i + 1,
            input_tokens=100,
            output_tokens=20,
        )

        # Set prediction result
        if i < 4:  # True Positive (ground truth: true, predicted: true)
            model_outcome.prediction_result = PredictionResult.TRUE_POSITIVE
        elif i == 4:  # False Negative (ground truth: true, predicted: false)
            model_outcome.prediction_result = PredictionResult.FALSE_NEGATIVE
        elif i < 7:  # True Negative (ground truth: false, predicted: false)
            model_outcome.prediction_result = PredictionResult.TRUE_NEGATIVE
        else:  # False Positive (ground truth: false, predicted: true)
            model_outcome.prediction_result = PredictionResult.FALSE_POSITIVE

    db.session.commit()

    # Call the function to complete the task dataset execution
    _, status_code = complete_task_dataset_execution(str(task_dataset_execution.id))

    # Verify the response
    assert status_code == 204

    # Verify database state
    updated_execution = TaskDatasetExecution.query.get(task_dataset_execution.id)
    assert updated_execution.status == TaskDatasetExecutionStatus.COMPLETED

    # Verify metrics were added
    metrics = TaskDefinitionMetrics.query.filter_by(task_definition_id=env["task_definition"].id).first()
    assert metrics is not None

    # Performance metrics should be calculated from the prediction results
    # 4 TP, 1 FN, 2 TN, 3 FP
    # Precision = TP / (TP + FP) ~= 0.57
    # Recall = TP / (TP + FN) = 0.8
    # F1 = 2 * Precision * Recall / (Precision + Recall) ~= 0.91 / 1.37 ~= 0.66
    # Accuracy = (TP + TN) / (TP + TN + FP + FN) = 0.6

    assert 0.57 <= metrics.precision <= 0.58
    assert metrics.recall == 0.8
    assert 0.66 <= metrics.f1_score <= 0.67
    assert metrics.accuracy == 0.6

    # Verify model metrics
    assert len(metrics.task_model_metrics) == 1

    # Check model metrics fields
    model_metrics = metrics.task_model_metrics[0]
    assert model_metrics.sample_size == 10

    # Model metrics should also have the performance metrics
    assert 0.57 <= model_metrics.precision <= 0.58
    assert model_metrics.recall == 0.8
    assert 0.66 <= model_metrics.f1_score <= 0.67
    assert model_metrics.accuracy == 0.6


def test_create_dataset_with_inputs_and_ground_truths(app_context):
    """Test creating a new dataset with inputs and ground truths."""
    # Prepare test data
    request_body = {
        "description": "Test dataset creation",
        "processing_type": "CLASSIFICATION",
        "output_type": "BOOLEAN",
        "inputs": [
            {
                "input": {"question": "Is this a test?", "context": "This is a test context"},
                "ground_truth": {"value": True},
            },
            {
                "input": {"question": "Is this production?", "context": "This is a test context"},
                "ground_truth": {"value": False},
            },
            {
                "input": {"question": "Is this a question?", "context": "This is a question in a test"},
                "ground_truth": {"value": True},
            },
        ],
    }

    # Call the function
    result, status_code = create_dataset_with_inputs_and_ground_truths(request_body)

    # Verify response
    assert status_code == 201

    # Verify dataset was created
    dataset_id = result["id"]
    task_dataset = TaskDataset.query.get(dataset_id)
    assert task_dataset is not None
    assert task_dataset.description == request_body["description"]
    assert task_dataset.processing_type == request_body["processing_type"]
    assert task_dataset.output_type == request_body["output_type"]

    # Verify inputs were created
    dataset_inputs = TaskDatasetInput.query.filter_by(dataset_inputs_id=task_dataset.dataset_inputs_id).all()
    assert len(dataset_inputs) == 3

    # Verify ground truths were created
    for input_item in dataset_inputs:
        ground_truth = TaskDatasetGroundTruth.query.filter_by(
            task_dataset_id=task_dataset.id, task_dataset_input_id=input_item.id
        ).first()
        assert ground_truth is not None
        assert ground_truth.has_value is True

    # Verify the content of inputs and ground truths
    input_questions = [item["input"]["question"] for item in request_body["inputs"]]
    ground_truth_values = [item["ground_truth"]["value"] for item in request_body["inputs"]]

    for input_item in dataset_inputs:
        assert input_item.input["question"] in input_questions

        ground_truth = TaskDatasetGroundTruth.query.filter_by(
            task_dataset_id=task_dataset.id, task_dataset_input_id=input_item.id
        ).first()

        # Find the corresponding input in the request to verify ground truth
        idx = input_questions.index(input_item.input["question"])
        assert ground_truth.value["value"] == ground_truth_values[idx]


def test_upsert_task_dataset_input(app_context) -> None:
    organization = organization_fixture()
    user = user_fixture(organization_id=organization.id)
    file = file_fixture()
    report, submission = report_and_submission_fixture(owner_id=user.id)
    dataset_inputs_id = uuid.uuid4()
    task_dataset_id = uuid.uuid4()
    task_dataset_updated_at = datetime.fromisoformat("2024-10-24 13:46:50.445070+00:00")
    task_dataset = task_dataset_fixture(
        id=task_dataset_id, updated_at=task_dataset_updated_at, dataset_inputs_id=dataset_inputs_id
    )

    # Create a new object
    status = upsert_dataset_input(
        {
            "dataset_inputs_id": dataset_inputs_id,
            "submission_id": submission.id,
            "file_id": file.id,
            "input": {"data": "test"},
        }
    )
    assert status == HTTPStatus.CREATED
    first_task_dataset_updated_at = TaskDataset.query.get(task_dataset_id).updated_at
    assert first_task_dataset_updated_at != task_dataset_updated_at
    assert first_task_dataset_updated_at > task_dataset_updated_at

    # Create a second object
    id = uuid.uuid4()
    status = upsert_dataset_input(
        {
            "id": id,
            "dataset_inputs_id": dataset_inputs_id,
            "submission_id": submission.id,
            "file_id": file.id,
            "input": {"data": "test2"},
            "context": {"key": "value"},
        }
    )
    assert status == HTTPStatus.CREATED
    assert TaskDatasetInput.query.count() == 2
    second_task_dataset_updated_at = TaskDataset.query.get(task_dataset_id).updated_at
    assert second_task_dataset_updated_at != first_task_dataset_updated_at
    assert second_task_dataset_updated_at > first_task_dataset_updated_at

    # Update the second object
    status = upsert_dataset_input(
        {
            "id": id,
            "dataset_inputs_id": dataset_inputs_id,
            "submission_id": submission.id,
            "file_id": file.id,
            "input": {"data": "test3"},
        }
    )
    assert status == HTTPStatus.OK
    assert TaskDatasetInput.query.count() == 2
    assert TaskDatasetInput.query.filter_by(id=id).first().input["data"] == "test3"
    assert TaskDatasetInput.query.filter_by(id=id).first().context["key"] == "value"
    third_task_dataset_updated_at = TaskDataset.query.get(task_dataset_id).updated_at
    assert third_task_dataset_updated_at != second_task_dataset_updated_at
    assert third_task_dataset_updated_at > second_task_dataset_updated_at


def test_upsert_task_dataset_ground_truth(app_context) -> None:
    organization = organization_fixture()
    user = user_fixture(organization_id=organization.id)
    file = file_fixture()
    report, submission = report_and_submission_fixture(owner_id=user.id)
    dataset_inputs_id = uuid.uuid4()
    task_dataset_id = uuid.uuid4()
    task_dataset_updated_at = datetime.fromisoformat("2024-10-24 13:46:50.445070+00:00")
    task_dataset = task_dataset_fixture(
        id=task_dataset_id, updated_at=task_dataset_updated_at, dataset_inputs_id=dataset_inputs_id
    )
    task_dataset_input_id = uuid.uuid4()
    task_dataset_input_id2 = uuid.uuid4()
    upsert_dataset_input(
        {
            "id": task_dataset_input_id,
            "dataset_inputs_id": dataset_inputs_id,
            "submission_id": submission.id,
            "file_id": file.id,
            "input": {"data": "test"},
        }
    )
    upsert_dataset_input(
        {
            "id": task_dataset_input_id2,
            "dataset_inputs_id": dataset_inputs_id,
            "submission_id": submission.id,
            "file_id": file.id,
            "input": {"data": "test2"},
        }
    )

    # Create a new object
    status = upsert_dataset_ground_truth(
        {
            "task_dataset_id": task_dataset.id,
            "task_dataset_input_id": task_dataset_input_id,
            "value": {"data": "test"},
            "has_value": False,
        }
    )
    assert status == HTTPStatus.CREATED
    first_task_dataset_updated_at = TaskDataset.query.get(task_dataset_id).updated_at
    assert first_task_dataset_updated_at != task_dataset_updated_at
    assert first_task_dataset_updated_at > task_dataset_updated_at

    # Create a second object
    id = uuid.uuid4()
    status = upsert_dataset_ground_truth(
        {
            "id": id,
            "task_dataset_id": task_dataset.id,
            "task_dataset_input_id": task_dataset_input_id2,
            "value": {"data": "test2"},
            "has_value": False,
        }
    )
    assert status == HTTPStatus.CREATED
    assert TaskDatasetGroundTruth.query.count() == 2
    second_task_dataset_updated_at = TaskDataset.query.get(task_dataset_id).updated_at
    assert second_task_dataset_updated_at != first_task_dataset_updated_at
    assert second_task_dataset_updated_at > first_task_dataset_updated_at

    # Update the second object
    status = upsert_dataset_ground_truth(
        {
            "id": id,
            "task_dataset_id": task_dataset.id,
            "task_dataset_input_id": task_dataset_input_id2,
            "value": {"data": "test3"},
        }
    )
    assert status == HTTPStatus.OK
    assert TaskDatasetGroundTruth.query.count() == 2
    assert TaskDatasetGroundTruth.query.filter_by(id=id).first().value["data"] == "test3"
    assert TaskDatasetGroundTruth.query.filter_by(id=id).first().has_value is False
    third_task_dataset_updated_at = TaskDataset.query.get(task_dataset_id).updated_at
    assert third_task_dataset_updated_at != second_task_dataset_updated_at
    assert third_task_dataset_updated_at > second_task_dataset_updated_at

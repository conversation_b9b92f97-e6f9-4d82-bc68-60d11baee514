from typing import Dict, Optional
from unittest.mock import MagicMock, patch

from static_common.enums.organization import ExistingOrganizations
import marshmallow
import pytest
import werkzeug.exceptions

from copilot.models import Settings, SupportUser, User, db
from copilot.models.types import UserRoleEnum
from copilot.v3.controllers.admin_organizations import get_organizations
from copilot.v3.controllers.admin_users import (
    admin_create_user,
    admin_get_users,
    admin_update_user,
)
from tests.conftest import AuthenticatedUserHeaders
from tests.integration.factories import (
    organization_fixture,
    settings_fixture,
    user_fixture,
)
import copilot.exceptions


def test_get_users_happy_path(app_context, trusted_current_user):
    organization_fixture(id=1)
    organization_fixture(id=2)
    user_fixture(id=123, email="<EMAIL>", organization_id=1)
    user_fixture(id=456, email="<EMAIL>", organization_id=2)
    user_fixture(id=457, email="<EMAIL>", organization_id=1, cross_organization_access=True)
    settings_fixture(organization_id=2, user_id=456, show_management_dashboard=True, loss_runs_enabled=False)

    # Get all users
    all_users = admin_get_users()
    assert len(all_users) == 2

    # Get users filtered by email
    filtered_users_by_email = admin_get_users(email_contains="some2")
    assert len(filtered_users_by_email) == 1
    assert filtered_users_by_email[0]["email"] == "<EMAIL>"
    assert filtered_users_by_email[0]["is_enabled"] is True
    assert filtered_users_by_email[0]["settings"]["loss_runs_enabled"] is False
    assert filtered_users_by_email[0]["settings"]["show_management_dashboard"] is True

    # Get users filtered by organization_id
    filtered_users_by_org_id = admin_get_users(organization_id=1)
    assert len(filtered_users_by_org_id) == 1
    assert filtered_users_by_org_id[0]["email"] == "<EMAIL>"
    assert filtered_users_by_org_id[0]["settings"] is None


def test_get_users_includes_cross_org_users_for_kalepa_orgs(app_context, trusted_current_user):
    organization_fixture(id=ExistingOrganizations.KalepaTest.value)
    user_fixture(id=456, email="<EMAIL>", organization_id=ExistingOrganizations.KalepaTest.value)
    user_fixture(
        id=457,
        email="<EMAIL>",
        organization_id=ExistingOrganizations.KalepaTest.value,
        cross_organization_access=True,
    )

    all_users = admin_get_users()

    assert len(all_users) == 2


def test_get_users_includes_cross_org_users_when_requested(app_context, trusted_current_user):
    organization_fixture(id=1)
    user_fixture(id=456, email="<EMAIL>", organization_id=1)
    user_fixture(id=457, email="<EMAIL>", organization_id=1, cross_organization_access=True)

    all_users = admin_get_users(exclude_cross_org_users=False)

    assert len(all_users) == 2


def test_get_users_no_authenticated_caller(app_context, authenticated_user_headers: AuthenticatedUserHeaders):
    authenticated_user_headers.headers = {}
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1)
    with pytest.raises(copilot.exceptions.AuthError, match=r"Only authenticated users can call this endpoint"):
        admin_get_users()


def test_get_users_caller_not_from_trusted_kalepa_user(app_context, untrusted_current_user):
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1)
    with pytest.raises(copilot.exceptions.AuthError, match=r"Only Kalepa trusted users can call this endpoint"):
        admin_get_users()


def test_create_user_with_trusted_user_specified(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    _mock_create_user_in_auth0(mocker)
    organization_fixture(id=1)
    create_user_request = {"email": "<EMAIL>", "organization_id": 1, "password": "some_password"}
    created_user = admin_create_user(create_organization_if_not_specified=False, body=create_user_request)

    assert created_user is not None
    assert created_user["organization_id"] == 1
    assert created_user["cross_organization_access"] is False
    assert created_user["role"] == UserRoleEnum.underwriter
    assert created_user["is_enabled"] is True
    assert created_user["external_id"] == "auth0|user_id"

    all_users = admin_get_users(email_contains="newuser1")
    assert len(all_users) == 1

    settings = db.session.query(Settings).all()
    assert len(settings) == 1
    assert settings[0].user_id == created_user["id"]
    assert settings[0].is_support is False


def test_create_user_initially_in_disabled_state(app_context, mocker, trusted_current_user):
    auth0_management_mock = _mock_auth0_management_client(mocker)
    _mock_create_user_in_auth0(mocker)
    disable_user_mock = _mock_disable_user(mocker)
    organization_fixture(id=1)

    create_user_request = {
        "email": "<EMAIL>",
        "organization_id": 1,
        "password": "some_password",
        "is_enabled": False,
    }
    created_user = admin_create_user(create_organization_if_not_specified=False, body=create_user_request)

    assert created_user is not None
    assert created_user["is_enabled"] is False
    assert created_user["external_id"] == "auth0|user_id"
    disable_user_mock.assert_called_once_with(created_user["external_id"], auth0_management_mock())


def test_create_user_with_missing_organization_id_and_without_create_organization_if_not_specified(
    app_context, trusted_current_user
):
    organization_fixture(id=1)
    create_user_request = {
        "email": "<EMAIL>",
    }
    with pytest.raises(
        werkzeug.exceptions.BadRequest,
        match=(
            r"Cannot create new User with missing 'organization_id' when 'create_organization_if_not_specified' is not"
            r" 'True'."
        ),
    ):
        admin_create_user(create_organization_if_not_specified=False, body=create_user_request)


def test_create_user_with_organization_specified_that_does_not_exist(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    organization_fixture(id=1)
    create_user_request = {"email": "<EMAIL>", "organization_id": 2, "password": "some_password"}
    with pytest.raises(werkzeug.exceptions.BadRequest, match=r"Organization with id '2' does not exist."):
        admin_create_user(create_organization_if_not_specified=False, body=create_user_request)


def test_create_user_when_name_must_be_trimmed(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    _mock_create_user_in_auth0(mocker)
    organization_fixture(id=1)
    create_user_request = {
        "email": "<EMAIL>",
        "organization_id": 1,
        "name": "a" * 200,
        "password": "some_password",
    }
    created_user = admin_create_user(create_organization_if_not_specified=False, body=create_user_request)
    assert len(created_user["name"]) == 100


def test_create_user_when_user_with_same_email_already_exists(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1)
    create_user_request = {"email": "<EMAIL>", "organization_id": 1, "password": "some_password"}
    with pytest.raises(
        werkzeug.exceptions.BadRequest,
        match=r"User with email '<EMAIL>' already exists.",
    ):
        admin_create_user(create_organization_if_not_specified=False, body=create_user_request)


def test_create_user_with_email_that_is_not_allowed_for_specified_organization(
    app_context, mocker, trusted_current_user
):
    _mock_auth0_management_client(mocker)
    organization_fixture(id=1)
    settings_fixture(organization_id=1, email_domains=["allowed.com"])
    create_user_request = {"email": "<EMAIL>", "organization_id": 1, "password": "some_password"}
    with pytest.raises(
        werkzeug.exceptions.BadRequest,
        match=r"User with email '<EMAIL>' is not allowed in organization with ID '1'.",
    ):
        admin_create_user(create_organization_if_not_specified=False, body=create_user_request)


def test_create_user_without_email_not_allowed(app_context, trusted_current_user):
    organization_fixture(id=1)
    create_user_request = {
        "organization_id": 1,
        "password": "some_password",
    }
    with pytest.raises(
        marshmallow.exceptions.ValidationError,
        match=r"Missing data for required field.",
    ):
        admin_create_user(create_organization_if_not_specified=False, body=create_user_request)


def test_create_user_without_password_when_user_not_using_sso_not_allowed(app_context, trusted_current_user):
    organization_fixture(id=1)
    create_user_request = {
        "email": "<EMAIL>",
        "organization_id": 1,
    }
    with pytest.raises(
        werkzeug.exceptions.BadRequest,
        match=r"Cannot create new User without providing password.",
    ):
        admin_create_user(create_organization_if_not_specified=False, body=create_user_request)


def test_create_user_without_password_when_user_is_using_sso_is_allowed(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    create_user_in_auth0_mock = _mock_create_user_in_auth0(mocker)
    organization_fixture(id=1)
    create_user_request = {
        "email": "<EMAIL>",
        "organization_id": 1,
    }
    created_user = admin_create_user(create_organization_if_not_specified=False, body=create_user_request)
    assert created_user is not None
    assert created_user["organization_id"] == 1
    assert created_user["cross_organization_access"] is False
    assert created_user["role"] == UserRoleEnum.underwriter
    assert created_user["is_enabled"] is True
    assert created_user["external_id"] is None
    assert create_user_in_auth0_mock.called is False


def test_create_user_with_cross_organization_access(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    _mock_create_user_in_auth0(mocker)
    organization_fixture(id=1)
    create_user_request = {
        "email": "<EMAIL>",
        "organization_id": 1,
        "cross_organization_access": True,
        "password": "some_password",
    }
    created_user = admin_create_user(create_organization_if_not_specified=False, body=create_user_request)

    assert created_user is not None
    assert created_user["cross_organization_access"] is True

    # New user with 'cross_organization_access=True' must create new Settings with 'is_support' set to 'True'
    settings = db.session.query(Settings).all()
    assert len(settings) == 1
    assert settings[0].user_id == created_user["id"]
    assert settings[0].is_support is True


def test_create_user_with_create_organization_if_not_specified(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    _mock_create_user_in_auth0(mocker)
    organization_fixture(id=1)
    create_user_request = {
        "email": "<EMAIL>",
        "name": "new user 1",
        "password": "some_password",
    }
    created_user = admin_create_user(create_organization_if_not_specified=True, body=create_user_request)

    assert created_user is not None
    assert created_user["organization_id"] == 2
    assert created_user["cross_organization_access"] is False
    assert created_user["is_enabled"] is True

    filtered_organizations = get_organizations(name_contains="new user 1")
    assert len(filtered_organizations) == 1
    assert filtered_organizations[0]["name"] == "new user 1"

    assert db.session.query(Settings).count() == 2
    settings = db.session.query(Settings).filter(Settings.organization_id == filtered_organizations[0]["id"]).first()
    assert settings.organization_id == filtered_organizations[0]["id"]
    assert "fakenonexistingdomain.com" in settings.email_domains
    assert "<EMAIL>" in settings.whitelisted_emails
    assert "<EMAIL>" in settings.whitelisted_emails


def test_update_user_happy_path(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    auth0_email_update_mock = mocker.patch("copilot.v3.controllers.admin_users._update_email_in_auth0")
    auth0_name_update_mock = mocker.patch("copilot.v3.controllers.admin_users._update_name_in_auth0")
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1, external_id="external_id_123")
    settings_fixture(user_id=123, show_management_dashboard=False, loss_runs_enabled=True)
    update_user_request = {
        "id": 123,
        "email": "<EMAIL>",
        "name": "new user 1",
        "settings": {"show_management_dashboard": True, "loss_runs_enabled": True},
    }
    updated_user: dict = admin_update_user(update_user_request)
    assert updated_user["email"] == "<EMAIL>"
    assert updated_user["name"] == "new user 1"
    assert updated_user["settings"]["show_management_dashboard"] is True
    assert updated_user["settings"]["loss_runs_enabled"] is True
    updated_user: User | None = db.session.query(User).get(123)
    assert updated_user.email == "<EMAIL>"
    assert updated_user.name == "new user 1"
    assert updated_user.settings.show_management_dashboard is True
    assert updated_user.settings.loss_runs_enabled is True
    assert auth0_email_update_mock.called is True
    assert auth0_name_update_mock.called is True


def test_update_user_creates_settings_when_none_exist(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    mocker.patch("copilot.v3.controllers.admin_users._update_email_in_auth0")
    mocker.patch("copilot.v3.controllers.admin_users._update_name_in_auth0")
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1, external_id="external_id_123")

    update_user_request = {
        "id": 123,
        "email": "<EMAIL>",
        "name": "new user 1",
        "settings": {"show_management_dashboard": True, "loss_runs_enabled": False},
    }

    admin_update_user(update_user_request)

    updated_user = db.session.query(User).get(123)
    assert updated_user.settings.show_management_dashboard is True
    assert updated_user.settings.loss_runs_enabled is False


def test_update_user_when_settings_is_none_on_request(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    mocker.patch("copilot.v3.controllers.admin_users._update_email_in_auth0")
    mocker.patch("copilot.v3.controllers.admin_users._update_name_in_auth0")
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1, external_id="external_id_123")
    settings_fixture(user_id=123, show_management_dashboard=False, loss_runs_enabled=True)
    update_user_request = {
        "id": 123,
        "email": "<EMAIL>",
        "name": "new user 1",
    }

    admin_update_user(update_user_request)

    updated_user = db.session.query(User).get(123)
    assert updated_user.settings.show_management_dashboard is False
    assert updated_user.settings.loss_runs_enabled is True


def test_update_user_without_user_id_not_allowed(app_context, trusted_current_user):
    organization_fixture(id=1)
    update_user_request = {
        "email": "<EMAIL>",
    }
    with pytest.raises(werkzeug.exceptions.BadRequest, match=r"Cannot update User without providing its ID."):
        admin_update_user(body=update_user_request)


def test_update_user_that_does_not_exist(app_context, trusted_current_user):
    organization_fixture(id=1)
    update_user_request = {
        "id": 666,
        "email": "<EMAIL>",
    }
    with pytest.raises(werkzeug.exceptions.NotFound, match=r"User with ID '666' not found."):
        admin_update_user(body=update_user_request)


def test_update_user_when_new_email_already_exists_in_copilot_db(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1)
    user_fixture(id=456, email="<EMAIL>", organization_id=1)
    update_user_request = {
        "id": 456,
        "email": "<EMAIL>",
    }
    with pytest.raises(werkzeug.exceptions.BadRequest, match=r"User with email '<EMAIL>' already exists."):
        admin_update_user(body=update_user_request)


def test_update_user_when_new_email_is_not_allowed_for_user_current_organization(
    app_context, mocker, trusted_current_user
):
    _mock_auth0_management_client(mocker)
    organization_fixture(id=1)
    settings_fixture(organization_id=1, email_domains=["allowed.com"])
    user_fixture(id=456, email="<EMAIL>", organization_id=1)
    update_user_request = {
        "id": 456,
        "email": "<EMAIL>",
    }
    with pytest.raises(
        werkzeug.exceptions.BadRequest,
        match=r"User with email '<EMAIL>' is not allowed in organization with ID '1'.",
    ):
        admin_update_user(body=update_user_request)


def test_update_with_disabling_cross_organization_access(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    mocker.patch("copilot.v3.controllers.admin_users._update_email_in_auth0")
    mocker.patch("copilot.v3.controllers.admin_users._update_name_in_auth0")
    organization_fixture(id=1)
    user_fixture(
        id=123,
        email="<EMAIL>",
        organization_id=1,
        external_id="external_id_123",
        cross_organization_access=True,
    )
    update_user_request = {
        "id": 123,
        "email": "<EMAIL>",
        "name": "new user 1",
        "cross_organization_access": False,
    }
    updated_user = admin_update_user(update_user_request)
    assert updated_user["email"] == "<EMAIL>"

    settings = db.session.query(Settings).all()
    assert len(settings) == 1
    assert settings[0].user_id == 123
    assert settings[0].is_support is False


def test_update_with_enabling_cross_organization_access(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    mocker.patch("copilot.v3.controllers.admin_users._update_email_in_auth0")
    mocker.patch("copilot.v3.controllers.admin_users._update_name_in_auth0")
    organization_fixture(id=1)
    user_fixture(
        id=123,
        email="<EMAIL>",
        organization_id=1,
        external_id="external_id_123",
        cross_organization_access=False,
    )
    update_user_request = {
        "id": 123,
        "email": "<EMAIL>",
        "name": "new user 1",
        "cross_organization_access": True,
    }
    updated_user = admin_update_user(update_user_request)
    assert updated_user["email"] == "<EMAIL>"

    settings = db.session.query(Settings).all()
    assert len(settings) == 1
    assert settings[0].user_id == 123
    assert settings[0].is_support is True
    support_users = db.session.query(SupportUser).all()
    support_user: SupportUser = support_users[0]
    assert len(support_users) == 1
    assert support_user.user_id == 123
    assert support_user.current_report_id is None
    assert support_user.can_load_submissions is False


def test_update_user_when_user_is_becoming_disabled(app_context, mocker, trusted_current_user):
    _mock_auth0_management_client(mocker)
    mocker.patch("copilot.v3.controllers.admin_users._update_email_in_auth0")
    mocker.patch("copilot.v3.controllers.admin_users._update_name_in_auth0")
    auth0_block_mock = mocker.patch("copilot.v3.controllers.admin_users._block_or_unblock_user_in_auth0")
    boto3_mock = mocker.patch("copilot.v3.controllers.admin_users.boto3")
    boto3_resource_mock = MagicMock(name="boto3_resource_mock")
    boto3_mock.resource.return_value = boto3_resource_mock
    dynamo_table_mock = MagicMock(name="dynamo_table_mock")
    boto3_resource_mock.Table.return_value = dynamo_table_mock
    dynamo_table_mock.query.return_value = {"Items": [{"is_enabled": "True"}]}
    organization_fixture(id=1)
    user_fixture(id=123, email="<EMAIL>", organization_id=1, external_id="external_id_123", is_enabled=True)
    update_user_request = {
        "id": 123,
        "email": "<EMAIL>",
        "name": "new user 1",
        "is_enabled": False,
    }

    updated_user = admin_update_user(update_user_request)

    assert updated_user["is_enabled"] is False
    updated_user = db.session.query(User).get(123)
    assert updated_user.is_enabled is False
    assert dynamo_table_mock.put_item.called is True
    assert dynamo_table_mock.put_item.call_args_list[0].kwargs["Item"]["is_enabled"] == "False"
    assert auth0_block_mock.called is True
    assert auth0_block_mock.call_args_list[0].kwargs["is_enabled"] is False


def _mock_auth0_management_client(mocker):
    return mocker.patch("copilot.v3.controllers.admin_users._create_authenticated_auth0_management_client")


def _mock_create_user_in_auth0(mocker):
    create_user_in_auth0_mock = mocker.patch("copilot.v3.controllers.admin_users._create_user_in_auth0")
    create_user_in_auth0_mock.return_value = {"user_id": "auth0|user_id"}
    return create_user_in_auth0_mock


def _mock_disable_user(mocker):
    return mocker.patch("copilot.v3.controllers.admin_users._disable_user")

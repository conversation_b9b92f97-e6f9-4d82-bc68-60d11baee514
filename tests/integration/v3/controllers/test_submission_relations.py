from datetime import datetime
from http import HTTPStatus
from uuid import UUID, uuid4

from entity_resolution_service_client_v3 import EntityNameRequest, NameScoreResponse
from pydantic import BaseModel as PydanticBaseModel
from pydantic import Field
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.submission import (
    SubmissionRelationSource,
    SubmissionRelationType,
    SubmissionStage,
)
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
import pytest
import werkzeug.exceptions

from copilot.models import ReportV2, db
from copilot.models.reports import Coverage, Submission
from copilot.models.submission_relations import SubmissionRelation
from copilot.models.types import CoverageType
from copilot.v3.controllers.submission_relations import (
    calculate_lookalike_bind_rate_for_submissions,
    create_automatic_relations_for_submission,
    create_submission_relation,
    replace_submission_relations,
)
from tests.integration.factories import (
    coverage_fixture,
    organization_fixture,
    report_and_submission_fixture,
    report_fixture,
    submission_business_fixture,
    submission_coverage_fixture,
    submission_fixture,
    submission_relation_fixture,
    user_fixture,
)
from tests.integration.utils import Anon<PERSON>bj
from tests.test_utils import close_numbers


class BindRateSubmissionTestScenarioData(PydanticBaseModel):
    submission_id: UUID = Field(default_factory=uuid4)
    expected_bind_rate: float | None = None
    initial_bind_rate: float | None = None
    related_submissions: list["BindRateSubmissionTestScenarioData"]
    stage: SubmissionStage


def _populate_db(mocker):
    def entity_scores_side_effect(name: str, potential_names: list[str], enforce_flexible_in_name_value: bool = True):
        if (
            name.lower() == "jonah jameson's paper dba the daily bugle newspaper"
            and len(potential_names) == 1
            and potential_names[0].lower() == "the daily bugle newspaper c/o"
        ):
            return [NameScoreResponse(name=EntityNameRequest(value=name), score=0.97)]
        return None

    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    mocker.patch(
        "flask.current_app.ers_client_v3.get_entity_names_score",
        side_effect=entity_scores_side_effect,
    )

    now = datetime.now()
    org1 = organization_fixture()
    user_fixture()
    report1, submission1 = report_and_submission_fixture(
        name="the daily bugle newspaper c/o",
        proposed_effective_date=now.replace(now.year - 1),
        is_verified=True,
        stage="QUOTED_BOUND",
        organization_id=org1.id,
    )
    coverage1 = coverage_fixture(
        name="liability", organization_id=org1.id, coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS]
    )
    submission_coverage_fixture(
        submission_id=submission1.id, coverage_id=coverage1.id, coverage_type=CoverageType.PRIMARY
    )

    org2 = organization_fixture(id=2)
    user2 = user_fixture(organization_id=org2.id, id=2)
    report2, submission2 = report_and_submission_fixture(
        name="The Daily Bugle",
        proposed_effective_date=now.replace(now.year - 1),
        owner_id=user2.id,
        is_verified=True,
        organization_id=org2.id,
    )
    coverage2 = coverage_fixture(name="liability", organization_id=org2.id, coverage_types=[CoverageType.PRIMARY])
    submission_coverage_fixture(
        submission_id=submission2.id, coverage_id=coverage2.id, coverage_type=CoverageType.PRIMARY
    )

    return submission1, submission2


def test_auto_relations_creation(app_context, mocker):
    now = datetime.now()
    org1_sub, org2_sub = _populate_db(mocker)
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    report, submission = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    db.session.commit()

    create_automatic_relations_for_submission(submission.id)
    relations = SubmissionRelation.query.all()
    assert len(relations) == 1
    rel = relations[0]
    assert rel.is_active is True
    assert close_numbers(rel.confidence, 0.97)
    assert rel.to_submission_id == org1_sub.id


def test_replace_submission_relation(app_context, mocker):
    now = datetime.now()
    org1_sub, _ = _populate_db(mocker)
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    report, submission = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    report_1, submission_1 = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Another Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission_1.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    db.session.commit()

    # Should create a new relation since not exists
    relations, status = replace_submission_relations(
        [
            {
                "from_submission_id": org1_sub.id,
                "to_submission_id": submission.id,
                "type": SubmissionRelationType.LOOKALIKE,
                "confidence": 0.97,
                "is_active": True,
                "source": "MANUAL",
            }
        ]
    )
    assert status == HTTPStatus.OK
    assert len(relations) == 1
    rel = relations[0]
    assert rel["is_active"] is True
    assert close_numbers(rel["confidence"], 0.97)
    assert rel["type"] == SubmissionRelationType.LOOKALIKE
    assert rel["from_submission_id"] == str(org1_sub.id)
    assert rel["to_submission_id"] == str(submission.id)

    relations, status = replace_submission_relations(
        [
            {
                "from_submission_id": org1_sub.id,
                "to_submission_id": submission_1.id,
                "type": SubmissionRelationType.LOOKALIKE,
                "confidence": 0.95,
                "is_active": True,
                "source": "MANUAL",
            }
        ]
    )
    assert status == HTTPStatus.OK
    assert len(relations) == 1
    rel = relations[0]
    assert rel["is_active"] is True
    assert close_numbers(rel["confidence"], 0.95)
    assert rel["type"] == SubmissionRelationType.LOOKALIKE
    assert rel["from_submission_id"] == str(org1_sub.id)
    assert rel["to_submission_id"] == str(submission_1.id)

    db_rel = SubmissionRelation.query.all()
    # Since we replaced the relation, we should have only one
    assert len(db_rel) == 1


def test_replace_submission_relation_not_allow_non_lookalike_relations(app_context, mocker):
    now = datetime.now()
    org1_sub, _ = _populate_db(mocker)
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    report, submission = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    report_1, submission_1 = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Another Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission_1.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    db.session.commit()

    try:
        replace_submission_relations(
            [
                {
                    "from_submission_id": org1_sub.id,
                    "to_submission_id": submission.id,
                    "type": SubmissionRelationType.RENEWAL,
                    "confidence": 0.97,
                    "is_active": True,
                    "source": "MANUAL",
                }
            ]
        )
    except werkzeug.exceptions.BadRequest as e:
        assert e.code == HTTPStatus.BAD_REQUEST
        assert e.description == "Only LOOKALIKE relation type is supported"


def test_replace_submission_relation_not_allow_multiple_from_submissions(app_context, mocker):
    now = datetime.now()
    org1_sub, _ = _populate_db(mocker)
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    report, submission = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    report_1, submission_1 = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Another Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission_1.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    db.session.commit()

    try:
        replace_submission_relations(
            [
                {
                    "from_submission_id": org1_sub.id,
                    "to_submission_id": submission.id,
                    "type": SubmissionRelationType.RENEWAL,
                    "confidence": 0.97,
                    "is_active": True,
                    "source": "MANUAL",
                },
                {
                    "from_submission_id": submission.id,
                    "to_submission_id": org1_sub.id,
                    "type": SubmissionRelationType.RENEWAL,
                    "confidence": 0.97,
                    "is_active": True,
                    "source": "MANUAL",
                },
            ]
        )
    except werkzeug.exceptions.BadRequest as e:
        assert e.code == HTTPStatus.BAD_REQUEST
        assert e.description == "Multiple 'from_submission_id' not supported"


def test_replace_submission_relation_not_allow_self_relation(app_context, mocker):
    now = datetime.now()
    org1_sub, _ = _populate_db(mocker)
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    report, submission = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    report_1, submission_1 = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="Another Jonah Jameson's Paper DBA The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission_1.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    db.session.commit()

    try:
        replace_submission_relations(
            [
                {
                    "from_submission_id": org1_sub.id,
                    "to_submission_id": org1_sub.id,
                    "type": SubmissionRelationType.LOOKALIKE,
                    "confidence": 0.97,
                    "is_active": True,
                    "source": "MANUAL",
                }
            ]
        )
    except werkzeug.exceptions.BadRequest as e:
        assert e.code == HTTPStatus.BAD_REQUEST
        assert e.description == "'from_submission_id' and 'to_submission_id' cannot be the same"


def test_activation_of_relations(app_context, mocker):
    now = datetime.now()
    org1_sub, org2_sub = _populate_db(mocker)
    report, submission = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="The Daily Bugle Newspaper",
        is_verified=True,
        proposed_effective_date=now,
        stage=SubmissionStage.QUOTED_BOUND,
    )
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.EXCESS)

    report1, submission1 = report_and_submission_fixture(
        organization_id=1,
        owner_id=1,
        name="The Daily Bugle Newspaper C/O",
        is_verified=True,
        proposed_effective_date=now,
    )
    submission_coverage_fixture(submission_id=submission1.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    db.session.commit()

    create_automatic_relations_for_submission(submission.id)
    relations = SubmissionRelation.query.all()
    rel1 = relations[0]
    assert len(relations) == 1
    assert rel1.is_active is True
    assert close_numbers(rel1.confidence, 0.8166)
    assert rel1.to_submission_id == org1_sub.id

    create_automatic_relations_for_submission(submission1.id)
    relations = SubmissionRelation.query.order_by(SubmissionRelation.confidence.desc()).all()
    assert len(relations) == 2
    rel2 = relations[0]
    assert rel2.is_active is True
    assert rel2.confidence == 1.0
    assert rel2.to_submission_id == org1_sub.id
    assert rel2.from_submission_id == submission1.id

    assert relations[1].from_submission_id == submission.id
    assert relations[1].is_active is False

    SubmissionRelation.query.delete()
    submission_relation_fixture(
        from_submission_id=submission1.id,
        to_submission_id=org1_sub.id,
        confidence=1.0,
        type=SubmissionRelationType.RENEWAL,
        source=SubmissionRelationSource.SYNC,
        is_active=True,
    )
    submission.proposed_effective_date = now.replace(now.year - 1)
    db.session.add(submission)
    db.session.commit()

    create_automatic_relations_for_submission(submission1.id)

    relations = SubmissionRelation.query.order_by(SubmissionRelation.confidence.desc()).all()
    assert len(relations) == 2
    assert relations[0].is_active is True
    assert relations[0].confidence == 1.0
    assert relations[0].to_submission_id == org1_sub.id
    assert relations[0].from_submission_id == submission1.id
    assert relations[0].source == SubmissionRelationSource.SYNC

    assert relations[1].from_submission_id == submission1.id
    assert relations[1].to_submission_id == submission.id
    assert relations[1].is_active is False


def test_create_automatic_relations_for_submission(app_context, mocker):
    now = datetime.now()
    org1_sub, org2_sub = _populate_db(mocker)
    business_id = uuid4()

    assert len(SubmissionRelation.query.all()) == 0

    report, submission = report_and_submission_fixture(
        report=report_fixture(organization_id=1, owner_id=1),
        submission=submission_fixture(name="The Daily Bugle Newspaper", owner_id=1, is_verified=True),
    )
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)

    # the submission has no proposed effective date should be no relations created

    create_automatic_relations_for_submission(submission.id)
    assert len(SubmissionRelation.query.all()) == 0

    # add proposed effective date
    submission.proposed_effective_date = now
    db.session.add(submission)
    db.session.commit()

    create_automatic_relations_for_submission(submission.id)
    relations = SubmissionRelation.query.all()
    assert len(relations) == 1
    rel: SubmissionRelation = relations[0]
    assert rel.from_submission_id == submission.id
    assert rel.to_submission_id == org1_sub.id
    assert rel.type == SubmissionRelationType.RENEWAL
    assert close_numbers(rel.confidence, 0.8666)

    db.session.delete(rel)
    db.session.commit()

    # add a submission business to the submission
    submission_business_fixture(
        submission_id=submission.id,
        business_id=business_id,
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    db.session.commit()

    create_automatic_relations_for_submission(submission.id)
    assert len(SubmissionRelation.query.all()) == 0

    # add a submission business to the org1 submission
    submission_business_fixture(
        submission_id=org1_sub.id,
        business_id=business_id,
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    old_report_1, old_sub_1 = report_and_submission_fixture(
        report=report_fixture(organization_id=1, owner_id=1),
        submission=submission_fixture(
            name="The Daily Bugle Newspaper",
            owner_id=1,
            is_verified=True,
            proposed_effective_date=now.replace(now.year - 1),
            stage="QUOTED_BOUND",
        ),
    )
    submission_coverage_fixture(submission_id=old_sub_1.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    db.session.commit()

    create_automatic_relations_for_submission(submission.id)
    relations = SubmissionRelation.query.order_by(SubmissionRelation.confidence.desc()).all()
    assert len(relations) == 2
    rel: SubmissionRelation = relations[0]
    assert rel.from_submission_id == submission.id
    assert rel.to_submission_id == org1_sub.id
    assert rel.type == SubmissionRelationType.RENEWAL
    assert close_numbers(rel.confidence, 0.8666)

    report11, submission11 = report_and_submission_fixture(
        report=report_fixture(organization_id=1, owner_id=1),
        submission=submission_fixture(
            name="the daily bugle newspaper",
            owner_id=1,
            is_verified=True,
            proposed_effective_date=now,
            stage="QUOTED_BOUND",
        ),
    )
    submission_business_fixture(
        submission_id=submission11.id,
        business_id=business_id,
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    submission_coverage_fixture(submission_id=submission11.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)

    resp, status_code = create_automatic_relations_for_submission(submission11.id)
    assert len(resp) == 2
    assert str(resp[0]["to_submission_id"]) == str(org1_sub.id)

    submission_relations = (
        SubmissionRelation.query.filter(SubmissionRelation.from_submission_id == submission.id)
        .order_by(SubmissionRelation.confidence.desc())
        .all()
    )
    assert len(submission_relations) == 2
    # even if there is a relation with higher confidence, the to_submission_id is already 'taken' by another
    # submission with higher confidence. So we use the next best one
    assert submission_relations[1].is_active

    submission11.proposed_effective_date = now.replace(now.year - 1)
    db.session.commit()

    resp, status_code = create_automatic_relations_for_submission(submission.id)
    assert len(resp) == 3
    assert resp[0]["to_submission_id"] == str(submission11.id)
    assert resp[1]["to_submission_id"] == str(org1_sub.id)

    db.session.delete(rel)
    db.session.commit()


def test_create_automatic_relations_for_submission_with_pre_renewal(app_context, mocker):
    now = datetime.now()
    org1_sub, org2_sub = _populate_db(mocker)

    renewal_shell_rep, renewal_shell_sub = report_and_submission_fixture(
        report=report_fixture(organization_id=1, owner_id=1),
        submission=submission_fixture(
            name="The Daily Bugle Newspaper", owner_id=1, is_renewal_shell=True, is_verified_shell=True
        ),
    )

    submission_relation_fixture(
        from_submission_id=renewal_shell_sub.id,
        to_submission_id=org1_sub.id,
        type=SubmissionRelationType.RENEWAL,
        confidence=1.0,
        is_active=True,
        source=SubmissionRelationSource.PRE_RENEWAL,
    )

    report, submission = report_and_submission_fixture(
        report=report_fixture(organization_id=1, owner_id=1),
        submission=submission_fixture(
            name="The Daily Bugle Newspaper", owner_id=1, is_verified=True, proposed_effective_date=now
        ),
    )
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)

    db.session.commit()

    create_automatic_relations_for_submission(submission.id)
    relations = SubmissionRelation.query.all()
    assert len(relations) == 1
    rel: SubmissionRelation = relations[0]
    assert rel.from_submission_id == submission.id
    assert rel.to_submission_id == org1_sub.id
    assert rel.type == SubmissionRelationType.RENEWAL
    assert close_numbers(rel.confidence, 0.8666)

    renewal_shell_rep = ReportV2.query.get(renewal_shell_rep.id)
    assert renewal_shell_rep.is_deleted


def test_create_relation_for_submission_with_pre_renewal(app_context, mocker):
    now = datetime.now()
    org1_sub, org2_sub = _populate_db(mocker)

    renewal_shell_rep, renewal_shell_sub = report_and_submission_fixture(
        report=report_fixture(organization_id=1, owner_id=1),
        submission=submission_fixture(
            name="The Daily Bugle Newspaper", owner_id=1, is_renewal_shell=True, is_verified_shell=True
        ),
    )

    submission_relation_fixture(
        from_submission_id=renewal_shell_sub.id,
        to_submission_id=org1_sub.id,
        type=SubmissionRelationType.RENEWAL,
        confidence=1.0,
        is_active=True,
        source=SubmissionRelationSource.PRE_RENEWAL,
    )

    report, submission = report_and_submission_fixture(
        report=report_fixture(organization_id=1, owner_id=1),
        submission=submission_fixture(
            name="The Daily Bugle Newspaper", owner_id=1, is_verified=True, proposed_effective_date=now
        ),
    )
    cov1 = Coverage.query.filter_by(organization_id=1, name="liability").first()
    submission_coverage_fixture(submission_id=submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)

    db.session.commit()

    create_submission_relation(
        {
            "from_submission_id": submission.id,
            "to_submission_id": org1_sub.id,
            "type": "RENEWAL",
            "is_active": True,
            "confidence": 1,
            "source": "MANUAL",
        }
    )

    relations = SubmissionRelation.query.all()
    assert len(relations) == 1
    rel: SubmissionRelation = relations[0]
    assert rel.from_submission_id == submission.id
    assert rel.to_submission_id == org1_sub.id
    assert rel.type == SubmissionRelationType.RENEWAL
    assert rel.confidence == 1

    renewal_shell_rep = ReportV2.query.get(renewal_shell_rep.id)
    assert renewal_shell_rep.is_deleted


def prepare_test_bind_rate_scenarios():
    # Scenario 1 = submission with no related submissions
    sc1 = [
        BindRateSubmissionTestScenarioData(
            expected_bind_rate=None,
            related_submissions=[],
            stage=SubmissionStage.QUOTED_BOUND,
        )
    ]

    # Scenario 2 = submission with one related submission
    sc2_related_submission = BindRateSubmissionTestScenarioData(
        expected_bind_rate=None,
        related_submissions=[],
        stage=SubmissionStage.QUOTED_BOUND,
    )
    sc2_original_submission = BindRateSubmissionTestScenarioData(
        expected_bind_rate=1.0,
        related_submissions=[sc2_related_submission],
        stage=SubmissionStage.ON_MY_PLATE,
    )
    sc2 = [sc2_original_submission]

    # Scenario 3 = two submissions pointing at each other, only one is bound, other is expired
    sc3_related_submission_1 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=0.0,
        initial_bind_rate=1.0,
        related_submissions=[],
        stage=SubmissionStage.QUOTED_BOUND,
    )
    sc3_related_submission_2 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=1.0,
        initial_bind_rate=0.5,
        related_submissions=[],
        stage=SubmissionStage.EXPIRED,
    )
    sc3_related_submission_1.related_submissions.append(sc3_related_submission_2)
    sc3_related_submission_2.related_submissions.append(sc3_related_submission_1)
    sc3 = [sc3_related_submission_1, sc3_related_submission_2]

    # Scenario 4 = two "main" submissions are pointing at the same 4 "lookalike" submissions
    # In 4, one is bound, one is lost, one is declined and one is currently being worked on = 1/3 bind rate
    sc4_related_submission_1 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=0.5,
        initial_bind_rate=0.6,
        related_submissions=[],
        stage=SubmissionStage.QUOTED_LOST,
    )
    sc4_related_submission_2 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=0.5,
        initial_bind_rate=0.6,
        related_submissions=[],
        stage=SubmissionStage.QUOTED_BOUND,
    )
    sc4_related_submission_3 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=0.5,
        initial_bind_rate=0.6,
        related_submissions=[],
        stage=SubmissionStage.ON_MY_PLATE,
    )
    sc4_related_submission_4 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=0.5,
        initial_bind_rate=0.6,
        related_submissions=[],
        stage=SubmissionStage.DECLINED,
    )
    sc4_main_submission_1 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=1 / 3,
        initial_bind_rate=0.6,
        related_submissions=[
            sc4_related_submission_1,
            sc4_related_submission_2,
            sc4_related_submission_3,
            sc4_related_submission_4,
        ],
        stage=SubmissionStage.QUOTED_BOUND,
    )
    sc4_main_submission_2 = BindRateSubmissionTestScenarioData(
        expected_bind_rate=1 / 3,
        initial_bind_rate=0.6,
        related_submissions=[
            sc4_related_submission_1,
            sc4_related_submission_2,
            sc4_related_submission_3,
            sc4_related_submission_4,
        ],
        stage=SubmissionStage.EXPIRED,
    )
    sc4 = [sc4_main_submission_1, sc4_main_submission_2]

    return [
        pytest.param(sc1, id="Scenario 1 = submission with no related submissions"),
        pytest.param(sc2, id="Scenario 2 = submission with one related submission"),
        pytest.param(
            sc3, id="Scenario 3 = two submissions pointing at each other, only one is bound, other is expired"
        ),
        pytest.param(sc4, id="Scenario 4 = two 'main' submissions are pointing at the same 4 'lookalike' submissions"),
    ]


@pytest.mark.parametrize("scenario", prepare_test_bind_rate_scenarios())
def test_calculate_lookalike_bind_rate(scenario: list[BindRateSubmissionTestScenarioData], app_context, mocker):
    submissions_to_create = {}
    organization_fixture()
    user_fixture()

    already_created_subs = set()
    for sc in scenario:
        submissions_to_create[sc.submission_id] = sc
        for related_submission in sc.related_submissions:
            submissions_to_create[related_submission.submission_id] = related_submission

        for related_submission in submissions_to_create.values():
            if related_submission.submission_id in already_created_subs:
                continue

            already_created_subs.add(related_submission.submission_id)
            report_and_submission_fixture(
                report=report_fixture(organization_id=1, owner_id=1),
                submission=submission_fixture(
                    name="The Daily Bugle Newspaper",
                    owner_id=1,
                    is_verified=True,
                    proposed_effective_date=datetime.now(),
                    id=related_submission.submission_id,
                    stage=related_submission.stage,
                    lookalike_bind_rate=related_submission.initial_bind_rate,
                ),
            )

        for related_submission in sc.related_submissions:
            submission_relation_fixture(
                from_submission_id=sc.submission_id,
                to_submission_id=related_submission.submission_id,
                type="LOOKALIKE",
                confidence=1.0,
                is_active=True,
                source=SubmissionRelationSource.AUTO,
            )

    db.session.commit()

    submission_ids_to_calculate = []
    for sc in scenario:
        submission_ids_to_calculate.append(sc.submission_id)

    response, code = calculate_lookalike_bind_rate_for_submissions(
        body={"calculation_data": [{"submission_id": submission_id} for submission_id in submission_ids_to_calculate]}
    )

    assert code == 200
    assert len(response["calculation_data"]) == len(submission_ids_to_calculate)
    for calculation_data in response["calculation_data"]:
        submission_id = UUID(calculation_data["submission_id"])
        submission = submissions_to_create[submission_id]
        assert submission.expected_bind_rate == calculation_data["lookalike_bind_rate"]

    for sc in scenario:
        submission = db.session.query(Submission).get(sc.submission_id)
        assert submission.lookalike_bind_rate == sc.expected_bind_rate

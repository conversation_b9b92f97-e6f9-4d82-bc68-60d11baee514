from datetime import datetime, timedelta

from flask.ctx import AppContext
from static_common.enums.submission_processing_state import SubmissionProcessingState

from copilot.models import ReportV2, Settings, Submission, db
from copilot.models.submission_processing import SubmissionProcessing
from copilot.v3.controllers.submission_assignment import (
    create_or_update_submission_priority,
    get_assignment_queue_count,
    get_submissions_queue,
)
from copilot.v3.controllers.submissions import update_submission
from tests.integration.factories import (
    organization_fixture,
    report_fixture,
    submission_fixture,
    submission_processing_fixture,
    support_user_fixture,
    support_user_report_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj

USER_ID = 83
SUBMISSION_ID = "e4a64fe3-3087-41f8-8948-e7678a16a4d9"


def _simple_submission_processing_fixture(submission: Submission, report: ReportV2) -> SubmissionProcessing:
    return submission_processing_fixture(
        submission_id=submission.id,
        report_id=report.id,
        created_at=submission.updated_at,
        updated_at=submission.updated_at,
        processing_state=SubmissionProcessingState.PROCESSING,
        verification_sla=report.created_at + timedelta(hours=2),
        is_active=True,
        is_for_night_shift_processing=True,
    )


def _setup(mocker, additional_subs: int = 0):
    mocker.patch("copilot.v3.utils.support_users._now_in_est", lambda: datetime(2023, 6, 1, 22))

    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            is_trusted=True,
            has_submission_permission=lambda type, id: True,
            organization_id=1,
            applicable_settings=Settings(is_cs_manager=True),
            email="<EMAIL>",
            is_being_impersonated=False,
            cross_organization_access=True,
        ),
    )
    mocker.patch(
        "copilot.models.Organization.is_auto_processing_enabled",
        return_value=True,
    )
    organization_fixture(id=6)
    user = user_fixture(id=USER_ID, organization_id=6)
    for i in range(14, 24):
        is_full_pds = "true" if i % 2 == 0 else "false"
        is_auto_processed = is_full_pds == "true"
        processing_state = "ENTITY_MAPPING" if i % 4 == 0 else "NEEDS_CLEARING"
        missing_data_status = None
        id = None
        if i == 14:
            missing_data_status = "NO_FILES"
        if i == 16:
            missing_data_status = "INVALID_FILES"
        if i == 18:
            id = SUBMISSION_ID
            missing_data_status = "NO_DATA"
        report = report_fixture(
            owner_id=user.id, organization_id=6, tier=1, name=f"Report{i}", additional_data={"full_pds": is_full_pds}
        )
        submission = submission_fixture(
            id=id,
            owner_id=user.id,
            processing_state=processing_state,
            report=report,
            is_auto_processed=is_auto_processed,
            missing_data_status=missing_data_status,
            updated_at=datetime.now(),
        )
        _simple_submission_processing_fixture(submission=submission, report=report)

        db.session.commit()

    if additional_subs > 0:
        for i in range(0, additional_subs):
            report = report_fixture(
                owner_id=user.id, organization_id=6, tier=1, name=f"Report{i}", additional_data={"full_pds": "true"}
            )
            submission = submission_fixture(
                owner_id=user.id,
                processing_state="ENTITY_MAPPING",
                report=report,
                is_auto_processed=True,
                updated_at=datetime.now(),
            )
            _simple_submission_processing_fixture(submission=submission, report=report)
            db.session.commit()


def test_submissions_queue_length(app_context, mocker):
    _setup(mocker)

    reports_envelope = get_submissions_queue()
    assert reports_envelope["total_reports"] == 5

    not_matching_report = report_fixture(
        owner_id=USER_ID, organization_id=6, tier=4, name=f"Report not matching", additional_data={"full_pds": "false"}
    )
    submission = submission_fixture(
        owner_id=USER_ID, processing_state="ENTITY_MAPPING", report=not_matching_report, updated_at=datetime.now()
    )
    _simple_submission_processing_fixture(submission=submission, report=not_matching_report)
    db.session.commit()

    reports_envelope = get_submissions_queue()
    assert reports_envelope["total_reports"] == 5


def test_submissions_queue_by_name(app_context, mocker):
    _setup(mocker)

    reports_envelope = get_submissions_queue(name="report1")
    assert reports_envelope["total_reports"] == 3

    reports_envelope = get_submissions_queue()
    assert reports_envelope["total_reports"] == 5


def test_escalate(app_context, mocker):
    _setup(mocker)

    escalated_report_name = "report16"
    reports_envelope = get_submissions_queue(name=escalated_report_name)

    submission_id = reports_envelope["reports"][0]["submissions"][0]["id"]
    create_or_update_submission_priority(
        submission_id, {"escalated_at": str(datetime.now()), "submission_id": submission_id}
    )

    reports_envelope = get_submissions_queue()
    report = reports_envelope["reports"][0]
    submission = report["submissions"][0]

    assert report["name"].lower() == escalated_report_name
    assert submission["priority"]["escalated_at"]

    # de-escalate
    submission_id = reports_envelope["reports"][0]["submissions"][0]["id"]
    create_or_update_submission_priority(submission_id, {"escalated_at": None, "submission_id": submission_id})

    reports_envelope = get_submissions_queue(name=escalated_report_name)
    submission = reports_envelope["reports"][0]["submissions"][0]

    assert submission["priority"]["escalated_at"] is None


def test_submissions_queue_exclude_missing_data(app_context, mocker):
    _setup(mocker)

    update_submission(
        SUBMISSION_ID,
        {
            "stuck_reason": "Test Reason",
        },
    )
    reports_envelope = get_submissions_queue(stuck_only=True)
    assert reports_envelope["total_reports"] == 1
    reports_envelope = get_submissions_queue(stuck_only=True, exclude_missing_data=True)
    assert reports_envelope["total_reports"] == 0


def test_submissions_queue_missing_files(app_context, mocker):
    _setup(mocker)

    reports_envelope = get_submissions_queue(missing_files_only=True)
    assert reports_envelope["total_reports"] == 1


def test_submissions_queue_invalid_files(app_context, mocker):
    _setup(mocker)

    reports_envelope = get_submissions_queue(invalid_files_only=True)
    assert reports_envelope["total_reports"] == 1


def test_submissions_queue_missing_data(app_context, mocker):
    _setup(mocker)

    reports_envelope = get_submissions_queue(missing_data_only=True)
    assert reports_envelope["total_reports"] == 1


def test_submissions_queue_clearing(app_context, mocker):
    _setup(mocker)

    reports_envelope = get_submissions_queue(needs_clearing_only=True)
    assert reports_envelope["total_reports"] == 3


def test_get_submission_queue_exclude_assigned(app_context: AppContext, mocker) -> None:
    _setup(mocker)

    report = report_fixture(
        owner_id=USER_ID, organization_id=6, tier=1, name=f"Report_em", additional_data={"full_pds": "true"}
    )
    submission = submission_fixture(
        owner_id=USER_ID,
        processing_state="ENTITY_MAPPING",
        report=report,
        is_manual_verified=False,
        is_auto_processed=True,
        updated_at=datetime.now(),
    )
    _simple_submission_processing_fixture(submission=submission, report=report)
    support_user = support_user_fixture(user_id=USER_ID, current_report_id=str(report.id))
    support_user_report_fixture(support_user_id=support_user.id, report_id=report.id)
    db.session.commit()

    reports_envelope = get_submissions_queue(for_auto_assignment_only=True, exclude_assigned=False)
    assert reports_envelope["total_reports"] == 3

    reports_envelope = get_submissions_queue(for_auto_assignment_only=True, exclude_assigned=True)
    assert reports_envelope["total_reports"] == 2


def test_get_submission_queue_assignment_date(app_context: AppContext, mocker) -> None:
    _setup(mocker)

    report = report_fixture(
        owner_id=USER_ID, organization_id=6, tier=1, name=f"Report_em", additional_data={"full_pds": "true"}
    )
    submission = submission_fixture(
        owner_id=USER_ID,
        processing_state="ENTITY_MAPPING",
        report=report,
        is_manual_verified=False,
        is_auto_processed=True,
        updated_at=datetime.now(),
    )
    _simple_submission_processing_fixture(submission=submission, report=report)
    support_user = support_user_fixture(user_id=USER_ID, current_report_id=str(report.id))
    support_user_report_fixture(support_user_id=support_user.id, report_id=report.id, created_at=datetime(2024, 1, 11))
    db.session.commit()

    reports_envelope = get_submissions_queue(for_auto_assignment_only=True, exclude_assigned=False)
    report_from_fixture = next((r for r in reports_envelope["reports"] if r["id"] == str(report.id)), None)

    assert report_from_fixture["last_assigned_at"] == "2024-01-11T00:00:00+00:00"


def test_get_assignment_queue_count(app_context: AppContext, mocker) -> None:
    _setup(mocker)
    report = report_fixture(
        owner_id=USER_ID, organization_id=6, tier=1, name=f"Report_em", additional_data={"full_pds": "true"}
    )
    submission = submission_fixture(
        owner_id=USER_ID,
        processing_state="ENTITY_MAPPING",
        report=report,
        is_manual_verified=False,
        is_auto_processed=True,
        updated_at=datetime.now(),
    )
    _simple_submission_processing_fixture(submission=submission, report=report)
    support_user = support_user_fixture(user_id=USER_ID, current_report_id=str(report.id))
    support_user_report_fixture(support_user_id=support_user.id, report_id=report.id)
    db.session.commit()

    count_response, _ = get_assignment_queue_count()
    assert count_response["queue_count"] == 3
    count_response, _ = get_assignment_queue_count(exclude_assigned=True)
    assert count_response["queue_count"] == 2
    assert count_response["specialists_needed"] == 0.00


def test_get_assignment_specialists_needed(app_context: AppContext, mocker) -> None:
    _setup(mocker, 63)
    for i in range(0, 15):
        user_fixture(id=USER_ID + i, organization_id=6)
        support_user_fixture(user_id=USER_ID + i)
    db.session.commit()

    count_response, _ = get_assignment_queue_count()
    assert count_response["queue_count"] == 65
    assert count_response["specialists_needed"] == 6.67


def test_submissions_queue_escalated_lane(app_context, mocker):
    _setup(mocker)

    update_submission(
        SUBMISSION_ID,
        {
            "is_escalated": True,
            "stuck_reason": "some stuck reason",
        },
    )
    reports_envelope = get_submissions_queue(escalated_only=True)
    assert reports_envelope["total_reports"] == 1
    reports_envelope = get_submissions_queue(exclude_escalated=True)
    assert reports_envelope["total_reports"] == 4

    update_submission(
        SUBMISSION_ID,
        {
            "stuck_reason": "",
        },
    )
    reports_envelope = get_submissions_queue(escalated_only=True)
    assert reports_envelope["total_reports"] == 0

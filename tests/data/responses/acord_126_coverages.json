{"named_insured": {"value": "Leisure Import, Inc.", "type": "string", "source": "Leisure Import, Inc.", "lines": [{"boundingPolygon": [{"x": 3.9444, "y": 0.7496}, {"x": 5.3674, "y": 0.7496}, {"x": 5.3674, "y": 0.9405}, {"x": 3.9444, "y": 0.9405}], "page": 0, "confidence": 0.963}]}, "application_information": [{"phone": {"value": "(*************", "type": "string", "source": "(*************", "lines": [{"boundingPolygon": [{"x": 1.509, "y": 0.697}, {"x": 2.4402, "y": 0.697}, {"x": 2.4402, "y": 0.8355}, {"x": 1.509, "y": 0.8355}], "page": 0, "confidence": 0.982}]}, "fax": {"value": "(*************", "type": "string", "source": "(*************", "lines": [{"boundingPolygon": [{"x": 1.5138, "y": 0.8498}, {"x": 2.4545, "y": 0.8498}, {"x": 2.4545, "y": 0.9883}, {"x": 1.5138, "y": 0.9883}], "page": 0, "confidence": 0.994}]}, "agency": {"value": "Compass Direct Insurance Services, Inc. 1100 Town & Country Rd. 1250 Orange, CA 92868", "type": "string", "source": "Compass Direct Insurance Services, Inc. 1100 Town & Country Rd. #1250 Orange, CA 92868", "lines": [{"boundingPolygon": [{"x": 0.2817, "y": 1.079}, {"x": 3.1517, "y": 1.079}, {"x": 3.1517, "y": 1.6185}, {"x": 0.2817, "y": 1.6185}], "page": 0, "confidence": 0.982}]}, "code": null, "sub_code": null, "agency_customer_id": null, "applicant_first_named_insured": {"value": "Leisure Import, Inc.", "type": "string", "source": "Leisure Import, Inc.", "lines": [{"boundingPolygon": [{"x": 3.9444, "y": 0.7496}, {"x": 5.3674, "y": 0.7496}, {"x": 5.3674, "y": 0.9405}, {"x": 3.9444, "y": 0.9405}], "page": 0, "confidence": 0.963}]}, "effective_date": {"value": "2023-03-27T00:00:00+00:00", "type": "date", "source": "03/27/2023", "lines": [{"boundingPolygon": [{"x": 3.5051, "y": 1.3273}, {"x": 4.207, "y": 1.3273}, {"x": 4.207, "y": 1.4657}, {"x": 3.5051, "y": 1.4657}], "page": 0, "confidence": 0.995}]}, "expiration_date": {"value": "2024-03-27T00:00:00+00:00", "type": "date", "source": "03/27/2024", "lines": [{"boundingPolygon": [{"x": 4.4076, "y": 1.332}, {"x": 5.1, "y": 1.332}, {"x": 5.1, "y": 1.4609}, {"x": 4.4076, "y": 1.4609}], "page": 0, "confidence": 0.994}]}, "bill_direct": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 5.2045, "y": 1.1785}, {"x": 5.3989, "y": 1.1785}, {"x": 5.3989, "y": 1.3207}, {"x": 5.2045, "y": 1.3207}], "page": 0, "confidence": 0.774}]}, "bill_agency": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 5.2121, "y": 1.3439}, {"x": 5.3813, "y": 1.3439}, {"x": 5.3813, "y": 1.4837}, {"x": 5.2121, "y": 1.4837}], "page": 0, "confidence": 0.913}]}, "payment_plan": null, "audit": null, "for_company_use_only": null, "date": {"value": "2023-02-08T00:00:00+00:00", "type": "date", "source": "2/08/2023", "lines": [{"boundingPolygon": [{"x": 7.3301, "y": 0.5061}, {"x": 7.9461, "y": 0.5061}, {"x": 7.9461, "y": 0.6445}, {"x": 7.3301, "y": 0.6445}], "page": 0, "confidence": 0.995}]}}], "coverages": [{"commercial_general_liability": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2867, "y": 2.1829}, {"x": 0.4608, "y": 2.1829}, {"x": 0.4608, "y": 2.3227}, {"x": 0.2867, "y": 2.3227}], "page": 0, "confidence": 0.823}]}, "claims_made": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.5048, "y": 2.3279}, {"x": 0.7117, "y": 2.3279}, {"x": 0.7117, "y": 2.5055}, {"x": 0.5048, "y": 2.5055}], "page": 0, "confidence": 0.1}]}, "occurrence": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 1.6832, "y": 2.3294}, {"x": 1.8838, "y": 2.3294}, {"x": 1.8838, "y": 2.5136}, {"x": 1.6832, "y": 2.5136}], "page": 0, "confidence": 0.97}]}, "owners_and_contractors_protective": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2488, "y": 2.5076}, {"x": 0.5088, "y": 2.5076}, {"x": 0.5088, "y": 2.6728}, {"x": 0.2488, "y": 2.6728}], "page": 0, "confidence": 0.93}]}, "deductibles_property_damage": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2577, "y": 3.0042}, {"x": 0.4962, "y": 3.0042}, {"x": 0.4962, "y": 3.1553}, {"x": 0.2577, "y": 3.1553}], "page": 0, "confidence": 0.953}]}, "deductibles_property_damage_amount": null, "deductibles_bodily_injury": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2589, "y": 3.1658}, {"x": 0.5025, "y": 3.1658}, {"x": 0.5025, "y": 3.3145}, {"x": 0.2589, "y": 3.3145}], "page": 0, "confidence": 0.93}]}, "deductibles_bodily_injury_amount": null, "deductibles_per_occurrence": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 2.5104, "y": 3.3479}, {"x": 2.6808, "y": 3.3479}, {"x": 2.6808, "y": 3.4825}, {"x": 2.5104, "y": 3.4825}], "page": 0, "confidence": 0.882}]}, "limits_general_aggregate": {"value": 2000000.0, "type": "number", "source": "$ 2,000,000", "lines": [{"boundingPolygon": [{"x": 5.5919, "y": 2.1819}, {"x": 6.8096, "y": 2.1819}, {"x": 6.8096, "y": 2.3203}, {"x": 5.5919, "y": 2.3203}], "page": 0, "confidence": 0.982}]}, "limits_products_and_completed_operations_aggregate": {"value": 2000000.0, "type": "number", "source": "$ 2,000,000", "lines": [{"boundingPolygon": [{"x": 5.5919, "y": 2.3442}, {"x": 6.8, "y": 2.3442}, {"x": 6.8, "y": 2.4874}, {"x": 5.5919, "y": 2.4874}], "page": 0, "confidence": 0.984}]}, "limits_personal_and_advertising_injury": {"value": 1000000.0, "type": "number", "source": "$ 1,000,000", "lines": [{"boundingPolygon": [{"x": 5.5919, "y": 2.5208}, {"x": 6.8143, "y": 2.5208}, {"x": 6.8143, "y": 2.6593}, {"x": 5.5919, "y": 2.6593}], "page": 0, "confidence": 0.982}]}, "limits_each_occurrence": {"value": 1000000.0, "type": "number", "source": "$ 1,000,000", "lines": [{"boundingPolygon": [{"x": 5.5919, "y": 2.6832}, {"x": 6.8143, "y": 2.6832}, {"x": 6.8143, "y": 2.8216}, {"x": 5.5919, "y": 2.8216}], "page": 0, "confidence": 0.961}]}, "limits_damage_to_rented_premises": {"value": 300000.0, "type": "number", "source": "$ 300,000", "lines": [{"boundingPolygon": [{"x": 5.5919, "y": 2.8407}, {"x": 6.8143, "y": 2.8407}, {"x": 6.8143, "y": 2.9839}, {"x": 5.5919, "y": 2.9839}], "page": 0, "confidence": 0.973}]}, "limits_medical_expense": {"value": 10000.0, "type": "number", "source": "$ 10,000", "lines": [{"boundingPolygon": [{"x": 5.5919, "y": 3.0174}, {"x": 6.8143, "y": 3.0174}, {"x": 6.8143, "y": 3.1606}, {"x": 5.5919, "y": 3.1606}], "page": 0, "confidence": 0.978}]}, "limits_employee_benefits": {"value": 1000000.0, "type": "number", "source": "$ 1,000,000", "lines": [{"boundingPolygon": [{"x": 5.5919, "y": 3.1845}, {"x": 6.8048, "y": 3.1845}, {"x": 6.8048, "y": 3.3277}, {"x": 5.5919, "y": 3.3277}], "page": 0, "confidence": 0.978}]}, "premiums_premises_operations": null, "premiums_products": null, "premiums_other": null, "premiums_total": null, "other_coverages": {"value": "Blanket Al's, Vendors Broad Form 80 ME", "type": "string", "source": "Blanket Al's, Vendors Broad Form 80% ME", "lines": [{"boundingPolygon": [{"x": 0.2674, "y": 3.7144}, {"x": 2.3256, "y": 3.7144}, {"x": 2.3256, "y": 4.0152}, {"x": 0.2674, "y": 4.0152}], "page": 0, "confidence": 0.992}]}}], "schedule_of_hazards": [{"location_number": {"value": 1, "type": "number", "source": "1", "lines": [{"boundingPolygon": [{"x": 0.3725, "y": 5.2708}, {"x": 0.4298, "y": 5.2708}, {"x": 0.4298, "y": 5.3854}, {"x": 0.3725, "y": 5.3854}], "page": 0, "confidence": 0.969}]}, "hazard_number": {"value": 1, "type": "number", "source": "1", "lines": [{"boundingPolygon": [{"x": 0.7306, "y": 5.2708}, {"x": 0.7831, "y": 5.2708}, {"x": 0.7831, "y": 5.3854}, {"x": 0.7306, "y": 5.3854}], "page": 0, "confidence": 0.995}]}, "class_code": {"value": "98550", "type": "string", "source": "98550", "lines": [{"boundingPolygon": [{"x": 2.6503, "y": 5.2613}, {"x": 3.0323, "y": 5.2613}, {"x": 3.0323, "y": 5.3997}, {"x": 2.6503, "y": 5.3997}], "page": 0, "confidence": 0.995}]}, "premium_basis": {"value": "Gross Sales", "source": "S", "lines": [{"boundingPolygon": [{"x": 3.5146, "y": 5.2899}, {"x": 3.5719, "y": 5.2899}, {"x": 3.5719, "y": 5.3902}, {"x": 3.5146, "y": 5.3902}], "page": 0, "confidence": 0.992}]}, "exposure": {"value": 10000000, "type": "number", "source": "10,000,000", "lines": [{"boundingPolygon": [{"x": 4.164, "y": 5.2517}, {"x": 4.8517, "y": 5.2517}, {"x": 4.8517, "y": 5.4236}, {"x": 4.164, "y": 5.4236}], "page": 0, "confidence": 0.993}]}, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": {"value": "APPLIANCE MFG/BBQ", "type": "string", "source": "APPLIANCE MFG/BBQ", "lines": [{"boundingPolygon": [{"x": 0.9933, "y": 5.2565}, {"x": 2.4306, "y": 5.2565}, {"x": 2.4306, "y": 5.3997}, {"x": 0.9933, "y": 5.3997}], "page": 0, "confidence": 0.994}]}}, {"location_number": null, "hazard_number": null, "class_code": null, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": null}, {"location_number": null, "hazard_number": null, "class_code": null, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": null}, {"location_number": null, "hazard_number": null, "class_code": null, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": null}, {"location_number": null, "hazard_number": null, "class_code": null, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": null}, {"location_number": null, "hazard_number": null, "class_code": null, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": null}, {"location_number": null, "hazard_number": null, "class_code": null, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": null}, {"location_number": null, "hazard_number": null, "class_code": null, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null, "classification": null}], "claims_made": [{"proposed_retroactive_date": null, "entry_date_uninterrupted_claims_made_coverage": null, "any_product_work_accident_location_been_excluded": null, "any_product_work_accident_location_been_excluded_explanation": null, "tail_coverage_purchased": null, "tail_coverage_purchased_explanation": null}], "employee_benefits_liability": [{"deductible_per_claim": null, "number_of_employees": null, "number_of_employees_covered": null, "retroactive_date": null}], "contractors": [{"applicant_draw_plans_for_others": null, "applicant_draw_plans_for_others_explanation": null, "any_operations_include_blasting_or_explosive_material": null, "any_operations_include_blasting_or_explosive_material_explanation": null, "any_operation_include_excavation": null, "any_operation_include_excavation_explanation": null, "subcontractors_carry_coverages_less": null, "subcontractors_carry_coverages_less_explanation": null, "subcontractors_allowed_to_work_without_insurance_certificate": null, "subcontractors_allowed_to_work_without_insurance_certificate_explanation": null, "applicant_lease_equipment_to_others": null, "applicant_lease_equipment_to_others_explanation": null, "describe_type_of_work_subcontracted": null, "money_paid_to_subcontractors": null, "percentage_of_work_subcontracted": null, "number_of_full_time_staff": null, "number_of_part_time_staff": null}], "products_completed_operations": [{"products": {"value": "Outdoor Kitchen Cook", "type": "string", "source": "Outdoor Kitchen Cook", "lines": [{"boundingPolygon": [{"x": 0.2865, "y": 4.5547}, {"x": 1.657, "y": 4.5547}, {"x": 1.657, "y": 4.7027}, {"x": 0.2865, "y": 4.7027}], "page": 1, "confidence": 0.993}]}, "annual_gross_sales": {"value": 23000000.0, "type": "number", "source": "$23,000,000.0", "lines": [{"boundingPolygon": [{"x": 1.7239, "y": 4.5929}, {"x": 2.7983, "y": 4.5929}, {"x": 2.7983, "y": 4.7743}, {"x": 1.7239, "y": 4.7743}], "page": 1, "confidence": 0.993}]}, "number_of_units": null, "time_in_market": null, "expected_life": null, "intended_use": {"value": "Outdoor Cooking", "type": "string", "source": "Outdoor Cooking", "lines": [{"boundingPolygon": [{"x": 5.2242, "y": 4.5451}, {"x": 6.2795, "y": 4.5451}, {"x": 6.2795, "y": 4.7027}, {"x": 5.2242, "y": 4.7027}], "page": 1, "confidence": 0.994}]}, "principal_components": {"value": "Metal", "type": "string", "source": "Metal", "lines": [{"boundingPolygon": [{"x": 7.2489, "y": 4.5451}, {"x": 7.6022, "y": 4.5451}, {"x": 7.6022, "y": 4.6788}, {"x": 7.2489, "y": 4.6788}], "page": 1, "confidence": 0.993}]}}, {"products": null, "annual_gross_sales": null, "number_of_units": null, "time_in_market": null, "expected_life": null, "intended_use": null, "principal_components": null}, {"products": null, "annual_gross_sales": null, "number_of_units": null, "time_in_market": null, "expected_life": null, "intended_use": null, "principal_components": null}], "products": [{"applicant_installs_services_demonstrates_products": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9823, "y": 5.6906}, {"x": 8.1943, "y": 5.6906}, {"x": 8.1943, "y": 5.8875}, {"x": 7.9823, "y": 5.8875}], "page": 1, "confidence": 0.8}]}, "applicant_installs_services_demonstrates_products_explanation": null, "foreign_products": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9924, "y": 6.182}, {"x": 8.207, "y": 6.182}, {"x": 8.207, "y": 6.3904}, {"x": 7.9924, "y": 6.3904}], "page": 1, "confidence": 0.862}]}, "foreign_products_explanation": null, "research_and_development_conducted": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9722, "y": 6.443}, {"x": 8.207, "y": 6.443}, {"x": 8.207, "y": 6.6424}, {"x": 7.9722, "y": 6.6424}], "page": 1, "confidence": 0.892}]}, "research_and_development_conducted_explanation": null, "guarantees_warranties_hold_harmless_agreements": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9773, "y": 6.9306}, {"x": 8.2082, "y": 6.9306}, {"x": 8.2082, "y": 7.1517}, {"x": 7.9773, "y": 7.1517}], "page": 1, "confidence": 0.914}]}, "guarantees_warranties_hold_harmless_agreements_explanation": {"value": "Free from MFG defects", "type": "string", "source": "Free from MFG defects", "lines": [{"boundingPolygon": [{"x": 0.2817, "y": 7.1662}, {"x": 1.7143, "y": 7.1662}, {"x": 1.7143, "y": 7.3095}, {"x": 0.2817, "y": 7.3095}], "page": 1, "confidence": 0.984}]}, "products_related_to_aircraft_space_industry": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.971, "y": 7.4424}, {"x": 8.207, "y": 7.4424}, {"x": 8.207, "y": 7.6431}, {"x": 7.971, "y": 7.6431}], "page": 1, "confidence": 0.875}]}, "products_related_to_aircraft_space_industry_explanation": null, "products_recalled_discontinued_changed": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9823, "y": 7.939}, {"x": 8.1956, "y": 7.939}, {"x": 8.1956, "y": 8.1397}, {"x": 7.9823, "y": 8.1397}], "page": 1, "confidence": 0.698}]}, "products_recalled_discontinued_changed_explanation": null, "products_of_others_sold": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9747, "y": 8.4317}, {"x": 8.2044, "y": 8.4317}, {"x": 8.2044, "y": 8.6438}, {"x": 7.9747, "y": 8.6438}], "page": 1, "confidence": 0.803}]}, "products_of_others_sold_explanation": null, "vendors_coverage_required": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9861, "y": 9.4412}, {"x": 8.1842, "y": 9.4412}, {"x": 8.1842, "y": 9.6394}, {"x": 7.9861, "y": 9.6394}], "page": 1, "confidence": 0.962}]}, "vendors_coverage_required_explanation": {"value": "One or two expected", "type": "string", "source": "One or two expected", "lines": [{"boundingPolygon": [{"x": 0.277, "y": 9.6584}, {"x": 1.5567, "y": 9.6584}, {"x": 1.5567, "y": 9.816}, {"x": 0.277, "y": 9.816}], "page": 1, "confidence": 0.994}]}, "any_named_insureds_sell_to_others": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.976, "y": 9.939}, {"x": 8.1994, "y": 9.939}, {"x": 8.1994, "y": 10.141}, {"x": 7.976, "y": 10.141}], "page": 1, "confidence": 0.828}]}, "any_named_insureds_sell_to_others_explanation": null}], "additional_interest": [{"interest_additional_insured": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2677, "y": 0.659}, {"x": 0.4722, "y": 0.659}, {"x": 0.4722, "y": 0.8191}, {"x": 0.2677, "y": 0.8191}], "page": 2, "confidence": 0.982}]}, "interest_loss_payee": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2551, "y": 0.8271}, {"x": 0.4937, "y": 0.8271}, {"x": 0.4937, "y": 0.9859}, {"x": 0.2551, "y": 0.9859}], "page": 2, "confidence": 0.98}]}, "interest_mortgagee": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2577, "y": 0.9926}, {"x": 0.4899, "y": 0.9926}, {"x": 0.4899, "y": 1.1514}, {"x": 0.2577, "y": 1.1514}], "page": 2, "confidence": 0.982}]}, "interest_lienholder": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2614, "y": 1.1658}, {"x": 0.4886, "y": 1.1658}, {"x": 0.4886, "y": 1.3195}, {"x": 0.2614, "y": 1.3195}], "page": 2, "confidence": 0.98}]}, "interest_employee_as_lessor": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 0.2602, "y": 1.335}, {"x": 0.4924, "y": 1.335}, {"x": 0.4924, "y": 1.4786}, {"x": 0.2602, "y": 1.4786}], "page": 2, "confidence": 0.982}]}, "rank": null, "name_and_address": {"value": "To follow", "type": "string", "source": "To follow", "lines": [{"boundingPolygon": [{"x": 1.7287, "y": 0.7305}, {"x": 2.2492, "y": 0.7305}, {"x": 2.2492, "y": 0.8689}, {"x": 1.7287, "y": 0.8689}], "page": 2, "confidence": 0.995}]}, "reference_number": null, "certificate_required": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 4.9028, "y": 0.5002}, {"x": 5.1027, "y": 0.5002}, {"x": 5.1027, "y": 0.6655}, {"x": 4.9028, "y": 0.6655}], "page": 2, "confidence": 0.1}]}, "location": null, "building": null, "vehicle": null, "boat": null, "scheduled_item_number": null, "other": null}], "general_information": [{"any_medical_facilities_provided": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9785, "y": 2.0201}, {"x": 8.2007, "y": 2.0201}, {"x": 8.2007, "y": 2.2233}, {"x": 7.9785, "y": 2.2233}], "page": 2, "confidence": 0.831}]}, "any_medical_facilities_provided_explanation": null, "any_exposure_radioactive_nuclear_materials": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9773, "y": 2.6108}, {"x": 8.2007, "y": 2.6108}, {"x": 8.2007, "y": 2.8052}, {"x": 7.9773, "y": 2.8052}], "page": 2, "confidence": 0.818}]}, "any_exposure_radioactive_nuclear_materials_explanation": null, "any_operations_sold_acquired_discontinued": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9747, "y": 3.8522}, {"x": 8.2019, "y": 3.8522}, {"x": 8.2019, "y": 4.0592}, {"x": 7.9747, "y": 4.0592}], "page": 2, "confidence": 0.772}]}, "any_operations_sold_acquired_discontinued_explanation": null, "equipment_loaned_or_rented": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9823, "y": 4.5256}, {"x": 8.1969, "y": 4.5256}, {"x": 8.1969, "y": 4.7187}, {"x": 7.9823, "y": 4.7187}], "page": 2, "confidence": 0.782}]}, "equipment_loaned_or_rented_explanation": null, "equipment_loaned_or_rented_list": [{"equipment": null, "instruction_given": null, "small_tools": null, "large_equipment": null}, {"equipment": null, "instruction_given": null, "small_tools": null, "large_equipment": null}], "watercraft_docks_floats": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9722, "y": 5.0158}, {"x": 8.207, "y": 5.0158}, {"x": 8.207, "y": 5.2254}, {"x": 7.9722, "y": 5.2254}], "page": 2, "confidence": 0.847}]}, "watercraft_docks_floats_explanation": null, "parking_facilities_owned_rented": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9672, "y": 5.5174}, {"x": 8.212, "y": 5.5174}, {"x": 8.212, "y": 5.7308}, {"x": 7.9672, "y": 5.7308}], "page": 2, "confidence": 0.772}]}, "parking_facilities_owned_rented_explanation": null, "fee_charged_for_parking": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9874, "y": 6.0242}, {"x": 8.1931, "y": 6.0242}, {"x": 8.1931, "y": 6.2261}, {"x": 7.9874, "y": 6.2261}], "page": 2, "confidence": 0.687}]}, "fee_charged_for_parking_explanation": null, "recreation_facilities_provided": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9722, "y": 6.5245}, {"x": 8.207, "y": 6.5245}, {"x": 8.207, "y": 6.7302}, {"x": 7.9722, "y": 6.7302}], "page": 2, "confidence": 0.826}]}, "recreation_facilities_provided_explanation": null, "swimming_pool_on_premises": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9773, "y": 7.0185}, {"x": 8.2032, "y": 7.0185}, {"x": 8.2032, "y": 7.2281}, {"x": 7.9773, "y": 7.2281}], "page": 2, "confidence": 0.689}]}, "swimming_pool_on_premises_explanation": null, "social_events": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9848, "y": 7.5176}, {"x": 8.1956, "y": 7.5176}, {"x": 8.1956, "y": 7.7259}, {"x": 7.9848, "y": 7.7259}], "page": 2, "confidence": 0.738}]}, "social_events_explanation": null, "athletic_teams_sponsored_list": [{"type_of_sport": null, "contact_sport": null, "age_group_12_and_under": null, "age_group_13_to_18": null, "age_group_over_18": null}, {"type_of_sport": null, "contact_sport": null, "age_group_12_and_under": null, "age_group_13_to_18": null, "age_group_over_18": null}], "structural_alterations_contemplated": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9937, "y": 8.0255}, {"x": 8.1918, "y": 8.0255}, {"x": 8.1918, "y": 8.2161}, {"x": 7.9937, "y": 8.2161}], "page": 2, "confidence": 0.745}]}, "structural_alterations_contemplated_explanation": null, "any_demolition_exposure_contemplated": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9899, "y": 8.5322}, {"x": 8.1943, "y": 8.5322}, {"x": 8.1943, "y": 8.7228}, {"x": 7.9899, "y": 8.7228}], "page": 2, "confidence": 0.824}]}, "any_demolition_exposure_contemplated_explanation": null, "applicant_involved_joint_ventures": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9811, "y": 9.0224}, {"x": 8.1981, "y": 9.0224}, {"x": 8.1981, "y": 9.2308}, {"x": 7.9811, "y": 9.2308}], "page": 2, "confidence": 0.676}]}, "applicant_involved_joint_ventures_explanation": null, "lease_employees_other_employers": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9924, "y": 9.5354}, {"x": 8.1817, "y": 9.5354}, {"x": 8.1817, "y": 9.726}, {"x": 7.9924, "y": 9.726}], "page": 2, "confidence": 0.738}]}, "lease_employees_other_employers_explanation": null, "lease_employees_other_employers_lease_to_list": [{"name": null, "wc_coverage": null}, {"name": null, "wc_coverage": null}], "lease_employees_other_employers_lease_from_list": [{"name": null, "wc_coverage": null}, {"name": null, "wc_coverage": null}], "labor_interchange_other_business_or_subsidiaries": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9659, "y": 10.0345}, {"x": 8.2195, "y": 10.0345}, {"x": 8.2195, "y": 10.4707}, {"x": 7.9659, "y": 10.4707}], "page": 2, "confidence": 0.735}]}, "labor_interchange_other_business_or_subsidiaries_explanation": null, "day_care_facilities": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9937, "y": 0.6934}, {"x": 8.188, "y": 0.6934}, {"x": 8.188, "y": 0.8815}, {"x": 7.9937, "y": 0.8815}], "page": 3, "confidence": 0.81}]}, "day_care_facilities_explanation": null, "crimes_occured_or_attempted": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9861, "y": 1.1938}, {"x": 8.1981, "y": 1.1938}, {"x": 8.1981, "y": 1.3894}, {"x": 7.9861, "y": 1.3894}], "page": 3, "confidence": 0.845}]}, "crimes_occured_or_attempted_explanation": null, "formal_safety_policy": {"value": true, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9773, "y": 1.6827}, {"x": 8.2044, "y": 1.6827}, {"x": 8.2044, "y": 1.8948}, {"x": 7.9773, "y": 1.8948}], "page": 3, "confidence": 0.939}]}, "formal_safety_policy_explanation": null, "does_business_promotion_represent_premises_safety_or_security": {"value": false, "type": "boolean", "lines": [{"boundingPolygon": [{"x": 7.9811, "y": 2.1919}, {"x": 8.1981, "y": 2.1919}, {"x": 8.1981, "y": 2.3939}, {"x": 7.9811, "y": 2.3939}], "page": 3, "confidence": 0.845}]}, "does_business_promotion_represent_premises_safety_or_security_explanation": null}], "remarks": {"value": "Leisure Import, Inc. designs and private labels Gas Grills and Associated products for outdoor kitchens to American Distributors. They have an Exclusive ETL certified factory in Mainland China that manufactures to our specifications. They place the Brand associated with the products and ship directly to the Distributor/Retailer. <PERSON> travels on a regular basis to the factory in China, like leaving today for a week and a half, to check up on their high quality control safeguards and looking into better ways to improve their finished product. Management is VERY hands on in all aspects of this business.", "type": "string", "source": "Leisure Import, Inc. designs and private labels Gas Grills and Associated products for outdoor kitchens to American Distributors. They have an Exclusive ETL certified factory in Mainland China that manufactures to our specifications. They place the Brand associated with the products and ship directly to the Distributor/Retailer. <PERSON> travels on a regular basis to the factory in China, like leaving today for a week and a half, to check up on their high quality control safeguards and looking into better ways to improve their finished product. Management is VERY hands on in all aspects of this business.", "lines": [{"boundingPolygon": [{"x": 0.2674, "y": 2.8932}, {"x": 8.1419, "y": 2.8932}, {"x": 8.1419, "y": 3.7001}, {"x": 0.2674, "y": 3.7001}], "page": 3, "confidence": 0.99}]}, "signature": [{"date": {"value": "2023-02-08T00:00:00+00:00", "type": "date", "source": "2/08/2023", "lines": [{"boundingPolygon": [{"x": 7.3301, "y": 0.5061}, {"x": 7.9461, "y": 0.5061}, {"x": 7.9461, "y": 0.6445}, {"x": 7.3301, "y": 0.6445}], "page": 0, "confidence": 0.995}]}}], "lease_from_employees_information": [], "equipment_instruction_given_y_n": []}
[{"id": "a2cd79bb-5fbc-415a-aece-d7b14ad6f9f0", "created": "2023-06-05T11:25:58.904Z", "completed": "2023-06-05T11:26:02.907Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "36#9a6a276e-df8d-4722-9e33-8e31346af620#a6de7896-e4e3-4f3c-8260-a6d7eadd1ad5#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [], "page_count": 4, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "33cdc732-a5ad-481f-a77c-9cb6f57e7555", "created": "2023-06-05T11:07:13.753Z", "completed": "2023-06-05T11:07:18.964Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#56f693b5-8b7f-44e1-aca3-a18b24086844#73883f1d-238f-4211-bfce-a696c78b68b1#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "amrisc", "startPage": 0, "endPage": 1, "output": {"parsedDocument": {"report_generated_date": {"source": "03/03/2023", "value": "2023-03-03T00:00:00.000Z", "type": "date"}, "claims": [{"claim_number": {"type": "string", "value": "4174933"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "CPAT Santa Rosa ISD"}, "policy_effective_date": {"source": "09/01/2017", "value": "2017-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "7/25/2020", "value": "2020-07-25T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/25/2020", "value": "2020-07-25T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "37,267.77", "value": 37267.77, "type": "number"}, "total_amount_incurred": {"source": "134,073.76", "value": 134073.76, "type": "number"}, "total_amount_reserved_loss": {"source": "25,000.00", "value": 25000, "type": "number"}, "total_amount_reserved_expense": {"source": "71,805.99", "value": 71805.99, "type": "number"}, "loss_description": {"type": "string", "value": "Damage to Buildings/Contents due to Hurricane Hanna."}, "claim_status": {"type": "string", "value": "OPEN"}}]}, "configuration": "amrisc", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-03-03T06:43:35.000-06:00"}}, "errors": [], "classificationSummary": [{"configuration": "amrisc", "score": {"score": 16, "fieldsPresent": 16, "penalties": 0}}, {"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 2, "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "860f2852-1ef0-482c-a7d7-7e783b6b6e17", "created": "2023-06-05T10:57:51.587Z", "completed": "2023-06-05T10:57:56.865Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#40f02262-c42b-46a1-9edb-69af19d6ebba#ea464c8a-82a9-4ad1-96ef-50c35df841e0#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "amrisc", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"report_generated_date": {"source": "03/02/2023", "value": "2023-03-02T00:00:00.000Z", "type": "date"}, "claims": [{"claim_number": {"type": "string", "value": "4157311"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Alice ISD"}, "policy_effective_date": {"source": "09/01/2018", "value": "2018-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "10/15/2018", "value": "2018-10-15T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "10/15/2018", "value": "2018-10-15T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "8,738.40", "value": 8738.4, "type": "number"}, "total_amount_paid_expense": {"source": "8,040.84", "value": 8040.84, "type": "number"}, "total_amount_incurred": {"source": "16,779.24", "value": 16779.24, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "<PERSON> Middle School- <PERSON><PERSON><PERSON> caught fire."}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4158691"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Alice ISD"}, "policy_effective_date": {"source": "09/01/2018", "value": "2018-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "12/7/2018", "value": "2018-12-07T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "12/7/2018", "value": "2018-12-07T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "3,241.54", "value": 3241.54, "type": "number"}, "total_amount_incurred": {"source": "3,241.54", "value": 3241.54, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "Heavy rains caused roof leak at Williams Adams Middle School. Three classrooms were flooded."}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4165834"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Alice ISD"}, "policy_effective_date": {"source": "09/01/2018", "value": "2018-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "7/31/2019", "value": "2019-07-31T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/31/2019", "value": "2019-07-31T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "12,900.00", "value": 12900, "type": "number"}, "total_amount_paid_expense": {"source": "3,271.84", "value": 3271.84, "type": "number"}, "total_amount_incurred": {"source": "16,171.84", "value": 16171.84, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "A/C unit leaked water onto gym floor causing floor to bulge; Estimate for repairs is $17900. 900 W 3rd St,"}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4181767"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Alice ISD"}, "policy_effective_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "10,000.00", "value": 10000, "type": "number"}, "total_amount_paid_expense": {"source": "10,375.06", "value": 10375.06, "type": "number"}, "total_amount_incurred": {"source": "20,375.06", "value": 20375.06, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "Water damage at several campuses due to busted water lines resulting from Freeze; Campuses affected;"}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4182851"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Alice ISD"}, "policy_effective_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "86,266.11", "value": 86266.11, "type": "number"}, "total_amount_paid_expense": {"source": "13,097.55", "value": 13097.55, "type": "number"}, "total_amount_incurred": {"source": "116,513.66", "value": 116513.66, "type": "number"}, "total_amount_reserved_loss": {"source": "15,900.00", "value": 15900, "type": "number"}, "total_amount_reserved_expense": {"source": "1,250.00", "value": 1250, "type": "number"}, "loss_description": {"type": "string", "value": "Water damage at several campuses due to busted water lines resulting from Freeze; Campuses affected;"}, "claim_status": {"type": "string", "value": "OPEN"}}, {"claim_number": {"type": "string", "value": "4184110"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Alice ISD"}, "policy_effective_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "5/17/2021", "value": "2021-05-17T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/17/2021", "value": "2021-05-17T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "7,420.04", "value": 7420.04, "type": "number"}, "total_amount_incurred": {"source": "7,420.04", "value": 7420.04, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "Wind and Rain damage to several buildings and structures."}, "claim_status": {"type": "string", "value": "CLOSED"}}]}, "configuration": "amrisc", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-03-02T13:35:23.000-06:00"}}, "errors": [], "classificationSummary": [{"configuration": "amrisc", "score": {"score": 91, "fieldsPresent": 91, "penalties": 0}}, {"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 7, "fields_present": 7, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 7, "fields_present": 7, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "3bcec9ff-89d6-46db-af5f-e61d6dd17794", "created": "2023-06-05T10:51:49.806Z", "completed": "2023-06-05T10:51:57.094Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#d5577ce4-f1dd-48a1-a8c4-cee2bc6af4c3#58c2e488-bb10-4a22-bc2c-6f2f7650d7b0#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "rsui", "startPage": 0, "endPage": 1, "output": {"parsedDocument": {"report_generated_date": {"source": "3/2/2023", "value": "2023-03-02T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District /", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District /", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District /", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District /", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2017", "value": "2017-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2016", "value": "2016-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2017", "value": "2017-09-01T00:00:00.000Z", "type": "date"}, "claim_number": {"type": "string", "value": "7030125170"}, "total_amount_incurred": {"source": "$3,740.11", "value": 3740.11, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$3,740.11", "value": 3740.11, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$3,740.11", "value": 3740.11, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_reported_date": {"source": "7/26/2018", "value": "2018-07-26T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "8/25/2017", "value": "2017-08-25T00:00:00.000Z", "type": "date"}, "loss_location": {"value": "PREMONT, TX", "type": "string"}, "loss_description": {"type": "string", "value": "HURRICANE HARVEY policy numberLHT397852 0 False claim number 7030125170 ** Hidden Row ** Paid Reserve Incurred $3,740.11 $0.00 $3,740.11\nPolicy Gross Incurred Paid Reserve Incurred\nIndemnity $0.00 $0.00 $0.00 Expense $3,740.11 $0.00 $3,740.11 Total $3,740.11 $0.00 $3,740.11"}, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2015", "value": "2015-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2016", "value": "2016-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2014", "value": "2014-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2015", "value": "2015-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2013", "value": "2013-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2014", "value": "2014-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Premont Independent School District", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2012", "value": "2012-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2013", "value": "2013-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}]}, "configuration": "rsui", "validations": [], "fileMetadata": {"info": {"author": "", "title": "Loss Run", "creator": "Microsoft Reporting Services 20********", "producer": "Microsoft Reporting Services PDF Rendering Extension 20********", "creation_date": "2023-03-02T11:33:54.000-05:00"}}, "errors": [], "classificationSummary": [{"configuration": "rsui", "score": {"score": 62, "fieldsPresent": 62, "penalties": 0}}], "validation_summary": {"fields": 11, "fields_present": 11, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 2, "validation_summary": {"fields": 11, "fields_present": 11, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "a3fd5480-a1c3-4732-8e93-28f08a3c2431", "created": "2023-06-05T10:47:52.764Z", "completed": "2023-06-05T10:48:20.111Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "36#e0d27d78-4b9e-42da-8c74-9231ae9b799a#a2bfd1ce-f3fd-4198-bf34-22fe9a5d869d#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [{"documentType": "acord_forms", "configuration": "acord_125_2016_03", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"agency_information": [{"date": null, "agency": {"type": "string", "value": "Carlisle Insurance 500 N Water #900 Corpus Christi, TX 78401"}, "contact_name": {"type": "string", "value": "<PERSON>, CIC, CMIP, CPCU"}, "contact_phone": {"type": "string", "value": "(*************"}, "contact_fax": {"type": "string", "value": "(*************"}, "contact_email": null, "underwriter": null, "transaction_status_renew": {"type": "boolean", "value": false}}], "lines_of_business": [{"boilerMachinery": {"type": "boolean", "value": false}, "boilerMachinery_premium": null, "businessAuto": {"type": "boolean", "value": false}, "businessAuto_premium": null, "businessOwners": null, "businessOwners_premium": null, "generalLiability": {"type": "boolean", "value": false}, "generalLiability_premium": null, "commercialInlandMarine": {"type": "boolean", "value": false}, "commercialInlandMarine_premium": null, "property": {"type": "boolean", "value": true}, "property_premium": null, "crime": {"type": "boolean", "value": false}, "crime_premium": null, "cyberPrivacy": {"type": "boolean", "value": false}, "cyberPrivacy_premium": null, "fiduciaryLiability": {"type": "boolean", "value": false}, "fiduciaryLiability_premium": null, "garageDealers": {"type": "boolean", "value": false}, "garageDealers_premium": null, "liquorLiability": {"type": "boolean", "value": false}, "liquorLiability_premium": null, "motorCarriers": {"type": "boolean", "value": false}, "motorCarriers_premium": null, "truckers": {"type": "boolean", "value": false}, "truckers_premium": null, "umbrella": {"type": "boolean", "value": false}, "umbrella_premium": null, "yacht": {"type": "boolean", "value": false}, "yacht_premium": null, "cyber_liability": null, "cyber_liability_premium": null, "custom_1_name": null, "custom_1": {"type": "boolean", "value": false}, "custom_1_premium": null, "custom_2_name": null, "custom_2": {"type": "boolean", "value": false}, "custom_2_premium": null, "custom_3_name": null, "custom_3": {"type": "boolean", "value": false}, "custom_3_premium": null, "custom_4_name": null, "custom_4": {"type": "boolean", "value": false}, "custom_4_premium": null, "custom_5_name": null, "custom_5": {"type": "boolean", "value": false}, "custom_5_premium": null, "custom_6": {"type": "boolean", "value": false}, "custom_6_premium": null}], "policy_information": [{"proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "billing_plan_direct": {"type": "boolean", "value": false}, "billing_plan_agency": {"type": "boolean", "value": true}, "payment_plan": null, "method_of_payment": null, "audit": null, "deposit": null, "minimum_premium": null, "policy_premium": null}], "proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "primary_naics": null, "description_of_primary_operations": null, "applicant_information": [{"applicant_name_and_address": {"type": "string", "value": "Mission CISD 1201 Bryce Dr Mission, TX 78572-4399"}, "applicant_address": {"value": "1201 <PERSON>, TX 78572-4399", "type": "address"}, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}], "premises_info": [{"location_number": {"source": "1", "value": 1, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": null, "city": {"type": "string", "value": "Mission"}, "state": {"type": "string", "value": "TX"}, "county": null, "zip": {"type": "string", "value": "78572"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": {"type": "string", "value": "All locations - See Schedule"}}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}], "nature_of_business": [{"apartments": {"type": "boolean", "value": false}, "contractor": {"type": "boolean", "value": false}, "manufacturing": {"type": "boolean", "value": false}, "restaurant": {"type": "boolean", "value": false}, "service": {"type": "boolean", "value": false}, "condominiums": {"type": "boolean", "value": false}, "institutional": {"type": "boolean", "value": false}, "office": {"type": "boolean", "value": false}, "retail": {"type": "boolean", "value": false}, "wholesale": null, "custom": {"type": "boolean", "value": false}, "start_date": null, "description_of_primary_operations": null, "retail_or_service_percentage_sales": null, "installation_service_repair_work_percentage": null, "off_premises_work": null, "operations_description_of_other_named_insureds": null}], "general_information": [{"is_applicant_subsidiary": null, "subsidiary_parent_company_name": null, "subsidiary_percentage_owned": null, "does_applicant_have_subsidiaries": null, "applicants_subsidiary_company_name": null, "applicants_subsidiary_percentage_owned": null, "is_formal_safety_plan_in_operation": {"value": "Y", "type": "string"}, "safety_program_manual": {"type": "boolean", "value": false}, "safety_plan_monthly_meetings": {"type": "boolean", "value": false}, "safety_plan_safety_position": {"type": "boolean", "value": false}, "safety_plan_osha": {"type": "boolean", "value": false}, "any_exposure_flammables": null, "flammables_explanation": null, "any_policy_or_coverage_declined": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBS<PERSON>IARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-R<PERSON><PERSON><PERSON><PERSON> UNDERWRITING CONDITION CORRECTED (Describe): 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION W ITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 9. HAS APPLICANT HAD A FORECLOSURE, REPOSSESSION, BANKRUPTCY OR FILED FOR BANKRUPTCY DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR W HICH COVERAGE IS NOT REQUESTED? 14. DOES APPLICANT OW N / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "coverage_declined_non_payment": {"type": "boolean", "value": false}, "coverage_declined_agent": {"type": "boolean", "value": false}, "coverage_declined_non_renewal": {"type": "boolean", "value": false}, "coverage_declined_underwriting": {"type": "boolean", "value": false}, "coverage_declined_condition_corrected": {"type": "boolean", "value": false}, "any_past_losses_relating_to_sexual_abuse": null, "any_past_losses_relating_to_sexual_abuse_explanation": null, "any_applicant_been_indicted": null, "any_applicant_been_indicted_explanation": null, "any_uncorrected_fire_or_safety_violations": null, "has_applicant_had_forclosure": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBSIDIARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER 5. ANY POLICY OR COVERAGE DECLINED, CANCELLED OR NON-R<PERSON><PERSON><PERSON> ED DURING THE PRIOR THREE (3) YEARS FOR ANY PREMISES OR OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-RENEWAL UNDERWRITING CONDITION CORRECTED (Describe): 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION W ITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR W HICH COVERAGE IS NOT REQUESTED? 14. DOES APPLICANT OW N / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "has_applicant_had_judgement_or_lien": null, "is_business_placed_in_trust": null, "name_of_trust": null, "any_foreign_operations": null, "does_applicant_have_other_business_ventures": null, "does_applicant_have_other_business_ventures_explanation": null, "does_applicant_own_lease_operate_any_drones": null, "does_applicant_own_lease_operate_any_drones_explanation": null, "does_applicant_hire_others_to_operate_drones": null, "does_applicant_hire_others_to_operate_drones_explanation": null}]}, "configuration": "acord_125_2016_03", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "acord_125_2016_03", "score": {"score": 77, "fieldsPresent": 77, "penalties": 0}}], "validation_summary": {"fields": 16, "fields_present": 14, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 16, "fields_present": 14, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "762f3e07-a017-4a46-bb46-788dda4dc880", "created": "2023-06-05T10:32:20.501Z", "completed": "2023-06-05T10:32:26.364Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#e7fde94d-0b04-48de-b01f-ab82152fac3e#3ea49ebc-15d6-4855-86ee-77bf6578f752#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "velocity", "startPage": 0, "endPage": 0, "output": {"parsedDocument": {"report_generated_date": {"source": "01/31/2023", "value": "2023-01-31T00:00:00.000Z", "type": "date"}, "claims": []}, "configuration": "velocity", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-03-03T06:45:38.000-06:00"}}, "errors": [], "classificationSummary": [{"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 1, "fields_present": 1, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "amrisc", "startPage": 1, "endPage": 2, "output": {"parsedDocument": {"report_generated_date": {"source": "03/03/2023", "value": "2023-03-03T00:00:00.000Z", "type": "date"}, "claims": [{"claim_number": {"type": "string", "value": "4182326"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Tarkington Independent School District"}, "policy_effective_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "10,000.00", "value": 10000, "type": "number"}, "total_amount_paid_expense": {"source": "1,975.67", "value": 1975.67, "type": "number"}, "total_amount_incurred": {"source": "11,975.67", "value": 11975.67, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "Snow melting on HS roof, inundated new copy machine, Backflow preventers froze and cracked, Ag Shop"}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4183209"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Tarkington Independent School District"}, "policy_effective_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2/15/2021", "value": "2021-02-15T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "38,196.10", "value": 38196.1, "type": "number"}, "total_amount_paid_expense": {"source": "6,285.12", "value": 6285.12, "type": "number"}, "total_amount_incurred": {"source": "49,797.21", "value": 49797.21, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "5,315.99", "value": 5315.99, "type": "number"}, "loss_description": {"type": "string", "value": "Snow melting on HS roof, inundated new copy machine, Backflow preventers froze and cracked, Ag Shop"}, "claim_status": {"type": "string", "value": "OPEN"}}]}, "configuration": "amrisc", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-03-03T06:45:38.000-06:00"}}, "errors": [], "classificationSummary": [{"configuration": "amrisc", "score": {"score": 31, "fieldsPresent": 31, "penalties": 0}}, {"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 3, "fields_present": 3, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 3, "validation_summary": {"fields": 4, "fields_present": 4, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "e7704dd6-43e5-449c-bf99-11153310c919", "created": "2023-06-05T10:29:55.460Z", "completed": "2023-06-05T10:30:12.603Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#e6f0ffc3-ba3b-4e78-819b-e98242118573#511ba307-e3ea-4cfa-b35d-fbab36a4bb85#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 5, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "6d9189a5-b9f1-4794-b916-95eae48053d6", "created": "2023-06-05T10:29:54.019Z", "completed": "2023-06-05T10:30:10.243Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#e6f0ffc3-ba3b-4e78-819b-e98242118573#5e88ab76-bb67-4b6c-b0ca-eba824136825#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "5353f6ee-d245-4ac5-bdf6-6b5a6051751f", "created": "2023-06-05T10:29:51.737Z", "completed": "2023-06-05T10:30:07.342Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#e6f0ffc3-ba3b-4e78-819b-e98242118573#aa09661b-0ed0-4065-be4c-f862b5114af1#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "d63ebc7a-2496-44f1-a09f-edf2d58e0176", "created": "2023-06-05T10:18:50.407Z", "completed": "2023-06-05T10:19:01.889Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "36#24bdcf3b-6eac-4893-af28-0ef028d066ca#ba49933c-fa43-4cce-8bc2-de6019b7103b#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [{"documentType": "acord_forms", "configuration": "acord_125_2014_12", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"policy_information": [{"proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "billing_plan_direct": {"type": "boolean", "value": false}, "billing_plan_agency": {"type": "boolean", "value": false}, "payment_plan": null, "method_of_payment": null, "audit": null, "deposit": null, "minimum_premium": null, "policy_premium": null}], "lines_of_business": [], "proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "primary_naics": null, "description_of_primary_operations": {"type": "string", "value": "Manufacturers of natural gas compression fittings and risers to gas distribution companies, utilities, wholesale distributors, their supply chains across the US and Canada."}, "applicant_information": [{"applicant_name_and_address": {"type": "string", "value": "Norton McMurray Manufacturing Company Inc PO Box 588 Geneva IL 60134"}, "applicant_address": {"value": "Inc PO Box 588\nGeneva IL 60134", "type": "address"}, "applicant_info_gl_code_1": null, "applicant_naics": null, "applicant_website_address": null}, {"applicant_name_and_address": {"type": "string", "value": "Normac LLC P.O. Box 588 Geneva IL 60134"}, "applicant_address": {"value": "P.O. Box 588\nGeneva IL 60134", "type": "address"}, "applicant_info_gl_code_1": null, "applicant_naics": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_naics": null, "applicant_website_address": null}], "premises_info": [{"location_number": {"source": "1", "value": 1, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "2570 Kaneville Court"}, "city": {"type": "string", "value": "Geneva"}, "state": {"type": "string", "value": "IL"}, "county": {"value": "<PERSON>", "type": "string"}, "zip": {"type": "string", "value": "60134"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": {"type": "string", "value": "Office/Mfg (7 EE's)"}}, {"location_number": {"source": "2", "value": 2, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "2600 Kaneville Court"}, "city": {"type": "string", "value": "Geneva"}, "state": {"type": "string", "value": "IL"}, "county": {"value": "<PERSON>", "type": "string"}, "zip": {"type": "string", "value": "60134"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": {"type": "string", "value": "Office/Mfg (23 EE's)"}}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}], "nature_of_business": [{"apartments": {"type": "boolean", "value": false}, "contractor": {"type": "boolean", "value": false}, "manufacturing": {"type": "boolean", "value": false}, "restaurant": {"type": "boolean", "value": false}, "service": {"type": "boolean", "value": false}, "condominiums": {"type": "boolean", "value": false}, "institutional": {"type": "boolean", "value": false}, "office": {"type": "boolean", "value": false}, "retail": {"type": "boolean", "value": false}, "wholesale": {"type": "boolean", "value": false}, "custom_name": null, "custom": {"type": "boolean", "value": false}, "start_date": {"source": "01/01/1938", "value": "1938-01-01T00:00:00.000Z", "type": "date"}, "description_of_primary_operations": {"type": "string", "value": "Manufacturers of natural gas compression fittings and risers to gas distribution companies, utilities, wholesale distributors, their supply chains across the US and Canada."}, "retail_or_service_percentage_sales": null, "installation_service_repair_work_percentage": null, "off_premises_work": null, "operations_description_of_other_named_insureds": {"type": "string", "value": "Normac LLC, buys assets, investments and leases equipment."}}]}, "configuration": "acord_125_2014_12", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "acord_125_2014_12", "score": {"score": 57, "fieldsPresent": 57, "penalties": 0}}], "validation_summary": {"fields": 13, "fields_present": 12, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 13, "fields_present": 12, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "b6bd91d8-9a83-4729-890d-bcdadf89d887", "created": "2023-06-05T10:17:21.428Z", "completed": "2023-06-05T10:17:26.728Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#3e1f2de1-0d9c-44a2-aa79-32f1e56479b5#bca0d015-8211-40c2-9c6e-57c9bdc03b1b#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 2, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "5ad3f1ae-4886-4c3a-8b98-fa68fa583eb6", "created": "2023-06-05T10:15:15.885Z", "completed": "2023-06-05T10:15:22.050Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#bce2de59-7855-471b-b800-3ea50ec94a0d#3206f6b7-50d4-49fb-a49f-adf85f480c1f#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "velocity", "startPage": 0, "endPage": 0, "output": {"parsedDocument": {"report_generated_date": {"source": "03/31/2023", "value": "2023-03-31T00:00:00.000Z", "type": "date"}, "claims": []}, "configuration": "velocity", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-04-18T12:44:24.000-05:00"}}, "errors": [], "classificationSummary": [{"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 1, "fields_present": 1, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "amrisc", "startPage": 1, "endPage": 4, "output": {"parsedDocument": {"report_generated_date": {"source": "04/18/2023", "value": "2023-04-18T00:00:00.000Z", "type": "date"}, "claims": [{"claim_number": {"type": "string", "value": "4165753"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Little Cypress Mauriceville CISD"}, "policy_effective_date": {"source": "09/01/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "9/19/2019", "value": "2019-09-19T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "9/19/2019", "value": "2019-09-19T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "2,500,000.00", "value": 2500000, "type": "number"}, "total_amount_paid_expense": {"source": "61,066.24", "value": 61066.24, "type": "number"}, "total_amount_incurred": {"source": "2,561,066.24", "value": 2561066.24, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "Flood"}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4173817"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Little Cypress Mauriceville CISD"}, "policy_effective_date": {"source": "09/01/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "8/26/2020", "value": "2020-08-26T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "8/26/2020", "value": "2020-08-26T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "593,741.17", "value": 593741.17, "type": "number"}, "total_amount_paid_expense": {"source": "23,856.56", "value": 23856.56, "type": "number"}, "total_amount_incurred": {"source": "617,597.73", "value": 617597.73, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "Roof Damage and Water Intrusion. Fence and Awning Damage in Multiple Locations."}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4182015"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Little Cypress Mauriceville CISD"}, "policy_effective_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "2/17/2021", "value": "2021-02-17T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2/17/2021", "value": "2021-02-17T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "5,000.00", "value": 5000, "type": "number"}, "total_amount_paid_expense": {"source": "4,368.96", "value": 4368.96, "type": "number"}, "total_amount_incurred": {"source": "9,368.96", "value": 9368.96, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "CAT 17 ƒ€\" Water - Pipe Burst"}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4183807"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Little Cypress Mauriceville CISD"}, "policy_effective_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "2/17/2021", "value": "2021-02-17T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2/17/2021", "value": "2021-02-17T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "34,758.43", "value": 34758.43, "type": "number"}, "total_amount_paid_expense": {"source": "4,724.65", "value": 4724.65, "type": "number"}, "total_amount_incurred": {"source": "39,483.08", "value": 39483.08, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "CAT 17 – <PERSON> <PERSON> <PERSON><PERSON>."}, "claim_status": {"type": "string", "value": "CLOSED"}}, {"claim_number": {"type": "string", "value": "4197841"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Little Cypress Mauriceville CISD"}, "policy_effective_date": {"source": "09/01/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "9/13/2022", "value": "2022-09-13T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "9/13/2022", "value": "2022-09-13T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "4,954.62", "value": 4954.62, "type": "number"}, "total_amount_incurred": {"source": "14,113.27", "value": 14113.27, "type": "number"}, "total_amount_reserved_loss": {"source": "999.00", "value": 999, "type": "number"}, "total_amount_reserved_expense": {"source": "8,159.65", "value": 8159.65, "type": "number"}, "loss_description": {"type": "string", "value": "Fire"}, "claim_status": {"type": "string", "value": "OPEN"}}, {"claim_number": {"type": "string", "value": "4199954"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Little Cypress Mauriceville CISD"}, "policy_effective_date": {"source": "09/01/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "10/23/2022", "value": "2022-10-23T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "10/23/2022", "value": "2022-10-23T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "2,742.19", "value": 2742.19, "type": "number"}, "total_amount_incurred": {"source": "2,742.19", "value": 2742.19, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"type": "string", "value": "Vandalism."}, "claim_status": {"type": "string", "value": "CLOSED"}}]}, "configuration": "amrisc", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-04-18T12:44:24.000-05:00"}}, "errors": [], "classificationSummary": [{"configuration": "amrisc", "score": {"score": 91, "fieldsPresent": 91, "penalties": 0}}, {"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 7, "fields_present": 7, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 5, "validation_summary": {"fields": 8, "fields_present": 8, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "a4312390-7379-4770-adbf-647b6685feb1", "created": "2023-06-05T10:08:30.931Z", "completed": "2023-06-05T10:08:48.259Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "3#3247fee6-a72a-4617-9a81-ed653c09c58b#04d39322-c62e-44e7-b346-2e812f865409#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.stage.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [{"documentType": "acord_forms", "configuration": "acord_125_2016_03", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"agency_information": [{"date": {"source": "06/12/2023", "value": "2023-06-12T00:00:00.000Z", "type": "date"}, "agency": {"type": "string", "value": "AWA Insurance Agency 13700 Six Mile Cypress Pkwy Suite #1 Fort Myers, FL 33912"}, "contact_name": {"type": "string", "value": "<PERSON>"}, "contact_phone": {"type": "string", "value": "(*************"}, "contact_fax": {"type": "string", "value": "(*************"}, "contact_email": null, "underwriter": {"type": "string", "value": "<PERSON>"}, "transaction_status_renew": {"type": "boolean", "value": false}}], "lines_of_business": [{"boilerMachinery": {"type": "boolean", "value": false}, "boilerMachinery_premium": null, "businessAuto": {"type": "boolean", "value": false}, "businessAuto_premium": null, "businessOwners": {"type": "boolean", "value": false}, "businessOwners_premium": null, "generalLiability": {"type": "boolean", "value": true}, "generalLiability_premium": null, "commercialInlandMarine": {"type": "boolean", "value": false}, "commercialInlandMarine_premium": null, "property": {"type": "boolean", "value": false}, "property_premium": null, "crime": {"type": "boolean", "value": false}, "crime_premium": null, "cyberPrivacy": {"type": "boolean", "value": false}, "cyberPrivacy_premium": null, "fiduciaryLiability": {"type": "boolean", "value": false}, "fiduciaryLiability_premium": null, "garageDealers": {"type": "boolean", "value": false}, "garageDealers_premium": null, "liquorLiability": {"type": "boolean", "value": false}, "liquorLiability_premium": null, "motorCarriers": {"type": "boolean", "value": false}, "motorCarriers_premium": null, "truckers": {"type": "boolean", "value": false}, "truckers_premium": null, "umbrella": {"type": "boolean", "value": false}, "umbrella_premium": null, "yacht": {"type": "boolean", "value": false}, "yacht_premium": null, "cyber_liability": null, "cyber_liability_premium": null, "custom_1_name": null, "custom_1": {"type": "boolean", "value": false}, "custom_1_premium": null, "custom_2_name": null, "custom_2": {"type": "boolean", "value": false}, "custom_2_premium": null, "custom_3_name": null, "custom_3": {"type": "boolean", "value": false}, "custom_3_premium": null, "custom_4_name": null, "custom_4": {"type": "boolean", "value": false}, "custom_4_premium": null, "custom_5_name": null, "custom_5": {"type": "boolean", "value": false}, "custom_5_premium": null, "custom_6": {"type": "boolean", "value": false}, "custom_6_premium": null}], "policy_information": [{"proposed_effective_date": {"source": "06/12/2023", "value": "2023-06-12T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "06/12/2024", "value": "2024-06-12T00:00:00.000Z", "type": "date"}, "billing_plan_direct": {"type": "boolean", "value": false}, "billing_plan_agency": {"type": "boolean", "value": false}, "payment_plan": null, "method_of_payment": null, "audit": {"type": "string", "value": "A"}, "deposit": null, "minimum_premium": null, "policy_premium": null}], "proposed_effective_date": {"source": "06/12/2023", "value": "2023-06-12T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "06/12/2024", "value": "2024-06-12T00:00:00.000Z", "type": "date"}, "primary_naics": {"type": "string", "value": "238140"}, "description_of_primary_operations": {"type": "string", "value": "<PERSON><PERSON><PERSON> Contractor"}, "applicant_information": [{"applicant_name_and_address": {"type": "string", "value": "Badger Builders, Inc. 1912 SW 45th Ln Cape Coral, FL 33914"}, "applicant_address": {"value": "1912 SW 45th Ln\nCape Coral, FL 33914", "type": "address"}, "applicant_info_gl_code_1": {"type": "string", "value": "98449"}, "applicant_sic": {"type": "string", "value": "1742"}, "applicant_naics": {"type": "string", "value": "238140"}, "applicant_fein": {"type": "string", "value": "65-0542430"}, "applicant_website_address": {"type": "string", "value": "www.stuccobadgerbuilders.com"}}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}], "premises_info": [{"location_number": {"source": "1", "value": 1, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "1912 SW 45th Lane"}, "city": {"type": "string", "value": "Cape Coral"}, "state": {"type": "string", "value": "FL"}, "county": {"value": "<PERSON>", "type": "string"}, "zip": {"type": "string", "value": "33914"}, "city_limits_inside": {"type": "boolean", "value": true}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": true}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": {"source": "2,500,000", "value": 2500000, "unit": "$", "type": "currency"}, "occupied_area": {"source": "900", "value": 900, "type": "number"}, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": {"type": "string", "value": "<PERSON><PERSON><PERSON> Contractor"}}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}], "nature_of_business": [{"apartments": {"type": "boolean", "value": false}, "contractor": {"type": "boolean", "value": true}, "manufacturing": {"type": "boolean", "value": false}, "restaurant": {"type": "boolean", "value": false}, "service": {"type": "boolean", "value": false}, "condominiums": {"type": "boolean", "value": false}, "institutional": {"type": "boolean", "value": false}, "office": {"type": "boolean", "value": false}, "retail": {"type": "boolean", "value": false}, "wholesale": {"type": "boolean", "value": false}, "custom": {"type": "boolean", "value": false}, "start_date": {"source": "12/19/1994", "value": "1994-12-19T00:00:00.000Z", "type": "date"}, "description_of_primary_operations": {"type": "string", "value": "<PERSON><PERSON><PERSON> Contractor"}, "retail_or_service_percentage_sales": null, "installation_service_repair_work_percentage": null, "off_premises_work": null, "operations_description_of_other_named_insureds": null}], "general_information": [{"is_applicant_subsidiary": {"value": "N", "type": "string"}, "subsidiary_parent_company_name": null, "subsidiary_percentage_owned": null, "does_applicant_have_subsidiaries": {"value": "N", "type": "string"}, "applicants_subsidiary_company_name": null, "applicants_subsidiary_percentage_owned": null, "is_formal_safety_plan_in_operation": {"value": true, "type": "boolean"}, "safety_program_manual": {"type": "boolean", "value": false}, "safety_plan_monthly_meetings": {"type": "boolean", "value": false}, "safety_plan_safety_position": {"type": "boolean", "value": false}, "safety_plan_osha": {"type": "boolean", "value": false}, "any_exposure_flammables": {"value": "N", "type": "string"}, "flammables_explanation": null, "any_policy_or_coverage_declined": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBS<PERSON>IARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED Y 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA N 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? N 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER N 5. OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-R<PERSON><PERSON>WAL UNDERWRITING CONDITION CORRECTED (Describe): N 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? N 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION WITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). N 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N 9. HAS APPLICANT HAD A FORECLOSURE, REPOSSESSION, BANKRUPTCY OR FILED FOR BANKRUPTCY DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N N 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: N 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) N 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR WHICH COVERAGE IS NOT REQUESTED? N 14. DOES APPLICANT OWN / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) N DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "coverage_declined_non_payment": {"type": "boolean", "value": false}, "coverage_declined_agent": {"type": "boolean", "value": false}, "coverage_declined_non_renewal": {"type": "boolean", "value": false}, "coverage_declined_underwriting": {"type": "boolean", "value": false}, "coverage_declined_condition_corrected": {"type": "boolean", "value": false}, "any_past_losses_relating_to_sexual_abuse": {"value": "N", "type": "string"}, "any_past_losses_relating_to_sexual_abuse_explanation": {"type": "string", "value": "7."}, "any_applicant_been_indicted": {"value": "N", "type": "string"}, "any_applicant_been_indicted_explanation": null, "any_uncorrected_fire_or_safety_violations": {"type": "string", "value": "N"}, "has_applicant_had_forclosure": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBSIDIARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED Y 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA N 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? N 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER N 5. ANY POLICY OR COVERAGE DECLINED, CANCELLED OR NON-R<PERSON>EWED DURING THE PRIOR THREE (3) YEARS FOR ANY PREMISES OR OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-RENEWAL UNDERWRITING CONDITION CORRECTED (Describe): N 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? N 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION WITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). N 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N 9. OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N N 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: N 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) N 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR WHICH COVERAGE IS NOT REQUESTED? N 14. DOES APPLICANT OWN / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) N DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "has_applicant_had_judgement_or_lien": {"type": "string", "value": "N"}, "is_business_placed_in_trust": {"type": "string", "value": "N"}, "name_of_trust": null, "any_foreign_operations": {"value": false, "type": "boolean"}, "does_applicant_have_other_business_ventures": {"value": "N", "type": "string"}, "does_applicant_have_other_business_ventures_explanation": null, "does_applicant_own_lease_operate_any_drones": {"value": "N", "type": "string"}, "does_applicant_own_lease_operate_any_drones_explanation": null, "does_applicant_hire_others_to_operate_drones": {"value": "N", "type": "string"}, "does_applicant_hire_others_to_operate_drones_explanation": null}]}, "configuration": "acord_125_2016_03", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "acord_125_2016_03", "score": {"score": 108, "fieldsPresent": 108, "penalties": 0}}], "validation_summary": {"fields": 16, "fields_present": 16, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 16, "fields_present": 16, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "51832c0a-0108-4b5c-9b09-3ff015ba66b5", "created": "2023-06-05T10:07:43.891Z", "completed": "2023-06-05T10:07:48.293Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#40264c0d-bc3d-444c-a01a-f6f0199ebca2#ce17b1d9-d972-4f95-81b2-1d28541b4a87#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 3, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "2b6e47ea-477a-43df-85c1-e204f16d7f7b", "created": "2023-06-05T10:07:20.552Z", "completed": "2023-06-05T10:07:35.120Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#1051b787-8758-459c-ad75-e1a19004e534#28b321fb-ff81-4157-8366-b15dfeddde49#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 18, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "41098ba5-28e4-4849-a96f-b2b5f04cd446", "created": "2023-06-05T10:07:20.483Z", "completed": "2023-06-05T10:07:29.876Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#1051b787-8758-459c-ad75-e1a19004e534#ead1a455-d933-4f8b-889e-c2760514397c#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "4ed47562-b1e8-4bec-a0a5-953f90a54b77", "created": "2023-06-05T10:07:19.462Z", "completed": "2023-06-05T10:07:30.291Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#1051b787-8758-459c-ad75-e1a19004e534#d615ad4e-d4ef-4586-82c0-c5c2c96fc1a0#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "a6c71baf-798e-4489-b307-fc580fb93447", "created": "2023-06-05T10:07:18.826Z", "completed": "2023-06-05T10:07:29.120Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#40264c0d-bc3d-444c-a01a-f6f0199ebca2#786190cd-427e-49e6-8b90-ffdf2e030d04#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "5b6cd596-2f5c-4bdc-8b3c-265897fd52f5", "created": "2023-06-05T10:07:15.743Z", "completed": "2023-06-05T10:07:20.052Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#1051b787-8758-459c-ad75-e1a19004e534#eaeaa7a7-14a9-41b3-bed9-f08e62c87a6b#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "26f62e3d-a6b7-4813-9f45-a8c19808189a", "created": "2023-06-05T10:07:13.463Z", "completed": "2023-06-05T10:07:24.448Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#1051b787-8758-459c-ad75-e1a19004e534#c0f24fd3-799f-4400-86e7-23f4903149c8#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "d069d823-5838-4a67-95c9-14a9e1cb09f1", "created": "2023-06-05T10:07:07.864Z", "completed": "2023-06-05T10:07:13.678Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#1051b787-8758-459c-ad75-e1a19004e534#ee27968b-f3d4-49af-aa6c-4facc624ee42#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "f6e68f2c-3cd5-434c-81cd-a28b1da29442", "created": "2023-06-05T09:56:59.527Z", "completed": "2023-06-05T09:57:15.492Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#8575a674-4769-49c0-981c-6d364178455b#3da60e86-c062-466c-9846-1cbb6ca01588#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "cc2f90a2-7b7b-42aa-9785-fd6e99b672ce", "created": "2023-06-05T09:46:51.663Z", "completed": "2023-06-05T09:47:08.595Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#0c0a4588-4b55-4de3-81b5-c49ec4d31232#d166fcb6-f044-47e8-84fe-3dc62705918c#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 5, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "a9ec1358-8fff-41ba-80f3-b891d39fea75", "created": "2023-06-05T09:33:21.799Z", "completed": "2023-06-05T09:33:26.583Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "3#b6fd12f8-6d4d-4622-9d3a-b4b12144f32e#2db18b99-fafa-4c51-85a3-4ff987231761#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.stage.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "qbe_4", "startPage": 0, "endPage": 0, "output": {"parsedDocument": {"report_generated_date": {"source": "3/31/2023", "value": "2023-03-31T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "615127N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "7/5/2018", "value": "2018-07-05T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/30/2018", "value": "2018-07-30T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "NOTICE OF OCCURRENCE.TENANT WAS"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "687.19", "value": 687.19, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "687.19", "value": 687.19, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "AVE ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "632239N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "8/11/2018", "value": "2018-08-11T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "9/21/2018", "value": "2018-09-21T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "INSURED RECEIVED A NOTICE FROM AT"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "75,000.00", "value": 75000, "type": "number"}, "total_amount_paid_expense": {"source": "17,876.82", "value": 17876.82, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "92,876.82", "value": 92876.82, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "Concourse ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "639116N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "9/12/2018", "value": "2018-09-12T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "10/15/2018", "value": "2018-10-15T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "Plaintiff alleges the insured failed to maintain"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "40,000.00", "value": 40000, "type": "number"}, "total_amount_paid_expense": {"source": "30,247.51", "value": 30247.51, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "70,247.51", "value": 70247.51, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "Ave ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "696851N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "10/7/2018", "value": "2018-10-07T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "6/7/2019", "value": "2019-06-07T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "<PERSON><PERSON><PERSON> alleges injury when the bathroom ce"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "95,000.00", "value": 95000, "type": "number"}, "total_amount_paid_expense": {"source": "16,588.68", "value": 16588.68, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "111,588.68", "value": 111588.68, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "Rd Apt J6 ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "651417N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "10/16/2018", "value": "2018-10-16T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "12/5/2018", "value": "2018-12-05T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "ceiling in bathroom collapsed and tenant inju"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "6,053.00", "value": 6053, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "6,053.00", "value": 6053, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "OCEAN AVENUE ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "653431N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "11/28/2018", "value": "2018-11-28T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "12/14/2018", "value": "2018-12-14T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "claimant fell in stairway at insured location"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "0.00", "value": 0, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "Chestnut St ,Pennsylvania", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "692525N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "12/16/2018", "value": "2018-12-16T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/22/2019", "value": "2019-05-22T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "SUMMONS FOR TAMMY HENRY AND CLA"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "25,000.00", "value": 25000, "type": "number"}, "total_amount_paid_expense": {"source": "7,956.44", "value": 7956.44, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "32,956.44", "value": 32956.44, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "BROADWAY #6C ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "680502N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "1/19/2019", "value": "2019-01-19T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "4/3/2019", "value": "2019-04-03T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "PLAINTIFF ALLEGEDLY SUSTAINED PERS"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "5,000.00", "value": 5000, "type": "number"}, "total_amount_paid_expense": {"source": "12,552.11", "value": 12552.11, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "17,552.11", "value": 17552.11, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "BOTANICAL SQUARE APT 5B ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "669208N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2/1/2019", "value": "2019-02-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2/20/2019", "value": "2019-02-20T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "19 year old resident of apartment building wa"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "335,000.00", "value": 335000, "type": "number"}, "total_amount_paid_expense": {"source": "17,740.24", "value": 17740.24, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "352,740.24", "value": 352740.24, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "AVE ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "676286N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2/17/2019", "value": "2019-02-17T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "3/20/2019", "value": "2019-03-20T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "PLAINTIFF ALLEGEDLY SUSTAINED PERS"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "390,000.00", "value": 390000, "type": "number"}, "total_amount_paid_expense": {"source": "24,886.28", "value": 24886.28, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "414,886.28", "value": 414886.28, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "AVE ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "700767N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "3/2/2019", "value": "2019-03-02T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "6/21/2019", "value": "2019-06-21T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "PERSO<PERSON>L INJURIES SUSTAINED ON INS"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "450,000.00", "value": 450000, "type": "number"}, "total_amount_paid_expense": {"source": "22,030.66", "value": 22030.66, "type": "number"}, "total_amount_recovered": {"source": "-505.20", "value": -505.2, "type": "number"}, "total_amount_incurred": {"source": "472,030.66", "value": 472030.66, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "UNDERCLIFF AVENUE ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "705839N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "3/28/2019", "value": "2019-03-28T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/10/2019", "value": "2019-07-10T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "CLAIMANT WAS AL<PERSON>GEDLY INJURED AT"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "107,000.00", "value": 107000, "type": "number"}, "total_amount_paid_expense": {"source": "29,008.45", "value": 29008.45, "type": "number"}, "total_amount_recovered": {"source": "-76.50", "value": -76.5, "type": "number"}, "total_amount_incurred": {"source": "136,008.45", "value": 136008.45, "type": "number"}, "claim_status": {"type": "string", "value": "Open"}, "loss_location": {"value": "GRAND CONCOURSE ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "698794N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "4/1/2019", "value": "2019-04-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "6/13/2019", "value": "2019-06-13T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "ATTORNEY LETTER FOR LUZ ARIAS.PLA"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "356.43", "value": 356.43, "type": "number"}, "total_amount_recovered": {"source": "-76.50", "value": -76.5, "type": "number"}, "total_amount_incurred": {"source": "356.43", "value": 356.43, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "48TH STREET ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "713749N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "4/1/2019", "value": "2019-04-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "8/14/2019", "value": "2019-08-14T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "ATTORNEY LETTER FOR EVELYN HAWE"}, "total_amount_reserved_loss": {"source": "850,800.00", "value": 850800, "type": "number"}, "total_reserved_reserved_expense": {"source": "11,073.66", "value": 11073.66, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "16,490.23", "value": 16490.23, "type": "number"}, "total_amount_recovered": {"source": "-76.50", "value": -76.5, "type": "number"}, "total_amount_incurred": {"source": "878,363.89", "value": 878363.89, "type": "number"}, "claim_status": {"type": "string", "value": "Reopen"}, "loss_location": {"value": "BLVD ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "711760N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "6/3/2019", "value": "2019-06-03T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "8/2/2019", "value": "2019-08-02T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "PLAINTIFF ALLEGEDLY SUSTAINED PERS"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "27,500.00", "value": 27500, "type": "number"}, "total_amount_paid_expense": {"source": "5,397.85", "value": 5397.85, "type": "number"}, "total_amount_recovered": {"source": "-76.50", "value": -76.5, "type": "number"}, "total_amount_incurred": {"source": "32,897.85", "value": 32897.85, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "ST ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "711078N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "6/12/2019", "value": "2019-06-12T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/31/2019", "value": "2019-07-31T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "AS JAZMIN WAS APPROACHING THE THI"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "0.00", "value": 0, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "15 CHESTNUT ST ,Pennsylvania", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "721030N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "7/6/2019", "value": "2019-07-06T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "9/26/2019", "value": "2019-09-26T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "ATTORNEY LETTER FOR MARIE A. CELE"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "20,000.00", "value": 20000, "type": "number"}, "total_amount_paid_expense": {"source": "923.09", "value": 923.09, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "20,923.09", "value": 20923.09, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "RD ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "733136N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "8/9/2019", "value": "2019-08-09T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "12/27/2019", "value": "2019-12-27T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "Summons for <PERSON>.Plaintiff allegedly"}, "total_amount_reserved_loss": {"source": "84,713.00", "value": 84713, "type": "number"}, "total_reserved_reserved_expense": {"source": "22,834.57", "value": 22834.57, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "19,875.37", "value": 19875.37, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "127,422.94", "value": 127422.94, "type": "number"}, "claim_status": {"type": "string", "value": "Open"}, "loss_location": {"value": "GRAND CONCOURSE ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "737047N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "9/1/2019", "value": "2019-09-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "1/29/2020", "value": "2020-01-29T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "<PERSON><PERSON><PERSON> allegedly sustained personal injuries"}, "total_amount_reserved_loss": {"source": "59,710.00", "value": 59710, "type": "number"}, "total_reserved_reserved_expense": {"source": "7,904.89", "value": 7904.89, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "7,478.47", "value": 7478.47, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "75,093.36", "value": 75093.36, "type": "number"}, "claim_status": {"type": "string", "value": "Open"}, "loss_location": {"value": "33-53 82nd St and 33-33 82nd St ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "723237N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "10/1/2019", "value": "2019-10-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "10/11/2019", "value": "2019-10-11T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "ATTORNEY LETTER FOR THERESA GOO"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "40,000.00", "value": 40000, "type": "number"}, "total_amount_paid_expense": {"source": "1,476.44", "value": 1476.44, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "41,476.44", "value": 41476.44, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "ROAD ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "726616N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "10/3/2019", "value": "2019-10-03T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "11/4/2019", "value": "2019-11-04T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "Attorney letter for <PERSON>. Plaintiff alle"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "7,500.00", "value": 7500, "type": "number"}, "total_amount_paid_expense": {"source": "287.48", "value": 287.48, "type": "number"}, "total_amount_recovered": {"source": "-76.50", "value": -76.5, "type": "number"}, "total_amount_incurred": {"source": "7,787.48", "value": 7787.48, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "OF 43-30 48TH ST ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "728464N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "10/16/2019", "value": "2019-10-16T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "11/19/2019", "value": "2019-11-19T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "<PERSON><PERSON><PERSON> alleges he slipped and fell on steps g"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "23,739.71", "value": 23739.71, "type": "number"}, "total_paid_loss": {"source": "150,000.00", "value": 150000, "type": "number"}, "total_amount_paid_expense": {"source": "46,002.66", "value": 46002.66, "type": "number"}, "total_amount_recovered": {"source": "-68.00", "value": -68, "type": "number"}, "total_amount_incurred": {"source": "219,742.37", "value": 219742.37, "type": "number"}, "claim_status": {"type": "string", "value": "Open"}, "loss_location": {"value": "Pl ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "730496N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "11/6/2019", "value": "2019-11-06T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "12/5/2019", "value": "2019-12-05T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "Attorney Letter from <PERSON>. Plaintiff a"}, "total_amount_reserved_loss": {"source": "42,750.00", "value": 42750, "type": "number"}, "total_reserved_reserved_expense": {"source": "8,624.66", "value": 8624.66, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "12,495.28", "value": 12495.28, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "63,869.94", "value": 63869.94, "type": "number"}, "claim_status": {"type": "string", "value": "Open"}, "loss_location": {"value": "Ridge Blvd ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "752355N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "1/1/2020", "value": "2020-01-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/21/2020", "value": "2020-05-21T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "<PERSON><PERSON><PERSON> allegedly sustained personal injuries"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "90,000.00", "value": 90000, "type": "number"}, "total_amount_paid_expense": {"source": "10,879.37", "value": 10879.37, "type": "number"}, "total_amount_recovered": {"source": "-76.50", "value": -76.5, "type": "number"}, "total_amount_incurred": {"source": "100,879.37", "value": 100879.37, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "Avenue ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "755417N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "3/27/2020", "value": "2020-03-27T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "6/11/2020", "value": "2020-06-11T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "<PERSON><PERSON><PERSON> tripped and fell on the sidewalk at the"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "90,000.00", "value": 90000, "type": "number"}, "total_amount_paid_expense": {"source": "8,617.98", "value": 8617.98, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "98,617.98", "value": 98617.98, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "St ,New York", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "named_insured": {"type": "string", "value": "Residential Management (NY) Inc."}, "line_of_business": {"type": "string", "value": "Liability"}, "claim_number": {"type": "string", "value": "756235N"}, "policy_number": {"value": "EASX104648", "type": "string"}, "policy_effective_date": {"source": "6/29/2018", "value": "2018-06-29T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "6/29/2020", "value": "2020-06-29T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "6/11/2020", "value": "2020-06-11T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "6/17/2020", "value": "2020-06-17T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "Notice of Occurrence for Marcia AvilesFire st"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_reserved_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "0.00", "value": 0, "type": "number"}, "claim_status": {"type": "string", "value": "Closed"}, "loss_location": {"value": "82ND ST, AKA 8122-8140 BROADWAY ,New York", "type": "string"}}]}, "configuration": "qbe_4", "validations": [], "fileMetadata": {"info": {"author": "<PERSON><PERSON><PERSON>", "title": "Loss Run Residential Management Policy EASX104648 as of 3.31.2023.xlsx", "creator": "PScript5.dll Version 5.2.2", "producer": "Acrobat Distiller 10.1.16 (Windows)", "creation_date": "2023-04-04T06:40:25.000-07:00", "modification_date": "2023-04-04T06:40:25.000-07:00"}, "metadata": {"dc:format": "application/pdf", "dc:title": "Loss Run Residential Management Policy EASX104648 as of 3.31.2023.xlsx", "dc:creator": ["<PERSON><PERSON><PERSON>"], "xmp:createdate": "2023-04-04T06:40:25-07:00", "xmp:creatortool": "PScript5.dll Version 5.2.2", "xmp:modifydate": "2023-04-04T06:40:25-07:00", "pdf:producer": "Acrobat Distiller 10.1.16 (Windows)", "xmpmm:documentid": "uuid:0f8586d4-ac0a-4779-a24e-85c2d0914a58", "xmpmm:instanceid": "uuid:118957cc-4045-4724-bbfc-534e3507da5d"}}, "errors": [], "classificationSummary": [{"configuration": "qbe_4", "score": {"score": 469, "fieldsPresent": 469, "penalties": 0}}], "validation_summary": {"fields": 27, "fields_present": 27, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 1, "validation_summary": {"fields": 27, "fields_present": 27, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "fb5355d9-0a75-4b40-bbb2-e888abf50c12", "created": "2023-06-05T09:23:29.001Z", "completed": "2023-06-05T09:23:51.922Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#c1d4eac6-3617-492d-a3c6-ce5f43211bd4#2d7e59eb-5067-456e-8b09-6e73d795977a#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "aig", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"all_lines_in_doc": {"type": "string", "value": "AIG IntelliRisk® AIG Loss Run Policy : 0062502624-041-000 TEXAS MILITARY INSTITUTE OF SA 0062502624-041-001 TEXAS MILITARY INSTITUTE OF SA Filters: Status = ALL Requester ID: 549173 Report Date / Time: 03/24/2023 13:34 EST Valuation Date: 02/28/2023 Source: US/CA The AIG Loss Run is a detail report, providing claim and financial information. Run additional reports using IntelliRisk at https://aig.com/ir. Certain claim information may not be available in this report, since data availability can vary based on the insurance program or benefit state (due to regulatory considerations). The content contained in this report is subject to privacy and security laws and should be handled in accordance with the applicable laws and regulations. This report is intended for review and use by authorized representatives of the insured or other parties authorized by the insured solely for legitimate business reasons. The information contained herein should be treated as privileged and confidential. If you are not the intended recipient, you are hereby notified that any disclosure, copy or distribution of this information is strictly prohibited, as is the taking of any action by you in reliance on its contents. If you received this communication in error, please notify us immediately. AIG I IntelliRisk Services PAGE: 1 <EMAIL> ************ AIG IntelliRisk Continued AIG Loss Run Policy : PAGE: 2 IntelliRisk® PAGE: 3 AIG Loss Run Property TEXAS MILITARY INSTITUTE OF SA 13:34 EST Policy : 09/01/2021 09/01/2022 Report Date / Time: 03/24/2023 0062502624-041-000 Valuation Date: 02/28/2023 Currency: USD Div/H.O. Claimant Name Major Class Code/Description Claim # Loss Description OneClaim # Status Adjusting Closed Dt Alloc Exp Total Total Loss Date Receipt Date Indemnity Claim Examiner Email Loss Location Paid Exp Paid Incurred Paid Reserves Recoveries 010 -FIRE (PROPERTY DAMAGE) TEXAS MILITARY INSTITUTE O 093/166 501-912971-001 2910211861US Fire on Roof Closed 01/02/2022 08/01/2022 01/05/2022 <EMAIL> 20955 West Tejas Trail, San Antonio, TX 78257 54,448.50 54,448.50 .00 .00 .00 .00 010 -FIRE (PROPERTY DAMAGE) TEXAS MILITARY INSTITUTE o 093/166 Closed Fire on Roof 501-912971-002 2910211861US 01/02/2022 04/26/2022 08/09/2022 20955 West Tejas Trail, San Antonio, TX 78257 <EMAIL> 6,510.00 2,643.13 9,153.13 .00 .00 .00 Claim Count = Pol-Asco-Mod: 0062502624-041-000 2,643.13 60,958.50 2 .00 .00 .00 63,601.63 **Certain claim information may not be available since data availability can vary based on the insurance program. PAGE: 4 AIG Loss Run Property Policy : 0062502624-041-001 TEXAS MILITARY INSTITUTE OF SA 09/01/2022 - 09/01/2023 Report Date / Time: 03/24/2023 13:34 EST Valuation Date: 02/28/2023 Currency: USD Claimant Name Div/H.O. Major Class Code/Description Claim # OneClaim # Status Loss Description Loss Date Receipt Date Closed Dt Indemnity Adjusting Alloc Exp Total Total Loss Location Claim Examiner Email Paid Exp Paid Paid Reserves Recoveries Incurred 093/ No Claims for Policy 0062502624-041-001 / Criteria .00 .00 .00 .00 .00 .00 Pol-Asco-Mod: 0062502624-041-001 Claim Count = 0 .00 .00 .00 .00 .00 .00 Totals For Policy: 0062502624 Claim Count = 2 60,958.50 2,643.13 .00 .00 .00 63,601.63 Property Report Totals: Claim Count = 2 60,958.50 2,643.13 .00 .00 .00 63,601.63 **Certain claim information may not be available since data availability can vary based on the insurance program."}, "report_generated_date": {"source": "03/24/2023", "value": "2023-03-24T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"value": "AIG", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_term": {"value": "09/01/2021 09/01/2022", "type": "string"}, "named_insured": {"value": "TEXAS MILITARY INSTITUTE OF SA", "type": "string"}, "claim_number": {"type": "string", "value": "501-912971-001"}, "date_of_loss": {"source": "01/02/2022", "value": "2022-01-02T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "01/05/2022", "value": "2022-01-05T00:00:00.000Z", "type": "date"}, "_claim_type": {"value": "<EMAIL>", "type": "string"}, "loss_location": null, "accident_state": null, "claim_status": {"value": "Closed", "type": "string"}, "loss_description": {"type": "string", "value": "010 -FIRE (PROPERTY DAMAGE) Fire on Roof"}, "total_amount_incurred": {"source": "54,448.50", "value": 54448.5, "unit": "$", "type": "currency"}, "total_paid_medical": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "54,448.50", "value": 54448.5, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "policy_effective_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": null}, {"carrier": {"value": "AIG", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_term": {"value": "09/01/2021 09/01/2022", "type": "string"}, "named_insured": {"value": "TEXAS MILITARY INSTITUTE OF SA", "type": "string"}, "claim_number": {"type": "string", "value": "501-912971-002"}, "date_of_loss": {"source": "01/02/2022", "value": "2022-01-02T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "04/26/2022", "value": "2022-04-26T00:00:00.000Z", "type": "date"}, "_claim_type": {"value": "<EMAIL>", "type": "string"}, "loss_location": null, "accident_state": null, "claim_status": {"value": "Closed", "type": "string"}, "loss_description": {"type": "string", "value": "010 -FIRE (PROPERTY DAMAGE) Fire on Roof"}, "total_amount_incurred": {"source": "9,153.13", "value": 9153.13, "unit": "$", "type": "currency"}, "total_paid_medical": {"source": "2,643.13", "value": 2643.13, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "6,510.00", "value": 6510, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "policy_effective_date": {"source": "09/01/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": null}, {"carrier": {"value": "AIG", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_term": {"value": "09/01/2022 - 09/01/2023", "type": "string"}, "named_insured": {"value": "TEXAS MILITARY INSTITUTE OF SA", "type": "string"}, "claim_number": null, "date_of_loss": null, "loss_reported_date": null, "_claim_type": null, "loss_location": null, "accident_state": null, "claim_status": null, "loss_description": null, "total_amount_incurred": null, "total_paid_medical": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "total_amount_recovered": null, "policy_effective_date": {"source": "09/01/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}}]}, "configuration": "aig", "validations": [], "fileMetadata": {"info": {"author": "", "title": "Loss Run", "creator": "Microsoft Reporting Services 20********", "producer": "Microsoft Reporting Services PDF Rendering Extension 20********", "creation_date": "2023-03-24T14:30:24.000-04:00", "modification_date": "2023-05-02T09:40:26.000-05:00"}, "metadata": {"dc:format": "application/pdf", "dc:creator": [""], "dc:description": "", "dc:title": "Loss Run", "xmp:createdate": "2023-03-24T14:30:24-04:00", "xmp:creatortool": "Microsoft Reporting Services 20********", "xmp:modifydate": "2023-05-02T09:40:26-05:00", "xmp:metadatadate": "2023-05-02T09:40:26-05:00", "pdf:producer": "Microsoft Reporting Services PDF Rendering Extension 20********", "xmpmm:documentid": "uuid:b9ba0133-932e-4b4b-b5eb-1fe1b067e29b", "xmpmm:instanceid": "uuid:87ea0e73-e207-4e01-9141-ea3783f000ab"}}, "errors": [], "classificationSummary": [{"configuration": "aig", "score": {"score": 43, "fieldsPresent": 43, "penalties": 0}}], "validation_summary": {"fields": 6, "fields_present": 6, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "rsui", "startPage": 4, "endPage": 11, "output": {"parsedDocument": {"report_generated_date": {"source": "3/24/2023", "value": "2023-03-24T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Texas Military Institute of San Antonio", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "claim_number": null, "total_amount_incurred": null, "total_amount_paid": null, "total_amount_paid_expense": null, "total_amount_reserved": null, "loss_reported_date": null, "date_of_loss": null, "loss_location": null, "loss_description": null, "claim_status": null}, {"carrier": {"value": "RSUI", "type": "string"}, "named_insured": {"value": "Texas Military Institute of San Antonio", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "policy_effective_date": {"source": "9/1/2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "9/1/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "claim_number": {"type": "string", "value": "7030165567"}, "total_amount_incurred": {"source": "$63,663.34", "value": 63663.34, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$63,663.34", "value": 63663.34, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$2,704.84", "value": 2704.84, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_reported_date": {"source": "1/5/2022", "value": "2022-01-05T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "1/2/2022", "value": "2022-01-02T00:00:00.000Z", "type": "date"}, "loss_location": {"value": "San Antonio, TX", "type": "string"}, "loss_description": {"type": "string", "value": "fire on roof policy numberLHT920052 0 False claim number 7030165567 ** Hidden Row ** Paid Reserve Incurred $63,663.34 $0.00 $63,663.34\nPolicy Gross Incurred Paid Reserve Incurred\nIndemnity $60,958.50 $0.00 $60,958.50 Expense $2,704.84 $0.00 $2,704.84 Total $63,663.34 $0.00 $63,663.34\nTotal Gross Incurred Paid Reserve Incurred\nIndemnity $60,958.50 $0.00 $60,958.50 Expense $2,704.84 $0.00 $2,704.84 Total $63,663.34 $0.00 $63,663.34\nPage 1 of 1 RSUI Group, Inc. CLMSRS001E-3; 3/24/2023 2:30 PM Risk Manager\nPolicy Loss Analysis By Policy For All Locations\nInsured: Texas Military Institute of San Antonio, Texas Valuation: 03/26/2023\nLoss Period: 09/01/2021 to 03/27/2023 Claim Status: Open and Closed\nLine(s) of Business: < All > Incidents: Include\nProducer: 4203044 SIC Code: 8211 000\nPolicy: 7950162880000 Policy Term: 09/01/2021 - 09/01/2022\nClaim/Suffix"}, "claim_status": {"value": "CLOSED", "type": "string"}}]}, "configuration": "rsui", "validations": [], "fileMetadata": {"info": {"author": "", "title": "Loss Run", "creator": "Microsoft Reporting Services 20********", "producer": "Microsoft Reporting Services PDF Rendering Extension 20********", "creation_date": "2023-03-24T14:30:24.000-04:00", "modification_date": "2023-05-02T09:40:26.000-05:00"}, "metadata": {"dc:format": "application/pdf", "dc:creator": [""], "dc:description": "", "dc:title": "Loss Run", "xmp:createdate": "2023-03-24T14:30:24-04:00", "xmp:creatortool": "Microsoft Reporting Services 20********", "xmp:modifydate": "2023-05-02T09:40:26-05:00", "xmp:metadatadate": "2023-05-02T09:40:26-05:00", "pdf:producer": "Microsoft Reporting Services PDF Rendering Extension 20********", "xmpmm:documentid": "uuid:b9ba0133-932e-4b4b-b5eb-1fe1b067e29b", "xmpmm:instanceid": "uuid:87ea0e73-e207-4e01-9141-ea3783f000ab"}}, "errors": [], "classificationSummary": [{"configuration": "rsui", "score": {"score": 21, "fieldsPresent": 21, "penalties": 0}}], "validation_summary": {"fields": 3, "fields_present": 3, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 12, "validation_summary": {"fields": 9, "fields_present": 9, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "20d9d7f0-7885-4c7b-aea4-5f035cb797c6", "created": "2023-06-05T09:19:51.499Z", "completed": "2023-06-05T09:20:33.138Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#73a8ca6a-2d07-46b7-8d60-b8b5eb961fe1#7fa91bb1-a60c-4b5e-a6ff-0b0c57b81e5f#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 36, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "33d4213a-188c-4181-b307-bcca34f8dcbc", "created": "2023-06-05T09:19:51.286Z", "completed": "2023-06-05T09:20:07.279Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#73a8ca6a-2d07-46b7-8d60-b8b5eb961fe1#b2e51824-dade-4bd5-a986-9c51e8916c39#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "70f22a0f-52a6-4d3b-a041-707bc3a42ac9", "created": "2023-06-05T09:19:50.747Z", "completed": "2023-06-05T09:19:55.780Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#73a8ca6a-2d07-46b7-8d60-b8b5eb961fe1#0df5dcb8-6478-49ae-8634-3ab0b4e46d48#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "b1a94364-d59f-45f8-9bbd-781ae6dae0bf", "created": "2023-06-05T09:19:50.551Z", "completed": "2023-06-05T09:20:01.382Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#73a8ca6a-2d07-46b7-8d60-b8b5eb961fe1#b20a2802-6241-4278-b296-f2cb69700aa3#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 2, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "548062b7-dd6b-4a47-a694-a6e8a1795fb9", "created": "2023-06-05T09:19:50.533Z", "completed": "2023-06-05T09:20:06.748Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#73a8ca6a-2d07-46b7-8d60-b8b5eb961fe1#192d56d7-04ba-42d3-ad26-1d8d1c752ce8#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "fa2fcc88-db98-4338-a2b2-b1cebbb14087", "created": "2023-06-05T09:19:45.533Z", "completed": "2023-06-05T09:19:50.507Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#73a8ca6a-2d07-46b7-8d60-b8b5eb961fe1#32fc20f2-9c2f-4986-be18-9bddd344ed00#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "0956ed08-c6dc-4bb9-abc9-7f013ed12bba", "created": "2023-06-05T09:17:45.152Z", "completed": "2023-06-05T09:17:50.310Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "3#b6fd12f8-6d4d-4622-9d3a-b4b12144f32e#f48158bb-0e61-41c4-8655-6ce9d05d97af#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.stage.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 1, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "e4e3fb20-5eb3-4b38-8b61-7878f037c78a", "created": "2023-06-05T09:10:37.785Z", "completed": "2023-06-05T09:10:44.452Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#e87d8906-063e-4c6f-827c-54d97746758c#40298cc1-8024-423f-b822-46dac09c2267#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "amrisc", "startPage": 0, "endPage": 0, "output": {"parsedDocument": {"report_generated_date": {"source": "03/03/2023", "value": "2023-03-03T00:00:00.000Z", "type": "date"}, "claims": [{"claim_number": {"type": "string", "value": "4201301"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "Silsbee ISD"}, "policy_effective_date": {"source": "09/01/2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "12/23/2022", "value": "2022-12-23T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "12/23/2022", "value": "2022-12-23T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_incurred": {"source": "8,000.00", "value": 8000, "type": "number"}, "total_amount_reserved_loss": {"source": "3,000.00", "value": 3000, "type": "number"}, "total_amount_reserved_expense": {"source": "5,000.00", "value": 5000, "type": "number"}, "loss_description": {"type": "string", "value": "Freeze"}, "claim_status": {"type": "string", "value": "OPEN"}}]}, "configuration": "amrisc", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-03-03T06:42:03.000-06:00"}}, "errors": [], "classificationSummary": [{"configuration": "amrisc", "score": {"score": 16, "fieldsPresent": 16, "penalties": 0}}, {"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 1, "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "17c46952-06f5-4368-8c69-918b5c6523d1", "created": "2023-06-05T08:40:52.100Z", "completed": "2023-06-05T08:40:57.973Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#75574287-3dbe-4893-b2af-562b8d943d6c#d94a1b55-8af0-46f0-b825-292b33063809#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "sompo_1", "startPage": 0, "endPage": 0, "output": {"parsedDocument": {"report_generated_date": {"source": "3/2/2023", "value": "2023-03-02T00:00:00.000Z", "type": "date"}, "claims": []}, "configuration": "sompo_1", "validations": [], "fileMetadata": {"info": {"author": "<PERSON><PERSON>, <PERSON>", "title": "Claims Loss Run", "producer": "PDF Engine winx64h - (11.2)", "creation_date": "2023-03-07T04:05:48.000-05:00", "modification_date": "2023-03-07T04:05:48.000-05:00"}, "metadata": {"dc:format": "application/pdf", "dc:title": "Claims Loss Run", "dc:creator": ["<PERSON><PERSON>, <PERSON>"], "pdf:producer": "PDF Engine winx64h - (11.2)", "xmp:createdate": "2023-03-07T04:05:48-05:00", "xmp:modifydate": "2023-03-07T04:05:48-05:00", "xmp:metadatadate": "2023-03-07T04:05:48-05:00"}}, "errors": [], "classificationSummary": [{"configuration": "sompo_1", "score": {"score": 1, "fieldsPresent": 1, "penalties": 0}}], "validation_summary": {"fields": 1, "fields_present": 1, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 1, "validation_summary": {"fields": 1, "fields_present": 1, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "976dd68b-a5e0-4b46-a1c0-db5b3108fa6c", "created": "2023-06-05T08:40:50.280Z", "completed": "2023-06-05T08:40:55.641Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#75574287-3dbe-4893-b2af-562b8d943d6c#8022ae9e-5225-48f5-9a56-ebda4806a88b#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 2, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "614f4e87-99b0-4615-b746-82130abffb2c", "created": "2023-06-05T08:40:24.880Z", "completed": "2023-06-05T08:40:43.654Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#75574287-3dbe-4893-b2af-562b8d943d6c#1d8843e5-36b3-4d3e-98a9-cc20d96c2e01#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "axis_1", "startPage": 0, "endPage": 12, "output": {"parsedDocument": {"report_generated_date": {"source": "5/2/2023", "value": "2023-05-02T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2015", "value": "2015-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2016", "value": "2016-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2015 - 7/15/2016"}, "claim_number": {"type": "string", "value": "ATL121454"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building CAT 1617 - Wind damage"}, "date_of_loss": {"source": "3/5/2016", "value": "2016-03-05T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "3/18/2016", "value": "2016-03-18T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "CALIFORNIA"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$9,043.15", "value": 9043.15, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$89,689.35", "value": 89689.35, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$98,732.50", "value": 98732.5, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2015", "value": "2015-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2016", "value": "2016-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2015 - 7/15/2016"}, "claim_number": {"type": "string", "value": "ATL122621"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building CAT1625 - Hail damage"}, "date_of_loss": {"source": "4/15/2016", "value": "2016-04-15T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "4/18/2016", "value": "2016-04-18T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "TEXAS"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$18,809.08", "value": 18809.08, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$587,268.93", "value": 587268.93, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$606,078.01", "value": 606078.01, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2015", "value": "2015-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2016", "value": "2016-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2015 - 7/15/2016"}, "claim_number": {"type": "string", "value": "ATL125957"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building Damage due to a microburst storm 5/2/2023"}, "date_of_loss": {"source": "7/1/2016", "value": "2016-07-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/5/2016", "value": "2016-07-05T00:00:00.000Z", "type": "date"}, "accident_state": null, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$10,253.43", "value": 10253.43, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$216,377.23", "value": 216377.23, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$226,630.66", "value": 226630.66, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2016", "value": "2016-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2017", "value": "2017-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2016 - 7/15/2017"}, "claim_number": {"type": "string", "value": "ATL137921"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building There's smoke damage throughout the complex due to a fire. 5/2/2023"}, "date_of_loss": {"source": "5/1/2017", "value": "2017-05-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/2/2017", "value": "2017-05-02T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "ARIZONA"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$2,765.44", "value": 2765.44, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$37,375.41", "value": 37375.41, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$40,140.85", "value": 40140.85, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2017", "value": "2017-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2017 - 7/15/2018"}, "claim_number": {"type": "string", "value": "ATL147332"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building The insured reported that a fire occured at the Bidwell Canyon Marina location."}, "date_of_loss": {"source": "12/16/2017", "value": "2017-12-16T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "12/18/2017", "value": "2017-12-18T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "CALIFORNIA"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$3,030.89", "value": 3030.89, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$19,363.68", "value": 19363.68, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$22,394.57", "value": 22394.57, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2017", "value": "2017-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2017 - 7/15/2018"}, "claim_number": {"type": "string", "value": "ATL152995"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building The insured reported a customers boat caught on fire and sank, causing damage to 2 other boats and minor damage to the dock and roof. 5/2/2023"}, "date_of_loss": {"source": "5/15/2018", "value": "2018-05-15T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/18/2018", "value": "2018-05-18T00:00:00.000Z", "type": "date"}, "accident_state": null, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$6,948.31", "value": 6948.31, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$12,885.67", "value": 12885.67, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$19,736.17", "value": 19736.17, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2018 - 7/15/2020"}, "claim_number": {"type": "string", "value": "ATL155850"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building The insured reported wind damage to docks."}, "date_of_loss": {"source": "7/20/2018", "value": "2018-07-20T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/31/2018", "value": "2018-07-31T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "NEVADA"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$13,793.84", "value": 13793.84, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$203,312.09", "value": 203312.09, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$217,105.93", "value": 217105.93, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2018 - 7/15/2020"}, "claim_number": {"type": "string", "value": "ATL162855"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building CAT 1912 - Insured reported wind loss"}, "date_of_loss": {"source": "1/17/2019", "value": "2019-01-17T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "1/30/2019", "value": "2019-01-30T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "CALIFORNIA"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$1,289.57", "value": 1289.57, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$1,289.57", "value": 1289.57, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2018 - 7/15/2020"}, "claim_number": {"type": "string", "value": "ATL166293"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building Insured reported heavy snowfall on roof, partially collapsing a building"}, "date_of_loss": {"source": "4/24/2019", "value": "2019-04-24T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "4/24/2019", "value": "2019-04-24T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "WYOMING"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$3,794.30", "value": 3794.3, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$5,625.00", "value": 5625, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$9,419.30", "value": 9419.3, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2018 - 7/15/2020"}, "claim_number": {"type": "string", "value": "ATL167529"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building Insured reported fire loss"}, "date_of_loss": {"source": "5/21/2019", "value": "2019-05-21T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/23/2019", "value": "2019-05-23T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "TEXAS"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$10,185.61", "value": 10185.61, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$134,259.00", "value": 134259, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$144,444.61", "value": 144444.61, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2018 - 7/15/2020"}, "claim_number": {"type": "string", "value": "ATL168186"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building CAT 1934 - Insured reported wind damage to roof and water damage to office area 5/2/2023"}, "date_of_loss": {"source": "6/9/2019", "value": "2019-06-09T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "6/10/2019", "value": "2019-06-10T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "TEXAS"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$3,074.46", "value": 3074.46, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$41,690.86", "value": 41690.86, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$44,765.32", "value": 44765.32, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2018", "value": "2018-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2018 - 7/15/2020"}, "claim_number": {"type": "string", "value": "ATL182232"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building There was a water pipe break at Big Bend, TX. 5/2/2023"}, "date_of_loss": {"source": "6/18/2020", "value": "2020-06-18T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "6/24/2020", "value": "2020-06-24T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "ARIZONA"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$1,629.02", "value": 1629.02, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$9,015.00", "value": 9015, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$10,644.02", "value": 10644.02, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2021", "value": "2021-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2020 - 7/15/2021"}, "claim_number": {"type": "string", "value": "ATL188879"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building 38.5% CAT 2117 - The AVA plant in Dallas Texas suffered damage as a result of the freezing situation a short while ago down in Texas."}, "date_of_loss": {"source": "2/17/2021", "value": "2021-02-17T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "3/15/2021", "value": "2021-03-15T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "TEXAS"}, "claim_status": {"type": "string", "value": "OPEN"}, "total_amount_paid": {"source": "$3,038.98", "value": 3038.98, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$22,469.49", "value": 22469.49, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$25,508.47", "value": 25508.47, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2020", "value": "2020-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2021", "value": "2021-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2020 - 7/15/2021"}, "claim_number": {"type": "string", "value": "ATL190616"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building 38.5% CAT 2133 - Damage to roof. 5/2/2023"}, "date_of_loss": {"source": "5/26/2021", "value": "2021-05-26T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/27/2021", "value": "2021-05-27T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "TEXAS"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$5,435.44", "value": 5435.44, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$27,411.01", "value": 27411.01, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$32,846.45", "value": 32846.45, "unit": "$", "type": "currency"}}, {"carrier": {"type": "string", "value": "AXIS"}, "named_insured": {"value": "GREGG MAUGHAN REVOCABLE TRUST; INOS REVOCABLE", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "policy_effective_date": {"source": "7/15/2021", "value": "2021-07-15T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/15/2022", "value": "2022-07-15T00:00:00.000Z", "type": "date"}, "policy_term": {"type": "string", "value": "7/15/2021 - 7/15/2022"}, "claim_number": {"type": "string", "value": "ATL192090"}, "loss_location": null, "loss_description": {"type": "string", "value": "Building There is damage to the roof. They did not expect this to be that serious but the initial estimate is coming in at 75,000. Further, they are now discov 5/2/2023"}, "date_of_loss": {"source": "7/23/2021", "value": "2021-07-23T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "8/4/2021", "value": "2021-08-04T00:00:00.000Z", "type": "date"}, "accident_state": {"type": "string", "value": "ARIZONA"}, "claim_status": {"type": "string", "value": "CLOSED"}, "total_amount_paid": {"source": "$2,919.00", "value": 2919, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$3,238.10", "value": 3238.1, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "total_amount_incurred": null, "total_amount_paid_expense": {"source": "$6,157.10", "value": 6157.1, "unit": "$", "type": "currency"}}]}, "configuration": "axis_1", "validations": [], "fileMetadata": {"info": {"author": "", "title": "AxisLossRunReserve_New_PDF", "creator": "Microsoft Reporting Services 2019.11.0.0", "producer": "Microsoft Reporting Services PDF Rendering Extension 2019.11.0.0", "creation_date": "2023-05-02T15:46:32.000-04:00", "modification_date": "2023-05-02T13:22:04.000-07:00"}, "metadata": {"dc:format": "application/pdf", "dc:creator": [""], "dc:description": "", "dc:title": "AxisLossRunReserve_New_PDF", "xmp:createdate": "2023-05-02T15:46:32-04:00", "xmp:creatortool": "Microsoft Reporting Services 2019.11.0.0", "xmp:modifydate": "2023-05-02T13:22:04-07:00", "xmp:metadatadate": "2023-05-02T13:22:04-07:00", "pdf:producer": "Microsoft Reporting Services PDF Rendering Extension 2019.11.0.0", "xmpmm:documentid": "uuid:8cd1ca84-b54c-4d36-a76b-e99eb39f5cca", "xmpmm:instanceid": "uuid:66bcc42c-8085-43a0-b293-e60ec4600a37"}}, "errors": [], "classificationSummary": [{"configuration": "axis_1", "score": {"score": 224, "fieldsPresent": 224, "penalties": 0}}], "validation_summary": {"fields": 17, "fields_present": 16, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 13, "validation_summary": {"fields": 17, "fields_present": 16, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "4c0aef8a-194d-46f8-8fcf-4ee656969ce8", "created": "2023-06-05T08:40:20.762Z", "completed": "2023-06-05T08:40:47.938Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "36#75574287-3dbe-4893-b2af-562b8d943d6c#5f4066b1-f910-4fa4-8227-0f16828f3034#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [{"documentType": "acord_forms", "configuration": "acord_125_2016_03", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"agency_information": [{"date": null, "agency": {"type": "string", "value": "LeBaron & Carroll LLC 1350 E Southern Avenue Mesa, AZ 85204"}, "contact_name": {"type": "string", "value": "<PERSON>"}, "contact_phone": {"type": "string", "value": "(*************"}, "contact_fax": {"type": "string", "value": "(*************"}, "contact_email": {"type": "string", "value": "<EMAIL>"}, "underwriter": null, "transaction_status_renew": {"type": "boolean", "value": false}}], "lines_of_business": [{"boilerMachinery": {"type": "boolean", "value": false}, "boilerMachinery_premium": null, "businessAuto": {"type": "boolean", "value": true}, "businessAuto_premium": null, "businessOwners": {"type": "boolean", "value": false}, "businessOwners_premium": null, "generalLiability": {"type": "boolean", "value": true}, "generalLiability_premium": null, "commercialInlandMarine": {"type": "boolean", "value": false}, "commercialInlandMarine_premium": null, "property": {"type": "boolean", "value": false}, "property_premium": null, "crime": {"type": "boolean", "value": false}, "crime_premium": null, "cyberPrivacy": {"type": "boolean", "value": false}, "cyberPrivacy_premium": null, "fiduciaryLiability": {"type": "boolean", "value": false}, "fiduciaryLiability_premium": null, "garageDealers": {"type": "boolean", "value": false}, "garageDealers_premium": null, "liquorLiability": {"type": "boolean", "value": false}, "liquorLiability_premium": null, "motorCarriers": {"type": "boolean", "value": false}, "motorCarriers_premium": null, "truckers": {"type": "boolean", "value": false}, "truckers_premium": null, "umbrella": {"type": "boolean", "value": true}, "umbrella_premium": null, "yacht": {"type": "boolean", "value": false}, "yacht_premium": null, "cyber_liability": null, "cyber_liability_premium": null, "custom_1_name": null, "custom_1": {"type": "boolean", "value": false}, "custom_1_premium": null, "custom_2_name": {"type": "string", "value": "$$"}, "custom_2": {"type": "boolean", "value": false}, "custom_2_premium": null, "custom_3_name": {"type": "string", "value": "$$"}, "custom_3": {"type": "boolean", "value": false}, "custom_3_premium": null, "custom_4_name": {"type": "string", "value": "$$"}, "custom_4": {"type": "boolean", "value": false}, "custom_4_premium": null, "custom_5_name": {"type": "string", "value": "$$"}, "custom_5": {"type": "boolean", "value": false}, "custom_5_premium": null, "custom_6": {"type": "boolean", "value": false}, "custom_6_premium": null}], "policy_information": [{"proposed_effective_date": {"source": "07/01/2023", "value": "2023-07-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "07/01/2024", "value": "2024-07-01T00:00:00.000Z", "type": "date"}, "billing_plan_direct": {"type": "boolean", "value": false}, "billing_plan_agency": {"type": "boolean", "value": false}, "payment_plan": null, "method_of_payment": null, "audit": null, "deposit": null, "minimum_premium": null, "policy_premium": null}], "proposed_effective_date": {"source": "07/01/2023", "value": "2023-07-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "07/01/2024", "value": "2024-07-01T00:00:00.000Z", "type": "date"}, "primary_naics": {"type": "string", "value": "325412"}, "description_of_primary_operations": null, "applicant_information": [{"applicant_name_and_address": {"type": "string", "value": "Forever Living Products International LLC 7501 E McCormick Parkway #100 Scottsdale, AZ 85258"}, "applicant_address": {"value": "7501 E McCormick Parkway #100\nScottsdale, AZ 85258", "type": "address"}, "applicant_info_gl_code_1": null, "applicant_sic": {"type": "string", "value": "2834"}, "applicant_naics": {"type": "string", "value": "325412"}, "applicant_fein": {"type": "string", "value": "27-4216104"}, "applicant_website_address": null}, {"applicant_name_and_address": {"type": "string", "value": "<PERSON><PERSON>an Revocable Trust"}, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}], "premises_info": [{"location_number": {"source": "1", "value": 1, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "7501 E McCormick Parkway #100"}, "city": {"type": "string", "value": "<PERSON><PERSON>"}, "state": {"type": "string", "value": "AZ"}, "county": null, "zip": {"type": "string", "value": "85258"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": {"source": "2", "value": 2, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "7501 <PERSON>"}, "city": {"type": "string", "value": "<PERSON><PERSON>"}, "state": {"type": "string", "value": "AZ"}, "county": null, "zip": {"type": "string", "value": "85258"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": {"source": "3", "value": 3, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "13745 Jupiter Road"}, "city": {"type": "string", "value": "Dallas"}, "state": {"type": "string", "value": "TX"}, "county": null, "zip": {"type": "string", "value": "75238"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": {"source": "4", "value": 4, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "5401 N Inspiration Road"}, "city": {"type": "string", "value": "Mission"}, "state": {"type": "string", "value": "TX"}, "county": null, "zip": {"type": "string", "value": "78573"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}], "nature_of_business": [{"apartments": {"type": "boolean", "value": false}, "contractor": {"type": "boolean", "value": false}, "manufacturing": {"type": "boolean", "value": false}, "restaurant": {"type": "boolean", "value": false}, "service": {"type": "boolean", "value": false}, "condominiums": {"type": "boolean", "value": false}, "institutional": {"type": "boolean", "value": false}, "office": {"type": "boolean", "value": false}, "retail": {"type": "boolean", "value": false}, "wholesale": {"type": "boolean", "value": false}, "custom": {"type": "boolean", "value": false}, "start_date": null, "description_of_primary_operations": null, "retail_or_service_percentage_sales": null, "installation_service_repair_work_percentage": null, "off_premises_work": null, "operations_description_of_other_named_insureds": null}], "general_information": [{"is_applicant_subsidiary": {"value": "N", "type": "string"}, "subsidiary_parent_company_name": null, "subsidiary_percentage_owned": null, "does_applicant_have_subsidiaries": {"value": "N", "type": "string"}, "applicants_subsidiary_company_name": null, "applicants_subsidiary_percentage_owned": null, "is_formal_safety_plan_in_operation": {"value": true, "type": "boolean"}, "safety_program_manual": {"type": "boolean", "value": false}, "safety_plan_monthly_meetings": {"type": "boolean", "value": false}, "safety_plan_safety_position": {"type": "boolean", "value": false}, "safety_plan_osha": {"type": "boolean", "value": false}, "any_exposure_flammables": {"value": "N", "type": "string"}, "flammables_explanation": null, "any_policy_or_coverage_declined": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBSIDIARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED Y 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA N 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? N 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER N OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-R<PERSON><PERSON>WAL UNDERWRITING CONDITION CORRECTED (Describe): N 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? N 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION WITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). N 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N 9. HAS APPLICANT HAD A FORECLOSURE, REPOSSESSION, BANKRUPTCY OR FILED FOR BANKRUPTCY DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N N 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: N 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) N 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR WHICH COVERAGE IS NOT REQUESTED? N 14. DOES APPLICANT OWN / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) N DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "coverage_declined_non_payment": {"type": "boolean", "value": false}, "coverage_declined_agent": {"type": "boolean", "value": false}, "coverage_declined_non_renewal": {"type": "boolean", "value": false}, "coverage_declined_underwriting": {"type": "boolean", "value": false}, "coverage_declined_condition_corrected": {"type": "boolean", "value": false}, "any_past_losses_relating_to_sexual_abuse": {"value": "N", "type": "string"}, "any_past_losses_relating_to_sexual_abuse_explanation": null, "any_applicant_been_indicted": {"value": "N", "type": "string"}, "any_applicant_been_indicted_explanation": null, "any_uncorrected_fire_or_safety_violations": {"type": "string", "value": "N"}, "has_applicant_had_forclosure": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBSIDIARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED Y 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA N 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? N 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER N 5. ANY POLICY OR COVERAGE DECLINED, CANCELLED OR NON-R<PERSON>EWED DURING THE PRIOR THREE (3) YEARS FOR ANY PREMISES OR OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-RENEWAL UNDERWRITING CONDITION CORRECTED (Describe): N 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? N 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION WITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). N 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N N 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: N 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) N 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR WHICH COVERAGE IS NOT REQUESTED? N 14. DOES APPLICANT OWN / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) N DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "has_applicant_had_judgement_or_lien": {"type": "string", "value": "N"}, "is_business_placed_in_trust": {"type": "string", "value": "N"}, "name_of_trust": null, "any_foreign_operations": {"value": false, "type": "boolean"}, "does_applicant_have_other_business_ventures": {"value": "N", "type": "string"}, "does_applicant_have_other_business_ventures_explanation": null, "does_applicant_own_lease_operate_any_drones": {"value": "N", "type": "string"}, "does_applicant_own_lease_operate_any_drones_explanation": null, "does_applicant_hire_others_to_operate_drones": {"value": "N", "type": "string"}, "does_applicant_hire_others_to_operate_drones_explanation": null}]}, "configuration": "acord_125_2016_03", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "acord_125_2016_03", "score": {"score": 119, "fieldsPresent": 119, "penalties": 0}}], "validation_summary": {"fields": 16, "fields_present": 15, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 16, "fields_present": 15, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "ebd89089-0d5b-4f03-b300-c10e4f862940", "created": "2023-06-05T08:31:19.117Z", "completed": "2023-06-05T08:31:24.066Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#f02dde98-6551-4436-a8b1-a7a58b4810e8#667dc4d6-3b84-44b6-b956-572a5da640b3#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "aspen", "startPage": 0, "endPage": 2, "output": {"parsedDocument": {"report_generated_date": {"source": "4/26/2023", "value": "2023-04-26T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"value": "Aspen", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"type": "string", "value": "IM2070098772"}, "date_of_loss": {"source": "7/25/2020", "value": "2020-07-25T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/25/2020", "value": "2020-07-25T00:00:00.000Z", "type": "date"}, "policy_effective_date": {"source": "7/1/2020", "value": "2020-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/1/2021", "value": "2021-07-01T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "$43,294.23", "value": 43294.23, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$38,250.81", "value": 38250.81, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$5,043.42", "value": 5043.42, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "Roof and building damage from Hurricane Hanna"}, "loss_location": null, "claim_status": {"value": "CLOSED", "type": "string"}, "named_insured": {"value": "SPRINT WASTE SERVICES LP", "type": "string"}}, {"carrier": {"value": "Aspen", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"type": "string", "value": "IM2170160608"}, "date_of_loss": {"source": "5/21/2022", "value": "2022-05-21T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "5/21/2022", "value": "2022-05-21T00:00:00.000Z", "type": "date"}, "policy_effective_date": {"source": "7/1/2021", "value": "2021-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/1/2022", "value": "2022-07-01T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "$49,462.14", "value": 49462.14, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$48,587.14", "value": 48587.14, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$875.00", "value": 875, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "The Caterpillar 836G trash compactor at the Sprint Montgomery County Landfill had a small electrical fire start in the engine compartment while the machine was in operation. Smoke was noticed by the two equipment operators that were present in the landfill, and they were able to extinguish the fire quickly using handheld fire extinguishers that were mounted on the landfill equipment. Most of the damage was caused by melting of plastic and rubber parts in the engine compartment area, some of which also extended to under the cab area. Many of the damaged components were wiring harnesses. Only as wiring components were replaced and further diagnostics could be performed, could the full extent of the damage be identified."}, "loss_location": null, "claim_status": {"value": "CLOSED", "type": "string"}, "named_insured": {"value": "SPRINT WASTE SERVICES LP", "type": "string"}}, {"carrier": {"value": "Aspen", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": null, "date_of_loss": null, "loss_reported_date": null, "policy_effective_date": {"source": "7/1/2022", "value": "2022-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "7/1/2023", "value": "2023-07-01T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "No claims exist on this policy"}, "loss_location": null, "claim_status": {"value": "Exp Date:", "type": "string"}, "named_insured": {"value": "Sprint Environmental Services, LLC", "type": "string"}}]}, "configuration": "aspen", "validations": [], "fileMetadata": {"info": {"author": "", "title": "Loss Run", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-04-27T17:45:52.000+01:00"}}, "errors": [], "classificationSummary": [{"configuration": "aspen", "score": {"score": 43, "fieldsPresent": 43, "penalties": 0}}], "validation_summary": {"fields": 4, "fields_present": 4, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 3, "validation_summary": {"fields": 4, "fields_present": 4, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "4702e3ec-bcb8-4d5a-b1e4-43480ebe2dd3", "created": "2023-06-05T08:29:20.817Z", "completed": "2023-06-05T08:29:36.314Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "36#31282491-7615-4752-a51b-0809cbd1550b#14765e87-f9de-451c-875d-909b98d4978d#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [{"documentType": "acord_forms", "configuration": "acord_125_2016_03", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"agency_information": [{"date": null, "agency": {"type": "string", "value": "Carlisle Insurance 500 N Water #900 Corpus Christi, TX 78401"}, "contact_name": {"type": "string", "value": "<PERSON>, CIC, CMIP, CPCU"}, "contact_phone": {"type": "string", "value": "(*************"}, "contact_fax": {"type": "string", "value": "(*************"}, "contact_email": null, "underwriter": null, "transaction_status_renew": {"type": "boolean", "value": false}}], "lines_of_business": [{"boilerMachinery": {"type": "boolean", "value": false}, "boilerMachinery_premium": null, "businessAuto": {"type": "boolean", "value": false}, "businessAuto_premium": null, "businessOwners": null, "businessOwners_premium": null, "generalLiability": {"type": "boolean", "value": false}, "generalLiability_premium": null, "commercialInlandMarine": {"type": "boolean", "value": false}, "commercialInlandMarine_premium": null, "property": {"type": "boolean", "value": true}, "property_premium": null, "crime": {"type": "boolean", "value": false}, "crime_premium": null, "cyberPrivacy": {"type": "boolean", "value": false}, "cyberPrivacy_premium": null, "fiduciaryLiability": {"type": "boolean", "value": false}, "fiduciaryLiability_premium": null, "garageDealers": {"type": "boolean", "value": false}, "garageDealers_premium": null, "liquorLiability": {"type": "boolean", "value": false}, "liquorLiability_premium": null, "motorCarriers": {"type": "boolean", "value": false}, "motorCarriers_premium": null, "truckers": {"type": "boolean", "value": false}, "truckers_premium": null, "umbrella": {"type": "boolean", "value": false}, "umbrella_premium": null, "yacht": {"type": "boolean", "value": false}, "yacht_premium": null, "cyber_liability": null, "cyber_liability_premium": null, "custom_1_name": null, "custom_1": {"type": "boolean", "value": false}, "custom_1_premium": null, "custom_2_name": null, "custom_2": {"type": "boolean", "value": false}, "custom_2_premium": null, "custom_3_name": null, "custom_3": {"type": "boolean", "value": false}, "custom_3_premium": null, "custom_4_name": null, "custom_4": {"type": "boolean", "value": false}, "custom_4_premium": null, "custom_5_name": null, "custom_5": {"type": "boolean", "value": false}, "custom_5_premium": null, "custom_6": {"type": "boolean", "value": false}, "custom_6_premium": null}], "policy_information": [{"proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "billing_plan_direct": {"type": "boolean", "value": false}, "billing_plan_agency": {"type": "boolean", "value": true}, "payment_plan": null, "method_of_payment": null, "audit": null, "deposit": null, "minimum_premium": null, "policy_premium": null}], "proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "primary_naics": null, "description_of_primary_operations": null, "applicant_information": [{"applicant_name_and_address": {"type": "string", "value": "Valley View ISD 9701 S Jackson Rd Pharr, TX 78577"}, "applicant_address": {"value": "9701 S Jackson Rd\nPharr, TX 78577", "type": "address"}, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}], "premises_info": [{"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}], "nature_of_business": [{"apartments": {"type": "boolean", "value": false}, "contractor": {"type": "boolean", "value": false}, "manufacturing": {"type": "boolean", "value": false}, "restaurant": {"type": "boolean", "value": false}, "service": {"type": "boolean", "value": false}, "condominiums": {"type": "boolean", "value": false}, "institutional": {"type": "boolean", "value": false}, "office": {"type": "boolean", "value": false}, "retail": {"type": "boolean", "value": false}, "wholesale": null, "custom": {"type": "boolean", "value": false}, "start_date": null, "description_of_primary_operations": null, "retail_or_service_percentage_sales": null, "installation_service_repair_work_percentage": null, "off_premises_work": null, "operations_description_of_other_named_insureds": null}], "general_information": [{"is_applicant_subsidiary": null, "subsidiary_parent_company_name": null, "subsidiary_percentage_owned": null, "does_applicant_have_subsidiaries": null, "applicants_subsidiary_company_name": null, "applicants_subsidiary_percentage_owned": null, "is_formal_safety_plan_in_operation": {"value": "Y", "type": "string"}, "safety_program_manual": {"type": "boolean", "value": false}, "safety_plan_monthly_meetings": {"type": "boolean", "value": false}, "safety_plan_safety_position": {"type": "boolean", "value": false}, "safety_plan_osha": {"type": "boolean", "value": false}, "any_exposure_flammables": null, "flammables_explanation": null, "any_policy_or_coverage_declined": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBS<PERSON>IARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-R<PERSON><PERSON><PERSON><PERSON> UNDERWRITING CONDITION CORRECTED (Describe): 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION W ITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 9. HAS APPLICANT HAD A FORECLOSURE, REPOSSESSION, BANKRUPTCY OR FILED FOR BANKRUPTCY DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR W HICH COVERAGE IS NOT REQUESTED? 14. DOES APPLICANT OW N / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "coverage_declined_non_payment": {"type": "boolean", "value": false}, "coverage_declined_agent": {"type": "boolean", "value": false}, "coverage_declined_non_renewal": {"type": "boolean", "value": false}, "coverage_declined_underwriting": {"type": "boolean", "value": false}, "coverage_declined_condition_corrected": {"type": "boolean", "value": false}, "any_past_losses_relating_to_sexual_abuse": null, "any_past_losses_relating_to_sexual_abuse_explanation": null, "any_applicant_been_indicted": null, "any_applicant_been_indicted_explanation": null, "any_uncorrected_fire_or_safety_violations": null, "has_applicant_had_forclosure": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBSIDIARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER 5. ANY POLICY OR COVERAGE DECLINED, CANCELLED OR NON-R<PERSON><PERSON><PERSON> ED DURING THE PRIOR THREE (3) YEARS FOR ANY PREMISES OR OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-RENEWAL UNDERWRITING CONDITION CORRECTED (Describe): 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION W ITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR W HICH COVERAGE IS NOT REQUESTED? 14. DOES APPLICANT OW N / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "has_applicant_had_judgement_or_lien": null, "is_business_placed_in_trust": null, "name_of_trust": null, "any_foreign_operations": null, "does_applicant_have_other_business_ventures": null, "does_applicant_have_other_business_ventures_explanation": null, "does_applicant_own_lease_operate_any_drones": null, "does_applicant_own_lease_operate_any_drones_explanation": null, "does_applicant_hire_others_to_operate_drones": null, "does_applicant_hire_others_to_operate_drones_explanation": null}]}, "configuration": "acord_125_2016_03", "validations": [], "fileMetadata": {"info": {"producer": "PDF-XChange Standard (9.1 build 356) [GDI] [Windows 10 Enterprise x64 (Build 19043)]", "creation_date": "2023-03-27T13:19:43.000-05:00", "modification_date": "2023-03-27T13:20:20.000-05:00"}, "metadata": {"dc:format": "application/pdf", "xmpmm:documentid": "uuid:1a3c967e-6453-4654-ae4c-d72ba148e672", "xmpmm:instanceid": "uuid:a8bbcfd5-b087-47a8-85d1-c3511bce870a", "xmp:createdate": "2023-03-27T13:19:43-05:00", "xmp:modifydate": "2023-03-27T13:20:20-05:00", "pdf:producer": "PDF-XChange Standard (9.1 build 356) [GDI] [Windows 10 Enterprise x64 (Build 19043)]", "pdf:creatortool": "PDF-XChange Standard (9.1 build 356) [GDI] [Windows 10 Enterprise x64 (Build 19043)]"}}, "errors": [], "classificationSummary": [{"configuration": "acord_125_2016_03", "score": {"score": 71, "fieldsPresent": 71, "penalties": 0}}], "validation_summary": {"fields": 16, "fields_present": 14, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 16, "fields_present": 14, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "f960c70c-3d5e-4914-960b-853361b34c01", "created": "2023-06-05T08:28:40.515Z", "completed": "2023-06-05T08:28:57.412Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "36#bfcd1654-9792-4b16-92b1-0d9dd50dc053#fa2e1895-44a2-4c8a-b676-985eee6e14d3#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [{"documentType": "acord_forms", "configuration": "acord_125_2016_03", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"agency_information": [{"date": null, "agency": {"type": "string", "value": "<PERSON>tier Insurance Services, LLC 75 Sylvan Street Suite B202 Danvers, MA 01923"}, "contact_name": {"type": "string", "value": "<PERSON>"}, "contact_phone": {"type": "string", "value": "(*************"}, "contact_fax": {"type": "string", "value": "(*************"}, "contact_email": {"type": "string", "value": "<EMAIL>"}, "underwriter": null, "transaction_status_renew": {"type": "boolean", "value": false}}], "lines_of_business": [{"boilerMachinery": {"type": "boolean", "value": false}, "boilerMachinery_premium": null, "businessAuto": {"type": "boolean", "value": false}, "businessAuto_premium": null, "businessOwners": {"type": "boolean", "value": false}, "businessOwners_premium": null, "generalLiability": {"type": "boolean", "value": false}, "generalLiability_premium": null, "commercialInlandMarine": {"type": "boolean", "value": false}, "commercialInlandMarine_premium": null, "property": {"type": "boolean", "value": true}, "property_premium": null, "crime": {"type": "boolean", "value": false}, "crime_premium": null, "cyberPrivacy": {"type": "boolean", "value": false}, "cyberPrivacy_premium": null, "fiduciaryLiability": {"type": "boolean", "value": false}, "fiduciaryLiability_premium": null, "garageDealers": {"type": "boolean", "value": false}, "garageDealers_premium": null, "liquorLiability": {"type": "boolean", "value": false}, "liquorLiability_premium": null, "motorCarriers": {"type": "boolean", "value": false}, "motorCarriers_premium": null, "truckers": {"type": "boolean", "value": false}, "truckers_premium": null, "umbrella": {"type": "boolean", "value": false}, "umbrella_premium": null, "yacht": {"type": "boolean", "value": false}, "yacht_premium": null, "cyber_liability": null, "cyber_liability_premium": null, "custom_1_name": null, "custom_1": {"type": "boolean", "value": false}, "custom_1_premium": null, "custom_2_name": null, "custom_2": {"type": "boolean", "value": false}, "custom_2_premium": null, "custom_3_name": null, "custom_3": {"type": "boolean", "value": false}, "custom_3_premium": null, "custom_4_name": null, "custom_4": {"type": "boolean", "value": false}, "custom_4_premium": null, "custom_5_name": null, "custom_5": {"type": "boolean", "value": false}, "custom_5_premium": null, "custom_6": {"type": "boolean", "value": false}, "custom_6_premium": null}], "policy_information": [{"proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "billing_plan_direct": {"type": "boolean", "value": false}, "billing_plan_agency": {"type": "boolean", "value": false}, "payment_plan": null, "method_of_payment": null, "audit": null, "deposit": null, "minimum_premium": null, "policy_premium": null}], "proposed_effective_date": {"source": "09/01/2023", "value": "2023-09-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "09/01/2024", "value": "2024-09-01T00:00:00.000Z", "type": "date"}, "primary_naics": null, "description_of_primary_operations": {"type": "string", "value": "242 Unit Habitational Condominium Association"}, "applicant_information": [{"applicant_name_and_address": {"type": "string", "value": "St. George Seaside Condominiums & Beach Club c/o Crowninshield Management Co. 9 Atlantic Ave. Marblehead, MA 01945"}, "applicant_address": {"value": "9 Atlantic Ave.\nMarblehead, MA 01945", "type": "address"}, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": {"type": "string", "value": "04-3046933"}, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": null, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": null, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}], "premises_info": [{"location_number": {"source": "1", "value": 1, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "350 Revere Beach Blvd"}, "city": {"type": "string", "value": "Revere"}, "state": {"type": "string", "value": "MA"}, "county": null, "zip": {"type": "string", "value": "02151-4800"}, "city_limits_inside": {"type": "boolean", "value": true}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": true}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": {"source": "366,000", "value": 366000, "type": "number"}, "area_leased_to_others": null, "operations_description": {"type": "string", "value": "Condo Association"}}, {"location_number": {"source": "1", "value": 1, "type": "number"}, "building_number": {"source": "2", "value": 2, "type": "number"}, "street": {"type": "string", "value": "350 Revere Beach Blvd"}, "city": {"type": "string", "value": "Revere"}, "state": {"type": "string", "value": "MA"}, "county": null, "zip": {"type": "string", "value": "02151-4800"}, "city_limits_inside": {"type": "boolean", "value": true}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": true}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": {"source": "1,410", "value": 1410, "type": "number"}, "area_leased_to_others": null, "operations_description": {"type": "string", "value": "frame w/stucco with total area of 1,410"}}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}], "nature_of_business": [{"apartments": {"type": "boolean", "value": false}, "contractor": {"type": "boolean", "value": false}, "manufacturing": {"type": "boolean", "value": false}, "restaurant": {"type": "boolean", "value": false}, "service": {"type": "boolean", "value": false}, "condominiums": {"type": "boolean", "value": false}, "institutional": {"type": "boolean", "value": false}, "office": {"type": "boolean", "value": false}, "retail": {"type": "boolean", "value": false}, "wholesale": {"type": "boolean", "value": false}, "custom": {"type": "boolean", "value": false}, "start_date": null, "description_of_primary_operations": {"type": "string", "value": "242 Unit Habitational Condominium Association"}, "retail_or_service_percentage_sales": null, "installation_service_repair_work_percentage": null, "off_premises_work": null, "operations_description_of_other_named_insureds": null}], "general_information": [{"is_applicant_subsidiary": {"value": "N", "type": "string"}, "subsidiary_parent_company_name": null, "subsidiary_percentage_owned": null, "does_applicant_have_subsidiaries": {"value": "N", "type": "string"}, "applicants_subsidiary_company_name": null, "applicants_subsidiary_percentage_owned": null, "is_formal_safety_plan_in_operation": {"value": false, "type": "boolean"}, "safety_program_manual": {"type": "boolean", "value": false}, "safety_plan_monthly_meetings": {"type": "boolean", "value": false}, "safety_plan_safety_position": {"type": "boolean", "value": false}, "safety_plan_osha": {"type": "boolean", "value": false}, "any_exposure_flammables": {"value": "N", "type": "string"}, "flammables_explanation": null, "any_policy_or_coverage_declined": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBS<PERSON>IARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA N 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? N 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER N 5. OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-R<PERSON><PERSON>WAL UNDERWRITING CONDITION CORRECTED (Describe): N 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? N 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION WITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). N 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N 9. HAS APPLICANT HAD A FORECLOSURE, REPOSSESSION, BANKRUPTCY OR FILED FOR BANKRUPTCY DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N N 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: N 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) N 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR WHICH COVERAGE IS NOT REQUESTED? 14. DOES APPLICANT OWN / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "coverage_declined_non_payment": {"type": "boolean", "value": false}, "coverage_declined_agent": {"type": "boolean", "value": false}, "coverage_declined_non_renewal": {"type": "boolean", "value": false}, "coverage_declined_underwriting": {"type": "boolean", "value": false}, "coverage_declined_condition_corrected": {"type": "boolean", "value": false}, "any_past_losses_relating_to_sexual_abuse": {"value": "N", "type": "string"}, "any_past_losses_relating_to_sexual_abuse_explanation": {"type": "string", "value": "7."}, "any_applicant_been_indicted": {"value": "N", "type": "string"}, "any_applicant_been_indicted_explanation": null, "any_uncorrected_fire_or_safety_violations": {"type": "string", "value": "N"}, "has_applicant_had_forclosure": {"type": "string", "value": "AGENCY CUSTOMER ID: <PERSON><PERSON><PERSON><PERSON> INFORMATION EXPLAIN ALL \"YES\" RESPONSES Y / N N 1a. IS THE APPLICANT A SUBSIDIARY OF ANOTHER ENTITY ? PARENT COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 1b. DOES THE APPLICANT HAVE ANY SUBSIDIARIES? SUBSIDIARY COMPANY NAME RELATIONSHIP DESCRIPTION % OWNED N 2. IS A FORMAL SAFETY PROGRAM IN OPERATION? SAFETY MANUAL SAFETY POSITION MONTHLY MEETINGS OSHA N 3. ANY EXPOSURE TO FLAMMABLES, EXPLOSIVES, CHEMICALS? N 4. ANY OTHER INSURANCE WITH THIS COMPANY? (List policy numbers) LINE OF BUSINESS POLICY NUMBER LINE OF BUSINESS POLICY NUMBER N 5. ANY POLICY OR COVERAGE DECLINED, CANCELLED OR NON-R<PERSON>EWED DURING THE PRIOR THREE (3) YEARS FOR ANY PREMISES OR OPERATIONS? (Missouri Applicants - Do not answer this question) NON-PAYMENT AGENT NO LONGER REPRESENTS CARRIER NON-RENEWAL UNDERWRITING CONDITION CORRECTED (Describe): N 6. ANY PAST LOSSES OR CLAIMS RELATING TO SEXUAL ABUSE OR MOLESTATION ALLEGATIONS, DISCRIMINATION OR NEGLIGENT HIRING? N 7. DURING THE LAST FIVE YEARS (TEN IN RI), HAS ANY APPLICANT BEEN INDICTED FOR OR CONVICTED OF ANY DEGREE OF THE CRIME OF FRAUD, BRIBERY, ARSON OR ANY OTHER ARSON-RELATED CRIME IN CONNECTION WITH THIS OR ANY OTHER PROPERTY? (In RI, this question must be answered by any applicant for property insurance. Failure to disclose the existence of an arson conviction is a misdemeanor punishable by a sentence of up to one year of imprisonment). N 8. ANY UNCORRECTED FIRE AND/OR SAFETY CODE VIOLATIONS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N 9. OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE 10. HAS APPLICANT HAD A JUDGEMENT OR LIEN DURING THE LAST FIVE (5) YEARS? OCCUR DATE EXPLANATION RESOLUTION RESOLVE DATE N N 11. HAS BUSINESS BEEN PLACED IN A TRUST? NAME OF TRUST: N 12. ANY FOREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN USA, OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES? (If \"YES\", attach ACORD 815 for Liability Exposure and/or ACORD 816 for Property Exposure) N 13. DOES APPLICANT HAVE OTHER BUSINESS VENTURES FOR WHICH COVERAGE IS NOT REQUESTED? 14. DOES APPLICANT OWN / LEASE / OPERATE ANY DRONES? (If \"YES\", describe use) DOES APPLICANT HIRE OTHERS TO OPERATE DRONES? (If \"YES\", describe use) 15. REMARKS / PROCESSING INSTRUCTIONS (ACORD 101, Additional Remarks Schedule, may be attached if more space is required)"}, "has_applicant_had_judgement_or_lien": {"type": "string", "value": "N"}, "is_business_placed_in_trust": {"type": "string", "value": "N"}, "name_of_trust": null, "any_foreign_operations": {"value": false, "type": "boolean"}, "does_applicant_have_other_business_ventures": {"value": "N", "type": "string"}, "does_applicant_have_other_business_ventures_explanation": null, "does_applicant_own_lease_operate_any_drones": null, "does_applicant_own_lease_operate_any_drones_explanation": null, "does_applicant_hire_others_to_operate_drones": null, "does_applicant_hire_others_to_operate_drones_explanation": null}]}, "configuration": "acord_125_2016_03", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "acord_125_2016_03", "score": {"score": 104, "fieldsPresent": 104, "penalties": 0}}], "validation_summary": {"fields": 16, "fields_present": 15, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 16, "fields_present": 15, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "9394c9e6-dacb-4b7b-a513-1d7010284a68", "created": "2023-06-05T08:28:22.678Z", "completed": "2023-06-05T08:28:28.086Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#bfcd1654-9792-4b16-92b1-0d9dd50dc053#46a7d7d6-0468-4811-b68b-a6943effdb8f#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "sompo_1", "startPage": 0, "endPage": 0, "output": {"parsedDocument": {"report_generated_date": {"source": "6/29/2022", "value": "2022-06-29T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"value": "Sompo 1", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "10461408"}, "date_of_loss": {"source": "Sep 28, 2021", "value": "2021-09-28T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "Sep 29, 2021", "value": "2021-09-29T00:00:00.000Z", "type": "date"}, "policy_effective_date": {"source": "Sep 1, 2021", "value": "2021-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "Sep 1, 2022", "value": "2022-09-01T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "$ 74,102", "value": 74102, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$ 66,835", "value": 66835, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$ 0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$ 0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$ 7,268", "value": 7268, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "Water leak in unit 6-7M leaked into 6 floors and 6 to 8 units."}, "loss_location": {"type": "string", "value": "Massachusetts"}, "claim_status": {"value": "CLOSED", "type": "string"}}]}, "configuration": "sompo_1", "validations": [], "fileMetadata": {"info": {"producer": "Vertafore using ABCpdf"}}, "errors": [], "classificationSummary": [{"configuration": "sompo_1", "score": {"score": 16, "fieldsPresent": 16, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 1, "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "a502547f-b0c5-4b76-9553-ef75483fe5e5", "created": "2023-06-05T08:28:15.796Z", "completed": "2023-06-05T08:28:21.532Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#bfcd1654-9792-4b16-92b1-0d9dd50dc053#a3c9da52-c963-443f-a60e-6f37a4be7029#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 3, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "3a66ee58-fa3a-4392-bbf7-daa6f981d60d", "created": "2023-06-05T08:26:28.215Z", "completed": "2023-06-05T08:26:33.126Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#d61c0e2d-2a0a-49b2-afd4-a07c43b26c04#f5df346c-798f-4430-ae45-8496dee75f7d#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "amrisc", "startPage": 0, "endPage": 1, "output": {"parsedDocument": {"report_generated_date": {"source": "03/29/2023", "value": "2023-03-29T00:00:00.000Z", "type": "date"}, "claims": [{"claim_number": {"type": "string", "value": "4174933"}, "line_of_business": {"value": "Unknown", "type": "string"}, "named_insured": {"type": "string", "value": "CPAT Santa Rosa ISD"}, "policy_effective_date": {"source": "09/01/2017", "value": "2017-09-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "09/01/2020", "value": "2020-09-01T00:00:00.000Z", "type": "date"}, "carrier": {"value": "Amrisc", "type": "string"}, "date_of_loss": {"source": "7/25/2020", "value": "2020-07-25T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "7/25/2020", "value": "2020-07-25T00:00:00.000Z", "type": "date"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "39,928.30", "value": 39928.3, "type": "number"}, "total_amount_incurred": {"source": "134,073.76", "value": 134073.76, "type": "number"}, "total_amount_reserved_loss": {"source": "25,000.00", "value": 25000, "type": "number"}, "total_amount_reserved_expense": {"source": "69,145.46", "value": 69145.46, "type": "number"}, "loss_description": {"type": "string", "value": "Damage to Buildings/Contents due to Hurricane Hanna."}, "claim_status": {"type": "string", "value": "OPEN"}}]}, "configuration": "amrisc", "validations": [], "fileMetadata": {"info": {"author": "", "title": "LossRun", "creator": "Microsoft Reporting Services ********", "producer": "Microsoft Reporting Services PDF Rendering Extension ********", "creation_date": "2023-03-29T14:13:38.000-05:00"}}, "errors": [], "classificationSummary": [{"configuration": "amrisc", "score": {"score": 16, "fieldsPresent": 16, "penalties": 0}}, {"configuration": "velocity", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 2, "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "e6f523e9-ec3a-424d-9661-e7d92ff2994b", "created": "2023-06-05T08:24:23.773Z", "completed": "2023-06-05T08:24:46.903Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "36#88e95443-76ee-4fe3-a24b-0edc2cf888f1#839f406a-08db-4522-9a29-5309c5f3359c#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [{"documentType": "acord_forms", "configuration": "acord_125_2016_03", "startPage": 0, "endPage": 3, "output": {"parsedDocument": {"agency_information": [{"date": {"source": "07/01/2023", "value": "2023-07-01T00:00:00.000Z", "type": "date"}, "agency": {"type": "string", "value": "SullivanCurtisMonroe, LLC - #0E83670 Claims Reporting (800)427-3253 1920 Main St., Suite 600 Irvine CA 92614"}, "contact_name": {"type": "string", "value": "<PERSON>"}, "contact_phone": {"type": "string", "value": "(*************"}, "contact_fax": {"type": "string", "value": "(*************"}, "contact_email": {"type": "string", "value": "<EMAIL>"}, "underwriter": null, "transaction_status_renew": {"type": "boolean", "value": true}}], "lines_of_business": [{"boilerMachinery": {"type": "boolean", "value": false}, "boilerMachinery_premium": null, "businessAuto": {"type": "boolean", "value": false}, "businessAuto_premium": null, "businessOwners": {"type": "boolean", "value": false}, "businessOwners_premium": null, "generalLiability": {"type": "boolean", "value": false}, "generalLiability_premium": null, "commercialInlandMarine": {"type": "boolean", "value": false}, "commercialInlandMarine_premium": null, "property": {"type": "boolean", "value": false}, "property_premium": null, "crime": {"type": "boolean", "value": false}, "crime_premium": null, "cyberPrivacy": {"type": "boolean", "value": false}, "cyberPrivacy_premium": null, "fiduciaryLiability": {"type": "boolean", "value": false}, "fiduciaryLiability_premium": null, "garageDealers": {"type": "boolean", "value": false}, "garageDealers_premium": null, "liquorLiability": {"type": "boolean", "value": false}, "liquorLiability_premium": null, "motorCarriers": {"type": "boolean", "value": false}, "motorCarriers_premium": null, "truckers": {"type": "boolean", "value": false}, "truckers_premium": null, "umbrella": {"type": "boolean", "value": false}, "umbrella_premium": null, "yacht": {"type": "boolean", "value": false}, "yacht_premium": null, "cyber_liability": null, "cyber_liability_premium": null, "custom_1_name": {"type": "string", "value": "Comml Excess Liability (C)"}, "custom_1": {"type": "boolean", "value": true}, "custom_1_premium": null, "custom_2_name": null, "custom_2": {"type": "boolean", "value": false}, "custom_2_premium": null, "custom_3_name": null, "custom_3": {"type": "boolean", "value": false}, "custom_3_premium": null, "custom_4_name": null, "custom_4": {"type": "boolean", "value": false}, "custom_4_premium": null, "custom_5_name": null, "custom_5": {"type": "boolean", "value": false}, "custom_5_premium": null, "custom_6": {"type": "boolean", "value": false}, "custom_6_premium": null}], "policy_information": [{"proposed_effective_date": {"source": "07/01/2023", "value": "2023-07-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "07/01/2024", "value": "2024-07-01T00:00:00.000Z", "type": "date"}, "billing_plan_direct": {"type": "boolean", "value": false}, "billing_plan_agency": {"type": "boolean", "value": true}, "payment_plan": null, "method_of_payment": null, "audit": null, "deposit": null, "minimum_premium": null, "policy_premium": {"source": "0.00", "value": 0, "unit": "$", "type": "currency"}}], "proposed_effective_date": {"source": "07/01/2023", "value": "2023-07-01T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "07/01/2024", "value": "2024-07-01T00:00:00.000Z", "type": "date"}, "primary_naics": null, "description_of_primary_operations": {"type": "string", "value": "With operations based in Central California, the company is one of the largest producer/processors of market crops including pima cotton, processing tomatoes for paste, and seed in the world. The company has achieved a global impact with its core values based on integrity and a reputation for producing premium quality products with world-class people dedicated to innovative and sustainable production practices. J.G. Boswell Tomato is a vertically integrated company from growing and processing, to packaging and marketing every aspect of the process fully supports and exceeds customer requirements. J.G. Boswell products are sold to mills and food companies around the world. J.G. Boswell prides itself on safety and compliance and maintains a fulltime safety staff at their locations. Their commitment to safety is evidenced by their minimal loss activity over the last five years."}, "applicant_information": [{"applicant_name_and_address": {"type": "string", "value": "J.G. Boswell Company, Inc. 101 West Walnut Street Pasadena CA 91103"}, "applicant_address": {"value": "101 West Walnut Street\nPasadena CA 91103", "type": "address"}, "applicant_info_gl_code_1": null, "applicant_sic": {"type": "string", "value": "013100"}, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": {"type": "string", "value": "Boston Ranch Company, A Corporation"}, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": {"type": "string", "value": "013100"}, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}, {"applicant_name_and_address": {"type": "string", "value": "Boston Ranch Company, dba: Yokohl Valley Cattle Com"}, "applicant_address": null, "applicant_info_gl_code_1": null, "applicant_sic": {"type": "string", "value": "013100"}, "applicant_naics": null, "applicant_fein": null, "applicant_website_address": null}], "premises_info": [{"location_number": {"source": "1", "value": 1, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "101 West Walnut"}, "city": {"type": "string", "value": "Pasadena"}, "state": {"type": "string", "value": "CA"}, "county": {"value": "Los Angeles", "type": "string"}, "zip": {"type": "string", "value": "91103"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": {"source": "2", "value": 2, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "27095 Dairy Avenue"}, "city": {"type": "string", "value": "<PERSON><PERSON><PERSON>"}, "state": {"type": "string", "value": "CA"}, "county": {"value": "Kings", "type": "string"}, "zip": {"type": "string", "value": "93212"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": {"source": "3", "value": 3, "type": "number"}, "building_number": {"source": "1", "value": 1, "type": "number"}, "street": {"type": "string", "value": "36889 Highway 58"}, "city": {"type": "string", "value": "Buttonwillow"}, "state": {"type": "string", "value": "CA"}, "county": {"value": "<PERSON>", "type": "string"}, "zip": {"type": "string", "value": "93206"}, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}, {"location_number": null, "building_number": null, "street": null, "city": null, "state": null, "county": null, "zip": null, "city_limits_inside": {"type": "boolean", "value": false}, "city_limits_outside": {"type": "boolean", "value": false}, "interest_owner": {"type": "boolean", "value": false}, "interest_tenant": {"type": "boolean", "value": false}, "fulltime_employees": null, "parttime_employees": null, "annual_revenues": null, "occupied_area": null, "open_to_public_area": null, "building_area": null, "area_leased_to_others": null, "operations_description": null}], "nature_of_business": [{"apartments": {"type": "boolean", "value": false}, "contractor": {"type": "boolean", "value": false}, "manufacturing": {"type": "boolean", "value": false}, "restaurant": {"type": "boolean", "value": false}, "service": {"type": "boolean", "value": false}, "condominiums": {"type": "boolean", "value": false}, "institutional": {"type": "boolean", "value": false}, "office": {"type": "boolean", "value": false}, "retail": {"type": "boolean", "value": false}, "wholesale": {"type": "boolean", "value": false}, "custom": {"type": "boolean", "value": false}, "start_date": {"source": "01/01/1925", "value": "1925-01-01T00:00:00.000Z", "type": "date"}, "description_of_primary_operations": {"type": "string", "value": "With operations based in Central California, the company is one of the largest producer/processors of market crops including pima cotton, processing tomatoes for paste, and seed in the world. The company has achieved a global impact with its core values based on integrity and a reputation for producing premium quality products with world-class people dedicated to innovative and sustainable production practices. J.G. Boswell Tomato is a vertically integrated company from growing and processing, to packaging and marketing every aspect of the process fully supports and exceeds customer requirements. J.G. Boswell products are sold to mills and food companies around the world. J.G. Boswell prides itself on safety and compliance and maintains a fulltime safety staff at their locations. Their commitment to safety is evidenced by their minimal loss activity over the last five years."}, "retail_or_service_percentage_sales": null, "installation_service_repair_work_percentage": null, "off_premises_work": null, "operations_description_of_other_named_insureds": null}], "general_information": [{"is_applicant_subsidiary": {"value": "N", "type": "string"}, "subsidiary_parent_company_name": null, "subsidiary_percentage_owned": null, "does_applicant_have_subsidiaries": {"value": "Y", "type": "string"}, "applicants_subsidiary_company_name": null, "applicants_subsidiary_percentage_owned": null, "is_formal_safety_plan_in_operation": {"value": true, "type": "boolean"}, "safety_program_manual": {"type": "boolean", "value": false}, "safety_plan_monthly_meetings": {"type": "boolean", "value": false}, "safety_plan_safety_position": {"type": "boolean", "value": false}, "safety_plan_osha": {"type": "boolean", "value": false}, "any_exposure_flammables": {"value": "Y", "type": "string"}, "flammables_explanation": null, "any_policy_or_coverage_declined": {"type": "string", "value": "N"}, "coverage_declined_non_payment": {"type": "boolean", "value": false}, "coverage_declined_agent": {"type": "boolean", "value": false}, "coverage_declined_non_renewal": {"type": "boolean", "value": false}, "coverage_declined_underwriting": {"type": "boolean", "value": false}, "coverage_declined_condition_corrected": {"type": "boolean", "value": false}, "any_past_losses_relating_to_sexual_abuse": {"value": "N", "type": "string"}, "any_past_losses_relating_to_sexual_abuse_explanation": null, "any_applicant_been_indicted": {"value": "N", "type": "string"}, "any_applicant_been_indicted_explanation": null, "any_uncorrected_fire_or_safety_violations": {"type": "string", "value": "N"}, "has_applicant_had_forclosure": {"type": "string", "value": "N"}, "has_applicant_had_judgement_or_lien": {"type": "string", "value": "N"}, "is_business_placed_in_trust": {"type": "string", "value": "N"}, "name_of_trust": null, "any_foreign_operations": {"value": true, "type": "boolean"}, "does_applicant_have_other_business_ventures": {"value": "N", "type": "string"}, "does_applicant_have_other_business_ventures_explanation": null, "does_applicant_own_lease_operate_any_drones": null, "does_applicant_own_lease_operate_any_drones_explanation": null, "does_applicant_hire_others_to_operate_drones": null, "does_applicant_hire_others_to_operate_drones_explanation": null}]}, "configuration": "acord_125_2016_03", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "acord_125_2016_03", "score": {"score": 116, "fieldsPresent": 116, "penalties": 0}}], "validation_summary": {"fields": 16, "fields_present": 15, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 4, "validation_summary": {"fields": 16, "fields_present": 15, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "fe73e989-90cd-436c-8a2f-4517da60f35f", "created": "2023-06-05T08:23:57.814Z", "completed": "2023-06-05T08:24:23.986Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#88e95443-76ee-4fe3-a24b-0edc2cf888f1#0fb3b4bd-8992-468b-9424-db2f26db9c5c#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "chubb_2", "startPage": 0, "endPage": 6, "output": {"parsedDocument": {"report_generated_date": {"source": "04/09/2023", "value": "2023-04-09T00:00:00.000Z", "type": "date"}, "_named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "claims": [{"carrier": {"value": "<PERSON><PERSON>", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "040518041446"}, "named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "loss_location": {"type": "string", "value": "<PERSON><PERSON><PERSON>, CA"}, "policy_effective_date": {"source": "07/01/2018", "value": "2018-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "07/01/2019", "value": "2019-07-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "07/06/2018", "value": "2018-07-06T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "07/11/2018", "value": "2018-07-11T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "The insd had several crates of tomato paste have leaked, due to punctures in the interior polypropylene lining of each crate."}, "total_amount_incurred": {"source": "$5,675", "value": 5675, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$5,675", "value": 5675, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "<PERSON><PERSON>", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "040518041446"}, "named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "loss_location": {"type": "string", "value": "<PERSON><PERSON><PERSON>, CA"}, "policy_effective_date": {"source": "07/01/2018", "value": "2018-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "07/01/2019", "value": "2019-07-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "07/06/2018", "value": "2018-07-06T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "07/11/2018", "value": "2018-07-11T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "The insd had several crates of tomato paste have leaked, due to punctures in the interior polypropylene lining of each crate."}, "total_amount_incurred": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "<PERSON><PERSON>", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "047519000739"}, "named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "loss_location": {"type": "string", "value": "<PERSON><PERSON><PERSON>, CA"}, "policy_effective_date": {"source": "07/01/2018", "value": "2018-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "07/01/2019", "value": "2019-07-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "12/20/2018", "value": "2018-12-20T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "01/08/2019", "value": "2019-01-08T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "ID, <PERSON><PERSON><PERSON><PERSON>, was operating a tractor with a drip tape installation bar behind it heading eastbound along 165 North when the claimant vehicle was"}, "total_amount_incurred": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "<PERSON><PERSON>", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "040520045078"}, "named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "loss_location": {"type": "string", "value": "Unknown, CA"}, "policy_effective_date": {"source": "07/01/2020", "value": "2020-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "07/01/2021", "value": "2021-07-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "11/21/2020", "value": "2020-11-21T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "11/24/2020", "value": "2020-11-24T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "This incident occurred in Corcoran, California when a J.G. Bo<PERSON>well tractor got stuck on a railroad track causing the train to collide with the tractor. There"}, "total_amount_incurred": {"source": "$71,670", "value": 71670, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$70,819", "value": 70819, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$850", "value": 850, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "<PERSON><PERSON>", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "092021020682"}, "named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "loss_location": {"type": "string", "value": "<PERSON><PERSON><PERSON>, CA"}, "policy_effective_date": {"source": "07/01/2021", "value": "2021-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "07/01/2022", "value": "2022-07-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "10/14/2021", "value": "2021-10-14T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "10/18/2021", "value": "2021-10-18T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "<PERSON><PERSON><PERSON>'s grader was being followed by a Boswell truck with emergency flashing light when a semi truck attempted to pass and struck <PERSON><PERSON><PERSON>'s grader when"}, "total_amount_incurred": {"source": "$18,671", "value": 18671, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$18,671", "value": 18671, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "claim_status": {"value": "CLOSED", "type": "string"}}]}, "configuration": "chubb_2", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "chubb_2", "score": {"score": 87, "fieldsPresent": 87, "penalties": 0}}], "validation_summary": {"fields": 7, "fields_present": 7, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "chubb_2", "startPage": 7, "endPage": 21, "output": {"parsedDocument": {}, "configuration": "chubb_2", "validations": [], "fileMetadata": {"info": {}}, "errors": [{"type": "unexpected", "message": "AssertionError [ERR_ASSERTION]: The expression evaluated to a falsy value:\n\n  (0, assert_1.default)(page)\n"}], "classificationSummary": [{"configuration": "chubb_2", "score": {"score": 0, "fieldsPresent": 0, "penalties": 0}}], "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "chubb_2", "startPage": 22, "endPage": 26, "output": {"parsedDocument": {"report_generated_date": {"source": "04/09/2023", "value": "2023-04-09T00:00:00.000Z", "type": "date"}, "_named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "claims": []}, "configuration": "chubb_2", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "chubb_2", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "chubb_2", "startPage": 27, "endPage": 31, "output": {"parsedDocument": {"report_generated_date": {"source": "04/09/2023", "value": "2023-04-09T00:00:00.000Z", "type": "date"}, "_named_insured": {"type": "string", "value": "EL RICO RECLAMATION DISTRICT NO. 1618"}, "claims": []}, "configuration": "chubb_2", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "chubb_2", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "chubb_2", "startPage": 32, "endPage": 36, "output": {"parsedDocument": {"report_generated_date": {"source": "04/09/2023", "value": "2023-04-09T00:00:00.000Z", "type": "date"}, "_named_insured": {"type": "string", "value": "GATES-JONES MUTUAL WATER COMPANY"}, "claims": []}, "configuration": "chubb_2", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "chubb_2", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "chubb_2", "startPage": 37, "endPage": 42, "output": {"parsedDocument": {"report_generated_date": {"source": "04/09/2023", "value": "2023-04-09T00:00:00.000Z", "type": "date"}, "_named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "claims": [{"carrier": {"value": "<PERSON><PERSON>", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "092022010957"}, "named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "loss_location": {"type": "string", "value": "King county, CA"}, "policy_effective_date": {"source": "07/01/2018", "value": "2018-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "07/01/2019", "value": "2019-07-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "11/19/2018", "value": "2018-11-19T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "04/06/2022", "value": "2022-04-06T00:00:00.000Z", "type": "date"}, "loss_description": {"type": "string", "value": "Our insured driver rear-ended a farm tractor and the tractor driver was ejected."}, "total_amount_incurred": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "<PERSON><PERSON>", "type": "string"}, "line_of_business": {"value": "OTHER", "type": "string"}, "claim_number": {"type": "string", "value": "092022010957"}, "named_insured": {"type": "string", "value": "J.G. BOSWELL COMPANY, INC."}, "loss_location": {"type": "string", "value": "Coml Auto - Third Party King county, CA"}, "policy_effective_date": {"source": "07/01/2018", "value": "2018-07-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "07/01/2019", "value": "2019-07-01T00:00:00.000Z", "type": "date"}, "date_of_loss": null, "loss_reported_date": null, "loss_description": {"type": "string", "value": "Our insured driver rear-ended a farm tractor and the tractor driver was ejected."}, "total_amount_incurred": {"source": "$4,000,001", "value": 4000001, "unit": "$", "type": "currency"}, "total_amount_paid": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_loss": {"source": "$4,000,000", "value": 4000000, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$1", "value": 1, "unit": "$", "type": "currency"}, "claim_status": {"value": "Open", "type": "string"}}]}, "configuration": "chubb_2", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "chubb_2", "score": {"score": 34, "fieldsPresent": 34, "penalties": 0}}], "validation_summary": {"fields": 4, "fields_present": 4, "errors": 0, "warnings": 0, "skipped": 0}}}, {"documentType": "loss_runs", "configuration": "travelers", "startPage": 43, "endPage": 44, "output": {"parsedDocument": {"report_generated_date": {"source": "04/09/2023", "value": "2023-04-09T00:00:00.000Z", "type": "date"}, "claims": []}, "configuration": "travelers", "validations": [], "fileMetadata": {"info": {}}, "errors": [], "classificationSummary": [{"configuration": "travelers", "score": {"score": 2, "fieldsPresent": 2, "penalties": 0}}], "validation_summary": {"fields": 2, "fields_present": 2, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 45, "validation_summary": {"fields": 19, "fields_present": 19, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "7ad9b091-a624-4c4b-b21a-c90946d459e2", "created": "2023-06-05T08:22:21.313Z", "completed": "2023-06-05T08:22:28.606Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#ce32d3b8-c148-4752-8f75-e89e69da214a#95997bb9-728c-4aa2-8006-aae23096454e#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 6, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "dc763f2b-ae05-4e55-a46d-28d152a67ca3", "created": "2023-06-05T08:20:19.134Z", "completed": "2023-06-05T08:21:05.904Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#b1070081-bdb2-420d-8e33-9bcf763ced62#c579b37f-696b-45de-aa9a-045503d21dcc#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [{"documentType": "loss_runs", "configuration": "qbe", "startPage": 22, "endPage": 38, "output": {"parsedDocument": {"report_generated_date": {"source": "May 24, 2023", "value": "2023-05-24T00:00:00.000Z", "type": "date"}, "claims": [{"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "281051N", "type": "string"}, "date_of_loss": {"source": "02/9/2015", "value": "2015-02-09T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "02/10/2015", "value": "2015-02-10T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": {"value": "EB STONE & SON INC DBA PRO GUARD INC & GREENBALL", "type": "string"}, "policy_effective_date": {"source": "06/30/2014", "value": "2014-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2015", "value": "2015-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$8,018.65", "value": 8018.65, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$335.00", "value": 335, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$7,337.06", "value": 7337.06, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "PU TRUCK HAD HIT THEIR TRUCK AND <PERSON><PERSON><PERSON><PERSON> TRUCK. ACCORDING TO THE CHP THE DRIVER OF THE OTHER TRUCK HAD FALLEN ASLEEP. Insured Vehicle HAD TO HAVE TRUCK TOWED."}, "claim_status": {"value": "02/9/2015 02/10/2015 closed", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "314803N", "type": "string"}, "date_of_loss": {"source": "07/6/2015", "value": "2015-07-06T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "07/15/2015", "value": "2015-07-15T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": {"value": "E.B Stone & Son, Inc., DBA ProGuard, Inc", "type": "string"}, "policy_effective_date": {"source": "06/30/2015", "value": "2015-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2016", "value": "2016-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$1,762.00", "value": 1762, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$20.00", "value": 20, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "Insured vehicle struck another party's vehicle from behind at a slow speed causing minor physical damage to both vehicles."}, "claim_status": {"value": "Auto Liability - 07/6/2015 07/15/2015 closed", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "314803N", "type": "string"}, "date_of_loss": {"source": "07/6/2015", "value": "2015-07-06T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "07/15/2015", "value": "2015-07-15T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": {"value": "E.B Stone & Son, Inc., DBA ProGuard, Inc", "type": "string"}, "policy_effective_date": {"source": "06/30/2015", "value": "2015-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2016", "value": "2016-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$4,692.10", "value": 4692.1, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$102.00", "value": 102, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "Insured vehicle struck another party's vehicle from behind at a slow speed causing minor physical damage to both vehicles."}, "claim_status": {"value": "07/6/2015 07/15/2015 closed", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "367284N", "type": "string"}, "date_of_loss": {"source": "03/2/2016", "value": "2016-03-02T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "03/4/2016", "value": "2016-03-04T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": {"value": "E.B Stone & Son, Inc., DBA ProGuard, Inc", "type": "string"}, "policy_effective_date": {"source": "06/30/2015", "value": "2015-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2016", "value": "2016-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$27.00", "value": 27, "unit": "$", "type": "currency"}, "total_amount_reserved": null, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": null, "loss_description": null, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "367284N", "type": "string"}, "date_of_loss": null, "loss_reported_date": null, "loss_location": null, "named_insured": {"value": "E.B Stone & Son, Inc., DBA ProGuard, Inc", "type": "string"}, "policy_effective_date": {"source": "06/30/2015", "value": "2015-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2016", "value": "2016-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$4,807.10", "value": 4807.1, "unit": "$", "type": "currency"}, "total_amount_paid_expense": null, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": null, "total_amount_recovered": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "Insured pulled into hotel & put vehicle into park. As she was grabbing her purse, the car slipped out of park & into reverse. Her car door was open, so when the car started to reverse, the vehicle door was bent backwards by a pole that was next to her car."}, "claim_status": {"value": "OPEN", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "382605N", "type": "string"}, "date_of_loss": {"source": "05/13/2016", "value": "2016-05-13T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "05/16/2016", "value": "2016-05-16T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": {"value": "E.B Stone & Son, Inc., DBA ProGuard, Inc", "type": "string"}, "policy_effective_date": {"source": "06/30/2015", "value": "2015-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2016", "value": "2016-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$5,861.86", "value": 5861.86, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$15.00", "value": 15, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "Accident was on the corner of Sepulveda Blud and Vose Street Van Nuys 91406 Insured Vehicle was stopped at a red light with the jeep in front. The light turned green and the jeep started to move, as did Insured Vehicle. At the same moment a motorcycle passed on Insured Vehicle passenger side and merged in front of the jeep. The jeep hit the breaks but Insured Vehicle was unable to stop in time causing me to rear-end the jeep of OP."}, "claim_status": {"value": "Auto Liability - 05/13/2016 05/16/2016 closed", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "382605N", "type": "string"}, "date_of_loss": {"source": "05/13/2016", "value": "2016-05-13T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "05/16/2016", "value": "2016-05-16T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": {"value": "E.B Stone & Son, Inc., DBA ProGuard, Inc", "type": "string"}, "policy_effective_date": {"source": "06/30/2015", "value": "2015-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2016", "value": "2016-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$7,279.60", "value": 7279.6, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$170.50", "value": 170.5, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "Accident was on the corner of Sepulveda Blud and Vose Street Van Nuys 91406 Insured Vehicle was stopped at a red light with the jeep in front. The light turned green and the jeep started to move, as did Insured Vehicle. At the same moment a motorcycle passed on Insured Vehicle"}, "claim_status": {"value": "05/13/2016 05/16/2016 closed", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "470531N", "type": "string"}, "date_of_loss": {"source": "03/30/2017", "value": "2017-03-30T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "04/7/2017", "value": "2017-04-07T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": {"value": "EB Stone", "type": "string"}, "policy_effective_date": {"source": "06/30/2016", "value": "2016-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2017", "value": "2017-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$8,043.81", "value": 8043.81, "unit": "$", "type": "currency"}, "total_amount_paid_expense": {"source": "$299.23", "value": 299.23, "unit": "$", "type": "currency"}, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_recovered": {"source": "$2,676.70", "value": 2676.7, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "CV was passing on the right shoulder and merged into Insured Vehicle"}, "claim_status": {"value": "CLOSED", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "604499N", "type": "string"}, "date_of_loss": {"source": "06/18/2018", "value": "2018-06-18T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "06/25/2018", "value": "2018-06-25T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": null, "policy_effective_date": {"source": "06/30/2017", "value": "2017-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2018", "value": "2018-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$650.00", "value": 650, "unit": "$", "type": "currency"}, "total_amount_paid_expense": null, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": null, "total_amount_recovered": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "INSURED REARENDED OV1. OV1 WAS STOPPED IN TRAFFIC"}, "claim_status": {"value": "Auto Liability - 06/18/2018 06/25/2018 closed", "type": "string"}}, {"carrier": {"value": "QBE", "type": "string"}, "line_of_business": {"value": "Unknown", "type": "string"}, "claim_number": {"value": "604499N", "type": "string"}, "date_of_loss": {"source": "06/18/2018", "value": "2018-06-18T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "06/25/2018", "value": "2018-06-25T00:00:00.000Z", "type": "date"}, "loss_location": null, "named_insured": null, "policy_effective_date": {"source": "06/30/2017", "value": "2017-06-30T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "06/30/2018", "value": "2018-06-30T00:00:00.000Z", "type": "date"}, "total_amount_paid": {"source": "$1,690.02", "value": 1690.02, "unit": "$", "type": "currency"}, "total_amount_paid_expense": null, "total_amount_reserved": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "total_amount_reserved_expense": null, "total_amount_recovered": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency"}, "loss_description": {"type": "string", "value": "INSURED REARENDED OV1. OV1 WAS STOPPED IN TRAFFIC"}, "claim_status": {"value": "Auto Liability - 06/18/2018 06/25/2018 closed", "type": "string"}}]}, "configuration": "qbe", "validations": [], "fileMetadata": {"info": {"creator": "PDFium", "producer": "PDFium", "creation_date": "2023-05-23T15:43:39.000Z", "modification_date": "2023-05-30T08:19:53.000-06:00"}, "metadata": {"xmp:creatortool": "PDFium", "pdf:producer": "PDFium", "xmp:createdate": "2023-05-23T15:43:39Z", "xmp:modifydate": "2023-05-30T08:19:53-06:00", "xmp:metadatadate": "2023-05-30T08:19:53-06:00"}}, "errors": [], "classificationSummary": [{"configuration": "qbe", "score": {"score": 139, "fieldsPresent": 139, "penalties": 0}}], "validation_summary": {"fields": 12, "fields_present": 12, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 39, "validation_summary": {"fields": 12, "fields_present": 12, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "84117c23-b267-4c16-89ff-ee8021cc7e05", "created": "2023-06-05T08:07:07.589Z", "completed": "2023-06-05T08:07:11.804Z", "status": "COMPLETE", "types": ["acord_forms"], "webhook": {"payload": "6#2a4067b8-cf2e-4502-a8c2-a909daf31a63#bad9607c-6f96-4607-ba99-7172c9002b39#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/acords"}, "documents": [], "page_count": 2, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "322b27d0-27a2-4c69-9e10-327ed17a5d92", "created": "2023-06-05T08:01:03.349Z", "completed": "2023-06-05T08:01:37.423Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#0a7150c1-41bd-47df-9b56-010f7fc22de1#16be178e-1009-4236-ac19-5f1e33517a00#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 4, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}, {"id": "fcaab1a8-30cd-4a92-b48c-a2bb3ced233b", "created": "2023-06-05T08:01:02.446Z", "completed": "2023-06-05T08:01:26.355Z", "status": "COMPLETE", "types": ["loss_runs"], "webhook": {"payload": "36#0a7150c1-41bd-47df-9b56-010f7fc22de1#650938dc-9797-4508-9c7e-d9ed4e5caec2#1096b822aa9f0d2a3108ef3fcb850f7be2aa782837f708ee0031a9c6f71aa411", "url": "https://copilot.api.kalepa.com/api/v3.0/webhooks/sensible/lossruns"}, "documents": [], "page_count": 4, "validation_summary": {"fields": 0, "fields_present": 0, "errors": 0, "warnings": 0, "skipped": 0}}]
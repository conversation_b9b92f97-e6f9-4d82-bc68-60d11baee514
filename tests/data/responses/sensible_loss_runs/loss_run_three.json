{"id": "d84ab8d6-0269-41df-9374-7109a951619a", "created": "2023-10-12T12:33:26.331Z", "completed": "2023-10-12T12:34:02.047Z", "status": "COMPLETE", "types": ["loss_runs"], "environment": "production", "webhook": {"payload": "1#00000000-0000-0000-0000-000000000000#00000000-0000-0000-0000-000000000000#123", "url": "https://copilot-api.integration"}, "documents": [{"documentType": "loss_runs", "configuration": "skyward", "startPage": 0, "endPage": 30, "output": {"parsedDocument": {"report_generated_date": {"source": "10/5/2023", "value": "2023-10-05T00:00:00.000Z", "type": "date"}, "claims": [{"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "5710910572"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "CA00029689-01"}, "policy_effective_date": {"source": "2013-05-01", "value": "2013-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2014-04-30", "value": "2014-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2013-06-18", "value": "2013-06-18T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2013-07-16", "value": "2013-07-16T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "891.07", "value": 891.07, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "891.07", "value": 891.07, "type": "number"}, "total_paid_loss": {"source": "891.07", "value": 891.07, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " IV WENT AROUND SHARP CORNER, CV2 SWERVED AND CAME TO STOP AN", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "5710910572"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "CA00029689-01"}, "policy_effective_date": {"source": "2013-05-01", "value": "2013-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2014-04-30", "value": "2014-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2013-06-18", "value": "2013-06-18T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2013-07-16", "value": "2013-07-16T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " IV WENT AROUND SHARP CORNER, CV2 SWERVED AND CAME TO STOP AN", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "5710911369"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "CA00029689-01"}, "policy_effective_date": {"source": "2013-05-01", "value": "2013-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2014-04-30", "value": "2014-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2013-08-22", "value": "2013-08-22T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2013-08-28", "value": "2013-08-28T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "294.57", "value": 294.57, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "294.57", "value": 294.57, "type": "number"}, "total_paid_loss": {"source": "294.57", "value": 294.57, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " CLAIMANT STATES A ROCK FROM IV HIT WS, CLAIMANT CANNOT PROVE", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "HGGM18040036"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "CA00059714-05"}, "policy_effective_date": {"source": "2017-05-01", "value": "2017-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2018-04-30", "value": "2018-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2018-03-31", "value": "2018-03-31T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2018-04-17", "value": "2018-04-17T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "2,908.01", "value": 2908.01, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "2,908.01", "value": 2908.01, "type": "number"}, "total_paid_loss": {"source": "2,908.01", "value": 2908.01, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " IV BACKED INTO OV WHILE THE OTHER PARTY WAS PARKED.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"type": "string", "value": "CA Commercial Auto"}, "claim_number": null, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-00"}, "policy_effective_date": {"source": "2017-06-22", "value": "2017-06-22T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2018-06-21", "value": "2018-06-21T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": null, "total_amount_incurred": {"source": "275.24", "value": 275.24, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "275.24", "value": 275.24, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "18.23", "value": 18.23, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " Liner flopped up when cut throwing dirt/Eye laceration", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"type": "string", "value": "CA Commercial Auto"}, "claim_number": null, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-00"}, "policy_effective_date": {"source": "2017-06-22", "value": "2017-06-22T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2018-06-21", "value": "2018-06-21T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": null, "total_amount_incurred": {"source": "5,214.28", "value": 5214.28, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "5,214.28", "value": 5214.28, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "433.88", "value": 433.88, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " Tried catching falling equipment/R ring & middle finger contusion & laceration", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "HGIM19050056"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-00000122-00"}, "policy_effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2019-05-25", "value": "2019-05-25T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2019-05-29", "value": "2019-05-29T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "16,145.00", "value": 16145, "type": "number"}, "total_amount_recovered": {"source": "1,845.00", "value": 1845, "type": "number"}, "total_amount_paid": {"source": "16,145.00", "value": 16145, "type": "number"}, "total_paid_loss": {"source": "15,776.00", "value": 15776, "type": "number"}, "total_amount_paid_expense": {"source": "369.00", "value": 369, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " IV REAR ENDED CV.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "HGIM19090042"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-00000122-00"}, "policy_effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2019-08-28", "value": "2019-08-28T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2019-09-18", "value": "2019-09-18T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "28.00", "value": 28, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "28.00", "value": 28, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "28.00", "value": 28, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " INSURED'S <PERSON><PERSON> WAS STOLEN FROM THEIR YARD.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"type": "string", "value": "CA Commercial Auto"}, "claim_number": null, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-02"}, "policy_effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": null, "total_amount_incurred": {"source": "505.83", "value": 505.83, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "505.83", "value": 505.83, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "89.24", "value": 89.24, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " Twisted wrist while setting fence post down/RT wrist strain", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"type": "string", "value": "CA Commercial Auto"}, "claim_number": null, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-02"}, "policy_effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": null, "loss_reported_date": null, "total_amount_incurred": {"source": "10.15", "value": 10.15, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "10.15", "value": 10.15, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "10.15", "value": 10.15, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " EE doing work duties/ Seizure", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "GC017568"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-00000122-01"}, "policy_effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2021-01-27", "value": "2021-01-27T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2021-02-04", "value": "2021-02-04T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "505.00", "value": 505, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "505.00", "value": 505, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "505.00", "value": 505, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " CV made a U-turn on the highway, was struck by IV, then hit a guardrail.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "GC017568"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-00000122-01"}, "policy_effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2021-01-27", "value": "2021-01-27T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2021-02-04", "value": "2021-02-04T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "18,016.56", "value": 18016.56, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "18,016.56", "value": 18016.56, "type": "number"}, "total_paid_loss": {"source": "18,016.56", "value": 18016.56, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " CV made a U-turn on the highway, was struck by IV, then hit a guardrail.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "HWC20100012"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-03"}, "policy_effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2020-09-29", "value": "2020-09-29T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2020-10-02", "value": "2020-10-02T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "2,123.24", "value": 2123.24, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "2,123.24", "value": 2123.24, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "164.13", "value": 164.13, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " STRUCK BY OR AGAINST", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"type": "string", "value": "CA Commercial Auto"}, "claim_number": null, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-03"}, "policy_effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": null, "total_amount_incurred": {"source": "2,413.38", "value": 2413.38, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "2,413.38", "value": 2413.38, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "88.58", "value": 88.58, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " DRIVING AND PASSED OT.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "GC023545"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-CA-0000568-00"}, "policy_effective_date": {"source": "2021-05-01", "value": "2021-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2022-04-27", "value": "2022-04-27T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2022-06-23", "value": "2022-06-23T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "33,482.77", "value": 33482.77, "type": "number"}, "total_amount_recovered": {"source": "7,155.00", "value": 7155, "type": "number"}, "total_amount_paid": {"source": "33,482.77", "value": 33482.77, "type": "number"}, "total_paid_loss": {"source": "33,392.77", "value": 33392.77, "type": "number"}, "total_amount_paid_expense": {"source": "90.00", "value": 90, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " IVD driving at excessive speed, lost control of IV. After losing control, IV swerved towards the right-side embankment, where he contacted rocky material.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "************"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-04"}, "policy_effective_date": {"source": "2021-05-01", "value": "2021-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2022-04-30", "value": "2022-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2022-04-28", "value": "2022-04-28T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2022-04-29", "value": "2022-04-29T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "417.19", "value": 417.19, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "417.19", "value": 417.19, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "390.84", "value": 390.84, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " <WC01>CLOSING HIS LUBE TRUCK AND CLOSED THE DOOR</WC01>", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "HWC21090011"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-04"}, "policy_effective_date": {"source": "2021-05-01", "value": "2021-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2022-04-30", "value": "2022-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2021-08-30", "value": "2021-08-30T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2021-09-08", "value": "2021-09-08T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "465.21", "value": 465.21, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "465.21", "value": 465.21, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "35.23", "value": 35.23, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " SOCKET CAME LOOSE WHICH LAND ONTO EE'S LEFT SIDE OF FACE CAUSING A MINOR CUT.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "GC025697"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-CA-0000568-01"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2022-11-18", "value": "2022-11-18T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2022-11-28", "value": "2022-11-28T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "9,134.38", "value": 9134.38, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "9,134.38", "value": 9134.38, "type": "number"}, "total_paid_loss": {"source": "9,134.38", "value": 9134.38, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " IV was hauling a CAT scraper that did not clear the overpass & damaged a bridge. Debris from incident struck CV1 & CV2.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "GC025697"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-CA-0000568-01"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2022-11-18", "value": "2022-11-18T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2022-11-28", "value": "2022-11-28T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "50.00", "value": 50, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "50.00", "value": 50, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "50.00", "value": 50, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " IV was hauling a CAT scraper that did not clear the overpass & damaged a bridge. Debris from incident struck CV1 & CV2.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "CA", "type": "string"}, "claim_number": {"type": "string", "value": "GC025866"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-CA-0000568-01"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2022-10-31", "value": "2022-10-31T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2022-12-09", "value": "2022-12-09T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "29,153.16", "value": 29153.16, "type": "number"}, "total_amount_recovered": {"source": "26,224.27", "value": 26224.27, "type": "number"}, "total_amount_paid": {"source": "29,153.16", "value": 29153.16, "type": "number"}, "total_paid_loss": {"source": "26,224.27", "value": 26224.27, "type": "number"}, "total_amount_paid_expense": {"source": "2,928.89", "value": 2928.89, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " CV1 rear-ended IV, thus pushing IV into CV2.", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"type": "string", "value": "CA Commercial Auto"}, "claim_number": null, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-05"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-04-30", "value": "2023-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": null, "total_amount_incurred": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " <WC01><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON> HIGH MARK EMPLOYEES WERE CHECKING IN AT SECURITY. JOHN GOT OUT OF THE VAN TO LET ANOTHER EMPLOYEES SIGN IN SINCE HE</WC01>", "type": "string"}, "claim_status": {"value": "Open", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "009773001194"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-05"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-04-30", "value": "2023-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2022-10-27", "value": "2022-10-27T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2022-11-03", "value": "2022-11-03T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "339.22", "value": 339.22, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "339.22", "value": 339.22, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "48.82", "value": 48.82, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " <WC01><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WAS CLIMBING DOWN STEP LADDER FROM ROCK TRUCK AND MISSES LAST STEP CAUSING HIM TO FALL BACKWARDS ONTO HIS BACK.</WC01>", "type": "string"}, "claim_status": {"value": "Closed", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "009773001276"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-05"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-04-30", "value": "2023-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2022-12-01", "value": "2022-12-01T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2022-12-02", "value": "2022-12-02T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "31,000.00", "value": 31000, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "16,685.58", "value": 16685.58, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "10,968.78", "value": 10968.78, "type": "number"}, "total_amount_reserved_loss": {"source": "9,000.00", "value": 9000, "type": "number"}, "total_amount_reserved": {"source": "14,314.42", "value": 14314.42, "type": "number"}, "total_amount_reserved_expense": {"source": "1,031.22", "value": 1031.22, "type": "number"}, "loss_description": {"value": " <WC01><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON> HIGH MARK EMPLOYEES WERE CHECKING IN AT SECURITY. JOHN GOT OUT OF THE VAN TO LET ANOTHER EMPLOYEES SIGN IN SINCE HE</WC01>", "type": "string"}, "claim_status": {"value": "Open", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "009773001355"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-05"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-04-30", "value": "2023-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2023-01-03", "value": "2023-01-03T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2023-01-05", "value": "2023-01-05T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "78,233.00", "value": 78233, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "70,396.79", "value": 70396.79, "type": "number"}, "total_paid_loss": {"source": "4,673.30", "value": 4673.3, "type": "number"}, "total_amount_paid_expense": {"source": "31,309.61", "value": 31309.61, "type": "number"}, "total_amount_reserved_loss": {"source": "5,500.70", "value": 5500.7, "type": "number"}, "total_amount_reserved": {"source": "7,836.21", "value": 7836.21, "type": "number"}, "total_amount_reserved_expense": {"source": "491.39", "value": 491.39, "type": "number"}, "loss_description": {"value": " <WC01>SLIPED AND FELL ON ICE</WC01>", "type": "string"}, "claim_status": {"value": "Open", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "009773001696"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "IERD-********-05"}, "policy_effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2023-04-30", "value": "2023-04-30T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2023-04-19", "value": "2023-04-19T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2023-04-24", "value": "2023-04-24T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "19.00", "value": 19, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "19.00", "value": 19, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "19.00", "value": 19, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_expense": {"source": "0.00", "value": 0, "type": "number"}, "loss_description": {"value": " <WC01>OPERATING A SCRAPER MOVING DIRT. BOUNCING IN THE SEAT OF THE SCRAPER & ALLEGED HERNIA INJURY</WC01>", "type": "string"}, "claim_status": {"value": "Open", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}, {"line_of_business": {"value": "WC", "type": "string"}, "claim_number": {"type": "string", "value": "009773001869"}, "carrier": {"value": "Skyward", "type": "string"}, "named_insured": {"type": "string", "value": "High Mark Construction, LLC"}, "policy_number": {"type": "string", "value": "MNG-IIC-WC-0000239-00"}, "policy_effective_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "policy_expiration_date": {"source": "2024-05-01", "value": "2024-05-01T00:00:00.000Z", "type": "date"}, "date_of_loss": {"source": "2023-06-12", "value": "2023-06-12T00:00:00.000Z", "type": "date"}, "loss_reported_date": {"source": "2023-06-20", "value": "2023-06-20T00:00:00.000Z", "type": "date"}, "total_amount_incurred": {"source": "1,775.00", "value": 1775, "type": "number"}, "total_amount_recovered": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid": {"source": "0.00", "value": 0, "type": "number"}, "total_paid_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved_loss": {"source": "0.00", "value": 0, "type": "number"}, "total_amount_reserved": {"source": "1,775.00", "value": 1775, "type": "number"}, "total_amount_reserved_expense": {"source": "275.00", "value": 275, "type": "number"}, "loss_description": {"value": " <WC01><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WAS A PASSENGER IN COMPANY VAN RETURNING HOME FROM WORK. <PERSON><PERSON> WAS SETTING AT THE LIGHT AT INTERSECTION OF SR228 SR227 WHEN</WC01>", "type": "string"}, "claim_status": {"value": "Open", "type": "string"}, "loss_location": {"value": "Nevada", "type": "string"}}], "policies": [{"number": {"type": "string", "value": "GL00029690-01"}, "effective_date": {"source": "2013-05-01", "value": "2013-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2014-04-30", "value": "2014-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CA00029689-01"}, "effective_date": {"source": "2013-05-01", "value": "2013-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2014-04-30", "value": "2014-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "CA Commercial Auto"}}, {"number": {"type": "string", "value": "CA00029689-02"}, "effective_date": {"source": "2014-05-01", "value": "2014-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2015-04-30", "value": "2015-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CX00047189-01"}, "effective_date": {"source": "2014-05-01", "value": "2014-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2015-04-30", "value": "2015-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "GL00029690-02"}, "effective_date": {"source": "2014-05-01", "value": "2014-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2015-04-30", "value": "2015-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CA00059714-03"}, "effective_date": {"source": "2015-05-01", "value": "2015-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2016-04-30", "value": "2016-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CX00102561-02"}, "effective_date": {"source": "2015-05-01", "value": "2015-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2016-04-30", "value": "2016-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "GL00062613-03"}, "effective_date": {"source": "2015-05-01", "value": "2015-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2016-04-30", "value": "2016-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CA00059714-04"}, "effective_date": {"source": "2016-05-01", "value": "2016-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2017-04-30", "value": "2017-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CX00102561-03"}, "effective_date": {"source": "2016-05-01", "value": "2016-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2017-04-30", "value": "2017-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "GL00062613-04"}, "effective_date": {"source": "2016-05-01", "value": "2016-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2017-04-30", "value": "2017-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CX00102561-04"}, "effective_date": {"source": "2017-05-01", "value": "2017-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2018-04-30", "value": "2018-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "GL00062613-05"}, "effective_date": {"source": "2017-05-01", "value": "2017-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2018-04-30", "value": "2018-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CA00059714-05"}, "effective_date": {"source": "2017-05-01", "value": "2017-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2018-04-30", "value": "2018-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "CA Commercial Auto"}}, {"number": {"type": "string", "value": "IERD-********-00"}, "effective_date": {"source": "2017-06-22", "value": "2017-06-22T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2018-06-21", "value": "2018-06-21T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "WC Worker Compensation"}}, {"number": {"type": "string", "value": "CA00059714-06"}, "effective_date": {"source": "2018-05-01", "value": "2018-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2019-04-30", "value": "2019-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "CX00102561-05"}, "effective_date": {"source": "2018-05-01", "value": "2018-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2019-04-30", "value": "2019-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "GL00062613-06"}, "effective_date": {"source": "2018-05-01", "value": "2018-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2019-04-30", "value": "2019-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "IERD-********-01"}, "effective_date": {"source": "2018-06-22", "value": "2018-06-22T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-00000123-00"}, "effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-00000124-00"}, "effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-00000122-00"}, "effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "CA Commercial Auto"}}, {"number": {"type": "string", "value": "IERD-********-02"}, "effective_date": {"source": "2019-05-01", "value": "2019-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2020-04-30", "value": "2020-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "WC - Worker Compensation"}}, {"number": {"type": "string", "value": "MNG-IIC-00000123-01"}, "effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-00000124-01"}, "effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-00000122-01"}, "effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "CA Commercial Auto"}}, {"number": {"type": "string", "value": "IERD-********-03"}, "effective_date": {"source": "2020-05-01", "value": "2020-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2021-04-30", "value": "2021-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "WC Worker Compensation"}}, {"number": {"type": "string", "value": "MNG-IIC-CX-0000446-00"}, "effective_date": {"source": "2021-05-01", "value": "2021-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-GL-0000861-00"}, "effective_date": {"source": "2021-05-01", "value": "2021-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-CA-0000568-00"}, "effective_date": {"source": "2021-05-01", "value": "2021-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "CA Commercial Auto"}}, {"number": {"type": "string", "value": "IERD-********-04"}, "effective_date": {"source": "2021-05-01", "value": "2021-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2022-04-30", "value": "2022-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "WC Worker Compensation"}}, {"number": {"type": "string", "value": "MNG-IIC-CX-0000446-01"}, "effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-GL-0000861-01"}, "effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-CA-0000568-01"}, "effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "CA Commercial Auto"}}, {"number": {"type": "string", "value": "IERD-********-05"}, "effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2023-04-30", "value": "2023-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "N/A N/A"}}, {"number": {"type": "string", "value": "IERD-********-05"}, "effective_date": {"source": "2022-05-01", "value": "2022-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2023-04-30", "value": "2023-04-30T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "WC Worker Compensation"}}, {"number": {"type": "string", "value": "MNG-IIC-CA-0000568-02"}, "effective_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2024-05-01", "value": "2024-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-CX-0000446-02"}, "effective_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2024-05-01", "value": "2024-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-GL-0000861-02"}, "effective_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2024-05-01", "value": "2024-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": null}, {"number": {"type": "string", "value": "MNG-IIC-WC-0000239-00"}, "effective_date": {"source": "2023-05-01", "value": "2023-05-01T00:00:00.000Z", "type": "date"}, "expiration_date": {"source": "2024-05-01", "value": "2024-05-01T00:00:00.000Z", "type": "date"}, "line_of_business": {"type": "string", "value": "WC Worker Compensation"}}]}, "configuration": "skyward", "validations": [{"description": "reported_date_required", "severity": "warning"}, {"description": "loss_date_required", "severity": "warning"}, {"description": "claim_numb_required", "severity": "warning"}], "fileMetadata": {"info": {"creator": "Aspose Ltd.", "producer": "Aspose.Pdf for .NET 17.12", "creation_date": "2023-10-05T22:07:45.000", "modification_date": "2023-10-11T17:31:01.000-04:00"}, "metadata": {}}, "errors": [], "classificationSummary": [{"configuration": "skyward", "score": {"score": 635.5, "fieldsPresent": 637, "penalties": 1.5}}], "validation_summary": {"fields": 68, "fields_present": 68, "errors": 0, "warnings": 3, "skipped": 0}}}], "page_count": 31, "validation_summary": {"fields": 68, "fields_present": 68, "errors": 0, "warnings": 3, "skipped": 0}, "download_url": "https://sensible-so-document-type-bucket-prod-us-west-2.s3.us-west-2.amazonaws.com/kalepa/MULTIDOC_EXTRACTION/890b8b3d-4f35-449b-aa96-9461b3936bba.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIAR355P7AS3MJJ3KAP%2F20231012%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231012T152800Z&X-Amz-Expires=900&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEP%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLXdlc3QtMiJGMEQCIHDm9KjYn%2B%2BOYQFhPMgf2BmDx4YaQwUVcrbebRgxcoA%2FAiA7VZOzwxEfoFXNA0yg4Kx%2FF3LjwmWGF5In6QvtIrAlICqIAwgYEAIaDDEyODcwOTU1NjI2MSIM5fvlSWay5rWnqp1fKuUComIKXlH%2B6A%2F6D59C38hICg7w5Q2uZ94uhzVfSnjpSpTlOw%2FOjRNgAJcCHu6MxQX5cmlKBxXdv5kbFDBGfs0fpB%2FHpie5PTJ0SHlKELzfB26t2ZboVDbJMh8%2FcQWT6p%2FY770Y58hYhnElHfP9cgZpTkBBy%2BqKGAvYIUKWqDwVDf3Sz00u5MsA6XFvirsCk%2BuQ9YLemCsltbzDeg2FbewWlGkR4j%2BjcNoTypeZXhXVrtj9dUfCVW5Rqz40Us7tAspi3%2FJd28f5mSccLagb%2FFxPuASAmlp7vlE7fRRz0g2soQWFlH7hDnnqA7zKVZawRJp988dQ2aCkBORKNgiQSrwlSHgnM4zAirO6gm6k2l0zTFANj8Ae5Lx3h9geVMAcmdNO1XzLCUNEavSBoL2kUWj%2FUwC61eDT5y%2BIzTZ2y%2Bqaxv7%2F7f0mZ%2B0FiV%2BwOGWcyE%2Bzgs72HfRAemzmgoGS0rqe5JNkANMzMLyUoKkGOp8BN4LCT6vR5lJ%2FKHFpTzSAzQFW%2BOr7eb9AwBsgsrcQVtxIHbtePhL8d3iGl%2FyXOgxSDNLN%2FdDrb7U03Usl7t%2Bu8WntEDuaUELhWC8vvZqL0ErOjfqhzZyZKaCWn5S5ztP9EUUGmvlwrisd9QtoLOFkW%2FycPBQje1xFdVhMm1zqWZ7NcXpESeLgkc80icwyWHYrgQ3KzEaytk8ZlwgAEu%2Fi&X-Amz-Signature=13868861320852fdb8ec33a6b6238497b9cd0087af95a1f0b54699499ebc6eac&X-Amz-SignedHeaders=host&x-id=GetObject", "coverage": 0.9318181818181818}
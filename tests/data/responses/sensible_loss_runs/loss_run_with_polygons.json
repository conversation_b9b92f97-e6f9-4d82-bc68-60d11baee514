{"_comment": "Note: This JSON has been heavily modified to fit a specific test case", "id": "d84ab8d6-0269-41df-9374-7109a951619a", "created": "2023-11-08T15:57:16.369Z", "completed": "2023-11-08T16:07:47.898Z", "status": "COMPLETE", "types": ["loss_runs"], "environment": "production", "webhook": {"payload": "3#00000000-0000-0000-0000-000000000003#00000000-0000-0000-0000-000000000003#123", "url": "https://copilot-api.integration"}, "documents": [{"documentType": "loss_runs", "configuration": "hanover", "startPage": 0, "endPage": 1, "output": {"parsedDocument": {"report_generated_date": {"source": "11/06/2024", "value": "2024-11-06T00:00:00.000Z", "type": "date", "lines": [{"text": "11/06/2024", "page": 0, "boundingPolygon": [{"x": 7.273, "y": 3.597}, {"x": 8.017, "y": 3.593}, {"x": 8.017, "y": 3.683}, {"x": 7.273, "y": 3.693}], "confidence": 0.9579999999999999}], "anchorConfidence": 0.9579999999999999, "valueConfidence": 0.9579999999999999}, "claims": [{"carrier": {"value": "MERCURY INSURANCE", "type": "string", "valueConfidence": null}, "claim_number": null, "named_insured": {"value": "THE CLIFTON R WARREN REVOCABLE TRUST", "type": "string", "lines": [{"text": "THE CLIFTON R WARREN REVOCABLE TRUST", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 3.33}, {"x": 3.397, "y": 3.33}, {"x": 3.397, "y": 3.423}, {"x": 0.53, "y": 3.427}], "confidence": 0.9475}], "anchorConfidence": 0.9475, "valueConfidence": 0.9475}, "policy_number": {"value": "CACM0000003008", "type": "string", "lines": [{"text": "CACM0000003008", "page": 0, "boundingPolygon": [{"x": 0.54, "y": 3.18}, {"x": 2.033, "y": 3.18}, {"x": 2.033, "y": 3.287}, {"x": 0.54, "y": 3.287}], "confidence": 0.9026666666666667}], "anchorConfidence": 0.9026666666666667, "valueConfidence": 0.9026666666666667}, "policy_effective_date": {"source": "12/11/2021", "value": "2021-12-11T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2021 to 12/1 1/2022", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 5.63}, {"x": 1.937, "y": 5.637}, {"x": 1.937, "y": 5.733}, {"x": 0.53, "y": 5.727}], "confidence": 0.9563999999999998}], "anchorConfidence": 0.9563999999999998, "valueConfidence": 0.9563999999999998}, "policy_expiration_date": {"source": "1/2022", "value": "2022-01-01T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2021 to 12/1 1/2022", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 5.63}, {"x": 1.937, "y": 5.637}, {"x": 1.937, "y": 5.733}, {"x": 0.53, "y": 5.727}], "confidence": 0.9563999999999998}], "anchorConfidence": 0.9563999999999998, "valueConfidence": 0.9563999999999998}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "07/13/2022", "value": "2022-07-13T00:00:00.000Z", "type": "date", "lines": [{"text": "07/13/2022", "page": 0, "boundingPolygon": [{"x": 2.133, "y": 6.063}, {"x": 2.68, "y": 6.063}, {"x": 2.68, "y": 6.167}, {"x": 2.133, "y": 6.167}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "claim_status": {"type": "string", "value": "Closed", "lines": [{"text": "Closed", "page": 0, "boundingPolygon": [{"x": 7.49, "y": 6.067}, {"x": 7.807, "y": 6.07}, {"x": 7.807, "y": 6.153}, {"x": 7.49, "y": 6.15}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.959}, "loss_description": {"type": "string", "value": "Loss Description", "lines": [{"text": "Loss Description", "page": 0, "boundingPolygon": [{"x": 4.277, "y": 5.857}, {"x": 5.067, "y": 5.857}, {"x": 5.067, "y": 5.95}, {"x": 4.277, "y": 5.95}], "confidence": 0.953}], "anchorConfidence": 0.958, "valueConfidence": 0.953}, "loss_location": {"type": "string", "value": "Location 3400 N Valley Dr Manhattan Beach, CA 90266-3642", "lines": [{"text": "Location", "page": 0, "boundingPolygon": [{"x": 0.517, "y": 5.863}, {"x": 0.92, "y": 5.857}, {"x": 0.92, "y": 5.943}, {"x": 0.517, "y": 5.95}], "confidence": 0.959}, {"text": "3400 N Valley Dr", "page": 0, "boundingPolygon": [{"x": 0.5, "y": 6.067}, {"x": 1.26, "y": 6.067}, {"x": 1.26, "y": 6.163}, {"x": 0.5, "y": 6.16}], "confidence": 0.9395}, {"text": "Manhattan Beach, CA 90266-3642", "page": 0, "boundingPolygon": [{"x": 0.517, "y": 6.207}, {"x": 2.013, "y": 6.207}, {"x": 2.013, "y": 6.3}, {"x": 0.517, "y": 6.303}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.9521666666666665}, "total_amount_paid": {"source": "$0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "$0.00", "page": 0, "boundingPolygon": [{"x": 6.507, "y": 6.067}, {"x": 6.75, "y": 6.06}, {"x": 6.75, "y": 6.147}, {"x": 6.507, "y": 6.147}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_incurred": null}], "policies": [{"number": {"value": "CACM0000003008", "type": "string", "lines": [{"text": "CACM0000003008", "page": 0, "boundingPolygon": [{"x": 0.54, "y": 3.18}, {"x": 2.033, "y": 3.18}, {"x": 2.033, "y": 3.287}, {"x": 0.54, "y": 3.287}], "confidence": 0.9026666666666667}], "anchorConfidence": 0.9026666666666667, "valueConfidence": 0.9026666666666667}, "effective_date": {"source": "12/11/2020", "value": "2020-12-11T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2020 to 12/1 1/2021", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 4.243}, {"x": 1.933, "y": 4.243}, {"x": 1.933, "y": 4.34}, {"x": 0.53, "y": 4.34}], "confidence": 0.9568}], "anchorConfidence": 0.9568, "valueConfidence": 0.9568}, "expiration_date": {"source": "1/2021", "value": "2021-01-01T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2020 to 12/1 1/2021", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 4.243}, {"x": 1.933, "y": 4.243}, {"x": 1.933, "y": 4.34}, {"x": 0.53, "y": 4.34}], "confidence": 0.9568}], "anchorConfidence": 0.9568, "valueConfidence": 0.9568}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}}, {"number": {"value": "CACM0000003008", "type": "string", "lines": [{"text": "CACM0000003008", "page": 0, "boundingPolygon": [{"x": 0.54, "y": 3.18}, {"x": 2.033, "y": 3.18}, {"x": 2.033, "y": 3.287}, {"x": 0.54, "y": 3.287}], "confidence": 0.9026666666666667}], "anchorConfidence": 0.9026666666666667, "valueConfidence": 0.9026666666666667}, "effective_date": {"source": "12/11/2021", "value": "2021-12-11T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2021 to 12/1 1/2022", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 5.63}, {"x": 1.937, "y": 5.637}, {"x": 1.937, "y": 5.733}, {"x": 0.53, "y": 5.727}], "confidence": 0.9563999999999998}], "anchorConfidence": 0.9563999999999998, "valueConfidence": 0.9563999999999998}, "expiration_date": {"source": "1/2022", "value": "2022-01-01T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2021 to 12/1 1/2022", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 5.63}, {"x": 1.937, "y": 5.637}, {"x": 1.937, "y": 5.733}, {"x": 0.53, "y": 5.727}], "confidence": 0.9563999999999998}], "anchorConfidence": 0.9563999999999998, "valueConfidence": 0.9563999999999998}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}}, {"number": {"value": "CACM0000003008", "type": "string", "lines": [{"text": "CACM0000003008", "page": 0, "boundingPolygon": [{"x": 0.54, "y": 3.18}, {"x": 2.033, "y": 3.18}, {"x": 2.033, "y": 3.287}, {"x": 0.54, "y": 3.287}], "confidence": 0.9026666666666667}], "anchorConfidence": 0.9026666666666667, "valueConfidence": 0.9026666666666667}, "effective_date": {"source": "12/11/2022", "value": "2022-12-11T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2022 to 12/11/2023", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 7.233}, {"x": 1.937, "y": 7.237}, {"x": 1.937, "y": 7.333}, {"x": 0.53, "y": 7.327}], "confidence": 0.9480000000000001}], "anchorConfidence": 0.9480000000000001, "valueConfidence": 0.9480000000000001}, "expiration_date": {"source": "12/11/2023", "value": "2023-12-11T00:00:00.000Z", "type": "date", "lines": [{"text": "12/11/2022 to 12/11/2023", "page": 0, "boundingPolygon": [{"x": 0.53, "y": 7.233}, {"x": 1.937, "y": 7.237}, {"x": 1.937, "y": 7.333}, {"x": 0.53, "y": 7.327}], "confidence": 0.9480000000000001}], "anchorConfidence": 0.9480000000000001, "valueConfidence": 0.9480000000000001}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}}, {"number": {"value": "CACM0000003008", "type": "string", "lines": [{"text": "CACM0000003008", "page": 0, "boundingPolygon": [{"x": 0.54, "y": 3.18}, {"x": 2.033, "y": 3.18}, {"x": 2.033, "y": 3.287}, {"x": 0.54, "y": 3.287}], "confidence": 0.9026666666666667}], "anchorConfidence": 0.9026666666666667, "valueConfidence": 0.9026666666666667}, "effective_date": {"source": "1/2023", "value": "2023-01-01T00:00:00.000Z", "type": "date", "lines": [{"text": "12/1 1/2023 to 12/1 1/2024", "page": 1, "boundingPolygon": [{"x": 0.547, "y": 2.974}, {"x": 1.953, "y": 2.981}, {"x": 1.953, "y": 3.077}, {"x": 0.547, "y": 3.074}], "confidence": 0.9556666666666666}], "anchorConfidence": 0.9556666666666666, "valueConfidence": 0.9556666666666666}, "expiration_date": {"source": "1/2024", "value": "2024-01-01T00:00:00.000Z", "type": "date", "lines": [{"text": "12/1 1/2023 to 12/1 1/2024", "page": 1, "boundingPolygon": [{"x": 0.547, "y": 2.974}, {"x": 1.953, "y": 2.981}, {"x": 1.953, "y": 3.077}, {"x": 0.547, "y": 3.074}], "confidence": 0.9556666666666666}], "anchorConfidence": 0.9556666666666666, "valueConfidence": 0.9556666666666666}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}}]}, "configuration": "hanover", "validations": [], "fileMetadata": {"info": {"author": "Registered to: HANOVER ", "title": "PCL402 UWWS Claims Loss Enhancements", "creator": "HP Exstream Version 7.0.633 32-bit"}}, "errors": [], "classificationSummary": [{"configuration": "hanover", "score": {"score": 10599, "fieldsPresent": 10599, "penalties": 0}}], "text": {"pages": [{"width": 8.5, "height": 11, "rotation": 0, "transform": {"a": 1, "c": 0, "e": 0, "b": 0, "d": 1, "f": 0}, "lines": [{"text": "SKEARNEY", "boundingPolygon": [{"x": 7.522, "y": 0.481}, {"x": 8.219, "y": 0.48}, {"x": 8.219, "y": 0.59}, {"x": 7.522, "y": 0.591}], "confidence": 0.9986941528320312}, {"text": "AGENCY CUSTOMER ID: PROJCAJ-01", "boundingPolygon": [{"x": 3.892, "y": 0.484}, {"x": 6.004, "y": 0.481}, {"x": 6.004, "y": 0.585}, {"x": 3.892, "y": 0.588}], "confidence": 0.9948384857177734}, {"text": "ACORD", "boundingPolygon": [{"x": 0.284, "y": 0.648}, {"x": 1.519, "y": 0.647}, {"x": 1.519, "y": 0.805}, {"x": 0.284, "y": 0.806}], "confidence": 0.9903419494628907}, {"text": "DATE (MM/DD/YYYY)", "boundingPolygon": [{"x": 7.234, "y": 0.736}, {"x": 8.055, "y": 0.735}, {"x": 8.055, "y": 0.824}, {"x": 7.234, "y": 0.826}], "confidence": 0.9969764709472656}, {"text": "UMBRELLA / EXCESS SECTION", "boundingPolygon": [{"x": 2.377, "y": 0.763}, {"x": 5.911, "y": 0.757}, {"x": 5.911, "y": 0.947}, {"x": 2.377, "y": 0.953}], "confidence": 0.9232279205322266}, {"text": "12/23/2024", "boundingPolygon": [{"x": 7.329, "y": 0.899}, {"x": 7.949, "y": 0.898}, {"x": 7.949, "y": 1}, {"x": 7.329, "y": 1.001}], "confidence": 0.9989556121826172}, {"text": "IMPORTANT - If CLAIMS MADE is checked in the POLICY INFORMATION section below, this is an application for a claims-made policy.", "boundingPolygon": [{"x": 0.692, "y": 1.106}, {"x": 7.79, "y": 1.096}, {"x": 7.79, "y": 1.22}, {"x": 0.692, "y": 1.23}], "confidence": 0.9930812835693359}, {"text": "CARRIER", "boundingPolygon": [{"x": 4.297, "y": 1.312}, {"x": 4.801, "y": 1.311}, {"x": 4.801, "y": 1.403}, {"x": 4.297, "y": 1.404}], "confidence": 0.9994947052001953}, {"text": "AGENCY", "boundingPolygon": [{"x": 0.293, "y": 1.322}, {"x": 0.648, "y": 1.322}, {"x": 0.648, "y": 1.393}, {"x": 0.293, "y": 1.393}], "confidence": 0.999693603515625}, {"text": "NAIC CODE", "boundingPolygon": [{"x": 7.596, "y": 1.322}, {"x": 8.055, "y": 1.322}, {"x": 8.055, "y": 1.393}, {"x": 7.596, "y": 1.393}], "confidence": 0.9988487243652344}, {"text": "New York-Alliant Ins Svc Inc", "boundingPolygon": [{"x": 0.267, "y": 1.481}, {"x": 1.94, "y": 1.479}, {"x": 1.941, "y": 1.582}, {"x": 0.267, "y": 1.585}], "confidence": 0.9946037292480469}, {"text": "N/A", "boundingPolygon": [{"x": 7.565, "y": 1.482}, {"x": 7.777, "y": 1.481}, {"x": 7.777, "y": 1.582}, {"x": 7.565, "y": 1.582}], "confidence": 0.9993138122558594}, {"text": "NAMED INSURED(S)", "boundingPolygon": [{"x": 4.296, "y": 1.654}, {"x": 5.099, "y": 1.652}, {"x": 5.099, "y": 1.74}, {"x": 4.297, "y": 1.741}], "confidence": 0.9977395629882813}, {"text": "POLICY NUMBER", "boundingPolygon": [{"x": 0.296, "y": 1.656}, {"x": 0.981, "y": 1.655}, {"x": 0.982, "y": 1.727}, {"x": 0.297, "y": 1.728}], "confidence": 0.9993534851074218}, {"text": "EFFECTIVE DATE", "boundingPolygon": [{"x": 3.493, "y": 1.657}, {"x": 4.194, "y": 1.656}, {"x": 4.194, "y": 1.726}, {"x": 3.493, "y": 1.727}], "confidence": 0.9949352264404296}, {"text": "Cajun Holdings LLC", "boundingPolygon": [{"x": 4.264, "y": 1.815}, {"x": 5.454, "y": 1.813}, {"x": 5.454, "y": 1.94}, {"x": 4.264, "y": 1.942}], "confidence": 0.9974138641357422}, {"text": "10/07/2024", "boundingPolygon": [{"x": 3.534, "y": 1.817}, {"x": 4.157, "y": 1.816}, {"x": 4.157, "y": 1.914}, {"x": 3.534, "y": 1.915}], "confidence": 0.9989382934570312}, {"text": "7034789280", "boundingPolygon": [{"x": 0.262, "y": 1.818}, {"x": 0.958, "y": 1.817}, {"x": 0.958, "y": 1.915}, {"x": 0.262, "y": 1.916}], "confidence": 0.9991294097900391}, {"text": "POLICY INFORMATION", "boundingPolygon": [{"x": 0.299, "y": 1.994}, {"x": 1.585, "y": 1.993}, {"x": 1.586, "y": 2.088}, {"x": 0.299, "y": 2.09}], "confidence": 0.9994332885742188}, {"text": "LIMIT OF LIABILITY", "boundingPolygon": [{"x": 4.736, "y": 2.191}, {"x": 5.504, "y": 2.19}, {"x": 5.504, "y": 2.264}, {"x": 4.736, "y": 2.265}], "confidence": 0.9989975738525391}, {"text": "RETAINED LIMIT", "boundingPolygon": [{"x": 6.817, "y": 2.191}, {"x": 7.474, "y": 2.191}, {"x": 7.474, "y": 2.265}, {"x": 6.817, "y": 2.266}], "confidence": 0.998251953125}, {"text": "TRANSACTION TYPE", "boundingPolygon": [{"x": 1.647, "y": 2.193}, {"x": 2.486, "y": 2.191}, {"x": 2.486, "y": 2.263}, {"x": 1.647, "y": 2.264}], "confidence": 0.9982545471191406}, {"text": "10,000,000 EA OCC", "boundingPolygon": [{"x": 4.987, "y": 2.317}, {"x": 5.96, "y": 2.315}, {"x": 5.96, "y": 2.431}, {"x": 4.987, "y": 2.432}], "confidence": 0.9947937774658203}, {"text": "RETROACTIVE DATE", "boundingPolygon": [{"x": 2.977, "y": 2.323}, {"x": 3.811, "y": 2.322}, {"x": 3.811, "y": 2.396}, {"x": 2.977, "y": 2.398}], "confidence": 0.9984545135498046}, {"text": "NEW", "boundingPolygon": [{"x": 0.497, "y": 2.324}, {"x": 0.694, "y": 2.324}, {"x": 0.694, "y": 2.395}, {"x": 0.497, "y": 2.395}], "confidence": 0.9994335174560547}, {"text": "OCCURRENCE", "boundingPolygon": [{"x": 1.947, "y": 2.324}, {"x": 2.542, "y": 2.323}, {"x": 2.542, "y": 2.395}, {"x": 1.947, "y": 2.396}], "confidence": 0.9847600555419922}, {"text": "UMBRELLA", "boundingPolygon": [{"x": 1.196, "y": 2.325}, {"x": 1.647, "y": 2.325}, {"x": 1.647, "y": 2.394}, {"x": 1.196, "y": 2.395}], "confidence": 0.99831298828125}, {"text": "$", "boundingPolygon": [{"x": 4.243, "y": 2.347}, {"x": 4.293, "y": 2.347}, {"x": 4.293, "y": 2.428}, {"x": 4.243, "y": 2.429}], "confidence": 0.9994460296630859}, {"text": "$", "boundingPolygon": [{"x": 6.091, "y": 2.347}, {"x": 6.144, "y": 2.347}, {"x": 6.144, "y": 2.429}, {"x": 6.091, "y": 2.429}], "confidence": 0.9991921997070312}, {"text": "10,000,000", "boundingPolygon": [{"x": 4.554, "y": 2.483}, {"x": 5.177, "y": 2.482}, {"x": 5.177, "y": 2.597}, {"x": 4.554, "y": 2.597}], "confidence": 0.9960200500488281}, {"text": "EXCESS", "boundingPolygon": [{"x": 1.197, "y": 2.49}, {"x": 1.534, "y": 2.49}, {"x": 1.534, "y": 2.562}, {"x": 1.198, "y": 2.563}], "confidence": 0.9990330505371093}, {"text": "CURRENT", "boundingPolygon": [{"x": 3.593, "y": 2.49}, {"x": 4.002, "y": 2.49}, {"x": 4.002, "y": 2.563}, {"x": 3.593, "y": 2.564}], "confidence": 0.999677734375}, {"text": "PROPOSED", "boundingPolygon": [{"x": 2.761, "y": 2.491}, {"x": 3.229, "y": 2.49}, {"x": 3.229, "y": 2.563}, {"x": 2.761, "y": 2.564}], "confidence": 0.9984944152832032}, {"text": "RENEWAL", "boundingPolygon": [{"x": 0.498, "y": 2.492}, {"x": 0.906, "y": 2.491}, {"x": 0.906, "y": 2.561}, {"x": 0.499, "y": 2.562}], "confidence": 0.9991815185546875}, {"text": "CLAIMS MADE", "boundingPolygon": [{"x": 1.949, "y": 2.493}, {"x": 2.517, "y": 2.492}, {"x": 2.517, "y": 2.56}, {"x": 1.949, "y": 2.561}], "confidence": 0.9980484008789062}, {"text": "$", "boundingPolygon": [{"x": 4.243, "y": 2.512}, {"x": 4.294, "y": 2.512}, {"x": 4.294, "y": 2.596}, {"x": 4.243, "y": 2.596}], "confidence": 0.9993552398681641}, {"text": "01/03/2025", "boundingPolygon": [{"x": 2.684, "y": 2.649}, {"x": 3.307, "y": 2.649}, {"x": 3.307, "y": 2.747}, {"x": 2.684, "y": 2.748}], "confidence": 0.9989762878417969}, {"text": "10,000,000", "boundingPolygon": [{"x": 4.555, "y": 2.649}, {"x": 5.178, "y": 2.648}, {"x": 5.178, "y": 2.763}, {"x": 4.555, "y": 2.764}], "confidence": 0.9964199066162109}, {"text": "FIRST DOLLAR DEFENSE (Y / N)", "boundingPolygon": [{"x": 6.095, "y": 2.651}, {"x": 7.363, "y": 2.65}, {"x": 7.363, "y": 2.74}, {"x": 6.095, "y": 2.742}], "confidence": 0.9126219940185547}, {"text": "EXPIRING POL #:", "boundingPolygon": [{"x": 0.298, "y": 2.68}, {"x": 0.973, "y": 2.679}, {"x": 0.973, "y": 2.753}, {"x": 0.298, "y": 2.754}], "confidence": 0.9895748138427735}, {"text": "$", "boundingPolygon": [{"x": 4.242, "y": 2.687}, {"x": 4.295, "y": 2.686}, {"x": 4.295, "y": 2.768}, {"x": 4.242, "y": 2.768}], "confidence": 0.9990633392333984}, {"text": "EMPLOYEE BENEFITS LIABILITY", "boundingPolygon": [{"x": 0.3, "y": 2.827}, {"x": 2.14, "y": 2.824}, {"x": 2.14, "y": 2.922}, {"x": 0.3, "y": 2.925}], "confidence": 0.9993315124511719}, {"text": "AGGREGATE LIMIT FOR EBL", "boundingPolygon": [{"x": 2.493, "y": 2.989}, {"x": 3.662, "y": 2.987}, {"x": 3.663, "y": 3.062}, {"x": 2.493, "y": 3.064}], "confidence": 0.9977030181884765}, {"text": "RETROACTIVE DATE FOR EBL", "boundingPolygon": [{"x": 6.844, "y": 2.989}, {"x": 8.082, "y": 2.987}, {"x": 8.082, "y": 3.061}, {"x": 6.844, "y": 3.063}], "confidence": 0.9976397705078125}, {"text": "LIMIT OF INSURANCE (Ea Employee)", "boundingPolygon": [{"x": 0.298, "y": 2.99}, {"x": 1.766, "y": 2.987}, {"x": 1.766, "y": 3.078}, {"x": 0.298, "y": 3.08}], "confidence": 0.9964963531494141}, {"text": "RETAINED LIMIT FOR EBL", "boundingPolygon": [{"x": 4.745, "y": 2.99}, {"x": 5.802, "y": 2.988}, {"x": 5.802, "y": 3.062}, {"x": 4.745, "y": 3.064}], "confidence": 0.9964301300048828}, {"text": "$ 2,000,000", "boundingPolygon": [{"x": 2.492, "y": 3.149}, {"x": 3.165, "y": 3.148}, {"x": 3.165, "y": 3.264}, {"x": 2.492, "y": 3.265}], "confidence": 0.9950405120849609}, {"text": "1,000,000", "boundingPolygon": [{"x": 0.386, "y": 3.15}, {"x": 0.937, "y": 3.149}, {"x": 0.937, "y": 3.264}, {"x": 0.387, "y": 3.265}], "confidence": 0.9960944366455078}, {"text": "$ 10,000", "boundingPolygon": [{"x": 4.742, "y": 3.151}, {"x": 5.252, "y": 3.15}, {"x": 5.252, "y": 3.264}, {"x": 4.742, "y": 3.265}], "confidence": 0.9965009307861328}, {"text": "NAME OF BENEFIT PROGRAM", "boundingPolygon": [{"x": 0.296, "y": 3.32}, {"x": 1.519, "y": 3.318}, {"x": 1.519, "y": 3.396}, {"x": 0.296, "y": 3.398}], "confidence": 0.9989743804931641}, {"text": "PRIMARY LOCATION & SUBSIDIARIES (ACORD 125)", "boundingPolygon": [{"x": 0.298, "y": 3.659}, {"x": 3.216, "y": 3.655}, {"x": 3.216, "y": 3.775}, {"x": 0.298, "y": 3.779}], "confidence": 0.9975189208984375}, {"text": "FOREIGN GROSS SALES", "boundingPolygon": [{"x": 6.854, "y": 3.847}, {"x": 7.843, "y": 3.846}, {"x": 7.843, "y": 3.921}, {"x": 6.854, "y": 3.922}], "confidence": 0.998553237915039}, {"text": "ANNUAL PAYROLL", "boundingPolygon": [{"x": 4.963, "y": 3.848}, {"x": 5.726, "y": 3.847}, {"x": 5.726, "y": 3.922}, {"x": 4.963, "y": 3.923}], "confidence": 0.9989513397216797}, {"text": "NAME AND LOCATION OF PRIMARY AND ALL SUBSIDIARY COMPANIES (Describe Operations)", "boundingPolygon": [{"x": 0.828, "y": 3.849}, {"x": 4.561, "y": 3.844}, {"x": 4.561, "y": 3.934}, {"x": 0.828, "y": 3.94}], "confidence": 0.9978245544433594}, {"text": "ANN GROSS SALES", "boundingPolygon": [{"x": 5.945, "y": 3.85}, {"x": 6.74, "y": 3.848}, {"x": 6.74, "y": 3.921}, {"x": 5.945, "y": 3.922}], "confidence": 0.9986965942382813}, {"text": "# EMPL", "boundingPolygon": [{"x": 7.895, "y": 3.85}, {"x": 8.199, "y": 3.85}, {"x": 8.199, "y": 3.922}, {"x": 7.895, "y": 3.922}], "confidence": 0.9798529052734375}, {"text": "#", "boundingPolygon": [{"x": 0.367, "y": 3.852}, {"x": 0.423, "y": 3.851}, {"x": 0.423, "y": 3.917}, {"x": 0.367, "y": 3.917}], "confidence": 0.9919554138183594}, {"text": "NAME:", "boundingPolygon": [{"x": 0.594, "y": 4.035}, {"x": 0.864, "y": 4.035}, {"x": 0.864, "y": 4.106}, {"x": 0.594, "y": 4.107}], "confidence": 0.9942124176025391}, {"text": "LOCATION:", "boundingPolygon": [{"x": 0.596, "y": 4.202}, {"x": 1.05, "y": 4.201}, {"x": 1.05, "y": 4.273}, {"x": 0.596, "y": 4.274}], "confidence": 0.9932366180419921}, {"text": "DESCRIPTION:", "boundingPolygon": [{"x": 0.597, "y": 4.369}, {"x": 1.179, "y": 4.368}, {"x": 1.179, "y": 4.438}, {"x": 0.598, "y": 4.439}], "confidence": 0.96341552734375}, {"text": "NAME:", "boundingPolygon": [{"x": 0.596, "y": 4.535}, {"x": 0.864, "y": 4.535}, {"x": 0.864, "y": 4.605}, {"x": 0.596, "y": 4.606}], "confidence": 0.9966371917724609}, {"text": "LOCATION:", "boundingPolygon": [{"x": 0.598, "y": 4.702}, {"x": 1.046, "y": 4.701}, {"x": 1.046, "y": 4.772}, {"x": 0.598, "y": 4.772}], "confidence": 0.9807491302490234}, {"text": "DESCRIPTION:", "boundingPolygon": [{"x": 0.598, "y": 4.869}, {"x": 1.18, "y": 4.868}, {"x": 1.18, "y": 4.938}, {"x": 0.598, "y": 4.939}], "confidence": 0.976993408203125}, {"text": "NAME:", "boundingPolygon": [{"x": 0.596, "y": 5.035}, {"x": 0.863, "y": 5.035}, {"x": 0.863, "y": 5.105}, {"x": 0.596, "y": 5.105}], "confidence": 0.995567626953125}, {"text": "LOCATION:", "boundingPolygon": [{"x": 0.596, "y": 5.202}, {"x": 1.047, "y": 5.201}, {"x": 1.047, "y": 5.272}, {"x": 0.596, "y": 5.272}], "confidence": 0.985368881225586}, {"text": "DESCRIPTION:", "boundingPolygon": [{"x": 0.598, "y": 5.366}, {"x": 1.18, "y": 5.365}, {"x": 1.18, "y": 5.434}, {"x": 0.598, "y": 5.435}], "confidence": 0.9642875671386719}, {"text": "NAME:", "boundingPolygon": [{"x": 0.597, "y": 5.534}, {"x": 0.864, "y": 5.534}, {"x": 0.864, "y": 5.604}, {"x": 0.597, "y": 5.605}], "confidence": 0.9951296997070312}, {"text": "LOCATION:", "boundingPolygon": [{"x": 0.595, "y": 5.701}, {"x": 1.048, "y": 5.701}, {"x": 1.048, "y": 5.772}, {"x": 0.595, "y": 5.772}], "confidence": 0.986498794555664}, {"text": "DESCRIPTION:", "boundingPolygon": [{"x": 0.599, "y": 5.868}, {"x": 1.179, "y": 5.867}, {"x": 1.179, "y": 5.938}, {"x": 0.599, "y": 5.939}], "confidence": 0.9346462249755859}, {"text": "NAME:", "boundingPolygon": [{"x": 0.596, "y": 6.034}, {"x": 0.864, "y": 6.034}, {"x": 0.864, "y": 6.103}, {"x": 0.597, "y": 6.104}], "confidence": 0.9946456909179687}, {"text": "LOCATION:", "boundingPolygon": [{"x": 0.595, "y": 6.203}, {"x": 1.048, "y": 6.202}, {"x": 1.048, "y": 6.271}, {"x": 0.595, "y": 6.272}], "confidence": 0.9887498474121094}, {"text": "DESCRIPTION:", "boundingPolygon": [{"x": 0.599, "y": 6.367}, {"x": 1.179, "y": 6.366}, {"x": 1.179, "y": 6.438}, {"x": 0.599, "y": 6.439}], "confidence": 0.9170886993408203}, {"text": "NAME:", "boundingPolygon": [{"x": 0.596, "y": 6.535}, {"x": 0.863, "y": 6.535}, {"x": 0.863, "y": 6.606}, {"x": 0.596, "y": 6.606}], "confidence": 0.9953426361083985}, {"text": "LOCATION:", "boundingPolygon": [{"x": 0.593, "y": 6.7}, {"x": 1.048, "y": 6.699}, {"x": 1.048, "y": 6.777}, {"x": 0.593, "y": 6.777}], "confidence": 0.97677001953125}, {"text": "DESCRIPTION:", "boundingPolygon": [{"x": 0.596, "y": 6.858}, {"x": 1.181, "y": 6.857}, {"x": 1.181, "y": 6.928}, {"x": 0.596, "y": 6.929}], "confidence": 0.978757553100586}, {"text": "UNDERLYING INSURANCE", "boundingPolygon": [{"x": 0.298, "y": 6.992}, {"x": 1.788, "y": 6.99}, {"x": 1.788, "y": 7.084}, {"x": 0.298, "y": 7.087}], "confidence": 0.9987918853759765}, {"text": "LIST ALL LIABILITY / COMPENSATION POLICIES IN FORCE TO APPLY AS UNDERLYING INSURANCE", "boundingPolygon": [{"x": 2.059, "y": 7.188}, {"x": 6.023, "y": 7.182}, {"x": 6.023, "y": 7.263}, {"x": 2.059, "y": 7.269}], "confidence": 0.9851560974121094}, {"text": "+", "boundingPolygon": [{"x": 7.993, "y": 7.188}, {"x": 8.042, "y": 7.188}, {"x": 8.042, "y": 7.232}, {"x": 7.993, "y": 7.232}], "confidence": 0.8633047485351563}, {"text": "RATING", "boundingPolygon": [{"x": 7.889, "y": 7.243}, {"x": 8.198, "y": 7.243}, {"x": 8.198, "y": 7.319}, {"x": 7.889, "y": 7.32}], "confidence": 0.9993886566162109}, {"text": "ANNUAL RENEWAL", "boundingPolygon": [{"x": 6.952, "y": 7.293}, {"x": 7.735, "y": 7.292}, {"x": 7.735, "y": 7.364}, {"x": 6.952, "y": 7.365}], "confidence": 0.9990650177001953}, {"text": "MOD", "boundingPolygon": [{"x": 7.948, "y": 7.332}, {"x": 8.14, "y": 7.332}, {"x": 8.14, "y": 7.402}, {"x": 7.948, "y": 7.402}], "confidence": 0.9979728698730469}, {"text": "POLICY EXP DATE", "boundingPolygon": [{"x": 3.976, "y": 7.344}, {"x": 4.716, "y": 7.343}, {"x": 4.716, "y": 7.419}, {"x": 3.976, "y": 7.42}], "confidence": 0.9984517669677735}, {"text": "POLICY EFF DATE", "boundingPolygon": [{"x": 3.085, "y": 7.346}, {"x": 3.81, "y": 7.345}, {"x": 3.81, "y": 7.419}, {"x": 3.085, "y": 7.42}], "confidence": 0.9988235473632813}, {"text": "LIMITS", "boundingPolygon": [{"x": 5.686, "y": 7.346}, {"x": 5.955, "y": 7.346}, {"x": 5.955, "y": 7.419}, {"x": 5.686, "y": 7.419}], "confidence": 0.9990679168701172}, {"text": "CARRIER/POLICY NUMBER", "boundingPolygon": [{"x": 1.403, "y": 7.347}, {"x": 2.533, "y": 7.345}, {"x": 2.533, "y": 7.419}, {"x": 1.403, "y": 7.421}], "confidence": 0.9286256408691407}, {"text": "TYPE", "boundingPolygon": [{"x": 0.484, "y": 7.348}, {"x": 0.704, "y": 7.347}, {"x": 0.704, "y": 7.418}, {"x": 0.485, "y": 7.418}], "confidence": 0.9991666412353516}, {"text": "PREMIUM", "boundingPolygon": [{"x": 7.15, "y": 7.37}, {"x": 7.537, "y": 7.37}, {"x": 7.537, "y": 7.44}, {"x": 7.151, "y": 7.44}], "confidence": 0.9976540374755859}, {"text": "TBD", "boundingPolygon": [{"x": 0.964, "y": 7.485}, {"x": 1.217, "y": 7.485}, {"x": 1.217, "y": 7.58}, {"x": 0.964, "y": 7.58}], "confidence": 0.9979605102539062}, {"text": "1,000,000", "boundingPolygon": [{"x": 6.284, "y": 7.485}, {"x": 6.834, "y": 7.484}, {"x": 6.834, "y": 7.599}, {"x": 6.284, "y": 7.599}], "confidence": 0.998994140625}, {"text": "$", "boundingPolygon": [{"x": 6.89, "y": 7.511}, {"x": 6.945, "y": 7.511}, {"x": 6.945, "y": 7.598}, {"x": 6.89, "y": 7.598}], "confidence": 0.9987271118164063}, {"text": "$", "boundingPolygon": [{"x": 5.792, "y": 7.514}, {"x": 5.845, "y": 7.514}, {"x": 5.845, "y": 7.595}, {"x": 5.793, "y": 7.595}], "confidence": 0.9995341491699219}, {"text": "CSL EA ACC", "boundingPolygon": [{"x": 4.844, "y": 7.516}, {"x": 5.34, "y": 7.515}, {"x": 5.341, "y": 7.588}, {"x": 4.844, "y": 7.589}], "confidence": 0.9914717102050781}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 7.688}, {"x": 5.844, "y": 7.688}, {"x": 5.844, "y": 7.768}, {"x": 5.794, "y": 7.768}], "confidence": 0.9996735382080079}, {"text": "AUTOMOBILE", "boundingPolygon": [{"x": 0.32, "y": 7.689}, {"x": 0.868, "y": 7.688}, {"x": 0.868, "y": 7.761}, {"x": 0.32, "y": 7.762}], "confidence": 0.9801231384277344}, {"text": "BI EA ACC", "boundingPolygon": [{"x": 4.845, "y": 7.69}, {"x": 5.259, "y": 7.69}, {"x": 5.259, "y": 7.764}, {"x": 4.845, "y": 7.764}], "confidence": 0.9760798645019532}, {"text": "$", "boundingPolygon": [{"x": 6.891, "y": 7.735}, {"x": 6.945, "y": 7.735}, {"x": 6.945, "y": 7.821}, {"x": 6.891, "y": 7.821}], "confidence": 0.9987354278564453}, {"text": "LIABILITY", "boundingPolygon": [{"x": 0.404, "y": 7.789}, {"x": 0.786, "y": 7.788}, {"x": 0.786, "y": 7.857}, {"x": 0.404, "y": 7.857}], "confidence": 0.9993745422363282}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 7.847}, {"x": 5.845, "y": 7.847}, {"x": 5.845, "y": 7.929}, {"x": 5.794, "y": 7.929}], "confidence": 0.9997062683105469}, {"text": "BI EA PER", "boundingPolygon": [{"x": 4.846, "y": 7.851}, {"x": 5.252, "y": 7.85}, {"x": 5.252, "y": 7.923}, {"x": 4.846, "y": 7.924}], "confidence": 0.9843495178222657}, {"text": "TBD", "boundingPolygon": [{"x": 0.961, "y": 7.939}, {"x": 1.217, "y": 7.939}, {"x": 1.218, "y": 8.037}, {"x": 0.962, "y": 8.037}], "confidence": 0.9978738403320313}, {"text": "$", "boundingPolygon": [{"x": 6.892, "y": 8.012}, {"x": 6.945, "y": 8.011}, {"x": 6.945, "y": 8.098}, {"x": 6.892, "y": 8.098}], "confidence": 0.9982405090332032}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 8.013}, {"x": 5.844, "y": 8.013}, {"x": 5.845, "y": 8.095}, {"x": 5.794, "y": 8.095}], "confidence": 0.9997331237792969}, {"text": "PD EA ACC", "boundingPolygon": [{"x": 4.846, "y": 8.017}, {"x": 5.293, "y": 8.016}, {"x": 5.293, "y": 8.089}, {"x": 4.846, "y": 8.09}], "confidence": 0.9967568969726562}, {"text": "TBD", "boundingPolygon": [{"x": 0.964, "y": 8.151}, {"x": 1.217, "y": 8.15}, {"x": 1.217, "y": 8.246}, {"x": 0.964, "y": 8.246}], "confidence": 0.9976149749755859}, {"text": "1,000,000", "boundingPolygon": [{"x": 6.284, "y": 8.151}, {"x": 6.833, "y": 8.15}, {"x": 6.833, "y": 8.265}, {"x": 6.285, "y": 8.266}], "confidence": 0.9991096496582031}, {"text": "PREM/OPS", "boundingPolygon": [{"x": 6.899, "y": 8.18}, {"x": 7.388, "y": 8.179}, {"x": 7.388, "y": 8.26}, {"x": 6.899, "y": 8.26}], "confidence": 0.8907770538330078}, {"text": "EACH OCCURRENCE $", "boundingPolygon": [{"x": 4.847, "y": 8.181}, {"x": 5.844, "y": 8.18}, {"x": 5.844, "y": 8.262}, {"x": 4.847, "y": 8.264}], "confidence": 0.9957372283935547}, {"text": "GENERAL", "boundingPolygon": [{"x": 0.398, "y": 8.246}, {"x": 0.798, "y": 8.245}, {"x": 0.798, "y": 8.319}, {"x": 0.398, "y": 8.319}], "confidence": 0.9986431121826171}, {"text": "2,000,000", "boundingPolygon": [{"x": 6.28, "y": 8.318}, {"x": 6.839, "y": 8.317}, {"x": 6.839, "y": 8.432}, {"x": 6.28, "y": 8.433}], "confidence": 0.9992632293701171}, {"text": "$", "boundingPolygon": [{"x": 6.892, "y": 8.344}, {"x": 6.944, "y": 8.344}, {"x": 6.944, "y": 8.431}, {"x": 6.892, "y": 8.431}], "confidence": 0.9980497741699219}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 8.347}, {"x": 5.844, "y": 8.347}, {"x": 5.844, "y": 8.429}, {"x": 5.794, "y": 8.429}], "confidence": 0.9995525360107422}, {"text": "LIABILITY", "boundingPolygon": [{"x": 0.407, "y": 8.348}, {"x": 0.788, "y": 8.348}, {"x": 0.788, "y": 8.421}, {"x": 0.407, "y": 8.422}], "confidence": 0.9992224884033203}, {"text": "GENERAL AGGR", "boundingPolygon": [{"x": 4.845, "y": 8.349}, {"x": 5.512, "y": 8.348}, {"x": 5.512, "y": 8.424}, {"x": 4.845, "y": 8.425}], "confidence": 0.9977869415283203}, {"text": "POLICY TYPE", "boundingPolygon": [{"x": 0.323, "y": 8.45}, {"x": 0.869, "y": 8.449}, {"x": 0.869, "y": 8.522}, {"x": 0.323, "y": 8.523}], "confidence": 0.99661376953125}, {"text": "PROD & COMP OPS", "boundingPolygon": [{"x": 4.846, "y": 8.459}, {"x": 5.637, "y": 8.458}, {"x": 5.637, "y": 8.532}, {"x": 4.846, "y": 8.533}], "confidence": 0.9962239074707031}, {"text": "2,000,000", "boundingPolygon": [{"x": 6.28, "y": 8.484}, {"x": 6.835, "y": 8.484}, {"x": 6.835, "y": 8.597}, {"x": 6.281, "y": 8.598}], "confidence": 0.998899917602539}, {"text": "$", "boundingPolygon": [{"x": 5.793, "y": 8.514}, {"x": 5.844, "y": 8.513}, {"x": 5.844, "y": 8.595}, {"x": 5.794, "y": 8.595}], "confidence": 0.9995420837402343}, {"text": "PRODUCTS", "boundingPolygon": [{"x": 6.896, "y": 8.516}, {"x": 7.361, "y": 8.516}, {"x": 7.361, "y": 8.591}, {"x": 6.896, "y": 8.592}], "confidence": 0.9985740661621094}, {"text": "AGGREGATE", "boundingPolygon": [{"x": 4.843, "y": 8.54}, {"x": 5.378, "y": 8.539}, {"x": 5.378, "y": 8.61}, {"x": 4.843, "y": 8.611}], "confidence": 0.9988801574707031}, {"text": "PERSONAL & ADV", "boundingPolygon": [{"x": 4.848, "y": 8.626}, {"x": 5.573, "y": 8.625}, {"x": 5.573, "y": 8.7}, {"x": 4.848, "y": 8.701}], "confidence": 0.9971001434326172}, {"text": "OCCUR", "boundingPolygon": [{"x": 0.546, "y": 8.65}, {"x": 0.853, "y": 8.649}, {"x": 0.853, "y": 8.72}, {"x": 0.546, "y": 8.72}], "confidence": 0.9993447875976562}, {"text": "1,000,000", "boundingPolygon": [{"x": 6.284, "y": 8.65}, {"x": 6.835, "y": 8.649}, {"x": 6.835, "y": 8.765}, {"x": 6.284, "y": 8.766}], "confidence": 0.998928451538086}, {"text": "$", "boundingPolygon": [{"x": 6.892, "y": 8.677}, {"x": 6.943, "y": 8.677}, {"x": 6.943, "y": 8.764}, {"x": 6.892, "y": 8.764}], "confidence": 0.9974818420410156}, {"text": "$", "boundingPolygon": [{"x": 5.793, "y": 8.68}, {"x": 5.844, "y": 8.68}, {"x": 5.844, "y": 8.76}, {"x": 5.794, "y": 8.76}], "confidence": 0.9996786499023438}, {"text": "INJURY", "boundingPolygon": [{"x": 4.85, "y": 8.705}, {"x": 5.146, "y": 8.704}, {"x": 5.146, "y": 8.779}, {"x": 4.85, "y": 8.779}], "confidence": 0.9994486236572265}, {"text": "DAMAGE TO RENTED", "boundingPolygon": [{"x": 4.85, "y": 8.782}, {"x": 5.637, "y": 8.781}, {"x": 5.637, "y": 8.852}, {"x": 4.85, "y": 8.853}], "confidence": 0.999300308227539}, {"text": "CLAIMS", "boundingPolygon": [{"x": 0.546, "y": 8.791}, {"x": 0.854, "y": 8.791}, {"x": 0.854, "y": 8.86}, {"x": 0.546, "y": 8.86}], "confidence": 0.9980319976806641}, {"text": "100,000", "boundingPolygon": [{"x": 6.387, "y": 8.816}, {"x": 6.838, "y": 8.816}, {"x": 6.839, "y": 8.932}, {"x": 6.387, "y": 8.933}], "confidence": 0.9995284271240235}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 8.848}, {"x": 5.844, "y": 8.848}, {"x": 5.844, "y": 8.928}, {"x": 5.794, "y": 8.928}], "confidence": 0.9996555328369141}, {"text": "OTHER", "boundingPolygon": [{"x": 6.896, "y": 8.85}, {"x": 7.188, "y": 8.849}, {"x": 7.188, "y": 8.924}, {"x": 6.896, "y": 8.925}], "confidence": 0.9997843933105469}, {"text": "PREMISES", "boundingPolygon": [{"x": 4.844, "y": 8.876}, {"x": 5.24, "y": 8.875}, {"x": 5.24, "y": 8.942}, {"x": 4.844, "y": 8.943}], "confidence": 0.9967143249511718}, {"text": "MADE", "boundingPolygon": [{"x": 0.547, "y": 8.877}, {"x": 0.784, "y": 8.877}, {"x": 0.784, "y": 8.945}, {"x": 0.547, "y": 8.945}], "confidence": 0.9991327667236328}, {"text": "TBD", "boundingPolygon": [{"x": 0.962, "y": 8.952}, {"x": 1.218, "y": 8.952}, {"x": 1.218, "y": 9.051}, {"x": 0.962, "y": 9.052}], "confidence": 0.9976520538330078}, {"text": "0", "boundingPolygon": [{"x": 6.766, "y": 8.985}, {"x": 6.834, "y": 8.985}, {"x": 6.834, "y": 9.083}, {"x": 6.766, "y": 9.083}], "confidence": 0.9807769012451172}, {"text": "$", "boundingPolygon": [{"x": 6.891, "y": 9.012}, {"x": 6.944, "y": 9.011}, {"x": 6.944, "y": 9.096}, {"x": 6.891, "y": 9.096}], "confidence": 0.9980937957763671}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 9.015}, {"x": 5.844, "y": 9.015}, {"x": 5.844, "y": 9.095}, {"x": 5.794, "y": 9.095}], "confidence": 0.9995950317382812}, {"text": "MEDICAL EXPENSE", "boundingPolygon": [{"x": 4.845, "y": 9.016}, {"x": 5.628, "y": 9.015}, {"x": 5.628, "y": 9.09}, {"x": 4.845, "y": 9.091}], "confidence": 0.9986679840087891}, {"text": "TBD", "boundingPolygon": [{"x": 0.962, "y": 9.149}, {"x": 1.217, "y": 9.149}, {"x": 1.217, "y": 9.246}, {"x": 0.963, "y": 9.246}], "confidence": 0.9977914428710938}, {"text": "1,000,000", "boundingPolygon": [{"x": 6.284, "y": 9.15}, {"x": 6.834, "y": 9.149}, {"x": 6.834, "y": 9.265}, {"x": 6.284, "y": 9.266}], "confidence": 0.9989470672607422}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 9.182}, {"x": 5.844, "y": 9.181}, {"x": 5.844, "y": 9.261}, {"x": 5.794, "y": 9.261}], "confidence": 0.9994598388671875}, {"text": "EACH ACCIDENT", "boundingPolygon": [{"x": 4.847, "y": 9.184}, {"x": 5.526, "y": 9.183}, {"x": 5.526, "y": 9.258}, {"x": 4.847, "y": 9.259}], "confidence": 0.9985731506347656}, {"text": "DISEASE", "boundingPolygon": [{"x": 4.846, "y": 9.286}, {"x": 5.2, "y": 9.286}, {"x": 5.2, "y": 9.355}, {"x": 4.846, "y": 9.356}], "confidence": 0.9977941131591797}, {"text": "EMPLOYERS", "boundingPolygon": [{"x": 0.339, "y": 9.3}, {"x": 0.853, "y": 9.299}, {"x": 0.853, "y": 9.376}, {"x": 0.339, "y": 9.377}], "confidence": 0.9989993286132812}, {"text": "1,000,000", "boundingPolygon": [{"x": 6.284, "y": 9.317}, {"x": 6.837, "y": 9.316}, {"x": 6.837, "y": 9.432}, {"x": 6.285, "y": 9.432}], "confidence": 0.998819580078125}, {"text": "$", "boundingPolygon": [{"x": 6.891, "y": 9.345}, {"x": 6.945, "y": 9.345}, {"x": 6.945, "y": 9.428}, {"x": 6.891, "y": 9.429}], "confidence": 0.9969475555419922}, {"text": "$", "boundingPolygon": [{"x": 5.794, "y": 9.349}, {"x": 5.844, "y": 9.349}, {"x": 5.844, "y": 9.428}, {"x": 5.794, "y": 9.428}], "confidence": 0.999520034790039}, {"text": "EACH EMPLOYEE", "boundingPolygon": [{"x": 4.846, "y": 9.368}, {"x": 5.556, "y": 9.367}, {"x": 5.556, "y": 9.44}, {"x": 4.846, "y": 9.441}], "confidence": 0.9981854248046875}, {"text": "LIABILITY", "boundingPolygon": [{"x": 0.408, "y": 9.397}, {"x": 0.785, "y": 9.396}, {"x": 0.785, "y": 9.468}, {"x": 0.408, "y": 9.468}], "confidence": 0.9992717742919922}, {"text": "DISEASE", "boundingPolygon": [{"x": 4.843, "y": 9.454}, {"x": 5.199, "y": 9.453}, {"x": 5.199, "y": 9.528}, {"x": 4.843, "y": 9.529}], "confidence": 0.9966993713378907}, {"text": "TBD", "boundingPolygon": [{"x": 0.962, "y": 9.458}, {"x": 1.216, "y": 9.458}, {"x": 1.216, "y": 9.557}, {"x": 0.962, "y": 9.558}], "confidence": 0.9977268218994141}, {"text": "1,000,000", "boundingPolygon": [{"x": 6.285, "y": 9.484}, {"x": 6.835, "y": 9.483}, {"x": 6.835, "y": 9.598}, {"x": 6.285, "y": 9.599}], "confidence": 0.9990218353271484}, {"text": "$", "boundingPolygon": [{"x": 5.793, "y": 9.515}, {"x": 5.845, "y": 9.515}, {"x": 5.845, "y": 9.594}, {"x": 5.793, "y": 9.594}], "confidence": 0.9991739654541015}, {"text": "POLICY LIMIT", "boundingPolygon": [{"x": 4.846, "y": 9.534}, {"x": 5.39, "y": 9.533}, {"x": 5.39, "y": 9.606}, {"x": 4.847, "y": 9.607}], "confidence": 0.9989556884765625}, {"text": "$", "boundingPolygon": [{"x": 6.889, "y": 9.736}, {"x": 6.945, "y": 9.736}, {"x": 6.945, "y": 9.819}, {"x": 6.889, "y": 9.819}], "confidence": 0.9982376098632812}, {"text": "$", "boundingPolygon": [{"x": 6.891, "y": 10.073}, {"x": 6.944, "y": 10.073}, {"x": 6.944, "y": 10.152}, {"x": 6.891, "y": 10.152}], "confidence": 0.9992693328857422}, {"text": "ACORD 131 (2009/10)", "boundingPolygon": [{"x": 0.297, "y": 10.31}, {"x": 1.479, "y": 10.309}, {"x": 1.48, "y": 10.429}, {"x": 0.297, "y": 10.43}], "confidence": 0.9983099365234375}, {"text": "Page 1 of 5", "boundingPolygon": [{"x": 3.955, "y": 10.31}, {"x": 4.537, "y": 10.309}, {"x": 4.537, "y": 10.424}, {"x": 3.955, "y": 10.425}], "confidence": 0.9979203033447266}, {"text": "© 1991-2009 ACORD CORPORATION. All rights reserved.", "boundingPolygon": [{"x": 4.817, "y": 10.311}, {"x": 8.003, "y": 10.306}, {"x": 8.003, "y": 10.427}, {"x": 4.817, "y": 10.432}], "confidence": 0.9402392578125}, {"text": "ATTACH TO ACORD 125 AND ACORD 126", "boundingPolygon": [{"x": 3.073, "y": 10.477}, {"x": 5.409, "y": 10.474}, {"x": 5.409, "y": 10.573}, {"x": 3.073, "y": 10.576}], "confidence": 0.9994943237304688}, {"text": "The ACORD name and logo are registered marks of ACORD", "boundingPolygon": [{"x": 2.586, "y": 10.643}, {"x": 5.891, "y": 10.638}, {"x": 5.892, "y": 10.76}, {"x": 2.586, "y": 10.764}], "confidence": 0.9994136047363281}], "checkboxes": []}, {"width": 8.5, "height": 11, "rotation": 0, "transform": {"a": 1, "c": 0, "e": 0, "b": 0, "d": 1, "f": 0}, "lines": [{"text": "SKEARNEY", "boundingPolygon": [{"x": 7.522, "y": 0.476}, {"x": 8.219, "y": 0.475}, {"x": 8.219, "y": 0.595}, {"x": 7.522, "y": 0.596}], "confidence": 0.9982156372070312}, {"text": "AGENCY CUSTOMER ID: PROJCAJ-01", "boundingPolygon": [{"x": 3.889, "y": 0.48}, {"x": 6.009, "y": 0.477}, {"x": 6.01, "y": 0.588}, {"x": 3.889, "y": 0.59}], "confidence": 0.9959078216552735}, {"text": "UNDERLYING INSURANCE (continued)", "boundingPolygon": [{"x": 0.244, "y": 0.57}, {"x": 2.407, "y": 0.568}, {"x": 2.408, "y": 0.695}, {"x": 0.245, "y": 0.698}], "confidence": 0.9988487243652344}, {"text": "UNDERLYING GENERAL LIABILITY INFORMATION (Explain all \"YES\" responses)", "boundingPolygon": [{"x": 0.289, "y": 0.762}, {"x": 3.458, "y": 0.758}, {"x": 3.458, "y": 0.854}, {"x": 0.289, "y": 0.858}], "confidence": 0.9966693878173828}, {"text": "WITHIN AGGREGATE LIMITS?", "boundingPolygon": [{"x": 2.441, "y": 0.914}, {"x": 3.869, "y": 0.912}, {"x": 3.869, "y": 1.008}, {"x": 2.441, "y": 1.009}], "confidence": 0.9971281433105469}, {"text": "A SEPARATE LIMIT?", "boundingPolygon": [{"x": 4.54, "y": 0.914}, {"x": 5.514, "y": 0.913}, {"x": 5.514, "y": 1.008}, {"x": 4.54, "y": 1.009}], "confidence": 0.9978420257568359}, {"text": "1. ARE DEFENSE COSTS:", "boundingPolygon": [{"x": 0.303, "y": 0.915}, {"x": 1.603, "y": 0.913}, {"x": 1.603, "y": 1.01}, {"x": 0.303, "y": 1.011}], "confidence": 0.9944434356689453}, {"text": "UNLIMITED?", "boundingPolygon": [{"x": 6.145, "y": 0.915}, {"x": 6.743, "y": 0.914}, {"x": 6.743, "y": 1.009}, {"x": 6.145, "y": 1.009}], "confidence": 0.9973038482666016}, {"text": "2. INDICATE THE EDITION DATE OF THE ISO FORM OR <PERSON><PERSON><PERSON><PERSON> FILING FOR THE UNDERLYING COVERAGE:", "boundingPolygon": [{"x": 0.297, "y": 1.081}, {"x": 5.561, "y": 1.075}, {"x": 5.561, "y": 1.176}, {"x": 0.297, "y": 1.182}], "confidence": 0.9878865814208985}, {"text": "3. HAS ANY PRODUCT, WOR<PERSON>, ACCIDENT, OR LOCATION BEEN EXCLUDED, UNINSURED OR SELF INSURED FROM ANY PREVIOUS COVERAGE? (Y/N)", "boundingPolygon": [{"x": 0.298, "y": 1.248}, {"x": 7.664, "y": 1.239}, {"x": 7.664, "y": 1.355}, {"x": 0.298, "y": 1.364}], "confidence": 0.9625701904296875}, {"text": "4. FOR <PERSON>LAIMS MADE, INDICATE RETROACTIVE DATE OF CURRENT UNDERLYING POLICY:", "boundingPolygon": [{"x": 0.298, "y": 2.415}, {"x": 4.768, "y": 2.41}, {"x": 4.768, "y": 2.515}, {"x": 0.299, "y": 2.52}], "confidence": 0.9970745086669922}, {"text": "5. FOR CLAIMS MADE, INDICATE ENTRY DATE INTO UNINTERRUPTED CLAIMS MADE COVERAGE:", "boundingPolygon": [{"x": 0.299, "y": 2.583}, {"x": 5.093, "y": 2.577}, {"x": 5.093, "y": 2.68}, {"x": 0.299, "y": 2.686}], "confidence": 0.9843659973144532}, {"text": "6. FOR CLAIMS MADE, WAS \"TAIL\" COVERAGE PURCHASED FOR ANY PREVIOUS PRIMARY OR EXCESS POLICY? (Y / N)", "boundingPolygon": [{"x": 0.3, "y": 2.749}, {"x": 6.169, "y": 2.742}, {"x": 6.169, "y": 2.856}, {"x": 0.3, "y": 2.863}], "confidence": 0.972265625}, {"text": "EFF. DATE:", "boundingPolygon": [{"x": 6.574, "y": 2.756}, {"x": 7.098, "y": 2.755}, {"x": 7.098, "y": 2.847}, {"x": 6.574, "y": 2.848}], "confidence": 0.9769618225097656}, {"text": "<PERSON><PERSON><PERSON> ALL COVERAGES IN UNDERLYING POLICIES. AL<PERSON> CHECK IF ANY EXPOSURES ARE PRESENT FOR EACH COVERAGE. PROVIDE AN EXPLANATION. EXPLAIN IF", "boundingPolygon": [{"x": 0.843, "y": 3.142}, {"x": 7.651, "y": 3.135}, {"x": 7.651, "y": 3.225}, {"x": 0.843, "y": 3.233}], "confidence": 0.9917359161376953}, {"text": "DIFFERENT LIMITS, EXTENSION<PERSON>, OR EXCLUSIONS. EXPLAIN ANY SPECIAL COVERAGES BEYOND STANDARD FORMS. EXPLAIN ALL EXPOSURES.", "boundingPolygon": [{"x": 0.843, "y": 3.245}, {"x": 6.764, "y": 3.238}, {"x": 6.764, "y": 3.328}, {"x": 0.843, "y": 3.335}], "confidence": 0.9847650146484375}, {"text": "CHECK IF APPROPRIATE", "boundingPolygon": [{"x": 1.041, "y": 3.402}, {"x": 2.052, "y": 3.401}, {"x": 2.052, "y": 3.481}, {"x": 1.041, "y": 3.482}], "confidence": 0.9985597991943359}, {"text": "COVERAGE", "boundingPolygon": [{"x": 2.891, "y": 3.402}, {"x": 3.37, "y": 3.401}, {"x": 3.37, "y": 3.481}, {"x": 2.891, "y": 3.482}], "confidence": 0.9991989135742188}, {"text": "COVERAGE", "boundingPolygon": [{"x": 5.891, "y": 3.404}, {"x": 6.372, "y": 3.404}, {"x": 6.372, "y": 3.481}, {"x": 5.891, "y": 3.481}], "confidence": 0.9991614532470703}, {"text": "EXPOSURE", "boundingPolygon": [{"x": 5.345, "y": 3.405}, {"x": 5.809, "y": 3.404}, {"x": 5.809, "y": 3.481}, {"x": 5.345, "y": 3.482}], "confidence": 0.9991410064697266}, {"text": "EXPOSURE", "boundingPolygon": [{"x": 7.744, "y": 3.405}, {"x": 8.204, "y": 3.404}, {"x": 8.204, "y": 3.481}, {"x": 7.744, "y": 3.481}], "confidence": 0.9988312530517578}, {"text": "ANY AUTO (SYMBOL 1)", "boundingPolygon": [{"x": 0.492, "y": 3.591}, {"x": 1.42, "y": 3.59}, {"x": 1.42, "y": 3.69}, {"x": 0.492, "y": 3.691}], "confidence": 0.9913803100585937}, {"text": "CARE, CUSTODY, CONTROL", "boundingPolygon": [{"x": 3.096, "y": 3.596}, {"x": 4.241, "y": 3.594}, {"x": 4.241, "y": 3.685}, {"x": 3.096, "y": 3.686}], "confidence": 0.9627633666992188}, {"text": "PROFESSIONAL LIABILITY (E&O)", "boundingPolygon": [{"x": 6.097, "y": 3.596}, {"x": 7.404, "y": 3.594}, {"x": 7.404, "y": 3.692}, {"x": 6.097, "y": 3.694}], "confidence": 0.9972792053222657}, {"text": "EMPLOYEE BENEFIT LIABILITY", "boundingPolygon": [{"x": 3.095, "y": 3.761}, {"x": 4.333, "y": 3.759}, {"x": 4.333, "y": 3.844}, {"x": 3.095, "y": 3.845}], "confidence": 0.9972469329833984}, {"text": "VENDORS LIABILITY", "boundingPolygon": [{"x": 6.092, "y": 3.762}, {"x": 6.917, "y": 3.761}, {"x": 6.917, "y": 3.843}, {"x": 6.092, "y": 3.844}], "confidence": 0.9981063079833984}, {"text": "CGL CLAIMS MADE", "boundingPolygon": [{"x": 0.491, "y": 3.763}, {"x": 1.319, "y": 3.762}, {"x": 1.319, "y": 3.843}, {"x": 0.491, "y": 3.843}], "confidence": 0.9959902191162109}, {"text": "FOREIGN LIABILITY/TRAVEL", "boundingPolygon": [{"x": 3.095, "y": 3.927}, {"x": 4.28, "y": 3.926}, {"x": 4.28, "y": 4.012}, {"x": 3.095, "y": 4.013}], "confidence": 0.8208501434326172}, {"text": "CGL OCCURRENCE", "boundingPolygon": [{"x": 0.491, "y": 3.928}, {"x": 1.34, "y": 3.927}, {"x": 1.34, "y": 4.009}, {"x": 0.491, "y": 4.01}], "confidence": 0.9803475952148437}, {"text": "WATERCRAFT LIABILITY", "boundingPolygon": [{"x": 6.093, "y": 3.929}, {"x": 7.084, "y": 3.928}, {"x": 7.085, "y": 4.01}, {"x": 6.093, "y": 4.011}], "confidence": 0.9954676055908203}, {"text": "COVERAGE", "boundingPolygon": [{"x": 0.293, "y": 4.071}, {"x": 0.769, "y": 4.07}, {"x": 0.769, "y": 4.148}, {"x": 0.293, "y": 4.149}], "confidence": 0.9993209838867188}, {"text": "EXPOSURE", "boundingPolygon": [{"x": 2.339, "y": 4.071}, {"x": 2.799, "y": 4.07}, {"x": 2.799, "y": 4.147}, {"x": 2.339, "y": 4.147}], "confidence": 0.9991111755371094}, {"text": "GARAGEKEEPERS LIABILITY", "boundingPolygon": [{"x": 3.093, "y": 4.096}, {"x": 4.254, "y": 4.094}, {"x": 4.254, "y": 4.178}, {"x": 3.093, "y": 4.179}], "confidence": 0.9966674041748047}, {"text": "AIRCRAFT LIABILITY", "boundingPolygon": [{"x": 0.493, "y": 4.262}, {"x": 1.319, "y": 4.261}, {"x": 1.319, "y": 4.344}, {"x": 0.493, "y": 4.345}], "confidence": 0.9985939025878906}, {"text": "INCIDENTAL MEDICAL MALPRACTICE", "boundingPolygon": [{"x": 3.094, "y": 4.263}, {"x": 4.601, "y": 4.262}, {"x": 4.601, "y": 4.343}, {"x": 3.094, "y": 4.344}], "confidence": 0.9959300231933593}, {"text": "LIQUOR LIABILITY", "boundingPolygon": [{"x": 3.093, "y": 4.428}, {"x": 3.825, "y": 4.427}, {"x": 3.825, "y": 4.508}, {"x": 3.093, "y": 4.509}], "confidence": 0.994691390991211}, {"text": "AIRCRAFT PASSENGER LIABILITY", "boundingPolygon": [{"x": 0.489, "y": 4.43}, {"x": 1.859, "y": 4.428}, {"x": 1.859, "y": 4.51}, {"x": 0.489, "y": 4.511}], "confidence": 0.9983049774169922}, {"text": "X", "boundingPolygon": [{"x": 0.29, "y": 4.544}, {"x": 0.382, "y": 4.544}, {"x": 0.382, "y": 4.653}, {"x": 0.29, "y": 4.653}], "confidence": 0.8010804748535156}, {"text": "POLLUTION LIABILITY", "boundingPolygon": [{"x": 3.093, "y": 4.595}, {"x": 3.975, "y": 4.594}, {"x": 3.975, "y": 4.676}, {"x": 3.093, "y": 4.677}], "confidence": 0.9980327606201171}, {"text": "ADDITIONAL INTERESTS", "boundingPolygon": [{"x": 0.49, "y": 4.596}, {"x": 1.49, "y": 4.595}, {"x": 1.49, "y": 4.676}, {"x": 0.49, "y": 4.677}], "confidence": 0.9985920715332032}, {"text": "UNDERLYING INSURANCE COVERAGE INFORMATION (INCLUDE ALL RESTRICTIONS; e.g. LASER ENDORSEMENTS, DISCRIMINATION, SUBROGATION WAIVERS, OR EXTENSIONS OF", "boundingPolygon": [{"x": 0.293, "y": 4.728}, {"x": 7.571, "y": 4.72}, {"x": 7.571, "y": 4.817}, {"x": 0.293, "y": 4.825}], "confidence": 0.9813412475585938}, {"text": "COVERAGE) Attach ACORD 101, Additional Remarks Schedule, if more space is required.", "boundingPolygon": [{"x": 0.292, "y": 4.826}, {"x": 3.685, "y": 4.823}, {"x": 3.685, "y": 4.92}, {"x": 0.292, "y": 4.924}], "confidence": 0.9952516174316406}, {"text": "PREVIOUS EXPERIENCE: (GIVE DETAILS OF ALL LIABILITY CLAIMS EXCEEDING $10,000 OR OCCURRENCES THAT MAY GIVE RISE TO CLAIMS, DURING THE PAST FIVE (5) YEARS,", "boundingPolygon": [{"x": 0.293, "y": 5.974}, {"x": 7.462, "y": 5.966}, {"x": 7.462, "y": 6.072}, {"x": 0.293, "y": 6.079}], "confidence": 0.9934122467041016}, {"text": "WHETHER INSURED OR NOT. SPECIFY DATE, CO<PERSON><PERSON><PERSON>, DESC<PERSON>PTION, AMOUNT PAID, AMOUNT OUTSTANDING) Attach ACORD 101, Additional Remarks Schedule, if more space is required.", "boundingPolygon": [{"x": 0.289, "y": 6.076}, {"x": 7.843, "y": 6.068}, {"x": 7.843, "y": 6.171}, {"x": 0.289, "y": 6.18}], "confidence": 0.9700334930419922}, {"text": "NO SUCH CLAIMS", "boundingPolygon": [{"x": 0.495, "y": 7.178}, {"x": 1.217, "y": 7.177}, {"x": 1.217, "y": 7.259}, {"x": 0.495, "y": 7.26}], "confidence": 0.9976895141601563}, {"text": "CARE, CUSTODY, CONTROL", "boundingPolygon": [{"x": 0.295, "y": 7.324}, {"x": 1.907, "y": 7.322}, {"x": 1.907, "y": 7.44}, {"x": 0.296, "y": 7.442}], "confidence": 0.9861037445068359}, {"text": "LOC", "boundingPolygon": [{"x": 0.332, "y": 7.508}, {"x": 0.511, "y": 7.508}, {"x": 0.511, "y": 7.589}, {"x": 0.332, "y": 7.59}], "confidence": 0.9979890441894531}, {"text": "PROPERTY TYPE", "boundingPolygon": [{"x": 0.697, "y": 7.511}, {"x": 1.395, "y": 7.51}, {"x": 1.395, "y": 7.589}, {"x": 0.697, "y": 7.59}], "confidence": 0.998717041015625}, {"text": "SQ FT OF BLDG OCC", "boundingPolygon": [{"x": 7.15, "y": 7.511}, {"x": 7.993, "y": 7.511}, {"x": 7.993, "y": 7.589}, {"x": 7.15, "y": 7.59}], "confidence": 0.9927906799316406}, {"text": "VALUE", "boundingPolygon": [{"x": 2.576, "y": 7.518}, {"x": 2.866, "y": 7.518}, {"x": 2.866, "y": 7.6}, {"x": 2.576, "y": 7.6}], "confidence": 0.9975282287597657}, {"text": "D*", "boundingPolygon": [{"x": 5.648, "y": 7.519}, {"x": 5.747, "y": 7.519}, {"x": 5.747, "y": 7.599}, {"x": 5.648, "y": 7.599}], "confidence": 0.9759297180175781}, {"text": "C*", "boundingPolygon": [{"x": 4.349, "y": 7.521}, {"x": 4.446, "y": 7.521}, {"x": 4.446, "y": 7.597}, {"x": 4.349, "y": 7.597}], "confidence": 0.9718791198730469}, {"text": "A*", "boundingPolygon": [{"x": 3.946, "y": 7.522}, {"x": 4.047, "y": 7.521}, {"x": 4.047, "y": 7.599}, {"x": 3.946, "y": 7.599}], "confidence": 0.9949420166015625}, {"text": "B*", "boundingPolygon": [{"x": 4.148, "y": 7.522}, {"x": 4.244, "y": 7.522}, {"x": 4.244, "y": 7.597}, {"x": 4.148, "y": 7.598}], "confidence": 0.9852617645263672}, {"text": "REAL", "boundingPolygon": [{"x": 0.844, "y": 7.68}, {"x": 1.071, "y": 7.68}, {"x": 1.071, "y": 7.758}, {"x": 0.844, "y": 7.758}], "confidence": 0.996201171875}, {"text": "PERSONAL", "boundingPolygon": [{"x": 0.843, "y": 7.85}, {"x": 1.306, "y": 7.849}, {"x": 1.306, "y": 7.924}, {"x": 0.843, "y": 7.925}], "confidence": 0.9984370422363281}, {"text": "OCCUPANCY /DESCRIPTION OF PERSONAL PROPERTY", "boundingPolygon": [{"x": 0.314, "y": 7.985}, {"x": 2.543, "y": 7.983}, {"x": 2.543, "y": 8.064}, {"x": 0.315, "y": 8.066}], "confidence": 0.9048709106445313}, {"text": "*APPLICANT: [A] IS HELD HARMLESS IN THE LEASE, [B] HAS A WAIVER OF SUBROGATION, [C] IS A NAMED INSURED IN THE FIRE POLICY, [D] OTHER (specify)", "boundingPolygon": [{"x": 0.457, "y": 8.664}, {"x": 8.022, "y": 8.656}, {"x": 8.022, "y": 8.775}, {"x": 0.457, "y": 8.783}], "confidence": 0.9912849426269531}, {"text": "VEHICLES", "boundingPolygon": [{"x": 0.293, "y": 8.823}, {"x": 0.88, "y": 8.823}, {"x": 0.88, "y": 8.927}, {"x": 0.293, "y": 8.927}], "confidence": 0.9988148498535157}, {"text": "RADIUS (MILES)", "boundingPolygon": [{"x": 7.172, "y": 9.022}, {"x": 7.821, "y": 9.022}, {"x": 7.821, "y": 9.113}, {"x": 7.172, "y": 9.114}], "confidence": 0.9980010986328125}, {"text": "# NON-", "boundingPolygon": [{"x": 2.151, "y": 9.062}, {"x": 2.442, "y": 9.061}, {"x": 2.442, "y": 9.137}, {"x": 2.151, "y": 9.138}], "confidence": 0.956826171875}, {"text": "TYPE", "boundingPolygon": [{"x": 0.777, "y": 9.11}, {"x": 1.003, "y": 9.11}, {"x": 1.003, "y": 9.188}, {"x": 0.777, "y": 9.189}], "confidence": 0.9989311981201172}, {"text": "PROPERTY HAULED", "boundingPolygon": [{"x": 4.487, "y": 9.11}, {"x": 5.307, "y": 9.11}, {"x": 5.307, "y": 9.191}, {"x": 4.487, "y": 9.192}], "confidence": 0.9989260101318359}, {"text": "# OWNED", "boundingPolygon": [{"x": 1.598, "y": 9.111}, {"x": 1.989, "y": 9.111}, {"x": 1.99, "y": 9.188}, {"x": 1.598, "y": 9.188}], "confidence": 0.9841698455810547}, {"text": "# LEASED", "boundingPolygon": [{"x": 2.589, "y": 9.111}, {"x": 2.997, "y": 9.111}, {"x": 2.997, "y": 9.188}, {"x": 2.589, "y": 9.188}], "confidence": 0.9734302520751953}, {"text": "INTER-", "boundingPolygon": [{"x": 7.352, "y": 9.115}, {"x": 7.641, "y": 9.115}, {"x": 7.641, "y": 9.196}, {"x": 7.352, "y": 9.196}], "confidence": 0.9645098114013672}, {"text": "LONG", "boundingPolygon": [{"x": 7.878, "y": 9.12}, {"x": 8.118, "y": 9.119}, {"x": 8.118, "y": 9.193}, {"x": 7.878, "y": 9.193}], "confidence": 0.9996852111816407}, {"text": "LOCAL", "boundingPolygon": [{"x": 6.85, "y": 9.15}, {"x": 7.147, "y": 9.149}, {"x": 7.147, "y": 9.231}, {"x": 6.85, "y": 9.231}], "confidence": 0.9984290313720703}, {"text": "OWNED", "boundingPolygon": [{"x": 2.137, "y": 9.159}, {"x": 2.457, "y": 9.158}, {"x": 2.457, "y": 9.234}, {"x": 2.137, "y": 9.235}], "confidence": 0.9996938323974609}, {"text": "MEDIATE", "boundingPolygon": [{"x": 7.31, "y": 9.195}, {"x": 7.688, "y": 9.194}, {"x": 7.688, "y": 9.272}, {"x": 7.31, "y": 9.272}], "confidence": 0.9994324493408203}, {"text": "DISTANCE", "boundingPolygon": [{"x": 7.785, "y": 9.197}, {"x": 8.214, "y": 9.196}, {"x": 8.215, "y": 9.272}, {"x": 7.785, "y": 9.273}], "confidence": 0.9989395904541015}, {"text": "PRIVATE PASSENGER", "boundingPolygon": [{"x": 0.444, "y": 9.346}, {"x": 1.347, "y": 9.345}, {"x": 1.347, "y": 9.428}, {"x": 0.444, "y": 9.429}], "confidence": 0.99768798828125}, {"text": "LIGHT", "boundingPolygon": [{"x": 0.891, "y": 9.511}, {"x": 1.149, "y": 9.511}, {"x": 1.149, "y": 9.594}, {"x": 0.891, "y": 9.595}], "confidence": 0.9985202026367187}, {"text": "MEDIUM", "boundingPolygon": [{"x": 0.893, "y": 9.681}, {"x": 1.238, "y": 9.68}, {"x": 1.238, "y": 9.763}, {"x": 0.893, "y": 9.763}], "confidence": 0.9977153778076172}, {"text": "TRUCKS", "boundingPolygon": [{"x": 0.369, "y": 9.761}, {"x": 0.721, "y": 9.76}, {"x": 0.721, "y": 9.844}, {"x": 0.369, "y": 9.844}], "confidence": 0.9990815734863281}, {"text": "HEAVY", "boundingPolygon": [{"x": 0.893, "y": 9.846}, {"x": 1.184, "y": 9.846}, {"x": 1.184, "y": 9.929}, {"x": 0.893, "y": 9.929}], "confidence": 0.9991708374023438}, {"text": "EX. HEAVY", "boundingPolygon": [{"x": 0.895, "y": 10.014}, {"x": 1.34, "y": 10.013}, {"x": 1.34, "y": 10.095}, {"x": 0.895, "y": 10.095}], "confidence": 0.9935270690917969}, {"text": "HEAVY", "boundingPolygon": [{"x": 0.892, "y": 10.18}, {"x": 1.184, "y": 10.18}, {"x": 1.184, "y": 10.262}, {"x": 0.892, "y": 10.263}], "confidence": 0.9991928100585937}, {"text": "TRUCKS", "boundingPolygon": [{"x": 0.344, "y": 10.212}, {"x": 0.715, "y": 10.212}, {"x": 0.715, "y": 10.292}, {"x": 0.344, "y": 10.292}], "confidence": 0.9954645538330078}, {"text": "TRACTORS", "boundingPolygon": [{"x": 0.313, "y": 10.311}, {"x": 0.779, "y": 10.31}, {"x": 0.779, "y": 10.389}, {"x": 0.313, "y": 10.389}], "confidence": 0.9977070617675782}, {"text": "EX. HEAVY", "boundingPolygon": [{"x": 0.895, "y": 10.347}, {"x": 1.341, "y": 10.347}, {"x": 1.341, "y": 10.427}, {"x": 0.895, "y": 10.428}], "confidence": 0.9943189239501953}, {"text": "BUSES", "boundingPolygon": [{"x": 0.4, "y": 10.515}, {"x": 0.692, "y": 10.515}, {"x": 0.692, "y": 10.596}, {"x": 0.4, "y": 10.596}], "confidence": 0.9956427001953125}, {"text": "Page 2 of 5", "boundingPolygon": [{"x": 3.954, "y": 10.638}, {"x": 4.544, "y": 10.638}, {"x": 4.544, "y": 10.761}, {"x": 3.954, "y": 10.762}], "confidence": 0.980782470703125}, {"text": "ACORD 131 (2009/10)", "boundingPolygon": [{"x": 0.294, "y": 10.639}, {"x": 1.483, "y": 10.638}, {"x": 1.483, "y": 10.765}, {"x": 0.294, "y": 10.766}], "confidence": 0.9861993408203125}], "checkboxes": []}, {"width": 8.5, "height": 11, "rotation": 0, "transform": {"a": 1, "c": 0, "e": 0, "b": 0, "d": 1, "f": 0}, "lines": [{"text": "SKEARNEY", "boundingPolygon": [{"x": 7.528, "y": 0.476}, {"x": 8.223, "y": 0.476}, {"x": 8.223, "y": 0.599}, {"x": 7.528, "y": 0.6}], "confidence": 0.9988204956054687}, {"text": "AGENCY CUSTOMER ID: PROJCAJ-01", "boundingPolygon": [{"x": 3.891, "y": 0.48}, {"x": 6.008, "y": 0.478}, {"x": 6.008, "y": 0.587}, {"x": 3.891, "y": 0.589}], "confidence": 0.9966075134277343}, {"text": "ADDITIONAL EXPOSURES", "boundingPolygon": [{"x": 0.293, "y": 0.574}, {"x": 1.774, "y": 0.573}, {"x": 1.774, "y": 0.676}, {"x": 0.293, "y": 0.677}], "confidence": 0.9984498596191407}, {"text": "Y/N", "boundingPolygon": [{"x": 8.002, "y": 0.731}, {"x": 8.196, "y": 0.731}, {"x": 8.196, "y": 0.812}, {"x": 8.002, "y": 0.812}], "confidence": 0.6235860443115234}, {"text": "EXPLAIN ALL \"YES\" RESPONSES, PROVIDE OTHER INFORMATION REQUIRED", "boundingPolygon": [{"x": 0.295, "y": 0.763}, {"x": 3.409, "y": 0.76}, {"x": 3.41, "y": 0.847}, {"x": 0.296, "y": 0.85}], "confidence": 0.9902872467041015}, {"text": "ADVERTISERS LIABILITY", "boundingPolygon": [{"x": 3.745, "y": 0.901}, {"x": 4.752, "y": 0.901}, {"x": 4.753, "y": 0.976}, {"x": 3.745, "y": 0.977}], "confidence": 0.9978449249267578}, {"text": "1. MEDIA USED:", "boundingPolygon": [{"x": 0.298, "y": 1.091}, {"x": 1.141, "y": 1.09}, {"x": 1.141, "y": 1.181}, {"x": 0.298, "y": 1.181}], "confidence": 0.9959568023681641}, {"text": "ANNUAL COST: $", "boundingPolygon": [{"x": 0.493, "y": 1.254}, {"x": 1.322, "y": 1.254}, {"x": 1.322, "y": 1.35}, {"x": 0.493, "y": 1.351}], "confidence": 0.9826500701904297}, {"text": "2. ARE SERVICES OF AN ADVERTISING AGENCY USED?", "boundingPolygon": [{"x": 0.292, "y": 1.396}, {"x": 3.076, "y": 1.393}, {"x": 3.076, "y": 1.486}, {"x": 0.292, "y": 1.489}], "confidence": 0.9990908813476562}, {"text": "N", "boundingPolygon": [{"x": 8.053, "y": 1.431}, {"x": 8.142, "y": 1.431}, {"x": 8.142, "y": 1.53}, {"x": 8.053, "y": 1.53}], "confidence": 0.9980038452148438}, {"text": "3. ANY COVERAGE PROVIDED UNDER AGENCY'S POLICY?", "boundingPolygon": [{"x": 0.293, "y": 1.896}, {"x": 3.206, "y": 1.894}, {"x": 3.207, "y": 1.986}, {"x": 0.293, "y": 1.989}], "confidence": 0.9993549346923828}, {"text": "N", "boundingPolygon": [{"x": 8.053, "y": 1.931}, {"x": 8.142, "y": 1.931}, {"x": 8.142, "y": 2.032}, {"x": 8.053, "y": 2.032}], "confidence": 0.997804946899414}, {"text": "AIRCRAFT LIABILITY", "boundingPolygon": [{"x": 3.825, "y": 2.403}, {"x": 4.667, "y": 2.402}, {"x": 4.667, "y": 2.48}, {"x": 3.826, "y": 2.481}], "confidence": 0.9993626403808594}, {"text": "4. DOES APPLICANT OWN / LEASE/OPERATE AIRCRAFT?", "boundingPolygon": [{"x": 0.292, "y": 2.562}, {"x": 3.176, "y": 2.56}, {"x": 3.176, "y": 2.653}, {"x": 0.292, "y": 2.655}], "confidence": 0.8887540435791016}, {"text": "N", "boundingPolygon": [{"x": 8.053, "y": 2.598}, {"x": 8.142, "y": 2.597}, {"x": 8.142, "y": 2.702}, {"x": 8.053, "y": 2.702}], "confidence": 0.997738037109375}, {"text": "AUTO LIABILITY", "boundingPolygon": [{"x": 3.916, "y": 3.069}, {"x": 4.574, "y": 3.068}, {"x": 4.574, "y": 3.147}, {"x": 3.916, "y": 3.147}], "confidence": 0.9985941314697265}, {"text": "5. ARE EXPLOSIVES, CAUSTICS, FLAMMABLES OR OTHER DANGEROUS CARGO HAULED?", "boundingPolygon": [{"x": 0.294, "y": 3.229}, {"x": 4.73, "y": 3.225}, {"x": 4.73, "y": 3.328}, {"x": 0.295, "y": 3.332}], "confidence": 0.9915779113769532}, {"text": "N", "boundingPolygon": [{"x": 8.053, "y": 3.263}, {"x": 8.141, "y": 3.263}, {"x": 8.141, "y": 3.367}, {"x": 8.053, "y": 3.367}], "confidence": 0.9979660797119141}, {"text": "6. ARE PASSENGERS CARRIED FOR A FEE?", "boundingPolygon": [{"x": 0.293, "y": 3.731}, {"x": 2.5, "y": 3.729}, {"x": 2.5, "y": 3.82}, {"x": 0.293, "y": 3.822}], "confidence": 0.998726577758789}, {"text": "N", "boundingPolygon": [{"x": 8.052, "y": 3.76}, {"x": 8.142, "y": 3.76}, {"x": 8.142, "y": 3.867}, {"x": 8.052, "y": 3.867}], "confidence": 0.9968708801269531}, {"text": "7. ANY UNITS NOT INSURED BY UNDERLYING POLICIES?", "boundingPolygon": [{"x": 0.293, "y": 4.23}, {"x": 3.127, "y": 4.228}, {"x": 3.127, "y": 4.319}, {"x": 0.293, "y": 4.322}], "confidence": 0.9992742919921875}, {"text": "N", "boundingPolygon": [{"x": 8.052, "y": 4.261}, {"x": 8.142, "y": 4.261}, {"x": 8.142, "y": 4.365}, {"x": 8.053, "y": 4.365}], "confidence": 0.9961665344238281}, {"text": "8. ARE ANY VEHICLES LEASED OR RENTED TO OTHERS?", "boundingPolygon": [{"x": 0.294, "y": 4.73}, {"x": 3.156, "y": 4.728}, {"x": 3.156, "y": 4.819}, {"x": 0.294, "y": 4.821}], "confidence": 0.9994947814941406}, {"text": "N", "boundingPolygon": [{"x": 8.053, "y": 4.764}, {"x": 8.142, "y": 4.763}, {"x": 8.142, "y": 4.866}, {"x": 8.053, "y": 4.866}], "confidence": 0.9983818054199218}, {"text": "9. ARE HIRED AND NON-OWNED COVERAGES PROVIDED?", "boundingPolygon": [{"x": 0.293, "y": 5.232}, {"x": 3.206, "y": 5.229}, {"x": 3.206, "y": 5.318}, {"x": 0.293, "y": 5.321}], "confidence": 0.999266128540039}, {"text": "N", "boundingPolygon": [{"x": 8.053, "y": 5.262}, {"x": 8.143, "y": 5.262}, {"x": 8.143, "y": 5.366}, {"x": 8.053, "y": 5.367}], "confidence": 0.9988045501708984}, {"text": "CONTRACTORS LIABILITY", "boundingPolygon": [{"x": 3.718, "y": 5.736}, {"x": 4.778, "y": 5.735}, {"x": 4.778, "y": 5.812}, {"x": 3.718, "y": 5.813}], "confidence": 0.9982003784179687}, {"text": "10. IS BRIDGE, DAM, OR MARINE WORK PERFORMED?", "boundingPolygon": [{"x": 0.3, "y": 5.896}, {"x": 2.957, "y": 5.894}, {"x": 2.957, "y": 5.993}, {"x": 0.3, "y": 5.996}], "confidence": 0.9865438079833985}, {"text": "N", "boundingPolygon": [{"x": 8.054, "y": 5.931}, {"x": 8.142, "y": 5.931}, {"x": 8.142, "y": 6.034}, {"x": 8.054, "y": 6.034}], "confidence": 0.9987368774414063}, {"text": "11. DESCRIBE TYPICAL JOBS PERFORMED (Attach ACORD 101, Additional Remarks Schedule, if more space is required)", "boundingPolygon": [{"x": 0.299, "y": 6.397}, {"x": 5.904, "y": 6.393}, {"x": 5.904, "y": 6.505}, {"x": 0.299, "y": 6.509}], "confidence": 0.9980216979980469}, {"text": "12. DESCRIBE AGREEMENT (Attach ACORD 101, Additional Remarks Schedule, if more space is required)", "boundingPolygon": [{"x": 0.298, "y": 6.981}, {"x": 5.165, "y": 6.977}, {"x": 5.166, "y": 7.086}, {"x": 0.298, "y": 7.089}], "confidence": 0.9978388214111328}, {"text": "13. DOES APPLICANT OWN, RENT, OR OTHERWISE USE CRANES?", "boundingPolygon": [{"x": 0.301, "y": 7.482}, {"x": 3.528, "y": 7.479}, {"x": 3.528, "y": 7.579}, {"x": 0.301, "y": 7.581}], "confidence": 0.9977088165283203}, {"text": "N", "boundingPolygon": [{"x": 8.053, "y": 7.511}, {"x": 8.144, "y": 7.511}, {"x": 8.144, "y": 7.617}, {"x": 8.054, "y": 7.617}], "confidence": 0.9956983184814453}, {"text": "14. <PERSON>O SUBCONTRACTORS CARRY COVERAGES OR <PERSON>IMITS LESS THAN APPLICANT?", "boundingPolygon": [{"x": 0.3, "y": 7.98}, {"x": 4.433, "y": 7.977}, {"x": 4.433, "y": 8.069}, {"x": 0.3, "y": 8.072}], "confidence": 0.9972500610351562}, {"text": "N", "boundingPolygon": [{"x": 8.054, "y": 8.013}, {"x": 8.144, "y": 8.013}, {"x": 8.144, "y": 8.116}, {"x": 8.054, "y": 8.116}], "confidence": 0.9973550415039063}, {"text": "EMPLOYERS LIABILITY", "boundingPolygon": [{"x": 3.781, "y": 8.486}, {"x": 4.716, "y": 8.485}, {"x": 4.716, "y": 8.562}, {"x": 3.781, "y": 8.562}], "confidence": 0.998853759765625}, {"text": "15. IS APPLICANT SELF-INSURED IN ANY STATE?", "boundingPolygon": [{"x": 0.3, "y": 8.646}, {"x": 2.696, "y": 8.644}, {"x": 2.696, "y": 8.735}, {"x": 0.3, "y": 8.737}], "confidence": 0.9975297546386719}, {"text": "16. SUBJECT TO:", "boundingPolygon": [{"x": 0.299, "y": 9.165}, {"x": 1.139, "y": 9.165}, {"x": 1.139, "y": 9.257}, {"x": 0.299, "y": 9.258}], "confidence": 0.9980472564697266}, {"text": "STOP GAP", "boundingPolygon": [{"x": 3.149, "y": 9.165}, {"x": 3.66, "y": 9.165}, {"x": 3.66, "y": 9.256}, {"x": 3.149, "y": 9.256}], "confidence": 0.9983421325683594}, {"text": "JONES ACT", "boundingPolygon": [{"x": 1.54, "y": 9.166}, {"x": 2.11, "y": 9.166}, {"x": 2.11, "y": 9.255}, {"x": 1.54, "y": 9.256}], "confidence": 0.9920765686035157}, {"text": "FELA", "boundingPolygon": [{"x": 2.446, "y": 9.166}, {"x": 2.701, "y": 9.166}, {"x": 2.701, "y": 9.255}, {"x": 2.446, "y": 9.256}], "confidence": 0.9961947631835938}, {"text": "OTHER:", "boundingPolygon": [{"x": 4.048, "y": 9.172}, {"x": 4.422, "y": 9.172}, {"x": 4.422, "y": 9.261}, {"x": 4.048, "y": 9.262}], "confidence": 0.9963625335693359}, {"text": "INCIDENTAL MALPRACTICE LIABILITY", "boundingPolygon": [{"x": 3.48, "y": 9.319}, {"x": 5.014, "y": 9.317}, {"x": 5.014, "y": 9.394}, {"x": 3.48, "y": 9.395}], "confidence": 0.9966851043701171}, {"text": "17. IS A HOSPITAL OR FIRST AID FACILITY MAINTAINED?", "boundingPolygon": [{"x": 0.3, "y": 9.479}, {"x": 3.059, "y": 9.477}, {"x": 3.059, "y": 9.566}, {"x": 0.3, "y": 9.568}], "confidence": 0.9979730224609376}, {"text": "18. ARE COVERAGES PROVIDED FOR DOCTORS / NURSES?", "boundingPolygon": [{"x": 0.299, "y": 9.979}, {"x": 3.235, "y": 9.977}, {"x": 3.236, "y": 10.069}, {"x": 0.299, "y": 10.071}], "confidence": 0.9863094329833985}, {"text": "19. INDICATE # OF DOCTORS:", "boundingPolygon": [{"x": 0.301, "y": 10.499}, {"x": 1.759, "y": 10.497}, {"x": 1.759, "y": 10.59}, {"x": 0.301, "y": 10.592}], "confidence": 0.9950371551513671}, {"text": "NURSES:", "boundingPolygon": [{"x": 2.245, "y": 10.499}, {"x": 2.689, "y": 10.498}, {"x": 2.689, "y": 10.59}, {"x": 2.245, "y": 10.59}], "confidence": 0.9828855895996094}, {"text": "BEDS:", "boundingPolygon": [{"x": 3.197, "y": 10.5}, {"x": 3.496, "y": 10.5}, {"x": 3.496, "y": 10.591}, {"x": 3.198, "y": 10.591}], "confidence": 0.9876979064941406}, {"text": "Page 3 of 5", "boundingPolygon": [{"x": 3.957, "y": 10.639}, {"x": 4.542, "y": 10.639}, {"x": 4.542, "y": 10.759}, {"x": 3.957, "y": 10.76}], "confidence": 0.9976634216308594}, {"text": "ACORD 131 (2009/10)", "boundingPolygon": [{"x": 0.295, "y": 10.64}, {"x": 1.483, "y": 10.639}, {"x": 1.483, "y": 10.765}, {"x": 0.295, "y": 10.765}], "confidence": 0.9990570831298828}], "checkboxes": []}, {"width": 8.5, "height": 11, "rotation": 0, "transform": {"a": 1, "c": 0, "e": 0, "b": 0, "d": 1, "f": 0}, "lines": [{"text": "SKEARNEY", "boundingPolygon": [{"x": 7.524, "y": 0.476}, {"x": 8.22, "y": 0.476}, {"x": 8.22, "y": 0.585}, {"x": 7.524, "y": 0.585}], "confidence": 0.9986443328857422}, {"text": "AGENCY CUSTOMER ID: PROJCAJ-01", "boundingPolygon": [{"x": 3.889, "y": 0.478}, {"x": 6.003, "y": 0.478}, {"x": 6.003, "y": 0.587}, {"x": 3.89, "y": 0.588}], "confidence": 0.9938449096679688}, {"text": "ADDITIONAL EXPOSURES (continued)", "boundingPolygon": [{"x": 0.294, "y": 0.571}, {"x": 2.438, "y": 0.57}, {"x": 2.438, "y": 0.693}, {"x": 0.294, "y": 0.693}], "confidence": 0.9986943054199219}, {"text": "Y/N", "boundingPolygon": [{"x": 7.999, "y": 0.733}, {"x": 8.191, "y": 0.733}, {"x": 8.191, "y": 0.811}, {"x": 7.999, "y": 0.811}], "confidence": 0.6609489440917968}, {"text": "EXPLAIN ALL \"YES\" RESPONSES, PROVIDE OTHER INFORMATION REQUIRED", "boundingPolygon": [{"x": 0.297, "y": 0.763}, {"x": 3.407, "y": 0.762}, {"x": 3.407, "y": 0.848}, {"x": 0.297, "y": 0.849}], "confidence": 0.9873832702636719}, {"text": "POLLUTION LIABILITY", "boundingPolygon": [{"x": 3.8, "y": 0.902}, {"x": 4.697, "y": 0.901}, {"x": 4.697, "y": 0.976}, {"x": 3.8, "y": 0.977}], "confidence": 0.9986175537109375}, {"text": "EPA #:", "boundingPolygon": [{"x": 0.296, "y": 0.932}, {"x": 0.557, "y": 0.932}, {"x": 0.557, "y": 1.006}, {"x": 0.296, "y": 1.006}], "confidence": 0.9711572265625}, {"text": "20. DO CURRENT OR PAST PRODUCTS, OR THEIR COMPONENTS, CONTAIN HAZARDOUS MATERIALS THAT MAY REQUIRE SPECIAL", "boundingPolygon": [{"x": 0.294, "y": 1.089}, {"x": 6.715, "y": 1.087}, {"x": 6.715, "y": 1.19}, {"x": 0.294, "y": 1.192}], "confidence": 0.9947059631347657}, {"text": "DISPOSAL METHODS?", "boundingPolygon": [{"x": 0.497, "y": 1.204}, {"x": 1.579, "y": 1.204}, {"x": 1.579, "y": 1.292}, {"x": 0.497, "y": 1.292}], "confidence": 0.9981929779052734}, {"text": "21. INDICATE THE COVERAGES CARRIED:", "boundingPolygon": [{"x": 0.292, "y": 1.649}, {"x": 2.348, "y": 1.648}, {"x": 2.348, "y": 1.736}, {"x": 0.292, "y": 1.737}], "confidence": 0.9943685913085938}, {"text": "GL WITH POLLUTION COVERAGE ENDORSEMENT", "boundingPolygon": [{"x": 3.645, "y": 1.833}, {"x": 6.058, "y": 1.832}, {"x": 6.058, "y": 1.92}, {"x": 3.645, "y": 1.92}], "confidence": 0.998419189453125}, {"text": "GL WITH STANDARD ISO POLLUTION EXCLUSION", "boundingPolygon": [{"x": 0.746, "y": 1.834}, {"x": 3.136, "y": 1.833}, {"x": 3.136, "y": 1.919}, {"x": 0.746, "y": 1.92}], "confidence": 0.9985166168212891}, {"text": "GL WITH STANDARD SUDDEN & ACCIDENTAL ONLY", "boundingPolygon": [{"x": 0.747, "y": 1.998}, {"x": 3.246, "y": 1.997}, {"x": 3.246, "y": 2.086}, {"x": 0.747, "y": 2.087}], "confidence": 0.9983676147460937}, {"text": "SEPARATE POLLUTION COVERAGE", "boundingPolygon": [{"x": 3.643, "y": 2}, {"x": 5.363, "y": 1.999}, {"x": 5.363, "y": 2.086}, {"x": 3.644, "y": 2.087}], "confidence": 0.9986008453369141}, {"text": "PRODUCT LIABILITY", "boundingPolygon": [{"x": 3.834, "y": 2.154}, {"x": 4.655, "y": 2.154}, {"x": 4.655, "y": 2.229}, {"x": 3.834, "y": 2.229}], "confidence": 0.9987881469726563}, {"text": "22. ARE MISSILES, <PERSON><PERSON>IN<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> SYSTEMS, FRAMES OR ANY OTHER PRODUCT USED / INSTALLED IN AIRCRAFT?", "boundingPolygon": [{"x": 0.295, "y": 2.31}, {"x": 6.204, "y": 2.308}, {"x": 6.204, "y": 2.411}, {"x": 0.295, "y": 2.412}], "confidence": 0.9885363006591796}, {"text": "23. ANY <PERSON>OREIGN OPERATIONS, FOREIGN PRODUCTS DISTRIBUTED IN THE USA OR US PRODUCTS SOLD / DISTRIBUTED IN FOREIGN COUNTRIES?", "boundingPolygon": [{"x": 0.294, "y": 2.836}, {"x": 7.508, "y": 2.835}, {"x": 7.508, "y": 2.935}, {"x": 0.294, "y": 2.937}], "confidence": 0.9809254455566406}, {"text": "(If \"YES\", Attach ACORD 815)", "boundingPolygon": [{"x": 0.497, "y": 2.952}, {"x": 1.833, "y": 2.952}, {"x": 1.833, "y": 3.058}, {"x": 0.497, "y": 3.058}], "confidence": 0.9923541259765625}, {"text": "24. PRODUCT LIABILITY LOSS IN PAST THREE (3) YEARS? (SPECIFY)", "boundingPolygon": [{"x": 0.294, "y": 3.145}, {"x": 3.636, "y": 3.144}, {"x": 3.636, "y": 3.252}, {"x": 0.295, "y": 3.253}], "confidence": 0.9991641998291015}, {"text": "25. GROSS SALES FROM EACH OF LAST THREE (3) YEARS: $", "boundingPolygon": [{"x": 0.294, "y": 3.638}, {"x": 3.355, "y": 3.637}, {"x": 3.355, "y": 3.749}, {"x": 0.294, "y": 3.75}], "confidence": 0.9981500244140625}, {"text": "$", "boundingPolygon": [{"x": 6.343, "y": 3.641}, {"x": 6.404, "y": 3.641}, {"x": 6.404, "y": 3.738}, {"x": 6.343, "y": 3.738}], "confidence": 0.9986414337158203}, {"text": "$", "boundingPolygon": [{"x": 4.843, "y": 3.642}, {"x": 4.903, "y": 3.642}, {"x": 4.903, "y": 3.737}, {"x": 4.843, "y": 3.737}], "confidence": 0.999370346069336}, {"text": "PROTECTIVE LIABILITY", "boundingPolygon": [{"x": 3.773, "y": 3.818}, {"x": 4.721, "y": 3.818}, {"x": 4.721, "y": 3.893}, {"x": 3.773, "y": 3.893}], "confidence": 0.9982563781738282}, {"text": "26. DESCRIBE INDEPENDENT CONTRACTORS (Attach ACORD 101, Additional Remarks Schedule, if more space is required)", "boundingPolygon": [{"x": 0.295, "y": 3.975}, {"x": 6.044, "y": 3.974}, {"x": 6.044, "y": 4.083}, {"x": 0.295, "y": 4.085}], "confidence": 0.9972859954833985}, {"text": "WATERCRAFT LIABILITY", "boundingPolygon": [{"x": 3.745, "y": 4.485}, {"x": 4.741, "y": 4.485}, {"x": 4.741, "y": 4.561}, {"x": 3.745, "y": 4.561}], "confidence": 0.9961714935302735}, {"text": "27. DOES APPLICANT OWN OR LEASE WATERCRAFT?", "boundingPolygon": [{"x": 0.293, "y": 4.642}, {"x": 2.933, "y": 4.642}, {"x": 2.933, "y": 4.73}, {"x": 0.293, "y": 4.73}], "confidence": 0.9975273895263672}, {"text": "LOC #", "boundingPolygon": [{"x": 0.573, "y": 4.822}, {"x": 0.819, "y": 4.821}, {"x": 0.819, "y": 4.896}, {"x": 0.573, "y": 4.896}], "confidence": 0.9923542785644531}, {"text": "# OWNED", "boundingPolygon": [{"x": 1.198, "y": 4.822}, {"x": 1.589, "y": 4.822}, {"x": 1.589, "y": 4.895}, {"x": 1.198, "y": 4.895}], "confidence": 0.9949063873291015}, {"text": "LENGTH", "boundingPolygon": [{"x": 2.248, "y": 4.822}, {"x": 2.592, "y": 4.821}, {"x": 2.592, "y": 4.898}, {"x": 2.248, "y": 4.898}], "confidence": 0.9993011474609375}, {"text": "HORSEPOWER", "boundingPolygon": [{"x": 3.14, "y": 4.822}, {"x": 3.751, "y": 4.822}, {"x": 3.751, "y": 4.898}, {"x": 3.14, "y": 4.899}], "confidence": 0.9985582733154297}, {"text": "# OWNED", "boundingPolygon": [{"x": 4.846, "y": 4.822}, {"x": 5.238, "y": 4.821}, {"x": 5.238, "y": 4.895}, {"x": 4.846, "y": 4.895}], "confidence": 0.9883126831054687}, {"text": "LENGTH", "boundingPolygon": [{"x": 5.871, "y": 4.822}, {"x": 6.21, "y": 4.822}, {"x": 6.21, "y": 4.896}, {"x": 5.871, "y": 4.897}], "confidence": 0.9993782806396484}, {"text": "HORSEPOWER", "boundingPolygon": [{"x": 6.814, "y": 4.822}, {"x": 7.424, "y": 4.822}, {"x": 7.424, "y": 4.898}, {"x": 6.814, "y": 4.898}], "confidence": 0.9980379486083985}, {"text": "LOC #", "boundingPolygon": [{"x": 4.272, "y": 4.823}, {"x": 4.52, "y": 4.822}, {"x": 4.52, "y": 4.898}, {"x": 4.272, "y": 4.898}], "confidence": 0.97828369140625}, {"text": "APARTMENTS / CONDOMINIUMS / HOTELS/MOTELS", "boundingPolygon": [{"x": 3.184, "y": 5.151}, {"x": 5.3, "y": 5.15}, {"x": 5.3, "y": 5.228}, {"x": 3.184, "y": 5.228}], "confidence": 0.9184459686279297}, {"text": "# UNITS", "boundingPolygon": [{"x": 1.734, "y": 5.315}, {"x": 2.058, "y": 5.315}, {"x": 2.058, "y": 5.39}, {"x": 1.734, "y": 5.39}], "confidence": 0.977686767578125}, {"text": "# UNITS", "boundingPolygon": [{"x": 5.43, "y": 5.315}, {"x": 5.753, "y": 5.315}, {"x": 5.753, "y": 5.389}, {"x": 5.43, "y": 5.389}], "confidence": 0.9822149658203125}, {"text": "LOC #", "boundingPolygon": [{"x": 0.598, "y": 5.32}, {"x": 0.846, "y": 5.32}, {"x": 0.846, "y": 5.399}, {"x": 0.598, "y": 5.399}], "confidence": 0.9928382873535156}, {"text": "# SWIMMING POOLS", "boundingPolygon": [{"x": 2.254, "y": 5.321}, {"x": 3.082, "y": 5.321}, {"x": 3.082, "y": 5.4}, {"x": 2.254, "y": 5.4}], "confidence": 0.9972740173339844}, {"text": "# DIVING BOARDS", "boundingPolygon": [{"x": 3.199, "y": 5.321}, {"x": 3.937, "y": 5.321}, {"x": 3.937, "y": 5.399}, {"x": 3.199, "y": 5.399}], "confidence": 0.995759506225586}, {"text": "LOC #", "boundingPolygon": [{"x": 4.297, "y": 5.321}, {"x": 4.545, "y": 5.321}, {"x": 4.545, "y": 5.399}, {"x": 4.297, "y": 5.399}], "confidence": 0.9959832763671875}, {"text": "# SWIMMING POOLS", "boundingPolygon": [{"x": 5.952, "y": 5.321}, {"x": 6.782, "y": 5.321}, {"x": 6.782, "y": 5.399}, {"x": 5.953, "y": 5.4}], "confidence": 0.9971152496337891}, {"text": "# STORIES", "boundingPolygon": [{"x": 1.028, "y": 5.322}, {"x": 1.464, "y": 5.322}, {"x": 1.464, "y": 5.397}, {"x": 1.028, "y": 5.397}], "confidence": 0.9753891754150391}, {"text": "# DIVING BOARDS", "boundingPolygon": [{"x": 6.901, "y": 5.322}, {"x": 7.636, "y": 5.322}, {"x": 7.636, "y": 5.399}, {"x": 6.901, "y": 5.399}], "confidence": 0.9872377777099609}, {"text": "# STORIES", "boundingPolygon": [{"x": 4.727, "y": 5.323}, {"x": 5.161, "y": 5.322}, {"x": 5.161, "y": 5.397}, {"x": 4.727, "y": 5.397}], "confidence": 0.9671360778808594}, {"text": "28.", "boundingPolygon": [{"x": 0.296, "y": 5.345}, {"x": 0.432, "y": 5.345}, {"x": 0.432, "y": 5.427}, {"x": 0.296, "y": 5.427}], "confidence": 0.9994993591308594}, {"text": "REMARKS (Attach ACORD 101, Additional Remarks Schedule, if more space is required)", "boundingPolygon": [{"x": 0.298, "y": 5.652}, {"x": 5.196, "y": 5.651}, {"x": 5.196, "y": 5.775}, {"x": 0.298, "y": 5.776}], "confidence": 0.9966867065429688}, {"text": "ACORD 131 (2009/10)", "boundingPolygon": [{"x": 0.294, "y": 10.637}, {"x": 1.482, "y": 10.637}, {"x": 1.482, "y": 10.763}, {"x": 0.294, "y": 10.763}], "confidence": 0.997246322631836}, {"text": "Page 4 of 5", "boundingPolygon": [{"x": 3.956, "y": 10.641}, {"x": 4.536, "y": 10.641}, {"x": 4.536, "y": 10.758}, {"x": 3.956, "y": 10.758}], "confidence": 0.9679534149169922}], "checkboxes": []}, {"width": 8.5, "height": 11, "rotation": 0, "transform": {"a": 1, "c": 0, "e": 0, "b": 0, "d": 1, "f": 0}, "lines": [{"text": "AGENCY CUSTOMER ID: PROJCAJ-01", "boundingPolygon": [{"x": 3.89, "y": 0.475}, {"x": 6.005, "y": 0.477}, {"x": 6.005, "y": 0.59}, {"x": 3.89, "y": 0.588}], "confidence": 0.9926541900634765}, {"text": "SKEARNEY", "boundingPolygon": [{"x": 7.516, "y": 0.475}, {"x": 8.219, "y": 0.475}, {"x": 8.219, "y": 0.599}, {"x": 7.516, "y": 0.598}], "confidence": 0.9980723571777343}, {"text": "REMARKS (Attach ACORD 101, Additional Remarks Schedule, if more space is required)", "boundingPolygon": [{"x": 0.297, "y": 0.65}, {"x": 5.199, "y": 0.654}, {"x": 5.199, "y": 0.778}, {"x": 0.297, "y": 0.774}], "confidence": 0.9979447174072266}, {"text": "SIGNATURE", "boundingPolygon": [{"x": 0.294, "y": 3.402}, {"x": 0.984, "y": 3.403}, {"x": 0.984, "y": 3.502}, {"x": 0.294, "y": 3.501}], "confidence": 0.9993350219726562}, {"text": "ANY PERSON WHO KNOWINGLY AND WITH INTENT TO DEF<PERSON>UD ANY INSURANCE COMPANY OR ANOTHER PERSON FILES AN APPLICATION FOR INSURANCE OR", "boundingPolygon": [{"x": 0.341, "y": 3.593}, {"x": 8.134, "y": 3.6}, {"x": 8.134, "y": 3.698}, {"x": 0.341, "y": 3.691}], "confidence": 0.9945265960693359}, {"text": "STATEMENT OF CLAIM CONTAINING ANY MATERIALLY FALSE INFORMATION, OR CONCEALS FOR THE PURPOSE OF MISLEADING INFORMATION CONCERNING ANY", "boundingPolygon": [{"x": 0.344, "y": 3.704}, {"x": 8.14, "y": 3.711}, {"x": 8.14, "y": 3.818}, {"x": 0.344, "y": 3.811}], "confidence": 0.9973974609375}, {"text": "FACT MATERIAL THERETO, COMMITS A FRAUDULENT INSURANCE ACT, WHICH IS A CRIME AND SUBJECTS THE PERSON TO CRIMINAL AND [NY: SUBSTANTIAL] CIVIL", "boundingPolygon": [{"x": 0.344, "y": 3.819}, {"x": 8.158, "y": 3.826}, {"x": 8.158, "y": 3.934}, {"x": 0.344, "y": 3.927}], "confidence": 0.9931080627441407}, {"text": "PENALTIES. (Not applicable in CO, DC, FL, HI, MA, NE, OH, OK, OR, VT or WA; in LA, ME, TN and VA, insurance benefits may also be denied)", "boundingPolygon": [{"x": 0.344, "y": 3.935}, {"x": 6.574, "y": 3.94}, {"x": 6.574, "y": 4.052}, {"x": 0.344, "y": 4.047}], "confidence": 0.9957512664794922}, {"text": "IN THE DISTRICT OF COLUMBIA, WARNING: IT IS A CRIME TO PROVIDE FALSE OR MISLEADING INFORMATION TO AN INSURER FOR THE PURPOSE OF DEFRAUDING", "boundingPolygon": [{"x": 0.345, "y": 4.124}, {"x": 8.138, "y": 4.131}, {"x": 8.138, "y": 4.234}, {"x": 0.345, "y": 4.228}], "confidence": 0.9934508514404297}, {"text": "THE INSURER OR ANY OTHER PERSON. PENALTIES INCLUDE IMPRISONMENT AND/OR FINES.", "boundingPolygon": [{"x": 0.34, "y": 4.24}, {"x": 4.803, "y": 4.244}, {"x": 4.803, "y": 4.336}, {"x": 0.34, "y": 4.332}], "confidence": 0.9856288146972656}, {"text": "IN FLORIDA, ANY <PERSON>SON WHO KNOWINGLY AND WITH INTENT TO INJURE, DEFRAUD, OR DECEIVE ANY INSURER FILES A STATEMENT OF CLAIM OR AN", "boundingPolygon": [{"x": 0.345, "y": 4.416}, {"x": 8.148, "y": 4.423}, {"x": 8.148, "y": 4.53}, {"x": 0.345, "y": 4.523}], "confidence": 0.9948513031005859}, {"text": "APPLICATION CONTAINING ANY FALSE, INCOMPLETE, OR MISLEADING INFORMATION IS GUILTY OF A FELONY OF THE THIRD DEGREE.", "boundingPolygon": [{"x": 0.338, "y": 4.529}, {"x": 6.741, "y": 4.535}, {"x": 6.741, "y": 4.641}, {"x": 0.338, "y": 4.635}], "confidence": 0.9924216461181641}, {"text": "IN MASSACHUSETTS, NEBRASKA, OR<PERSON><PERSON> AND VERMONT, ANY <PERSON>ERSON WHO KNOWINGLY AND WITH INTENT TO DEFRAUD ANY INSURANCE COMPANY OR", "boundingPolygon": [{"x": 0.345, "y": 4.74}, {"x": 8.132, "y": 4.747}, {"x": 8.132, "y": 4.853}, {"x": 0.345, "y": 4.846}], "confidence": 0.987738265991211}, {"text": "ANOTHER <PERSON>ERSON FILES AN APPLICATION FOR INSURANCE OR STATEMENT OF CLAIM CONTAINING ANY MATERIALLY FALSE INFORMATION, OR CONCEALS FOR", "boundingPolygon": [{"x": 0.341, "y": 4.853}, {"x": 8.127, "y": 4.86}, {"x": 8.127, "y": 4.962}, {"x": 0.341, "y": 4.955}], "confidence": 0.9971482849121094}, {"text": "THE PURPOSE OF MISLEADING INFORMATION CONCERNING ANY FACT MATERIAL THERETO, MAY BE COMMITTING A FRAUDULENT INSURANCE ACT, WHICH MAY BE", "boundingPolygon": [{"x": 0.34, "y": 4.968}, {"x": 8.179, "y": 4.975}, {"x": 8.179, "y": 5.076}, {"x": 0.34, "y": 5.069}], "confidence": 0.991600341796875}, {"text": "A CRIME AND MAY SUBJECT THE PERSON TO CRIMINAL AND CIVIL PENALTIES.", "boundingPolygon": [{"x": 0.34, "y": 5.084}, {"x": 4.092, "y": 5.087}, {"x": 4.092, "y": 5.18}, {"x": 0.34, "y": 5.176}], "confidence": 0.9955480194091797}, {"text": "IN WASHINGTON, IT IS A CRIME TO KNOWINGLY PROVIDE FALSE, INCOMPLETE, OR MISLEADING INFORMATION TO AN INSURANCE COMPANY FOR THE PURPOSE OF", "boundingPolygon": [{"x": 0.345, "y": 5.29}, {"x": 8.185, "y": 5.298}, {"x": 8.185, "y": 5.403}, {"x": 0.345, "y": 5.396}], "confidence": 0.9828089904785157}, {"text": "DEFRAUDING THE COMPANY. PENALTIES INCLUDE IMPRISONMENT, FINES, AND DENIAL OF INSURANCE BENEFITS.", "boundingPolygon": [{"x": 0.344, "y": 5.405}, {"x": 5.846, "y": 5.41}, {"x": 5.846, "y": 5.512}, {"x": 0.344, "y": 5.507}], "confidence": 0.9885579681396485}, {"text": "IF THE COMPANY TO WHICH I AM APPLYING OFFERS UNINSURED MOTORISTS (UM) AND/OR UNDERINSURED MOTORISTS (UIM) COVERAGE IN MY STATE:", "boundingPolygon": [{"x": 0.345, "y": 5.679}, {"x": 7.627, "y": 5.685}, {"x": 7.627, "y": 5.797}, {"x": 0.345, "y": 5.79}], "confidence": 0.9702700042724609}, {"text": "UNDERINSURED MOTORISTS (UIM) COVERAGE: $ 1,000,000", "boundingPolygon": [{"x": 4.094, "y": 5.908}, {"x": 7.07, "y": 5.911}, {"x": 7.07, "y": 6.031}, {"x": 4.094, "y": 6.028}], "confidence": 0.9677708435058594}, {"text": "UNINSURED MOTORISTS (UM) COVERAGE: $ 1,000,000", "boundingPolygon": [{"x": 0.345, "y": 5.909}, {"x": 3.054, "y": 5.912}, {"x": 3.054, "y": 6.032}, {"x": 0.345, "y": 6.029}], "confidence": 0.9664286804199219}, {"text": "IF APPLICABLE IN YOUR STATE", "boundingPolygon": [{"x": 0.406, "y": 6.089}, {"x": 1.888, "y": 6.09}, {"x": 1.888, "y": 6.181}, {"x": 0.406, "y": 6.18}], "confidence": 0.9953724670410157}, {"text": "APPLICABLE ONLY IN LOUISIANA, NEW HAMPSHIRE, VERMONT AND WISCONSIN", "boundingPolygon": [{"x": 2.311, "y": 6.311}, {"x": 6.175, "y": 6.315}, {"x": 6.175, "y": 6.415}, {"x": 2.311, "y": 6.411}], "confidence": 0.9827477264404297}, {"text": "APPLICABLE ONLY IN LOUISIANA:", "boundingPolygon": [{"x": 0.342, "y": 6.481}, {"x": 1.98, "y": 6.482}, {"x": 1.98, "y": 6.571}, {"x": 0.342, "y": 6.57}], "confidence": 0.993214111328125}, {"text": "I ACKN<PERSON>LEDGE THAT UM COVERAGE HAS BEEN EXPLAINED TO ME, AND I HAVE BEEN OFFERED THE OPTION OF SELECTING UM LIMITS EQUAL TO MY LIABILITY", "boundingPolygon": [{"x": 0.346, "y": 6.709}, {"x": 8.135, "y": 6.716}, {"x": 8.135, "y": 6.816}, {"x": 0.345, "y": 6.809}], "confidence": 0.984290542602539}, {"text": "LIMITS, UM LIMITS LOWER THAN MY LIABILITY LIMITS, OR TO REJECT UM COVERAGE ENTIRELY.", "boundingPolygon": [{"x": 0.345, "y": 6.823}, {"x": 4.91, "y": 6.827}, {"x": 4.91, "y": 6.93}, {"x": 0.345, "y": 6.926}], "confidence": 0.9909408569335938}, {"text": "2. I REJECT UM COVERAGE IN ITS ENTIRETY.", "boundingPolygon": [{"x": 4.691, "y": 7.089}, {"x": 6.853, "y": 7.091}, {"x": 6.853, "y": 7.183}, {"x": 4.691, "y": 7.181}], "confidence": 0.9721846771240235}, {"text": "1. I SELECT UM LIMITS INDICATED IN THIS APPLICATION.", "boundingPolygon": [{"x": 0.348, "y": 7.091}, {"x": 3.045, "y": 7.093}, {"x": 3.045, "y": 7.183}, {"x": 0.348, "y": 7.18}], "confidence": 0.9758641815185547}, {"text": "OR", "boundingPolygon": [{"x": 3.995, "y": 7.092}, {"x": 4.146, "y": 7.092}, {"x": 4.146, "y": 7.18}, {"x": 3.995, "y": 7.18}], "confidence": 0.9992082977294922}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 3.189, "y": 7.204}, {"x": 3.599, "y": 7.205}, {"x": 3.599, "y": 7.299}, {"x": 3.189, "y": 7.298}], "confidence": 0.9979257202148437}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 7.088, "y": 7.205}, {"x": 7.499, "y": 7.205}, {"x": 7.499, "y": 7.299}, {"x": 7.088, "y": 7.298}], "confidence": 0.9975493621826171}, {"text": "APPLICABLE ONLY IN NEW HAMPSHIRE:", "boundingPolygon": [{"x": 0.342, "y": 7.313}, {"x": 2.283, "y": 7.315}, {"x": 2.283, "y": 7.405}, {"x": 0.342, "y": 7.403}], "confidence": 0.9764725494384766}, {"text": "I ACKN<PERSON>LEDGE THAT UM COVERAGE HAS BEEN EXPLAINED TO ME, AND I HAVE BEEN OFFERED THE OPTION OF SELECTING UM LIMITS EQUAL TO MY LIABILITY", "boundingPolygon": [{"x": 0.346, "y": 7.5}, {"x": 8.135, "y": 7.508}, {"x": 8.135, "y": 7.612}, {"x": 0.346, "y": 7.604}], "confidence": 0.980306396484375}, {"text": "LIMITS OR TO REJECT UM COVERAGE ENTIRELY.", "boundingPolygon": [{"x": 0.344, "y": 7.618}, {"x": 2.685, "y": 7.62}, {"x": 2.685, "y": 7.712}, {"x": 0.344, "y": 7.709}], "confidence": 0.9888140869140625}, {"text": "1. I SELECT UM LIMITS INDICATED IN THIS APPLICATION.", "boundingPolygon": [{"x": 0.297, "y": 7.839}, {"x": 2.999, "y": 7.841}, {"x": 2.999, "y": 7.933}, {"x": 0.297, "y": 7.93}], "confidence": 0.9479921722412109}, {"text": "2. I REJECT UM COVERAGE IN ITS ENTIRETY.", "boundingPolygon": [{"x": 4.691, "y": 7.839}, {"x": 6.854, "y": 7.841}, {"x": 6.854, "y": 7.932}, {"x": 4.691, "y": 7.93}], "confidence": 0.9754389190673828}, {"text": "OR", "boundingPolygon": [{"x": 4.096, "y": 7.842}, {"x": 4.245, "y": 7.842}, {"x": 4.245, "y": 7.93}, {"x": 4.096, "y": 7.93}], "confidence": 0.9995873260498047}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 7.08, "y": 7.948}, {"x": 7.512, "y": 7.949}, {"x": 7.512, "y": 8.056}, {"x": 7.08, "y": 8.056}], "confidence": 0.9980350494384765}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 3.189, "y": 7.952}, {"x": 3.6, "y": 7.952}, {"x": 3.6, "y": 8.049}, {"x": 3.189, "y": 8.049}], "confidence": 0.998027572631836}, {"text": "APPLICABLE ONLY IN VERMONT:", "boundingPolygon": [{"x": 0.344, "y": 8.087}, {"x": 1.931, "y": 8.089}, {"x": 1.931, "y": 8.178}, {"x": 0.344, "y": 8.176}], "confidence": 0.9895304107666015}, {"text": "I ACKNOWLEDGE THAT I HAVE BEEN OFFERED UM COVERAGE EQUAL TO MY LIABILITY LIMITS. I HAVE SELECTED THE LIMITS INDICATED IN THIS", "boundingPolygon": [{"x": 0.345, "y": 8.252}, {"x": 7.575, "y": 8.259}, {"x": 7.575, "y": 8.353}, {"x": 0.345, "y": 8.346}], "confidence": 0.9788803100585938}, {"text": "APPLICATION.", "boundingPolygon": [{"x": 0.342, "y": 8.372}, {"x": 1.017, "y": 8.372}, {"x": 1.017, "y": 8.459}, {"x": 0.341, "y": 8.458}], "confidence": 0.9692057037353515}, {"text": "APPLICABLE ONLY IN WISCONSIN:", "boundingPolygon": [{"x": 0.34, "y": 8.586}, {"x": 2.006, "y": 8.588}, {"x": 2.006, "y": 8.677}, {"x": 0.34, "y": 8.675}], "confidence": 0.9974800109863281}, {"text": "I ACK<PERSON><PERSON><PERSON><PERSON><PERSON> THAT I HAVE BEEN OFFERED UNINSURED MOTORIST (UM) COVERAGE AND UNDERINSURED MOTORIST (UIM) COVERAGE.", "boundingPolygon": [{"x": 0.344, "y": 8.764}, {"x": 6.991, "y": 8.77}, {"x": 6.991, "y": 8.879}, {"x": 0.344, "y": 8.872}], "confidence": 0.9672660064697266}, {"text": "1. I SELECT UM LIMITS INDICATED IN THIS APPLICATION.", "boundingPolygon": [{"x": 0.349, "y": 9.004}, {"x": 3.048, "y": 9.006}, {"x": 3.048, "y": 9.099}, {"x": 0.349, "y": 9.096}], "confidence": 0.9665552520751953}, {"text": "2. I REJECT UM COVERAGE IN ITS ENTIRETY.", "boundingPolygon": [{"x": 4.69, "y": 9.005}, {"x": 6.854, "y": 9.007}, {"x": 6.854, "y": 9.1}, {"x": 4.69, "y": 9.098}], "confidence": 0.9776719665527344}, {"text": "OR", "boundingPolygon": [{"x": 3.995, "y": 9.008}, {"x": 4.145, "y": 9.008}, {"x": 4.145, "y": 9.096}, {"x": 3.995, "y": 9.095}], "confidence": 0.9995323944091797}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 3.19, "y": 9.121}, {"x": 3.601, "y": 9.121}, {"x": 3.601, "y": 9.215}, {"x": 3.19, "y": 9.215}], "confidence": 0.9980957794189453}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 7.086, "y": 9.121}, {"x": 7.5, "y": 9.121}, {"x": 7.5, "y": 9.217}, {"x": 7.085, "y": 9.216}], "confidence": 0.9971574401855469}, {"text": "4. I REJECT <PERSON><PERSON> COVERAGE IN ITS ENTIRETY.", "boundingPolygon": [{"x": 4.69, "y": 9.338}, {"x": 6.881, "y": 9.34}, {"x": 6.881, "y": 9.434}, {"x": 4.69, "y": 9.431}], "confidence": 0.971563949584961}, {"text": "3. I SELECT UIM LIMITS INDICATED IN THIS APPLICATION.", "boundingPolygon": [{"x": 0.344, "y": 9.339}, {"x": 3.075, "y": 9.341}, {"x": 3.075, "y": 9.434}, {"x": 0.344, "y": 9.431}], "confidence": 0.9603654479980469}, {"text": "OR", "boundingPolygon": [{"x": 3.995, "y": 9.341}, {"x": 4.144, "y": 9.341}, {"x": 4.144, "y": 9.43}, {"x": 3.995, "y": 9.43}], "confidence": 0.9996695709228516}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 3.19, "y": 9.454}, {"x": 3.599, "y": 9.454}, {"x": 3.599, "y": 9.547}, {"x": 3.19, "y": 9.547}], "confidence": 0.9980471038818359}, {"text": "(INITIALS)", "boundingPolygon": [{"x": 7.088, "y": 9.454}, {"x": 7.498, "y": 9.454}, {"x": 7.498, "y": 9.549}, {"x": 7.088, "y": 9.549}], "confidence": 0.998072509765625}, {"text": "IMPORTANT - THE STATEMENTS (ANSWERS) <PERSON><PERSON><PERSON> ABOVE ARE TRUE AND ACCURATE. THE APPLICANT HAS NOT WILLFULLY CONCEALED OR <PERSON><PERSON>EPRESENTED", "boundingPolygon": [{"x": 0.343, "y": 9.668}, {"x": 8.128, "y": 9.676}, {"x": 8.128, "y": 9.786}, {"x": 0.343, "y": 9.778}], "confidence": 0.9889088439941406}, {"text": "ANY MATERIAL FACT OR CIRCUMSTANCE CONCERNING THIS APPLICATION. THIS APPLICATION DOES NOT CONSTITUTE A BINDER.", "boundingPolygon": [{"x": 0.341, "y": 9.782}, {"x": 6.566, "y": 9.788}, {"x": 6.566, "y": 9.882}, {"x": 0.341, "y": 9.875}], "confidence": 0.9930276489257812}, {"text": "STATE PRODUCER LICENSE NO", "boundingPolygon": [{"x": 6.89, "y": 9.95}, {"x": 8.178, "y": 9.951}, {"x": 8.178, "y": 10.031}, {"x": 6.89, "y": 10.03}], "confidence": 0.9980709075927734}, {"text": "PRODUCER'S SIGNATURE", "boundingPolygon": [{"x": 0.298, "y": 9.983}, {"x": 1.347, "y": 9.984}, {"x": 1.347, "y": 10.062}, {"x": 0.298, "y": 10.061}], "confidence": 0.9984748840332032}, {"text": "PRODUCER'S NAME (Please Print)", "boundingPolygon": [{"x": 3.594, "y": 9.983}, {"x": 4.95, "y": 9.984}, {"x": 4.95, "y": 10.077}, {"x": 3.594, "y": 10.076}], "confidence": 0.9982970428466796}, {"text": "(Required in Florida)", "boundingPolygon": [{"x": 6.896, "y": 10.029}, {"x": 7.697, "y": 10.029}, {"x": 7.697, "y": 10.12}, {"x": 6.896, "y": 10.119}], "confidence": 0.991380844116211}, {"text": "<PERSON>", "boundingPolygon": [{"x": 3.598, "y": 10.13}, {"x": 4.588, "y": 10.131}, {"x": 4.588, "y": 10.235}, {"x": 3.598, "y": 10.234}], "confidence": 0.9976315307617187}, {"text": "NATIONAL PRODUCER NUMBER", "boundingPolygon": [{"x": 6.894, "y": 10.315}, {"x": 8.197, "y": 10.316}, {"x": 8.197, "y": 10.399}, {"x": 6.894, "y": 10.398}], "confidence": 0.9983526611328125}, {"text": "APPLICANT'S SIGNATURE", "boundingPolygon": [{"x": 0.292, "y": 10.317}, {"x": 1.346, "y": 10.318}, {"x": 1.346, "y": 10.395}, {"x": 0.292, "y": 10.394}], "confidence": 0.9986402130126953}, {"text": "DATE", "boundingPolygon": [{"x": 5.893, "y": 10.318}, {"x": 6.123, "y": 10.319}, {"x": 6.123, "y": 10.395}, {"x": 5.893, "y": 10.395}], "confidence": 0.99881591796875}, {"text": "Page 5 of 5", "boundingPolygon": [{"x": 3.954, "y": 10.638}, {"x": 4.541, "y": 10.639}, {"x": 4.541, "y": 10.761}, {"x": 3.954, "y": 10.761}], "confidence": 0.9946513366699219}, {"text": "ACORD 131 (2009/10)", "boundingPolygon": [{"x": 0.293, "y": 10.64}, {"x": 1.478, "y": 10.641}, {"x": 1.478, "y": 10.767}, {"x": 0.293, "y": 10.765}], "confidence": 0.9983208465576172}], "checkboxes": []}]}, "validation_summary": {"fields": 628, "fields_present": 628, "errors": 0, "warnings": 0, "skipped": 0}}}], "page_count": 1, "validation_summary": {"fields": 628, "fields_present": 628, "errors": 0, "warnings": 0, "skipped": 0}, "download_url": "https://sensible-so-document-type-bucket-prod-us-west-2.s3.us-west-2.amazonaws.com/kalepa/MULTIDOC_EXTRACTION/a8721e8e-cb61-4639-b035-0a10062a518c.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIAR355P7ASQ7YYBBGC%2F20231115%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20231115T183856Z&X-Amz-Expires=900&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEDMaCXVzLXdlc3QtMiJIMEYCIQCFd47LiiDnGn8cGmSw1M6JvW%2Fg0q5u2mYkgVkcqbDWLwIhAISUa5wA2Isk%2BRExbg1BcjthMAJIXUCzNV9k7A19xHP2KogDCHwQAhoMMTI4NzA5NTU2MjYxIgz6umNaWUPAa4uAAzAq5QK1PAuVwHRtBaE8GtWQSwkLcYJ6JX9BacW7wCk2T4QxF9JenEcHS5fHRu9nao1o0Ezd5y0KD7dUsWGsZ4BtMJCmSVYuPYAacwFhqHdpxq9cZy%2BGOE2lAfAY%2Fa%2BnoZFnoMDxAYeG2%2BafyURyOtQy6f6IZpgMmW3gLgIdO81pNqFNK9BFvm0qhbateAu0ZRGDxpoT6P5YgKXCoW%2FEGu5WPmsCJkDmK7Icm0okod4x2fdn4EsV4NYAHpAQtFCrXzSHds0v7a%2BMAHtbqX6oNQoKeAx5h8WSYNod354L1Wb%2Biif5o1h2PikRHP1e89UOXw6g4dxlcXOx8oRf2xDswJgrqJ600YYuY%2FTsaRPqLYD0kqlwZYMdN7CgiHvWC8eWo5E7bGLQKCEY8oBzAOIO3V9ATMqXZCupSC5dRn0Ikr57GSlJLuKkivCvO9WZCI2%2FlxTzuYpR83LXXIOc3BHbuoi61mX7zENRKFsw05vUqgY6nQFJ31njaqfb6XG0xKrKxJr6ljfN5VnexfQ0c%2FmlnhWHAN07uVizFa86p16kz09EQZI764rL0veh2FfHKnbv%2FlMY%2BKipVNnvrdZpe%2FU3q5KWS5QPPEz3yV%2BBriDWDdWqj2tlcQS6CU%2Bg0X1O7Hrl2OzVCAG2j7RcTRs8Jmm9W9kuC1%2FvpS%2FVqhAn7gcCVtfzigo7%2Fx9sjWcU9YQdfTZY&X-Amz-Signature=2cf4acae2480c1ebcb02a414bdd0043c80b096baff5c2cb8ca964d92e7fbc4e6&X-Amz-SignedHeaders=host&x-id=GetObject", "coverage": 0.974083264405845}
{"id": "98f43e24-dbf1-4cb7-a765-6d66b50aed22", "created": "2023-10-05T14:35:02.854Z", "completed": "2023-10-05T14:35:15.818Z", "status": "COMPLETE", "type": "acord_forms", "configuration": "acord_130_2013_09_test", "environment": "development", "page_count": 4, "parsed_document": {"rating_information": [{"state": {"type": "string", "value": "NJ"}, "table": [{"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "DESCR", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "CLASS CODE", "type": "string"}, "description_code": {"value": "CODE", "type": "string"}, "category": {"value": "CATEGORIES, DUTIES, CLASSIFICATIONS", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "SIC", "type": "string"}, "naics": {"value": "NAICS", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": {"source": "1", "value": 1, "type": "number"}, "class_code": {"value": "8810", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "Clerical", "type": "string"}, "full_time_employees": {"source": "1", "value": 1, "type": "number"}, "part_time_employees": {"source": "0", "value": 0, "type": "number"}, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": {"source": "62,200", "value": 62200, "unit": "$", "type": "currency"}, "rate": {"source": ".18", "value": 0.18, "type": "number"}, "est_annual_manual_premium": {"source": "37.00", "value": 37, "unit": "$", "type": "currency"}}, {"location_number": {"source": "002", "value": 2, "type": "number"}, "class_code": {"value": "7219", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "Truckmen NOC", "type": "string"}, "full_time_employees": {"source": "4", "value": 4, "type": "number"}, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": {"source": "120,785", "value": 120785, "unit": "$", "type": "currency"}, "rate": {"source": "16.62", "value": 16.62, "type": "number"}, "est_annual_manual_premium": {"source": "16,418.00", "value": 16418, "unit": "$", "type": "currency"}}, {"location_number": {"source": "003", "value": 3, "type": "number"}, "class_code": {"value": "7219", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "trucking", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}, {"location_number": null, "class_code": {"value": "", "type": "string"}, "description_code": {"value": "", "type": "string"}, "category": {"value": "", "type": "string"}, "full_time_employees": null, "part_time_employees": null, "sic": {"value": "", "type": "string"}, "naics": {"value": "", "type": "string"}, "payroll": null, "rate": null, "est_annual_manual_premium": null}]}], "locations": [{"location_number": {"source": "1", "value": 1, "type": "number"}, "highest_floor": null, "requested_address": {"value": "2503 Ogden St\nLinden, NJ 07036", "type": "address"}}], "agency_information": [{"agency_name_and_address": {"type": "string", "value": "KHD, LLC 1259 Route 46 East Building 1, Suite 125 Parsippany, NJ 07054 <PERSON>"}, "agency_address": {"value": "1259 Route 46 East Building 1, Suite 125\nParsippany, NJ 07054", "type": "address"}, "producer_name": {"type": "string", "value": "<PERSON>"}, "cs_representative_name": {"type": "string", "value": "<PERSON>"}, "office_phone": null, "mobile_phone": null, "fax": {"type": "phoneNumber", "source": "************", "value": "+***********"}, "email": null, "code": null, "sub_code": null, "customer_id": {"type": "string", "value": "POWER-2"}}], "applicant_information": [{"requested_name": {"type": "string", "value": "Power Intermodal Corp."}, "company": null, "underwriter": null, "office_phone": null, "mobile_phone": null, "requested_address": {"value": "2503 OGDEN ST\nLinden, NJ 07036", "type": "address"}, "years_in_business": {"source": "4", "value": 4, "type": "number"}, "sic": null, "naics": null, "website_address": null, "email": null, "org_type_sole_proprietorship": {"type": "boolean", "value": false}, "org_type_partnership": {"type": "boolean", "value": false}, "org_type_corporation": {"type": "boolean", "value": false}, "org_type_s_corp": {"type": "boolean", "value": false}, "org_type_llc": {"type": "boolean", "value": false}, "org_type_joint_venture": {"type": "boolean", "value": false}, "org_type_trust": {"type": "boolean", "value": false}, "org_type_other": {"type": "boolean", "value": false}, "org_type_unincorporated_association": {"type": "boolean", "value": false}, "credit_bureau_name": null, "id_number": null, "federal_employer_id_number": null, "nccl_risk_id_number": null, "other_rating_bureau_id": null}], "submission_status": [{"quote": {"type": "boolean", "value": false}, "issue_policy": {"type": "boolean", "value": false}, "bound": {"type": "boolean", "value": false}, "assigned_risk": {"type": "boolean", "value": false}}], "billing_audit_information": [{"billing_plan_agency": {"type": "boolean", "value": false}, "billing_plan_direct": {"type": "boolean", "value": true}, "payment_plan_annual": {"type": "boolean", "value": false}, "payment_plan_semi_annual": {"type": "boolean", "value": false}, "payment_plan_quarterly": {"type": "boolean", "value": false}, "audit_expiration": {"type": "boolean", "value": false}, "audit_monthly": {"type": "boolean", "value": false}, "audit_semi_annual": {"type": "boolean", "value": false}, "audit_quarterly": {"type": "boolean", "value": false}}], "policy_information": [{"proposed_effective_date": {"source": "11/13/2023", "value": "2023-11-13T00:00:00.000Z", "type": "date"}, "proposed_expiration_date": {"source": "11/13/2024", "value": "2024-11-13T00:00:00.000Z", "type": "date"}, "policy_rating_effective_date": null, "normal_anniversary_rating_date": null, "participating": {"type": "boolean", "value": false}, "non_participating": {"type": "boolean", "value": false}, "retro_plan": null, "workers_comp_states": null, "employers_liability_each_accident": {"source": "1,000,000", "value": 1000000, "unit": "$", "type": "currency"}, "employers_liability_disease_policy_limit": {"source": "1,000,000", "value": 1000000, "unit": "$", "type": "currency"}, "employers_liability_disease_each_employee": {"source": "1,000,000", "value": 1000000, "unit": "$", "type": "currency"}, "workers_comp_state_other": null, "deductible_medical": {"type": "boolean", "value": false}, "deductible_indemnity": {"type": "boolean", "value": false}, "policy_amount": null, "other_coverages_usl": {"type": "boolean", "value": false}, "other_coverages_voluntary": {"type": "boolean", "value": false}, "other_coverages_foreign": {"type": "boolean", "value": false}, "other_coverages_managed_care": {"type": "boolean", "value": false}, "dividend_plan": null, "additional_company_information": null, "additional_coverages": null}], "prior_carrier_information": [{"year": {"source": "2017", "value": 2017, "type": "number"}, "carrier": {"type": "string", "value": "LIBERTY"}, "policy_number": {"type": "string", "value": "WC533S371292028"}, "annual_premium": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "mod": null, "number_of_claims": null, "amount_paid": null, "reserve": null}, {"year": {"source": "2018", "value": 2018, "type": "number"}, "carrier": {"type": "string", "value": "LIBERTY"}, "policy_number": {"type": "string", "value": "WC533S37129"}, "annual_premium": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "mod": null, "number_of_claims": null, "amount_paid": null, "reserve": null}, {"year": {"source": "2019", "value": 2019, "type": "number"}, "carrier": {"type": "string", "value": "LIBERTY"}, "policy_number": {"type": "string", "value": "WC533S37129"}, "annual_premium": {"source": "0", "value": 0, "unit": "$", "type": "currency"}, "mod": null, "number_of_claims": null, "amount_paid": null, "reserve": null}, {"year": null, "carrier": null, "policy_number": null, "annual_premium": null, "mod": null, "number_of_claims": null, "amount_paid": null, "reserve": null}, {"year": null, "carrier": null, "policy_number": null, "annual_premium": null, "mod": null, "number_of_claims": null, "amount_paid": null, "reserve": null}], "general_information": [{"other_coverages_aircraft_or_watercraft": {"type": "string", "value": "N"}, "other_coverages_aircraft_or_watercraft_explanation": null, "other_coverages_hazardous_material": {"type": "string", "value": "N"}, "other_coverages_hazerdous_material_explanation": null, "other_coverages_underground_work": {"type": "string", "value": "N"}, "other_coverages_underground_work_explanation": null, "other_coverages_work_over_water": {"type": "string", "value": "N"}, "other_coverages_work_over_water_explanation": null, "other_coverages_other_business_engagement": null, "other_coverages_other_business_engagement_explanation": null, "other_coverages_subcontractors_used": {"type": "string", "value": "Y"}, "other_coverages_subcontractors_used_explanation": {"type": "string", "value": "50"}, "other_coverages_work_sublet_without_insurance": null, "other_coverages_work_sublet_without_insurance_explanation": null, "other_coverages_written_safety_program": null, "other_coverages_written_safety_program_explanation": null, "other_coverages_group_transportation": null, "other_coverages_group_transportation_explanation": null, "other_coverages_employees_under_16_over_60": null, "other_coverages_employees_under_16_over_60_explanation": null, "other_coverages_seasonal_employees": null, "other_coverages_seasonal_employees_explanation": null, "other_coverages_volunteer_or_donated_labor": null, "other_coverages_volunteer_or_donated_labor_explanation": null, "other_coverages_employees_with_physical_handicaps": null, "other_coverages_employees_with_physical_handicaps_explanation": null, "other_coverages_employees_out_of_state": null, "other_coverages_employees_out_of_state_explanation": null, "other_coverages_sponsors_athletic_teams": null, "other_coverages_sponsors_athletic_teams_explanation": null, "other_coverages_requires_employee_physical": null, "other_coverages_requires_employee_physical_explanation": null, "other_coverages_has_other_insurance": null, "other_coverages_has_other_insurance_explanation": null, "other_coverages_prior_college_declined": null, "other_coverages_prior_college_declined_explanation": null, "other_coverages_provides_employee_health_plans": null, "other_coverages_provides_employee_health_plans_explanation": null, "other_coverages_employees_perform_for_other_businesses": null, "other_coverages_employees_perform_for_other_businesses_explanation": null, "other_coverages_leases_employees": null, "other_coverages_leases_employees_explanation": null, "other_coverages_employees_work_from_home": null, "other_coverages_employees_work_from_home_explanation": null, "other_coverages_tax_liens_or_bankruptcy": null, "other_coverages_tax_liens_or_bankruptcy_explanation": null, "other_coverages_undisputed_and_unpaid_workers_comp": null, "other_coverages_undisputed_and_unpaid_workers_comp_explanation": null}]}, "validations": [], "validation_summary": {"fields": 13, "fields_present": 13, "errors": 0, "warnings": 0, "skipped": 0}, "classification_summary": [{"configuration": "acord_130_2013_09_test", "fingerprints": 1, "fingerprints_present": 4, "score": {"value": 66, "fields_present": 66, "penalties": 0}}], "errors": [], "file_metadata": {"info": {"author": "bsutera.khdll01", "title": "POWER-2C-4", "creator": "PDF Writer - bioPDF (11.9.0.2735)", "producer": "PDF Writer - bioPDF / http://www.biopdf.com / FEG / Applied Systems, Inc.", "creation_date": "2020-11-12T11:56:11.000-05:00", "modification_date": "2023-10-02T10:45:57.000-04:00"}, "metadata": {"pdf:producer": "PDF Writer - bioPDF / http://www.biopdf.com / FEG / Applied Systems, Inc.", "xmp:createdate": "2020-11-12T11:56:11-05:00", "xmp:creatortool": "PDF Writer - bioPDF (11.9.0.2735)", "xmp:metadatadate": "2020-11-12T12:56:59-05:00", "xmpmm:documentid": "uuid:775f1121-2763-11eb-0000-87f9dc4d158c", "xmpmm:instanceid": "uuid:aed1bc19-0085-4b5b-8a93-4046e3517b39", "dc:format": "application/pdf", "dc:title": "POWER-2C-4", "dc:creator": ["bsutera.khdll01"], "xmp:modifydate": "2023-10-02T10:45:57-04:00"}}, "coverage": 0.4024390243902439}
{"report_generated_date": {"source": "02/14/2023", "value": "2023-02-14T00:00:00.000Z", "type": "date", "lines": [{"text": "of 02/14/2023", "page": 0, "boundingPolygon": [{"x": 0.56, "y": 1.675}, {"x": 2.16, "y": 1.675}, {"x": 2.16, "y": 1.795}, {"x": 0.56, "y": 1.792}], "confidence": 0.9541999999999999}], "anchorConfidence": 0.9541999999999999, "valueConfidence": 0.9541999999999999}, "claims": [{"claim_number": {"type": "string", "value": "261945", "lines": [{"text": "261945", "page": 0, "boundingPolygon": [{"x": 1.872, "y": 5.494}, {"x": 2.245, "y": 5.496}, {"x": 2.245, "y": 5.606}, {"x": 1.872, "y": 5.601}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "09/30/2021", "value": "2021-09-30T00:00:00.000Z", "type": "date", "lines": [{"text": "09/30/2021", "page": 0, "boundingPolygon": [{"x": 3.202, "y": 5.499}, {"x": 3.769, "y": 5.502}, {"x": 3.768, "y": 5.615}, {"x": 3.202, "y": 5.613}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.692, "y": 5.509}, {"x": 4.899, "y": 5.503}, {"x": 4.895, "y": 5.606}, {"x": 4.692, "y": 5.615}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.675, "y": 5.516}, {"x": 5.879, "y": 5.513}, {"x": 5.875, "y": 5.607}, {"x": 5.675, "y": 5.612}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.649, "y": 5.52}, {"x": 6.859, "y": 5.514}, {"x": 6.855, "y": 5.614}, {"x": 6.648, "y": 5.616}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.729, "y": 5.524}, {"x": 7.945, "y": 5.522}, {"x": 7.945, "y": 5.625}, {"x": 7.728, "y": 5.624}], "confidence": 0.958}], "anchorConfidence": 0.948, "valueConfidence": 0.951}, "loss_reported_date": {"source": "09/30/2021", "value": "2021-09-30T00:00:00.000Z", "type": "date", "lines": [{"text": "09/30/2021", "page": 0, "boundingPolygon": [{"x": 3.202, "y": 5.499}, {"x": 3.769, "y": 5.502}, {"x": 3.768, "y": 5.615}, {"x": 3.202, "y": 5.613}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.692, "y": 5.509}, {"x": 4.899, "y": 5.503}, {"x": 4.895, "y": 5.606}, {"x": 4.692, "y": 5.615}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.675, "y": 5.516}, {"x": 5.879, "y": 5.513}, {"x": 5.875, "y": 5.607}, {"x": 5.675, "y": 5.612}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.649, "y": 5.52}, {"x": 6.859, "y": 5.514}, {"x": 6.855, "y": 5.614}, {"x": 6.648, "y": 5.616}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.729, "y": 5.524}, {"x": 7.945, "y": 5.522}, {"x": 7.945, "y": 5.625}, {"x": 7.728, "y": 5.624}], "confidence": 0.958}], "anchorConfidence": 0.948, "valueConfidence": 0.951}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.692, "y": 5.509}, {"x": 4.899, "y": 5.503}, {"x": 4.895, "y": 5.606}, {"x": 4.692, "y": 5.615}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.675, "y": 5.516}, {"x": 5.879, "y": 5.513}, {"x": 5.875, "y": 5.607}, {"x": 5.675, "y": 5.612}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.649, "y": 5.52}, {"x": 6.859, "y": 5.514}, {"x": 6.855, "y": 5.614}, {"x": 6.648, "y": 5.616}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.729, "y": 5.524}, {"x": 7.945, "y": 5.522}, {"x": 7.945, "y": 5.625}, {"x": 7.728, "y": 5.624}], "confidence": 0.958}], "anchorConfidence": 0.951, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.692, "y": 5.509}, {"x": 4.899, "y": 5.503}, {"x": 4.895, "y": 5.606}, {"x": 4.692, "y": 5.615}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.675, "y": 5.516}, {"x": 5.879, "y": 5.513}, {"x": 5.875, "y": 5.607}, {"x": 5.675, "y": 5.612}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.649, "y": 5.52}, {"x": 6.859, "y": 5.514}, {"x": 6.855, "y": 5.614}, {"x": 6.648, "y": 5.616}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.729, "y": 5.524}, {"x": 7.945, "y": 5.522}, {"x": 7.945, "y": 5.625}, {"x": 7.728, "y": 5.624}], "confidence": 0.958}], "anchorConfidence": 0.951, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.692, "y": 5.509}, {"x": 4.899, "y": 5.503}, {"x": 4.895, "y": 5.606}, {"x": 4.692, "y": 5.615}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.675, "y": 5.516}, {"x": 5.879, "y": 5.513}, {"x": 5.875, "y": 5.607}, {"x": 5.675, "y": 5.612}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.649, "y": 5.52}, {"x": 6.859, "y": 5.514}, {"x": 6.855, "y": 5.614}, {"x": 6.648, "y": 5.616}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.729, "y": 5.524}, {"x": 7.945, "y": 5.522}, {"x": 7.945, "y": 5.625}, {"x": 7.728, "y": 5.624}], "confidence": 0.958}], "anchorConfidence": 0.951, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.692, "y": 5.509}, {"x": 4.899, "y": 5.503}, {"x": 4.895, "y": 5.606}, {"x": 4.692, "y": 5.615}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.675, "y": 5.516}, {"x": 5.879, "y": 5.513}, {"x": 5.875, "y": 5.607}, {"x": 5.675, "y": 5.612}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.649, "y": 5.52}, {"x": 6.859, "y": 5.514}, {"x": 6.855, "y": 5.614}, {"x": 6.648, "y": 5.616}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.729, "y": 5.524}, {"x": 7.945, "y": 5.522}, {"x": 7.945, "y": 5.625}, {"x": 7.728, "y": 5.624}], "confidence": 0.958}], "anchorConfidence": 0.951, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2021", "value": "2021-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2022", "value": "2022-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "254989", "lines": [{"text": "254989", "page": 0, "boundingPolygon": [{"x": 1.828, "y": 6.404}, {"x": 2.202, "y": 6.399}, {"x": 2.205, "y": 6.505}, {"x": 1.828, "y": 6.51}], "confidence": 0.959}], "anchorConfidence": 0.959, "valueConfidence": 0.959}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "09/09/2020", "value": "2020-09-09T00:00:00.000Z", "type": "date", "lines": [{"text": "09/09/2020", "page": 0, "boundingPolygon": [{"x": 3.192, "y": 6.409}, {"x": 3.755, "y": 6.408}, {"x": 3.755, "y": 6.518}, {"x": 3.191, "y": 6.519}], "confidence": 0.953}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.678, "y": 6.419}, {"x": 4.888, "y": 6.416}, {"x": 4.885, "y": 6.516}, {"x": 4.678, "y": 6.515}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.665, "y": 6.429}, {"x": 5.862, "y": 6.42}, {"x": 5.858, "y": 6.517}, {"x": 5.665, "y": 6.522}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.635, "y": 6.43}, {"x": 6.838, "y": 6.417}, {"x": 6.838, "y": 6.52}, {"x": 6.635, "y": 6.526}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.735, "y": 6.431}, {"x": 7.938, "y": 6.425}, {"x": 7.938, "y": 6.528}, {"x": 7.735, "y": 6.527}], "confidence": 0.958}], "anchorConfidence": 0.948, "valueConfidence": 0.953}, "loss_reported_date": {"source": "09/09/2020", "value": "2020-09-09T00:00:00.000Z", "type": "date", "lines": [{"text": "09/09/2020", "page": 0, "boundingPolygon": [{"x": 3.192, "y": 6.409}, {"x": 3.755, "y": 6.408}, {"x": 3.755, "y": 6.518}, {"x": 3.191, "y": 6.519}], "confidence": 0.953}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.678, "y": 6.419}, {"x": 4.888, "y": 6.416}, {"x": 4.885, "y": 6.516}, {"x": 4.678, "y": 6.515}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.665, "y": 6.429}, {"x": 5.862, "y": 6.42}, {"x": 5.858, "y": 6.517}, {"x": 5.665, "y": 6.522}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.635, "y": 6.43}, {"x": 6.838, "y": 6.417}, {"x": 6.838, "y": 6.52}, {"x": 6.635, "y": 6.526}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.735, "y": 6.431}, {"x": 7.938, "y": 6.425}, {"x": 7.938, "y": 6.528}, {"x": 7.735, "y": 6.527}], "confidence": 0.958}], "anchorConfidence": 0.948, "valueConfidence": 0.953}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.678, "y": 6.419}, {"x": 4.888, "y": 6.416}, {"x": 4.885, "y": 6.516}, {"x": 4.678, "y": 6.515}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.665, "y": 6.429}, {"x": 5.862, "y": 6.42}, {"x": 5.858, "y": 6.517}, {"x": 5.665, "y": 6.522}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.635, "y": 6.43}, {"x": 6.838, "y": 6.417}, {"x": 6.838, "y": 6.52}, {"x": 6.635, "y": 6.526}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.735, "y": 6.431}, {"x": 7.938, "y": 6.425}, {"x": 7.938, "y": 6.528}, {"x": 7.735, "y": 6.527}], "confidence": 0.958}], "anchorConfidence": 0.953, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.678, "y": 6.419}, {"x": 4.888, "y": 6.416}, {"x": 4.885, "y": 6.516}, {"x": 4.678, "y": 6.515}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.665, "y": 6.429}, {"x": 5.862, "y": 6.42}, {"x": 5.858, "y": 6.517}, {"x": 5.665, "y": 6.522}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.635, "y": 6.43}, {"x": 6.838, "y": 6.417}, {"x": 6.838, "y": 6.52}, {"x": 6.635, "y": 6.526}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.735, "y": 6.431}, {"x": 7.938, "y": 6.425}, {"x": 7.938, "y": 6.528}, {"x": 7.735, "y": 6.527}], "confidence": 0.958}], "anchorConfidence": 0.953, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.678, "y": 6.419}, {"x": 4.888, "y": 6.416}, {"x": 4.885, "y": 6.516}, {"x": 4.678, "y": 6.515}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.665, "y": 6.429}, {"x": 5.862, "y": 6.42}, {"x": 5.858, "y": 6.517}, {"x": 5.665, "y": 6.522}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.635, "y": 6.43}, {"x": 6.838, "y": 6.417}, {"x": 6.838, "y": 6.52}, {"x": 6.635, "y": 6.526}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.735, "y": 6.431}, {"x": 7.938, "y": 6.425}, {"x": 7.938, "y": 6.528}, {"x": 7.735, "y": 6.527}], "confidence": 0.958}], "anchorConfidence": 0.953, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.678, "y": 6.419}, {"x": 4.888, "y": 6.416}, {"x": 4.885, "y": 6.516}, {"x": 4.678, "y": 6.515}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.665, "y": 6.429}, {"x": 5.862, "y": 6.42}, {"x": 5.858, "y": 6.517}, {"x": 5.665, "y": 6.522}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.635, "y": 6.43}, {"x": 6.838, "y": 6.417}, {"x": 6.838, "y": 6.52}, {"x": 6.635, "y": 6.526}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.735, "y": 6.431}, {"x": 7.938, "y": 6.425}, {"x": 7.938, "y": 6.528}, {"x": 7.735, "y": 6.527}], "confidence": 0.958}], "anchorConfidence": 0.953, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2021", "value": "2021-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "254042", "lines": [{"text": "254042", "page": 0, "boundingPolygon": [{"x": 1.828, "y": 6.537}, {"x": 2.218, "y": 6.532}, {"x": 2.217, "y": 6.642}, {"x": 1.827, "y": 6.644}], "confidence": 0.957}], "anchorConfidence": 0.957, "valueConfidence": 0.957}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "07/21/2020", "value": "2020-07-21T00:00:00.000Z", "type": "date", "lines": [{"text": "07/21/2020", "page": 0, "boundingPolygon": [{"x": 3.191, "y": 6.539}, {"x": 3.758, "y": 6.542}, {"x": 3.757, "y": 6.652}, {"x": 3.191, "y": 6.653}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.555}, {"x": 4.891, "y": 6.546}, {"x": 4.891, "y": 6.649}, {"x": 4.677, "y": 6.655}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.661, "y": 6.562}, {"x": 5.868, "y": 6.553}, {"x": 5.864, "y": 6.65}, {"x": 5.664, "y": 6.659}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.628, "y": 6.563}, {"x": 6.841, "y": 6.55}, {"x": 6.844, "y": 6.654}, {"x": 6.631, "y": 6.66}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.721, "y": 6.567}, {"x": 7.941, "y": 6.558}, {"x": 7.937, "y": 6.658}, {"x": 7.724, "y": 6.664}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "loss_reported_date": {"source": "07/21/2020", "value": "2020-07-21T00:00:00.000Z", "type": "date", "lines": [{"text": "07/21/2020", "page": 0, "boundingPolygon": [{"x": 3.191, "y": 6.539}, {"x": 3.758, "y": 6.542}, {"x": 3.757, "y": 6.652}, {"x": 3.191, "y": 6.653}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.555}, {"x": 4.891, "y": 6.546}, {"x": 4.891, "y": 6.649}, {"x": 4.677, "y": 6.655}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.661, "y": 6.562}, {"x": 5.868, "y": 6.553}, {"x": 5.864, "y": 6.65}, {"x": 5.664, "y": 6.659}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.628, "y": 6.563}, {"x": 6.841, "y": 6.55}, {"x": 6.844, "y": 6.654}, {"x": 6.631, "y": 6.66}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.721, "y": 6.567}, {"x": 7.941, "y": 6.558}, {"x": 7.937, "y": 6.658}, {"x": 7.724, "y": 6.664}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.555}, {"x": 4.891, "y": 6.546}, {"x": 4.891, "y": 6.649}, {"x": 4.677, "y": 6.655}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.661, "y": 6.562}, {"x": 5.868, "y": 6.553}, {"x": 5.864, "y": 6.65}, {"x": 5.664, "y": 6.659}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.628, "y": 6.563}, {"x": 6.841, "y": 6.55}, {"x": 6.844, "y": 6.654}, {"x": 6.631, "y": 6.66}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.721, "y": 6.567}, {"x": 7.941, "y": 6.558}, {"x": 7.937, "y": 6.658}, {"x": 7.724, "y": 6.664}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.555}, {"x": 4.891, "y": 6.546}, {"x": 4.891, "y": 6.649}, {"x": 4.677, "y": 6.655}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.661, "y": 6.562}, {"x": 5.868, "y": 6.553}, {"x": 5.864, "y": 6.65}, {"x": 5.664, "y": 6.659}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.628, "y": 6.563}, {"x": 6.841, "y": 6.55}, {"x": 6.844, "y": 6.654}, {"x": 6.631, "y": 6.66}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.721, "y": 6.567}, {"x": 7.941, "y": 6.558}, {"x": 7.937, "y": 6.658}, {"x": 7.724, "y": 6.664}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.959}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.555}, {"x": 4.891, "y": 6.546}, {"x": 4.891, "y": 6.649}, {"x": 4.677, "y": 6.655}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.661, "y": 6.562}, {"x": 5.868, "y": 6.553}, {"x": 5.864, "y": 6.65}, {"x": 5.664, "y": 6.659}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.628, "y": 6.563}, {"x": 6.841, "y": 6.55}, {"x": 6.844, "y": 6.654}, {"x": 6.631, "y": 6.66}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.721, "y": 6.567}, {"x": 7.941, "y": 6.558}, {"x": 7.937, "y": 6.658}, {"x": 7.724, "y": 6.664}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.555}, {"x": 4.891, "y": 6.546}, {"x": 4.891, "y": 6.649}, {"x": 4.677, "y": 6.655}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.661, "y": 6.562}, {"x": 5.868, "y": 6.553}, {"x": 5.864, "y": 6.65}, {"x": 5.664, "y": 6.659}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.628, "y": 6.563}, {"x": 6.841, "y": 6.55}, {"x": 6.844, "y": 6.654}, {"x": 6.631, "y": 6.66}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.721, "y": 6.567}, {"x": 7.941, "y": 6.558}, {"x": 7.937, "y": 6.658}, {"x": 7.724, "y": 6.664}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2021", "value": "2021-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "252658", "lines": [{"text": "252658", "page": 0, "boundingPolygon": [{"x": 1.821, "y": 6.67}, {"x": 2.204, "y": 6.672}, {"x": 2.207, "y": 6.775}, {"x": 1.82, "y": 6.78}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "05/04/2020", "value": "2020-05-04T00:00:00.000Z", "type": "date", "lines": [{"text": "05/04/2020", "page": 0, "boundingPolygon": [{"x": 3.184, "y": 6.679}, {"x": 3.751, "y": 6.678}, {"x": 3.75, "y": 6.788}, {"x": 3.184, "y": 6.789}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.682}, {"x": 4.894, "y": 6.689}, {"x": 4.894, "y": 6.786}, {"x": 4.674, "y": 6.785}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 5.521, "y": 6.685}, {"x": 5.871, "y": 6.683}, {"x": 5.87, "y": 6.793}, {"x": 5.52, "y": 6.792}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.621, "y": 6.69}, {"x": 6.841, "y": 6.684}, {"x": 6.837, "y": 6.79}, {"x": 6.617, "y": 6.796}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 6.693}, {"x": 7.937, "y": 6.695}, {"x": 7.937, "y": 6.802}, {"x": 7.59, "y": 6.8}], "confidence": 0.959}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "loss_reported_date": {"source": "05/04/2020", "value": "2020-05-04T00:00:00.000Z", "type": "date", "lines": [{"text": "05/04/2020", "page": 0, "boundingPolygon": [{"x": 3.184, "y": 6.679}, {"x": 3.751, "y": 6.678}, {"x": 3.75, "y": 6.788}, {"x": 3.184, "y": 6.789}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.682}, {"x": 4.894, "y": 6.689}, {"x": 4.894, "y": 6.786}, {"x": 4.674, "y": 6.785}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 5.521, "y": 6.685}, {"x": 5.871, "y": 6.683}, {"x": 5.87, "y": 6.793}, {"x": 5.52, "y": 6.792}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.621, "y": 6.69}, {"x": 6.841, "y": 6.684}, {"x": 6.837, "y": 6.79}, {"x": 6.617, "y": 6.796}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 6.693}, {"x": 7.937, "y": 6.695}, {"x": 7.937, "y": 6.802}, {"x": 7.59, "y": 6.8}], "confidence": 0.959}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_incurred": {"source": "323.00", "value": 323, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.682}, {"x": 4.894, "y": 6.689}, {"x": 4.894, "y": 6.786}, {"x": 4.674, "y": 6.785}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 5.521, "y": 6.685}, {"x": 5.871, "y": 6.683}, {"x": 5.87, "y": 6.793}, {"x": 5.52, "y": 6.792}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.621, "y": 6.69}, {"x": 6.841, "y": 6.684}, {"x": 6.837, "y": 6.79}, {"x": 6.617, "y": 6.796}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 6.693}, {"x": 7.937, "y": 6.695}, {"x": 7.937, "y": 6.802}, {"x": 7.59, "y": 6.8}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.959}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.682}, {"x": 4.894, "y": 6.689}, {"x": 4.894, "y": 6.786}, {"x": 4.674, "y": 6.785}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 5.521, "y": 6.685}, {"x": 5.871, "y": 6.683}, {"x": 5.87, "y": 6.793}, {"x": 5.52, "y": 6.792}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.621, "y": 6.69}, {"x": 6.841, "y": 6.684}, {"x": 6.837, "y": 6.79}, {"x": 6.617, "y": 6.796}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 6.693}, {"x": 7.937, "y": 6.695}, {"x": 7.937, "y": 6.802}, {"x": 7.59, "y": 6.8}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.682}, {"x": 4.894, "y": 6.689}, {"x": 4.894, "y": 6.786}, {"x": 4.674, "y": 6.785}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 5.521, "y": 6.685}, {"x": 5.871, "y": 6.683}, {"x": 5.87, "y": 6.793}, {"x": 5.52, "y": 6.792}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.621, "y": 6.69}, {"x": 6.841, "y": 6.684}, {"x": 6.837, "y": 6.79}, {"x": 6.617, "y": 6.796}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 6.693}, {"x": 7.937, "y": 6.695}, {"x": 7.937, "y": 6.802}, {"x": 7.59, "y": 6.8}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "323.00", "value": 323, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.674, "y": 6.682}, {"x": 4.894, "y": 6.689}, {"x": 4.894, "y": 6.786}, {"x": 4.674, "y": 6.785}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 5.521, "y": 6.685}, {"x": 5.871, "y": 6.683}, {"x": 5.87, "y": 6.793}, {"x": 5.52, "y": 6.792}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.621, "y": 6.69}, {"x": 6.841, "y": 6.684}, {"x": 6.837, "y": 6.79}, {"x": 6.617, "y": 6.796}], "confidence": 0.958}, {"text": "323.00", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 6.693}, {"x": 7.937, "y": 6.695}, {"x": 7.937, "y": 6.802}, {"x": 7.59, "y": 6.8}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.959}, "policy_effective_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2021", "value": "2021-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "251682", "lines": [{"text": "251682", "page": 0, "boundingPolygon": [{"x": 1.84, "y": 6.807}, {"x": 2.217, "y": 6.802}, {"x": 2.216, "y": 6.912}, {"x": 1.84, "y": 6.917}], "confidence": 0.957}], "anchorConfidence": 0.957, "valueConfidence": 0.957}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "03/10/2020", "value": "2020-03-10T00:00:00.000Z", "type": "date", "lines": [{"text": "03/10/2020", "page": 0, "boundingPolygon": [{"x": 3.187, "y": 6.813}, {"x": 3.767, "y": 6.815}, {"x": 3.766, "y": 6.925}, {"x": 3.186, "y": 6.923}], "confidence": 0.954}, {"text": "2,754.08", "page": 0, "boundingPolygon": [{"x": 4.453, "y": 6.814}, {"x": 4.89, "y": 6.813}, {"x": 4.89, "y": 6.929}, {"x": 4.453, "y": 6.934}], "confidence": 0.863}, {"text": "109.36", "page": 0, "boundingPolygon": [{"x": 5.537, "y": 6.822}, {"x": 5.87, "y": 6.82}, {"x": 5.87, "y": 6.927}, {"x": 5.54, "y": 6.929}], "confidence": 0.919}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.627, "y": 6.83}, {"x": 6.84, "y": 6.824}, {"x": 6.84, "y": 6.927}, {"x": 6.626, "y": 6.926}], "confidence": 0.958}, {"text": "2,863.44", "page": 0, "boundingPolygon": [{"x": 7.497, "y": 6.833}, {"x": 7.943, "y": 6.828}, {"x": 7.946, "y": 6.935}, {"x": 7.496, "y": 6.94}], "confidence": 0.912}], "anchorConfidence": 0.958, "valueConfidence": 0.954}, "loss_reported_date": {"source": "03/10/2020", "value": "2020-03-10T00:00:00.000Z", "type": "date", "lines": [{"text": "03/10/2020", "page": 0, "boundingPolygon": [{"x": 3.187, "y": 6.813}, {"x": 3.767, "y": 6.815}, {"x": 3.766, "y": 6.925}, {"x": 3.186, "y": 6.923}], "confidence": 0.954}, {"text": "2,754.08", "page": 0, "boundingPolygon": [{"x": 4.453, "y": 6.814}, {"x": 4.89, "y": 6.813}, {"x": 4.89, "y": 6.929}, {"x": 4.453, "y": 6.934}], "confidence": 0.863}, {"text": "109.36", "page": 0, "boundingPolygon": [{"x": 5.537, "y": 6.822}, {"x": 5.87, "y": 6.82}, {"x": 5.87, "y": 6.927}, {"x": 5.54, "y": 6.929}], "confidence": 0.919}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.627, "y": 6.83}, {"x": 6.84, "y": 6.824}, {"x": 6.84, "y": 6.927}, {"x": 6.626, "y": 6.926}], "confidence": 0.958}, {"text": "2,863.44", "page": 0, "boundingPolygon": [{"x": 7.497, "y": 6.833}, {"x": 7.943, "y": 6.828}, {"x": 7.946, "y": 6.935}, {"x": 7.496, "y": 6.94}], "confidence": 0.912}], "anchorConfidence": 0.958, "valueConfidence": 0.954}, "total_amount_incurred": {"source": "2,863.44", "value": 2863.44, "unit": "$", "type": "currency", "lines": [{"text": "2,754.08", "page": 0, "boundingPolygon": [{"x": 4.453, "y": 6.814}, {"x": 4.89, "y": 6.813}, {"x": 4.89, "y": 6.929}, {"x": 4.453, "y": 6.934}], "confidence": 0.863}, {"text": "109.36", "page": 0, "boundingPolygon": [{"x": 5.537, "y": 6.822}, {"x": 5.87, "y": 6.82}, {"x": 5.87, "y": 6.927}, {"x": 5.54, "y": 6.929}], "confidence": 0.919}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.627, "y": 6.83}, {"x": 6.84, "y": 6.824}, {"x": 6.84, "y": 6.927}, {"x": 6.626, "y": 6.926}], "confidence": 0.958}, {"text": "2,863.44", "page": 0, "boundingPolygon": [{"x": 7.497, "y": 6.833}, {"x": 7.943, "y": 6.828}, {"x": 7.946, "y": 6.935}, {"x": 7.496, "y": 6.94}], "confidence": 0.912}], "anchorConfidence": 0.954, "valueConfidence": 0.912}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "2,754.08", "page": 0, "boundingPolygon": [{"x": 4.453, "y": 6.814}, {"x": 4.89, "y": 6.813}, {"x": 4.89, "y": 6.929}, {"x": 4.453, "y": 6.934}], "confidence": 0.863}, {"text": "109.36", "page": 0, "boundingPolygon": [{"x": 5.537, "y": 6.822}, {"x": 5.87, "y": 6.82}, {"x": 5.87, "y": 6.927}, {"x": 5.54, "y": 6.929}], "confidence": 0.919}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.627, "y": 6.83}, {"x": 6.84, "y": 6.824}, {"x": 6.84, "y": 6.927}, {"x": 6.626, "y": 6.926}], "confidence": 0.958}, {"text": "2,863.44", "page": 0, "boundingPolygon": [{"x": 7.497, "y": 6.833}, {"x": 7.943, "y": 6.828}, {"x": 7.946, "y": 6.935}, {"x": 7.496, "y": 6.94}], "confidence": 0.912}], "anchorConfidence": 0.954, "valueConfidence": 0.958}, "total_amount_paid": {"source": "2,754.08", "value": 2754.08, "unit": "$", "type": "currency", "lines": [{"text": "2,754.08", "page": 0, "boundingPolygon": [{"x": 4.453, "y": 6.814}, {"x": 4.89, "y": 6.813}, {"x": 4.89, "y": 6.929}, {"x": 4.453, "y": 6.934}], "confidence": 0.863}, {"text": "109.36", "page": 0, "boundingPolygon": [{"x": 5.537, "y": 6.822}, {"x": 5.87, "y": 6.82}, {"x": 5.87, "y": 6.927}, {"x": 5.54, "y": 6.929}], "confidence": 0.919}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.627, "y": 6.83}, {"x": 6.84, "y": 6.824}, {"x": 6.84, "y": 6.927}, {"x": 6.626, "y": 6.926}], "confidence": 0.958}, {"text": "2,863.44", "page": 0, "boundingPolygon": [{"x": 7.497, "y": 6.833}, {"x": 7.943, "y": 6.828}, {"x": 7.946, "y": 6.935}, {"x": 7.496, "y": 6.94}], "confidence": 0.912}], "anchorConfidence": 0.954, "valueConfidence": 0.863}, "total_amount_paid_expense": {"source": "109.36", "value": 109.36, "unit": "$", "type": "currency", "lines": [{"text": "2,754.08", "page": 0, "boundingPolygon": [{"x": 4.453, "y": 6.814}, {"x": 4.89, "y": 6.813}, {"x": 4.89, "y": 6.929}, {"x": 4.453, "y": 6.934}], "confidence": 0.863}, {"text": "109.36", "page": 0, "boundingPolygon": [{"x": 5.537, "y": 6.822}, {"x": 5.87, "y": 6.82}, {"x": 5.87, "y": 6.927}, {"x": 5.54, "y": 6.929}], "confidence": 0.919}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.627, "y": 6.83}, {"x": 6.84, "y": 6.824}, {"x": 6.84, "y": 6.927}, {"x": 6.626, "y": 6.926}], "confidence": 0.958}, {"text": "2,863.44", "page": 0, "boundingPolygon": [{"x": 7.497, "y": 6.833}, {"x": 7.943, "y": 6.828}, {"x": 7.946, "y": 6.935}, {"x": 7.496, "y": 6.94}], "confidence": 0.912}], "anchorConfidence": 0.954, "valueConfidence": 0.919}, "policy_effective_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2021", "value": "2021-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "250572", "lines": [{"text": "250572", "page": 0, "boundingPolygon": [{"x": 1.836, "y": 7.707}, {"x": 2.213, "y": 7.709}, {"x": 2.213, "y": 7.815}, {"x": 1.836, "y": 7.817}], "confidence": 0.953}], "anchorConfidence": 0.953, "valueConfidence": 0.953}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "01/06/2020", "value": "2020-01-06T00:00:00.000Z", "type": "date", "lines": [{"text": "01/06/2020", "page": 0, "boundingPolygon": [{"x": 3.19, "y": 7.716}, {"x": 3.763, "y": 7.718}, {"x": 3.763, "y": 7.825}, {"x": 3.189, "y": 7.826}], "confidence": 0.955}, {"text": "154.14", "page": 0, "boundingPolygon": [{"x": 4.556, "y": 7.728}, {"x": 4.88, "y": 7.726}, {"x": 4.879, "y": 7.819}, {"x": 4.559, "y": 7.828}], "confidence": 0.959}, {"text": "24.71", "page": 0, "boundingPolygon": [{"x": 5.58, "y": 7.725}, {"x": 5.85, "y": 7.723}, {"x": 5.849, "y": 7.83}, {"x": 5.579, "y": 7.832}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.61, "y": 7.736}, {"x": 6.846, "y": 7.73}, {"x": 6.846, "y": 7.83}, {"x": 6.613, "y": 7.836}], "confidence": 0.958}, {"text": "178.85", "page": 0, "boundingPolygon": [{"x": 7.596, "y": 7.733}, {"x": 7.943, "y": 7.735}, {"x": 7.943, "y": 7.838}, {"x": 7.596, "y": 7.84}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.955}, "loss_reported_date": {"source": "01/06/2020", "value": "2020-01-06T00:00:00.000Z", "type": "date", "lines": [{"text": "01/06/2020", "page": 0, "boundingPolygon": [{"x": 3.19, "y": 7.716}, {"x": 3.763, "y": 7.718}, {"x": 3.763, "y": 7.825}, {"x": 3.189, "y": 7.826}], "confidence": 0.955}, {"text": "154.14", "page": 0, "boundingPolygon": [{"x": 4.556, "y": 7.728}, {"x": 4.88, "y": 7.726}, {"x": 4.879, "y": 7.819}, {"x": 4.559, "y": 7.828}], "confidence": 0.959}, {"text": "24.71", "page": 0, "boundingPolygon": [{"x": 5.58, "y": 7.725}, {"x": 5.85, "y": 7.723}, {"x": 5.849, "y": 7.83}, {"x": 5.579, "y": 7.832}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.61, "y": 7.736}, {"x": 6.846, "y": 7.73}, {"x": 6.846, "y": 7.83}, {"x": 6.613, "y": 7.836}], "confidence": 0.958}, {"text": "178.85", "page": 0, "boundingPolygon": [{"x": 7.596, "y": 7.733}, {"x": 7.943, "y": 7.735}, {"x": 7.943, "y": 7.838}, {"x": 7.596, "y": 7.84}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.955}, "total_amount_incurred": {"source": "178.85", "value": 178.85, "unit": "$", "type": "currency", "lines": [{"text": "154.14", "page": 0, "boundingPolygon": [{"x": 4.556, "y": 7.728}, {"x": 4.88, "y": 7.726}, {"x": 4.879, "y": 7.819}, {"x": 4.559, "y": 7.828}], "confidence": 0.959}, {"text": "24.71", "page": 0, "boundingPolygon": [{"x": 5.58, "y": 7.725}, {"x": 5.85, "y": 7.723}, {"x": 5.849, "y": 7.83}, {"x": 5.579, "y": 7.832}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.61, "y": 7.736}, {"x": 6.846, "y": 7.73}, {"x": 6.846, "y": 7.83}, {"x": 6.613, "y": 7.836}], "confidence": 0.958}, {"text": "178.85", "page": 0, "boundingPolygon": [{"x": 7.596, "y": 7.733}, {"x": 7.943, "y": 7.735}, {"x": 7.943, "y": 7.838}, {"x": 7.596, "y": 7.84}], "confidence": 0.959}], "anchorConfidence": 0.955, "valueConfidence": 0.959}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "154.14", "page": 0, "boundingPolygon": [{"x": 4.556, "y": 7.728}, {"x": 4.88, "y": 7.726}, {"x": 4.879, "y": 7.819}, {"x": 4.559, "y": 7.828}], "confidence": 0.959}, {"text": "24.71", "page": 0, "boundingPolygon": [{"x": 5.58, "y": 7.725}, {"x": 5.85, "y": 7.723}, {"x": 5.849, "y": 7.83}, {"x": 5.579, "y": 7.832}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.61, "y": 7.736}, {"x": 6.846, "y": 7.73}, {"x": 6.846, "y": 7.83}, {"x": 6.613, "y": 7.836}], "confidence": 0.958}, {"text": "178.85", "page": 0, "boundingPolygon": [{"x": 7.596, "y": 7.733}, {"x": 7.943, "y": 7.735}, {"x": 7.943, "y": 7.838}, {"x": 7.596, "y": 7.84}], "confidence": 0.959}], "anchorConfidence": 0.955, "valueConfidence": 0.958}, "total_amount_paid": {"source": "154.14", "value": 154.14, "unit": "$", "type": "currency", "lines": [{"text": "154.14", "page": 0, "boundingPolygon": [{"x": 4.556, "y": 7.728}, {"x": 4.88, "y": 7.726}, {"x": 4.879, "y": 7.819}, {"x": 4.559, "y": 7.828}], "confidence": 0.959}, {"text": "24.71", "page": 0, "boundingPolygon": [{"x": 5.58, "y": 7.725}, {"x": 5.85, "y": 7.723}, {"x": 5.849, "y": 7.83}, {"x": 5.579, "y": 7.832}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.61, "y": 7.736}, {"x": 6.846, "y": 7.73}, {"x": 6.846, "y": 7.83}, {"x": 6.613, "y": 7.836}], "confidence": 0.958}, {"text": "178.85", "page": 0, "boundingPolygon": [{"x": 7.596, "y": 7.733}, {"x": 7.943, "y": 7.735}, {"x": 7.943, "y": 7.838}, {"x": 7.596, "y": 7.84}], "confidence": 0.959}], "anchorConfidence": 0.955, "valueConfidence": 0.959}, "total_amount_paid_expense": {"source": "24.71", "value": 24.71, "unit": "$", "type": "currency", "lines": [{"text": "154.14", "page": 0, "boundingPolygon": [{"x": 4.556, "y": 7.728}, {"x": 4.88, "y": 7.726}, {"x": 4.879, "y": 7.819}, {"x": 4.559, "y": 7.828}], "confidence": 0.959}, {"text": "24.71", "page": 0, "boundingPolygon": [{"x": 5.58, "y": 7.725}, {"x": 5.85, "y": 7.723}, {"x": 5.849, "y": 7.83}, {"x": 5.579, "y": 7.832}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.61, "y": 7.736}, {"x": 6.846, "y": 7.73}, {"x": 6.846, "y": 7.83}, {"x": 6.613, "y": 7.836}], "confidence": 0.958}, {"text": "178.85", "page": 0, "boundingPolygon": [{"x": 7.596, "y": 7.733}, {"x": 7.943, "y": 7.735}, {"x": 7.943, "y": 7.838}, {"x": 7.596, "y": 7.84}], "confidence": 0.959}], "anchorConfidence": 0.955, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2019", "value": "2019-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "249915", "lines": [{"text": "249915", "page": 0, "boundingPolygon": [{"x": 1.826, "y": 7.847}, {"x": 2.196, "y": 7.845}, {"x": 2.195, "y": 7.952}, {"x": 1.825, "y": 7.957}], "confidence": 0.959}], "anchorConfidence": 0.959, "valueConfidence": 0.959}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "11/19/2019", "value": "2019-11-19T00:00:00.000Z", "type": "date", "lines": [{"text": "11/19/2019", "page": 0, "boundingPolygon": [{"x": 3.189, "y": 7.849}, {"x": 3.743, "y": 7.852}, {"x": 3.745, "y": 7.965}, {"x": 3.189, "y": 7.963}], "confidence": 0.909}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.673, "y": 7.862}, {"x": 4.886, "y": 7.853}, {"x": 4.882, "y": 7.959}, {"x": 4.672, "y": 7.962}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.649, "y": 7.869}, {"x": 5.863, "y": 7.86}, {"x": 5.859, "y": 7.96}, {"x": 5.652, "y": 7.966}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.619, "y": 7.873}, {"x": 6.839, "y": 7.86}, {"x": 6.835, "y": 7.964}, {"x": 6.622, "y": 7.97}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.723, "y": 7.877}, {"x": 7.933, "y": 7.865}, {"x": 7.932, "y": 7.968}, {"x": 7.725, "y": 7.974}], "confidence": 0.958}], "anchorConfidence": 0.957, "valueConfidence": 0.909}, "loss_reported_date": {"source": "11/19/2019", "value": "2019-11-19T00:00:00.000Z", "type": "date", "lines": [{"text": "11/19/2019", "page": 0, "boundingPolygon": [{"x": 3.189, "y": 7.849}, {"x": 3.743, "y": 7.852}, {"x": 3.745, "y": 7.965}, {"x": 3.189, "y": 7.963}], "confidence": 0.909}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.673, "y": 7.862}, {"x": 4.886, "y": 7.853}, {"x": 4.882, "y": 7.959}, {"x": 4.672, "y": 7.962}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.649, "y": 7.869}, {"x": 5.863, "y": 7.86}, {"x": 5.859, "y": 7.96}, {"x": 5.652, "y": 7.966}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.619, "y": 7.873}, {"x": 6.839, "y": 7.86}, {"x": 6.835, "y": 7.964}, {"x": 6.622, "y": 7.97}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.723, "y": 7.877}, {"x": 7.933, "y": 7.865}, {"x": 7.932, "y": 7.968}, {"x": 7.725, "y": 7.974}], "confidence": 0.958}], "anchorConfidence": 0.957, "valueConfidence": 0.909}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.673, "y": 7.862}, {"x": 4.886, "y": 7.853}, {"x": 4.882, "y": 7.959}, {"x": 4.672, "y": 7.962}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.649, "y": 7.869}, {"x": 5.863, "y": 7.86}, {"x": 5.859, "y": 7.96}, {"x": 5.652, "y": 7.966}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.619, "y": 7.873}, {"x": 6.839, "y": 7.86}, {"x": 6.835, "y": 7.964}, {"x": 6.622, "y": 7.97}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.723, "y": 7.877}, {"x": 7.933, "y": 7.865}, {"x": 7.932, "y": 7.968}, {"x": 7.725, "y": 7.974}], "confidence": 0.958}], "anchorConfidence": 0.909, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.673, "y": 7.862}, {"x": 4.886, "y": 7.853}, {"x": 4.882, "y": 7.959}, {"x": 4.672, "y": 7.962}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.649, "y": 7.869}, {"x": 5.863, "y": 7.86}, {"x": 5.859, "y": 7.96}, {"x": 5.652, "y": 7.966}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.619, "y": 7.873}, {"x": 6.839, "y": 7.86}, {"x": 6.835, "y": 7.964}, {"x": 6.622, "y": 7.97}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.723, "y": 7.877}, {"x": 7.933, "y": 7.865}, {"x": 7.932, "y": 7.968}, {"x": 7.725, "y": 7.974}], "confidence": 0.958}], "anchorConfidence": 0.909, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.673, "y": 7.862}, {"x": 4.886, "y": 7.853}, {"x": 4.882, "y": 7.959}, {"x": 4.672, "y": 7.962}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.649, "y": 7.869}, {"x": 5.863, "y": 7.86}, {"x": 5.859, "y": 7.96}, {"x": 5.652, "y": 7.966}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.619, "y": 7.873}, {"x": 6.839, "y": 7.86}, {"x": 6.835, "y": 7.964}, {"x": 6.622, "y": 7.97}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.723, "y": 7.877}, {"x": 7.933, "y": 7.865}, {"x": 7.932, "y": 7.968}, {"x": 7.725, "y": 7.974}], "confidence": 0.958}], "anchorConfidence": 0.909, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.673, "y": 7.862}, {"x": 4.886, "y": 7.853}, {"x": 4.882, "y": 7.959}, {"x": 4.672, "y": 7.962}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.649, "y": 7.869}, {"x": 5.863, "y": 7.86}, {"x": 5.859, "y": 7.96}, {"x": 5.652, "y": 7.966}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.619, "y": 7.873}, {"x": 6.839, "y": 7.86}, {"x": 6.835, "y": 7.964}, {"x": 6.622, "y": 7.97}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.723, "y": 7.877}, {"x": 7.933, "y": 7.865}, {"x": 7.932, "y": 7.968}, {"x": 7.725, "y": 7.974}], "confidence": 0.958}], "anchorConfidence": 0.909, "valueConfidence": 0.959}, "policy_effective_date": {"source": "03/01/2019", "value": "2019-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "248005", "lines": [{"text": "248005", "page": 0, "boundingPolygon": [{"x": 1.819, "y": 7.98}, {"x": 2.195, "y": 7.982}, {"x": 2.195, "y": 8.085}, {"x": 1.818, "y": 8.087}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "03/13/2019", "value": "2019-03-13T00:00:00.000Z", "type": "date", "lines": [{"text": "03/13/2019", "page": 0, "boundingPolygon": [{"x": 3.185, "y": 7.986}, {"x": 3.745, "y": 7.985}, {"x": 3.748, "y": 8.098}, {"x": 3.185, "y": 8.099}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.672, "y": 7.999}, {"x": 4.892, "y": 7.996}, {"x": 4.888, "y": 8.096}, {"x": 4.672, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.642, "y": 7.999}, {"x": 5.869, "y": 7.993}, {"x": 5.862, "y": 8.097}, {"x": 5.645, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.625, "y": 8.003}, {"x": 6.849, "y": 8}, {"x": 6.848, "y": 8.097}, {"x": 6.622, "y": 8.11}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.011}, {"x": 7.935, "y": 8.005}, {"x": 7.935, "y": 8.105}, {"x": 7.718, "y": 8.111}], "confidence": 0.956}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "loss_reported_date": {"source": "03/13/2019", "value": "2019-03-13T00:00:00.000Z", "type": "date", "lines": [{"text": "03/13/2019", "page": 0, "boundingPolygon": [{"x": 3.185, "y": 7.986}, {"x": 3.745, "y": 7.985}, {"x": 3.748, "y": 8.098}, {"x": 3.185, "y": 8.099}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.672, "y": 7.999}, {"x": 4.892, "y": 7.996}, {"x": 4.888, "y": 8.096}, {"x": 4.672, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.642, "y": 7.999}, {"x": 5.869, "y": 7.993}, {"x": 5.862, "y": 8.097}, {"x": 5.645, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.625, "y": 8.003}, {"x": 6.849, "y": 8}, {"x": 6.848, "y": 8.097}, {"x": 6.622, "y": 8.11}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.011}, {"x": 7.935, "y": 8.005}, {"x": 7.935, "y": 8.105}, {"x": 7.718, "y": 8.111}], "confidence": 0.956}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.672, "y": 7.999}, {"x": 4.892, "y": 7.996}, {"x": 4.888, "y": 8.096}, {"x": 4.672, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.642, "y": 7.999}, {"x": 5.869, "y": 7.993}, {"x": 5.862, "y": 8.097}, {"x": 5.645, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.625, "y": 8.003}, {"x": 6.849, "y": 8}, {"x": 6.848, "y": 8.097}, {"x": 6.622, "y": 8.11}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.011}, {"x": 7.935, "y": 8.005}, {"x": 7.935, "y": 8.105}, {"x": 7.718, "y": 8.111}], "confidence": 0.956}], "anchorConfidence": 0.958, "valueConfidence": 0.956}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.672, "y": 7.999}, {"x": 4.892, "y": 7.996}, {"x": 4.888, "y": 8.096}, {"x": 4.672, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.642, "y": 7.999}, {"x": 5.869, "y": 7.993}, {"x": 5.862, "y": 8.097}, {"x": 5.645, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.625, "y": 8.003}, {"x": 6.849, "y": 8}, {"x": 6.848, "y": 8.097}, {"x": 6.622, "y": 8.11}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.011}, {"x": 7.935, "y": 8.005}, {"x": 7.935, "y": 8.105}, {"x": 7.718, "y": 8.111}], "confidence": 0.956}], "anchorConfidence": 0.958, "valueConfidence": 0.951}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.672, "y": 7.999}, {"x": 4.892, "y": 7.996}, {"x": 4.888, "y": 8.096}, {"x": 4.672, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.642, "y": 7.999}, {"x": 5.869, "y": 7.993}, {"x": 5.862, "y": 8.097}, {"x": 5.645, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.625, "y": 8.003}, {"x": 6.849, "y": 8}, {"x": 6.848, "y": 8.097}, {"x": 6.622, "y": 8.11}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.011}, {"x": 7.935, "y": 8.005}, {"x": 7.935, "y": 8.105}, {"x": 7.718, "y": 8.111}], "confidence": 0.956}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.672, "y": 7.999}, {"x": 4.892, "y": 7.996}, {"x": 4.888, "y": 8.096}, {"x": 4.672, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.642, "y": 7.999}, {"x": 5.869, "y": 7.993}, {"x": 5.862, "y": 8.097}, {"x": 5.645, "y": 8.102}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.625, "y": 8.003}, {"x": 6.849, "y": 8}, {"x": 6.848, "y": 8.097}, {"x": 6.622, "y": 8.11}], "confidence": 0.951}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.011}, {"x": 7.935, "y": 8.005}, {"x": 7.935, "y": 8.105}, {"x": 7.718, "y": 8.111}], "confidence": 0.956}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2019", "value": "2019-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "246538", "lines": [{"text": "246538", "page": 0, "boundingPolygon": [{"x": 1.828, "y": 8.117}, {"x": 2.195, "y": 8.115}, {"x": 2.194, "y": 8.222}, {"x": 1.828, "y": 8.224}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "05/16/2019", "value": "2019-05-16T00:00:00.000Z", "type": "date", "lines": [{"text": "05/16/2019", "page": 0, "boundingPolygon": [{"x": 3.175, "y": 8.119}, {"x": 3.742, "y": 8.122}, {"x": 3.741, "y": 8.235}, {"x": 3.174, "y": 8.233}], "confidence": 0.956}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.675, "y": 8.129}, {"x": 4.882, "y": 8.123}, {"x": 4.878, "y": 8.229}, {"x": 4.678, "y": 8.229}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.645, "y": 8.136}, {"x": 5.851, "y": 8.133}, {"x": 5.848, "y": 8.233}, {"x": 5.648, "y": 8.239}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.615, "y": 8.143}, {"x": 6.828, "y": 8.127}, {"x": 6.824, "y": 8.234}, {"x": 6.614, "y": 8.236}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.141}, {"x": 7.928, "y": 8.141}, {"x": 7.924, "y": 8.241}, {"x": 7.714, "y": 8.244}], "confidence": 0.958}], "anchorConfidence": 0.947, "valueConfidence": 0.956}, "loss_reported_date": {"source": "05/16/2019", "value": "2019-05-16T00:00:00.000Z", "type": "date", "lines": [{"text": "05/16/2019", "page": 0, "boundingPolygon": [{"x": 3.175, "y": 8.119}, {"x": 3.742, "y": 8.122}, {"x": 3.741, "y": 8.235}, {"x": 3.174, "y": 8.233}], "confidence": 0.956}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.675, "y": 8.129}, {"x": 4.882, "y": 8.123}, {"x": 4.878, "y": 8.229}, {"x": 4.678, "y": 8.229}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.645, "y": 8.136}, {"x": 5.851, "y": 8.133}, {"x": 5.848, "y": 8.233}, {"x": 5.648, "y": 8.239}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.615, "y": 8.143}, {"x": 6.828, "y": 8.127}, {"x": 6.824, "y": 8.234}, {"x": 6.614, "y": 8.236}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.141}, {"x": 7.928, "y": 8.141}, {"x": 7.924, "y": 8.241}, {"x": 7.714, "y": 8.244}], "confidence": 0.958}], "anchorConfidence": 0.947, "valueConfidence": 0.956}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.675, "y": 8.129}, {"x": 4.882, "y": 8.123}, {"x": 4.878, "y": 8.229}, {"x": 4.678, "y": 8.229}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.645, "y": 8.136}, {"x": 5.851, "y": 8.133}, {"x": 5.848, "y": 8.233}, {"x": 5.648, "y": 8.239}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.615, "y": 8.143}, {"x": 6.828, "y": 8.127}, {"x": 6.824, "y": 8.234}, {"x": 6.614, "y": 8.236}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.141}, {"x": 7.928, "y": 8.141}, {"x": 7.924, "y": 8.241}, {"x": 7.714, "y": 8.244}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.675, "y": 8.129}, {"x": 4.882, "y": 8.123}, {"x": 4.878, "y": 8.229}, {"x": 4.678, "y": 8.229}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.645, "y": 8.136}, {"x": 5.851, "y": 8.133}, {"x": 5.848, "y": 8.233}, {"x": 5.648, "y": 8.239}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.615, "y": 8.143}, {"x": 6.828, "y": 8.127}, {"x": 6.824, "y": 8.234}, {"x": 6.614, "y": 8.236}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.141}, {"x": 7.928, "y": 8.141}, {"x": 7.924, "y": 8.241}, {"x": 7.714, "y": 8.244}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.675, "y": 8.129}, {"x": 4.882, "y": 8.123}, {"x": 4.878, "y": 8.229}, {"x": 4.678, "y": 8.229}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.645, "y": 8.136}, {"x": 5.851, "y": 8.133}, {"x": 5.848, "y": 8.233}, {"x": 5.648, "y": 8.239}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.615, "y": 8.143}, {"x": 6.828, "y": 8.127}, {"x": 6.824, "y": 8.234}, {"x": 6.614, "y": 8.236}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.141}, {"x": 7.928, "y": 8.141}, {"x": 7.924, "y": 8.241}, {"x": 7.714, "y": 8.244}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.675, "y": 8.129}, {"x": 4.882, "y": 8.123}, {"x": 4.878, "y": 8.229}, {"x": 4.678, "y": 8.229}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.645, "y": 8.136}, {"x": 5.851, "y": 8.133}, {"x": 5.848, "y": 8.233}, {"x": 5.648, "y": 8.239}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.615, "y": 8.143}, {"x": 6.828, "y": 8.127}, {"x": 6.824, "y": 8.234}, {"x": 6.614, "y": 8.236}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.715, "y": 8.141}, {"x": 7.928, "y": 8.141}, {"x": 7.924, "y": 8.241}, {"x": 7.714, "y": 8.244}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2019", "value": "2019-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2020", "value": "2020-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "241880", "lines": [{"text": "241880", "page": 0, "boundingPolygon": [{"x": 1.828, "y": 9.024}, {"x": 2.208, "y": 9.025}, {"x": 2.207, "y": 9.125}, {"x": 1.827, "y": 9.124}], "confidence": 0.959}], "anchorConfidence": 0.959, "valueConfidence": 0.959}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "08/28/2018", "value": "2018-08-28T00:00:00.000Z", "type": "date", "lines": [{"text": "08/28/2018", "page": 0, "boundingPolygon": [{"x": 3.181, "y": 9.029}, {"x": 3.748, "y": 9.032}, {"x": 3.747, "y": 9.138}, {"x": 3.181, "y": 9.136}], "confidence": 0.948}, {"text": "632.15", "page": 0, "boundingPolygon": [{"x": 4.541, "y": 9.041}, {"x": 4.885, "y": 9.036}, {"x": 4.884, "y": 9.133}, {"x": 4.541, "y": 9.138}], "confidence": 0.959}, {"text": "14.05", "page": 0, "boundingPolygon": [{"x": 5.588, "y": 9.042}, {"x": 5.861, "y": 9.043}, {"x": 5.864, "y": 9.137}, {"x": 5.591, "y": 9.135}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.618, "y": 9.05}, {"x": 6.838, "y": 9.04}, {"x": 6.831, "y": 9.14}, {"x": 6.617, "y": 9.143}], "confidence": 0.958}, {"text": "646.20", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 9.053}, {"x": 7.931, "y": 9.048}, {"x": 7.934, "y": 9.145}, {"x": 7.591, "y": 9.147}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.948}, "loss_reported_date": {"source": "08/28/2018", "value": "2018-08-28T00:00:00.000Z", "type": "date", "lines": [{"text": "08/28/2018", "page": 0, "boundingPolygon": [{"x": 3.181, "y": 9.029}, {"x": 3.748, "y": 9.032}, {"x": 3.747, "y": 9.138}, {"x": 3.181, "y": 9.136}], "confidence": 0.948}, {"text": "632.15", "page": 0, "boundingPolygon": [{"x": 4.541, "y": 9.041}, {"x": 4.885, "y": 9.036}, {"x": 4.884, "y": 9.133}, {"x": 4.541, "y": 9.138}], "confidence": 0.959}, {"text": "14.05", "page": 0, "boundingPolygon": [{"x": 5.588, "y": 9.042}, {"x": 5.861, "y": 9.043}, {"x": 5.864, "y": 9.137}, {"x": 5.591, "y": 9.135}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.618, "y": 9.05}, {"x": 6.838, "y": 9.04}, {"x": 6.831, "y": 9.14}, {"x": 6.617, "y": 9.143}], "confidence": 0.958}, {"text": "646.20", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 9.053}, {"x": 7.931, "y": 9.048}, {"x": 7.934, "y": 9.145}, {"x": 7.591, "y": 9.147}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.948}, "total_amount_incurred": {"source": "646.20", "value": 646.2, "unit": "$", "type": "currency", "lines": [{"text": "632.15", "page": 0, "boundingPolygon": [{"x": 4.541, "y": 9.041}, {"x": 4.885, "y": 9.036}, {"x": 4.884, "y": 9.133}, {"x": 4.541, "y": 9.138}], "confidence": 0.959}, {"text": "14.05", "page": 0, "boundingPolygon": [{"x": 5.588, "y": 9.042}, {"x": 5.861, "y": 9.043}, {"x": 5.864, "y": 9.137}, {"x": 5.591, "y": 9.135}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.618, "y": 9.05}, {"x": 6.838, "y": 9.04}, {"x": 6.831, "y": 9.14}, {"x": 6.617, "y": 9.143}], "confidence": 0.958}, {"text": "646.20", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 9.053}, {"x": 7.931, "y": 9.048}, {"x": 7.934, "y": 9.145}, {"x": 7.591, "y": 9.147}], "confidence": 0.959}], "anchorConfidence": 0.948, "valueConfidence": 0.959}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "632.15", "page": 0, "boundingPolygon": [{"x": 4.541, "y": 9.041}, {"x": 4.885, "y": 9.036}, {"x": 4.884, "y": 9.133}, {"x": 4.541, "y": 9.138}], "confidence": 0.959}, {"text": "14.05", "page": 0, "boundingPolygon": [{"x": 5.588, "y": 9.042}, {"x": 5.861, "y": 9.043}, {"x": 5.864, "y": 9.137}, {"x": 5.591, "y": 9.135}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.618, "y": 9.05}, {"x": 6.838, "y": 9.04}, {"x": 6.831, "y": 9.14}, {"x": 6.617, "y": 9.143}], "confidence": 0.958}, {"text": "646.20", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 9.053}, {"x": 7.931, "y": 9.048}, {"x": 7.934, "y": 9.145}, {"x": 7.591, "y": 9.147}], "confidence": 0.959}], "anchorConfidence": 0.948, "valueConfidence": 0.958}, "total_amount_paid": {"source": "632.15", "value": 632.15, "unit": "$", "type": "currency", "lines": [{"text": "632.15", "page": 0, "boundingPolygon": [{"x": 4.541, "y": 9.041}, {"x": 4.885, "y": 9.036}, {"x": 4.884, "y": 9.133}, {"x": 4.541, "y": 9.138}], "confidence": 0.959}, {"text": "14.05", "page": 0, "boundingPolygon": [{"x": 5.588, "y": 9.042}, {"x": 5.861, "y": 9.043}, {"x": 5.864, "y": 9.137}, {"x": 5.591, "y": 9.135}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.618, "y": 9.05}, {"x": 6.838, "y": 9.04}, {"x": 6.831, "y": 9.14}, {"x": 6.617, "y": 9.143}], "confidence": 0.958}, {"text": "646.20", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 9.053}, {"x": 7.931, "y": 9.048}, {"x": 7.934, "y": 9.145}, {"x": 7.591, "y": 9.147}], "confidence": 0.959}], "anchorConfidence": 0.948, "valueConfidence": 0.959}, "total_amount_paid_expense": {"source": "14.05", "value": 14.05, "unit": "$", "type": "currency", "lines": [{"text": "632.15", "page": 0, "boundingPolygon": [{"x": 4.541, "y": 9.041}, {"x": 4.885, "y": 9.036}, {"x": 4.884, "y": 9.133}, {"x": 4.541, "y": 9.138}], "confidence": 0.959}, {"text": "14.05", "page": 0, "boundingPolygon": [{"x": 5.588, "y": 9.042}, {"x": 5.861, "y": 9.043}, {"x": 5.864, "y": 9.137}, {"x": 5.591, "y": 9.135}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.618, "y": 9.05}, {"x": 6.838, "y": 9.04}, {"x": 6.831, "y": 9.14}, {"x": 6.617, "y": 9.143}], "confidence": 0.958}, {"text": "646.20", "page": 0, "boundingPolygon": [{"x": 7.591, "y": 9.053}, {"x": 7.931, "y": 9.048}, {"x": 7.934, "y": 9.145}, {"x": 7.591, "y": 9.147}], "confidence": 0.959}], "anchorConfidence": 0.948, "valueConfidence": 0.959}, "policy_effective_date": {"source": "03/01/2018", "value": "2018-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2019", "value": "2019-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "240037", "lines": [{"text": "240037", "page": 0, "boundingPolygon": [{"x": 1.831, "y": 9.157}, {"x": 2.201, "y": 9.159}, {"x": 2.2, "y": 9.265}, {"x": 1.827, "y": 9.26}], "confidence": 0.956}], "anchorConfidence": 0.956, "valueConfidence": 0.956}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "05/15/2018", "value": "2018-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2018", "page": 0, "boundingPolygon": [{"x": 3.177, "y": 9.159}, {"x": 3.741, "y": 9.162}, {"x": 3.74, "y": 9.278}, {"x": 3.177, "y": 9.276}], "confidence": 0.955}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.667, "y": 9.169}, {"x": 4.874, "y": 9.166}, {"x": 4.87, "y": 9.266}, {"x": 4.667, "y": 9.269}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.644, "y": 9.172}, {"x": 5.844, "y": 9.166}, {"x": 5.844, "y": 9.27}, {"x": 5.647, "y": 9.272}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.617, "y": 9.18}, {"x": 6.824, "y": 9.174}, {"x": 6.827, "y": 9.274}, {"x": 6.62, "y": 9.276}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.714, "y": 9.184}, {"x": 7.934, "y": 9.188}, {"x": 7.927, "y": 9.291}, {"x": 7.714, "y": 9.281}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.955}, "loss_reported_date": {"source": "05/15/2018", "value": "2018-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2018", "page": 0, "boundingPolygon": [{"x": 3.177, "y": 9.159}, {"x": 3.741, "y": 9.162}, {"x": 3.74, "y": 9.278}, {"x": 3.177, "y": 9.276}], "confidence": 0.955}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.667, "y": 9.169}, {"x": 4.874, "y": 9.166}, {"x": 4.87, "y": 9.266}, {"x": 4.667, "y": 9.269}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.644, "y": 9.172}, {"x": 5.844, "y": 9.166}, {"x": 5.844, "y": 9.27}, {"x": 5.647, "y": 9.272}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.617, "y": 9.18}, {"x": 6.824, "y": 9.174}, {"x": 6.827, "y": 9.274}, {"x": 6.62, "y": 9.276}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.714, "y": 9.184}, {"x": 7.934, "y": 9.188}, {"x": 7.927, "y": 9.291}, {"x": 7.714, "y": 9.281}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.955}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.667, "y": 9.169}, {"x": 4.874, "y": 9.166}, {"x": 4.87, "y": 9.266}, {"x": 4.667, "y": 9.269}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.644, "y": 9.172}, {"x": 5.844, "y": 9.166}, {"x": 5.844, "y": 9.27}, {"x": 5.647, "y": 9.272}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.617, "y": 9.18}, {"x": 6.824, "y": 9.174}, {"x": 6.827, "y": 9.274}, {"x": 6.62, "y": 9.276}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.714, "y": 9.184}, {"x": 7.934, "y": 9.188}, {"x": 7.927, "y": 9.291}, {"x": 7.714, "y": 9.281}], "confidence": 0.958}], "anchorConfidence": 0.955, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.667, "y": 9.169}, {"x": 4.874, "y": 9.166}, {"x": 4.87, "y": 9.266}, {"x": 4.667, "y": 9.269}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.644, "y": 9.172}, {"x": 5.844, "y": 9.166}, {"x": 5.844, "y": 9.27}, {"x": 5.647, "y": 9.272}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.617, "y": 9.18}, {"x": 6.824, "y": 9.174}, {"x": 6.827, "y": 9.274}, {"x": 6.62, "y": 9.276}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.714, "y": 9.184}, {"x": 7.934, "y": 9.188}, {"x": 7.927, "y": 9.291}, {"x": 7.714, "y": 9.281}], "confidence": 0.958}], "anchorConfidence": 0.955, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.667, "y": 9.169}, {"x": 4.874, "y": 9.166}, {"x": 4.87, "y": 9.266}, {"x": 4.667, "y": 9.269}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.644, "y": 9.172}, {"x": 5.844, "y": 9.166}, {"x": 5.844, "y": 9.27}, {"x": 5.647, "y": 9.272}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.617, "y": 9.18}, {"x": 6.824, "y": 9.174}, {"x": 6.827, "y": 9.274}, {"x": 6.62, "y": 9.276}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.714, "y": 9.184}, {"x": 7.934, "y": 9.188}, {"x": 7.927, "y": 9.291}, {"x": 7.714, "y": 9.281}], "confidence": 0.958}], "anchorConfidence": 0.955, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.667, "y": 9.169}, {"x": 4.874, "y": 9.166}, {"x": 4.87, "y": 9.266}, {"x": 4.667, "y": 9.269}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.644, "y": 9.172}, {"x": 5.844, "y": 9.166}, {"x": 5.844, "y": 9.27}, {"x": 5.647, "y": 9.272}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.617, "y": 9.18}, {"x": 6.824, "y": 9.174}, {"x": 6.827, "y": 9.274}, {"x": 6.62, "y": 9.276}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.714, "y": 9.184}, {"x": 7.934, "y": 9.188}, {"x": 7.927, "y": 9.291}, {"x": 7.714, "y": 9.281}], "confidence": 0.958}], "anchorConfidence": 0.955, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2018", "value": "2018-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2019", "value": "2019-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "237765", "lines": [{"text": "237765", "page": 0, "boundingPolygon": [{"x": 1.814, "y": 10.064}, {"x": 2.194, "y": 10.059}, {"x": 2.193, "y": 10.169}, {"x": 1.813, "y": 10.17}], "confidence": 0.953}], "anchorConfidence": 0.953, "valueConfidence": 0.953}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "01/18/2018", "value": "2018-01-18T00:00:00.000Z", "type": "date", "lines": [{"text": "01/18/2018", "page": 0, "boundingPolygon": [{"x": 3.18, "y": 10.066}, {"x": 3.737, "y": 10.068}, {"x": 3.737, "y": 10.178}, {"x": 3.18, "y": 10.176}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.664, "y": 10.078}, {"x": 4.877, "y": 10.076}, {"x": 4.873, "y": 10.173}, {"x": 4.663, "y": 10.178}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.65, "y": 10.089}, {"x": 5.85, "y": 10.08}, {"x": 5.847, "y": 10.177}, {"x": 5.65, "y": 10.182}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.62, "y": 10.093}, {"x": 6.827, "y": 10.08}, {"x": 6.827, "y": 10.177}, {"x": 6.62, "y": 10.186}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.717, "y": 10.091}, {"x": 7.924, "y": 10.081}, {"x": 7.923, "y": 10.185}, {"x": 7.717, "y": 10.191}], "confidence": 0.957}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "loss_reported_date": {"source": "01/18/2018", "value": "2018-01-18T00:00:00.000Z", "type": "date", "lines": [{"text": "01/18/2018", "page": 0, "boundingPolygon": [{"x": 3.18, "y": 10.066}, {"x": 3.737, "y": 10.068}, {"x": 3.737, "y": 10.178}, {"x": 3.18, "y": 10.176}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.664, "y": 10.078}, {"x": 4.877, "y": 10.076}, {"x": 4.873, "y": 10.173}, {"x": 4.663, "y": 10.178}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.65, "y": 10.089}, {"x": 5.85, "y": 10.08}, {"x": 5.847, "y": 10.177}, {"x": 5.65, "y": 10.182}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.62, "y": 10.093}, {"x": 6.827, "y": 10.08}, {"x": 6.827, "y": 10.177}, {"x": 6.62, "y": 10.186}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.717, "y": 10.091}, {"x": 7.924, "y": 10.081}, {"x": 7.923, "y": 10.185}, {"x": 7.717, "y": 10.191}], "confidence": 0.957}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.664, "y": 10.078}, {"x": 4.877, "y": 10.076}, {"x": 4.873, "y": 10.173}, {"x": 4.663, "y": 10.178}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.65, "y": 10.089}, {"x": 5.85, "y": 10.08}, {"x": 5.847, "y": 10.177}, {"x": 5.65, "y": 10.182}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.62, "y": 10.093}, {"x": 6.827, "y": 10.08}, {"x": 6.827, "y": 10.177}, {"x": 6.62, "y": 10.186}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.717, "y": 10.091}, {"x": 7.924, "y": 10.081}, {"x": 7.923, "y": 10.185}, {"x": 7.717, "y": 10.191}], "confidence": 0.957}], "anchorConfidence": 0.958, "valueConfidence": 0.957}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.664, "y": 10.078}, {"x": 4.877, "y": 10.076}, {"x": 4.873, "y": 10.173}, {"x": 4.663, "y": 10.178}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.65, "y": 10.089}, {"x": 5.85, "y": 10.08}, {"x": 5.847, "y": 10.177}, {"x": 5.65, "y": 10.182}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.62, "y": 10.093}, {"x": 6.827, "y": 10.08}, {"x": 6.827, "y": 10.177}, {"x": 6.62, "y": 10.186}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.717, "y": 10.091}, {"x": 7.924, "y": 10.081}, {"x": 7.923, "y": 10.185}, {"x": 7.717, "y": 10.191}], "confidence": 0.957}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.664, "y": 10.078}, {"x": 4.877, "y": 10.076}, {"x": 4.873, "y": 10.173}, {"x": 4.663, "y": 10.178}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.65, "y": 10.089}, {"x": 5.85, "y": 10.08}, {"x": 5.847, "y": 10.177}, {"x": 5.65, "y": 10.182}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.62, "y": 10.093}, {"x": 6.827, "y": 10.08}, {"x": 6.827, "y": 10.177}, {"x": 6.62, "y": 10.186}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.717, "y": 10.091}, {"x": 7.924, "y": 10.081}, {"x": 7.923, "y": 10.185}, {"x": 7.717, "y": 10.191}], "confidence": 0.957}], "anchorConfidence": 0.958, "valueConfidence": 0.959}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.664, "y": 10.078}, {"x": 4.877, "y": 10.076}, {"x": 4.873, "y": 10.173}, {"x": 4.663, "y": 10.178}], "confidence": 0.959}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.65, "y": 10.089}, {"x": 5.85, "y": 10.08}, {"x": 5.847, "y": 10.177}, {"x": 5.65, "y": 10.182}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.62, "y": 10.093}, {"x": 6.827, "y": 10.08}, {"x": 6.827, "y": 10.177}, {"x": 6.62, "y": 10.186}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.717, "y": 10.091}, {"x": 7.924, "y": 10.081}, {"x": 7.923, "y": 10.185}, {"x": 7.717, "y": 10.191}], "confidence": 0.957}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2017", "value": "2017-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2018", "value": "2018-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "237764", "lines": [{"text": "237764", "page": 0, "boundingPolygon": [{"x": 1.81, "y": 10.197}, {"x": 2.197, "y": 10.195}, {"x": 2.2, "y": 10.299}, {"x": 1.809, "y": 10.304}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "01/12/2018", "value": "2018-01-12T00:00:00.000Z", "type": "date", "lines": [{"text": "01/12/2018", "page": 0, "boundingPolygon": [{"x": 3.18, "y": 10.203}, {"x": 3.743, "y": 10.202}, {"x": 3.743, "y": 10.312}, {"x": 3.179, "y": 10.313}], "confidence": 0.936}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.66, "y": 10.212}, {"x": 4.88, "y": 10.209}, {"x": 4.876, "y": 10.313}, {"x": 4.659, "y": 10.315}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.63, "y": 10.216}, {"x": 5.86, "y": 10.21}, {"x": 5.853, "y": 10.31}, {"x": 5.633, "y": 10.319}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.613, "y": 10.22}, {"x": 6.836, "y": 10.214}, {"x": 6.839, "y": 10.314}, {"x": 6.613, "y": 10.326}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.227}, {"x": 7.923, "y": 10.218}, {"x": 7.916, "y": 10.318}, {"x": 7.706, "y": 10.327}], "confidence": 0.958}], "anchorConfidence": 0.954, "valueConfidence": 0.936}, "loss_reported_date": {"source": "01/12/2018", "value": "2018-01-12T00:00:00.000Z", "type": "date", "lines": [{"text": "01/12/2018", "page": 0, "boundingPolygon": [{"x": 3.18, "y": 10.203}, {"x": 3.743, "y": 10.202}, {"x": 3.743, "y": 10.312}, {"x": 3.179, "y": 10.313}], "confidence": 0.936}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.66, "y": 10.212}, {"x": 4.88, "y": 10.209}, {"x": 4.876, "y": 10.313}, {"x": 4.659, "y": 10.315}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.63, "y": 10.216}, {"x": 5.86, "y": 10.21}, {"x": 5.853, "y": 10.31}, {"x": 5.633, "y": 10.319}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.613, "y": 10.22}, {"x": 6.836, "y": 10.214}, {"x": 6.839, "y": 10.314}, {"x": 6.613, "y": 10.326}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.227}, {"x": 7.923, "y": 10.218}, {"x": 7.916, "y": 10.318}, {"x": 7.706, "y": 10.327}], "confidence": 0.958}], "anchorConfidence": 0.954, "valueConfidence": 0.936}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.66, "y": 10.212}, {"x": 4.88, "y": 10.209}, {"x": 4.876, "y": 10.313}, {"x": 4.659, "y": 10.315}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.63, "y": 10.216}, {"x": 5.86, "y": 10.21}, {"x": 5.853, "y": 10.31}, {"x": 5.633, "y": 10.319}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.613, "y": 10.22}, {"x": 6.836, "y": 10.214}, {"x": 6.839, "y": 10.314}, {"x": 6.613, "y": 10.326}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.227}, {"x": 7.923, "y": 10.218}, {"x": 7.916, "y": 10.318}, {"x": 7.706, "y": 10.327}], "confidence": 0.958}], "anchorConfidence": 0.936, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.66, "y": 10.212}, {"x": 4.88, "y": 10.209}, {"x": 4.876, "y": 10.313}, {"x": 4.659, "y": 10.315}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.63, "y": 10.216}, {"x": 5.86, "y": 10.21}, {"x": 5.853, "y": 10.31}, {"x": 5.633, "y": 10.319}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.613, "y": 10.22}, {"x": 6.836, "y": 10.214}, {"x": 6.839, "y": 10.314}, {"x": 6.613, "y": 10.326}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.227}, {"x": 7.923, "y": 10.218}, {"x": 7.916, "y": 10.318}, {"x": 7.706, "y": 10.327}], "confidence": 0.958}], "anchorConfidence": 0.936, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.66, "y": 10.212}, {"x": 4.88, "y": 10.209}, {"x": 4.876, "y": 10.313}, {"x": 4.659, "y": 10.315}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.63, "y": 10.216}, {"x": 5.86, "y": 10.21}, {"x": 5.853, "y": 10.31}, {"x": 5.633, "y": 10.319}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.613, "y": 10.22}, {"x": 6.836, "y": 10.214}, {"x": 6.839, "y": 10.314}, {"x": 6.613, "y": 10.326}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.227}, {"x": 7.923, "y": 10.218}, {"x": 7.916, "y": 10.318}, {"x": 7.706, "y": 10.327}], "confidence": 0.958}], "anchorConfidence": 0.936, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.66, "y": 10.212}, {"x": 4.88, "y": 10.209}, {"x": 4.876, "y": 10.313}, {"x": 4.659, "y": 10.315}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.63, "y": 10.216}, {"x": 5.86, "y": 10.21}, {"x": 5.853, "y": 10.31}, {"x": 5.633, "y": 10.319}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.613, "y": 10.22}, {"x": 6.836, "y": 10.214}, {"x": 6.839, "y": 10.314}, {"x": 6.613, "y": 10.326}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.227}, {"x": 7.923, "y": 10.218}, {"x": 7.916, "y": 10.318}, {"x": 7.706, "y": 10.327}], "confidence": 0.958}], "anchorConfidence": 0.936, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2017", "value": "2017-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2018", "value": "2018-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "237663", "lines": [{"text": "237663", "page": 0, "boundingPolygon": [{"x": 1.813, "y": 10.33}, {"x": 2.183, "y": 10.329}, {"x": 2.182, "y": 10.435}, {"x": 1.812, "y": 10.44}], "confidence": 0.959}], "anchorConfidence": 0.959, "valueConfidence": 0.959}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "01/16/2018", "value": "2018-01-16T00:00:00.000Z", "type": "date", "lines": [{"text": "01/16/2018", "page": 0, "boundingPolygon": [{"x": 3.173, "y": 10.336}, {"x": 3.726, "y": 10.338}, {"x": 3.726, "y": 10.448}, {"x": 3.172, "y": 10.449}], "confidence": 0.956}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.659, "y": 10.342}, {"x": 4.876, "y": 10.346}, {"x": 4.876, "y": 10.446}, {"x": 4.659, "y": 10.445}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.636, "y": 10.352}, {"x": 5.859, "y": 10.347}, {"x": 5.856, "y": 10.447}, {"x": 5.639, "y": 10.452}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.606, "y": 10.353}, {"x": 6.823, "y": 10.35}, {"x": 6.819, "y": 10.45}, {"x": 6.606, "y": 10.453}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.357}, {"x": 7.919, "y": 10.355}, {"x": 7.916, "y": 10.455}, {"x": 7.702, "y": 10.457}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.956}, "loss_reported_date": {"source": "01/16/2018", "value": "2018-01-16T00:00:00.000Z", "type": "date", "lines": [{"text": "01/16/2018", "page": 0, "boundingPolygon": [{"x": 3.173, "y": 10.336}, {"x": 3.726, "y": 10.338}, {"x": 3.726, "y": 10.448}, {"x": 3.172, "y": 10.449}], "confidence": 0.956}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.659, "y": 10.342}, {"x": 4.876, "y": 10.346}, {"x": 4.876, "y": 10.446}, {"x": 4.659, "y": 10.445}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.636, "y": 10.352}, {"x": 5.859, "y": 10.347}, {"x": 5.856, "y": 10.447}, {"x": 5.639, "y": 10.452}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.606, "y": 10.353}, {"x": 6.823, "y": 10.35}, {"x": 6.819, "y": 10.45}, {"x": 6.606, "y": 10.453}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.357}, {"x": 7.919, "y": 10.355}, {"x": 7.916, "y": 10.455}, {"x": 7.702, "y": 10.457}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.956}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.659, "y": 10.342}, {"x": 4.876, "y": 10.346}, {"x": 4.876, "y": 10.446}, {"x": 4.659, "y": 10.445}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.636, "y": 10.352}, {"x": 5.859, "y": 10.347}, {"x": 5.856, "y": 10.447}, {"x": 5.639, "y": 10.452}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.606, "y": 10.353}, {"x": 6.823, "y": 10.35}, {"x": 6.819, "y": 10.45}, {"x": 6.606, "y": 10.453}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.357}, {"x": 7.919, "y": 10.355}, {"x": 7.916, "y": 10.455}, {"x": 7.702, "y": 10.457}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.659, "y": 10.342}, {"x": 4.876, "y": 10.346}, {"x": 4.876, "y": 10.446}, {"x": 4.659, "y": 10.445}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.636, "y": 10.352}, {"x": 5.859, "y": 10.347}, {"x": 5.856, "y": 10.447}, {"x": 5.639, "y": 10.452}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.606, "y": 10.353}, {"x": 6.823, "y": 10.35}, {"x": 6.819, "y": 10.45}, {"x": 6.606, "y": 10.453}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.357}, {"x": 7.919, "y": 10.355}, {"x": 7.916, "y": 10.455}, {"x": 7.702, "y": 10.457}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.659, "y": 10.342}, {"x": 4.876, "y": 10.346}, {"x": 4.876, "y": 10.446}, {"x": 4.659, "y": 10.445}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.636, "y": 10.352}, {"x": 5.859, "y": 10.347}, {"x": 5.856, "y": 10.447}, {"x": 5.639, "y": 10.452}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.606, "y": 10.353}, {"x": 6.823, "y": 10.35}, {"x": 6.819, "y": 10.45}, {"x": 6.606, "y": 10.453}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.357}, {"x": 7.919, "y": 10.355}, {"x": 7.916, "y": 10.455}, {"x": 7.702, "y": 10.457}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 0, "boundingPolygon": [{"x": 4.659, "y": 10.342}, {"x": 4.876, "y": 10.346}, {"x": 4.876, "y": 10.446}, {"x": 4.659, "y": 10.445}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 5.636, "y": 10.352}, {"x": 5.859, "y": 10.347}, {"x": 5.856, "y": 10.447}, {"x": 5.639, "y": 10.452}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.606, "y": 10.353}, {"x": 6.823, "y": 10.35}, {"x": 6.819, "y": 10.45}, {"x": 6.606, "y": 10.453}], "confidence": 0.958}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 7.703, "y": 10.357}, {"x": 7.919, "y": 10.355}, {"x": 7.916, "y": 10.455}, {"x": 7.702, "y": 10.457}], "confidence": 0.958}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2017", "value": "2017-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2018", "value": "2018-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "234583", "lines": [{"text": "234583", "page": 0, "boundingPolygon": [{"x": 1.822, "y": 10.464}, {"x": 2.195, "y": 10.462}, {"x": 2.195, "y": 10.575}, {"x": 1.822, "y": 10.577}], "confidence": 0.956}], "anchorConfidence": 0.956, "valueConfidence": 0.956}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "07/21/2017", "value": "2017-07-21T00:00:00.000Z", "type": "date", "lines": [{"text": "07/21/2017", "page": 0, "boundingPolygon": [{"x": 3.175, "y": 10.473}, {"x": 3.749, "y": 10.475}, {"x": 3.748, "y": 10.588}, {"x": 3.175, "y": 10.583}], "confidence": 0.939}, {"text": "1,181.26", "page": 0, "boundingPolygon": [{"x": 4.435, "y": 10.478}, {"x": 4.869, "y": 10.476}, {"x": 4.872, "y": 10.583}, {"x": 4.438, "y": 10.591}], "confidence": 0.762}, {"text": "1,281.21", "page": 0, "boundingPolygon": [{"x": 5.412, "y": 10.485}, {"x": 5.839, "y": 10.473}, {"x": 5.842, "y": 10.586}, {"x": 5.412, "y": 10.595}], "confidence": 0.85}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.612, "y": 10.493}, {"x": 6.832, "y": 10.484}, {"x": 6.832, "y": 10.59}, {"x": 6.615, "y": 10.596}], "confidence": 0.958}, {"text": "2,462.47", "page": 0, "boundingPolygon": [{"x": 7.479, "y": 10.483}, {"x": 7.912, "y": 10.485}, {"x": 7.912, "y": 10.601}, {"x": 7.478, "y": 10.603}], "confidence": 0.799}], "anchorConfidence": 0.958, "valueConfidence": 0.939}, "loss_reported_date": {"source": "07/21/2017", "value": "2017-07-21T00:00:00.000Z", "type": "date", "lines": [{"text": "07/21/2017", "page": 0, "boundingPolygon": [{"x": 3.175, "y": 10.473}, {"x": 3.749, "y": 10.475}, {"x": 3.748, "y": 10.588}, {"x": 3.175, "y": 10.583}], "confidence": 0.939}, {"text": "1,181.26", "page": 0, "boundingPolygon": [{"x": 4.435, "y": 10.478}, {"x": 4.869, "y": 10.476}, {"x": 4.872, "y": 10.583}, {"x": 4.438, "y": 10.591}], "confidence": 0.762}, {"text": "1,281.21", "page": 0, "boundingPolygon": [{"x": 5.412, "y": 10.485}, {"x": 5.839, "y": 10.473}, {"x": 5.842, "y": 10.586}, {"x": 5.412, "y": 10.595}], "confidence": 0.85}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.612, "y": 10.493}, {"x": 6.832, "y": 10.484}, {"x": 6.832, "y": 10.59}, {"x": 6.615, "y": 10.596}], "confidence": 0.958}, {"text": "2,462.47", "page": 0, "boundingPolygon": [{"x": 7.479, "y": 10.483}, {"x": 7.912, "y": 10.485}, {"x": 7.912, "y": 10.601}, {"x": 7.478, "y": 10.603}], "confidence": 0.799}], "anchorConfidence": 0.958, "valueConfidence": 0.939}, "total_amount_incurred": {"source": "2,462.47", "value": 2462.47, "unit": "$", "type": "currency", "lines": [{"text": "1,181.26", "page": 0, "boundingPolygon": [{"x": 4.435, "y": 10.478}, {"x": 4.869, "y": 10.476}, {"x": 4.872, "y": 10.583}, {"x": 4.438, "y": 10.591}], "confidence": 0.762}, {"text": "1,281.21", "page": 0, "boundingPolygon": [{"x": 5.412, "y": 10.485}, {"x": 5.839, "y": 10.473}, {"x": 5.842, "y": 10.586}, {"x": 5.412, "y": 10.595}], "confidence": 0.85}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.612, "y": 10.493}, {"x": 6.832, "y": 10.484}, {"x": 6.832, "y": 10.59}, {"x": 6.615, "y": 10.596}], "confidence": 0.958}, {"text": "2,462.47", "page": 0, "boundingPolygon": [{"x": 7.479, "y": 10.483}, {"x": 7.912, "y": 10.485}, {"x": 7.912, "y": 10.601}, {"x": 7.478, "y": 10.603}], "confidence": 0.799}], "anchorConfidence": 0.939, "valueConfidence": 0.799}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "1,181.26", "page": 0, "boundingPolygon": [{"x": 4.435, "y": 10.478}, {"x": 4.869, "y": 10.476}, {"x": 4.872, "y": 10.583}, {"x": 4.438, "y": 10.591}], "confidence": 0.762}, {"text": "1,281.21", "page": 0, "boundingPolygon": [{"x": 5.412, "y": 10.485}, {"x": 5.839, "y": 10.473}, {"x": 5.842, "y": 10.586}, {"x": 5.412, "y": 10.595}], "confidence": 0.85}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.612, "y": 10.493}, {"x": 6.832, "y": 10.484}, {"x": 6.832, "y": 10.59}, {"x": 6.615, "y": 10.596}], "confidence": 0.958}, {"text": "2,462.47", "page": 0, "boundingPolygon": [{"x": 7.479, "y": 10.483}, {"x": 7.912, "y": 10.485}, {"x": 7.912, "y": 10.601}, {"x": 7.478, "y": 10.603}], "confidence": 0.799}], "anchorConfidence": 0.939, "valueConfidence": 0.958}, "total_amount_paid": {"source": "1,181.26", "value": 1181.26, "unit": "$", "type": "currency", "lines": [{"text": "1,181.26", "page": 0, "boundingPolygon": [{"x": 4.435, "y": 10.478}, {"x": 4.869, "y": 10.476}, {"x": 4.872, "y": 10.583}, {"x": 4.438, "y": 10.591}], "confidence": 0.762}, {"text": "1,281.21", "page": 0, "boundingPolygon": [{"x": 5.412, "y": 10.485}, {"x": 5.839, "y": 10.473}, {"x": 5.842, "y": 10.586}, {"x": 5.412, "y": 10.595}], "confidence": 0.85}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.612, "y": 10.493}, {"x": 6.832, "y": 10.484}, {"x": 6.832, "y": 10.59}, {"x": 6.615, "y": 10.596}], "confidence": 0.958}, {"text": "2,462.47", "page": 0, "boundingPolygon": [{"x": 7.479, "y": 10.483}, {"x": 7.912, "y": 10.485}, {"x": 7.912, "y": 10.601}, {"x": 7.478, "y": 10.603}], "confidence": 0.799}], "anchorConfidence": 0.939, "valueConfidence": 0.762}, "total_amount_paid_expense": {"source": "1,281.21", "value": 1281.21, "unit": "$", "type": "currency", "lines": [{"text": "1,181.26", "page": 0, "boundingPolygon": [{"x": 4.435, "y": 10.478}, {"x": 4.869, "y": 10.476}, {"x": 4.872, "y": 10.583}, {"x": 4.438, "y": 10.591}], "confidence": 0.762}, {"text": "1,281.21", "page": 0, "boundingPolygon": [{"x": 5.412, "y": 10.485}, {"x": 5.839, "y": 10.473}, {"x": 5.842, "y": 10.586}, {"x": 5.412, "y": 10.595}], "confidence": 0.85}, {"text": "0.00", "page": 0, "boundingPolygon": [{"x": 6.612, "y": 10.493}, {"x": 6.832, "y": 10.484}, {"x": 6.832, "y": 10.59}, {"x": 6.615, "y": 10.596}], "confidence": 0.958}, {"text": "2,462.47", "page": 0, "boundingPolygon": [{"x": 7.479, "y": 10.483}, {"x": 7.912, "y": 10.485}, {"x": 7.912, "y": 10.601}, {"x": 7.478, "y": 10.603}], "confidence": 0.799}], "anchorConfidence": 0.939, "valueConfidence": 0.85}, "policy_effective_date": {"source": "03/01/2017", "value": "2017-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2018", "value": "2018-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "230527", "lines": [{"text": "230527", "page": 1, "boundingPolygon": [{"x": 1.81, "y": 1.197}, {"x": 2.19, "y": 1.193}, {"x": 2.193, "y": 1.3}, {"x": 1.81, "y": 1.303}], "confidence": 0.959}], "anchorConfidence": 0.959, "valueConfidence": 0.959}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "11/11/2016", "value": "2016-11-11T00:00:00.000Z", "type": "date", "lines": [{"text": "11/11/2016", "page": 1, "boundingPolygon": [{"x": 3.177, "y": 1.197}, {"x": 3.737, "y": 1.19}, {"x": 3.74, "y": 1.3}, {"x": 3.177, "y": 1.307}], "confidence": 0.947}, {"text": "292.34", "page": 1, "boundingPolygon": [{"x": 4.537, "y": 1.197}, {"x": 4.88, "y": 1.193}, {"x": 4.883, "y": 1.297}, {"x": 4.54, "y": 1.3}], "confidence": 0.958}, {"text": "14.20", "page": 1, "boundingPolygon": [{"x": 5.58, "y": 1.2}, {"x": 5.85, "y": 1.19}, {"x": 5.847, "y": 1.297}, {"x": 5.58, "y": 1.3}], "confidence": 0.856}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.617, "y": 1.207}, {"x": 6.827, "y": 1.19}, {"x": 6.823, "y": 1.29}, {"x": 6.617, "y": 1.3}], "confidence": 0.958}, {"text": "306.54", "page": 1, "boundingPolygon": [{"x": 7.59, "y": 1.2}, {"x": 7.93, "y": 1.197}, {"x": 7.933, "y": 1.293}, {"x": 7.59, "y": 1.3}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.947}, "loss_reported_date": {"source": "11/11/2016", "value": "2016-11-11T00:00:00.000Z", "type": "date", "lines": [{"text": "11/11/2016", "page": 1, "boundingPolygon": [{"x": 3.177, "y": 1.197}, {"x": 3.737, "y": 1.19}, {"x": 3.74, "y": 1.3}, {"x": 3.177, "y": 1.307}], "confidence": 0.947}, {"text": "292.34", "page": 1, "boundingPolygon": [{"x": 4.537, "y": 1.197}, {"x": 4.88, "y": 1.193}, {"x": 4.883, "y": 1.297}, {"x": 4.54, "y": 1.3}], "confidence": 0.958}, {"text": "14.20", "page": 1, "boundingPolygon": [{"x": 5.58, "y": 1.2}, {"x": 5.85, "y": 1.19}, {"x": 5.847, "y": 1.297}, {"x": 5.58, "y": 1.3}], "confidence": 0.856}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.617, "y": 1.207}, {"x": 6.827, "y": 1.19}, {"x": 6.823, "y": 1.29}, {"x": 6.617, "y": 1.3}], "confidence": 0.958}, {"text": "306.54", "page": 1, "boundingPolygon": [{"x": 7.59, "y": 1.2}, {"x": 7.93, "y": 1.197}, {"x": 7.933, "y": 1.293}, {"x": 7.59, "y": 1.3}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.947}, "total_amount_incurred": {"source": "306.54", "value": 306.54, "unit": "$", "type": "currency", "lines": [{"text": "292.34", "page": 1, "boundingPolygon": [{"x": 4.537, "y": 1.197}, {"x": 4.88, "y": 1.193}, {"x": 4.883, "y": 1.297}, {"x": 4.54, "y": 1.3}], "confidence": 0.958}, {"text": "14.20", "page": 1, "boundingPolygon": [{"x": 5.58, "y": 1.2}, {"x": 5.85, "y": 1.19}, {"x": 5.847, "y": 1.297}, {"x": 5.58, "y": 1.3}], "confidence": 0.856}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.617, "y": 1.207}, {"x": 6.827, "y": 1.19}, {"x": 6.823, "y": 1.29}, {"x": 6.617, "y": 1.3}], "confidence": 0.958}, {"text": "306.54", "page": 1, "boundingPolygon": [{"x": 7.59, "y": 1.2}, {"x": 7.93, "y": 1.197}, {"x": 7.933, "y": 1.293}, {"x": 7.59, "y": 1.3}], "confidence": 0.959}], "anchorConfidence": 0.947, "valueConfidence": 0.959}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "292.34", "page": 1, "boundingPolygon": [{"x": 4.537, "y": 1.197}, {"x": 4.88, "y": 1.193}, {"x": 4.883, "y": 1.297}, {"x": 4.54, "y": 1.3}], "confidence": 0.958}, {"text": "14.20", "page": 1, "boundingPolygon": [{"x": 5.58, "y": 1.2}, {"x": 5.85, "y": 1.19}, {"x": 5.847, "y": 1.297}, {"x": 5.58, "y": 1.3}], "confidence": 0.856}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.617, "y": 1.207}, {"x": 6.827, "y": 1.19}, {"x": 6.823, "y": 1.29}, {"x": 6.617, "y": 1.3}], "confidence": 0.958}, {"text": "306.54", "page": 1, "boundingPolygon": [{"x": 7.59, "y": 1.2}, {"x": 7.93, "y": 1.197}, {"x": 7.933, "y": 1.293}, {"x": 7.59, "y": 1.3}], "confidence": 0.959}], "anchorConfidence": 0.947, "valueConfidence": 0.958}, "total_amount_paid": {"source": "292.34", "value": 292.34, "unit": "$", "type": "currency", "lines": [{"text": "292.34", "page": 1, "boundingPolygon": [{"x": 4.537, "y": 1.197}, {"x": 4.88, "y": 1.193}, {"x": 4.883, "y": 1.297}, {"x": 4.54, "y": 1.3}], "confidence": 0.958}, {"text": "14.20", "page": 1, "boundingPolygon": [{"x": 5.58, "y": 1.2}, {"x": 5.85, "y": 1.19}, {"x": 5.847, "y": 1.297}, {"x": 5.58, "y": 1.3}], "confidence": 0.856}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.617, "y": 1.207}, {"x": 6.827, "y": 1.19}, {"x": 6.823, "y": 1.29}, {"x": 6.617, "y": 1.3}], "confidence": 0.958}, {"text": "306.54", "page": 1, "boundingPolygon": [{"x": 7.59, "y": 1.2}, {"x": 7.93, "y": 1.197}, {"x": 7.933, "y": 1.293}, {"x": 7.59, "y": 1.3}], "confidence": 0.959}], "anchorConfidence": 0.947, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "14.20", "value": 14.2, "unit": "$", "type": "currency", "lines": [{"text": "292.34", "page": 1, "boundingPolygon": [{"x": 4.537, "y": 1.197}, {"x": 4.88, "y": 1.193}, {"x": 4.883, "y": 1.297}, {"x": 4.54, "y": 1.3}], "confidence": 0.958}, {"text": "14.20", "page": 1, "boundingPolygon": [{"x": 5.58, "y": 1.2}, {"x": 5.85, "y": 1.19}, {"x": 5.847, "y": 1.297}, {"x": 5.58, "y": 1.3}], "confidence": 0.856}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.617, "y": 1.207}, {"x": 6.827, "y": 1.19}, {"x": 6.823, "y": 1.29}, {"x": 6.617, "y": 1.3}], "confidence": 0.958}, {"text": "306.54", "page": 1, "boundingPolygon": [{"x": 7.59, "y": 1.2}, {"x": 7.93, "y": 1.197}, {"x": 7.933, "y": 1.293}, {"x": 7.59, "y": 1.3}], "confidence": 0.959}], "anchorConfidence": 0.947, "valueConfidence": 0.856}, "policy_effective_date": {"source": "03/01/2016", "value": "2016-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2017", "value": "2017-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "230071", "lines": [{"text": "230071", "page": 1, "boundingPolygon": [{"x": 1.81, "y": 1.327}, {"x": 2.183, "y": 1.327}, {"x": 2.183, "y": 1.433}, {"x": 1.81, "y": 1.433}], "confidence": 0.959}], "anchorConfidence": 0.959, "valueConfidence": 0.959}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "10/14/2016", "value": "2016-10-14T00:00:00.000Z", "type": "date", "lines": [{"text": "10/14/2016", "page": 1, "boundingPolygon": [{"x": 3.173, "y": 1.33}, {"x": 3.737, "y": 1.327}, {"x": 3.737, "y": 1.433}, {"x": 3.173, "y": 1.44}], "confidence": 0.958}, {"text": "197.34", "page": 1, "boundingPolygon": [{"x": 4.547, "y": 1.33}, {"x": 4.88, "y": 1.327}, {"x": 4.88, "y": 1.43}, {"x": 4.547, "y": 1.433}], "confidence": 0.959}, {"text": "5.75", "page": 1, "boundingPolygon": [{"x": 5.63, "y": 1.327}, {"x": 5.86, "y": 1.33}, {"x": 5.86, "y": 1.433}, {"x": 5.633, "y": 1.433}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.61, "y": 1.333}, {"x": 6.837, "y": 1.327}, {"x": 6.837, "y": 1.423}, {"x": 6.61, "y": 1.437}], "confidence": 0.958}, {"text": "203.09", "page": 1, "boundingPolygon": [{"x": 7.58, "y": 1.33}, {"x": 7.923, "y": 1.327}, {"x": 7.927, "y": 1.43}, {"x": 7.58, "y": 1.433}], "confidence": 0.866}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "loss_reported_date": {"source": "10/14/2016", "value": "2016-10-14T00:00:00.000Z", "type": "date", "lines": [{"text": "10/14/2016", "page": 1, "boundingPolygon": [{"x": 3.173, "y": 1.33}, {"x": 3.737, "y": 1.327}, {"x": 3.737, "y": 1.433}, {"x": 3.173, "y": 1.44}], "confidence": 0.958}, {"text": "197.34", "page": 1, "boundingPolygon": [{"x": 4.547, "y": 1.33}, {"x": 4.88, "y": 1.327}, {"x": 4.88, "y": 1.43}, {"x": 4.547, "y": 1.433}], "confidence": 0.959}, {"text": "5.75", "page": 1, "boundingPolygon": [{"x": 5.63, "y": 1.327}, {"x": 5.86, "y": 1.33}, {"x": 5.86, "y": 1.433}, {"x": 5.633, "y": 1.433}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.61, "y": 1.333}, {"x": 6.837, "y": 1.327}, {"x": 6.837, "y": 1.423}, {"x": 6.61, "y": 1.437}], "confidence": 0.958}, {"text": "203.09", "page": 1, "boundingPolygon": [{"x": 7.58, "y": 1.33}, {"x": 7.923, "y": 1.327}, {"x": 7.927, "y": 1.43}, {"x": 7.58, "y": 1.433}], "confidence": 0.866}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_incurred": {"source": "203.09", "value": 203.09, "unit": "$", "type": "currency", "lines": [{"text": "197.34", "page": 1, "boundingPolygon": [{"x": 4.547, "y": 1.33}, {"x": 4.88, "y": 1.327}, {"x": 4.88, "y": 1.43}, {"x": 4.547, "y": 1.433}], "confidence": 0.959}, {"text": "5.75", "page": 1, "boundingPolygon": [{"x": 5.63, "y": 1.327}, {"x": 5.86, "y": 1.33}, {"x": 5.86, "y": 1.433}, {"x": 5.633, "y": 1.433}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.61, "y": 1.333}, {"x": 6.837, "y": 1.327}, {"x": 6.837, "y": 1.423}, {"x": 6.61, "y": 1.437}], "confidence": 0.958}, {"text": "203.09", "page": 1, "boundingPolygon": [{"x": 7.58, "y": 1.33}, {"x": 7.923, "y": 1.327}, {"x": 7.927, "y": 1.43}, {"x": 7.58, "y": 1.433}], "confidence": 0.866}], "anchorConfidence": 0.958, "valueConfidence": 0.866}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "197.34", "page": 1, "boundingPolygon": [{"x": 4.547, "y": 1.33}, {"x": 4.88, "y": 1.327}, {"x": 4.88, "y": 1.43}, {"x": 4.547, "y": 1.433}], "confidence": 0.959}, {"text": "5.75", "page": 1, "boundingPolygon": [{"x": 5.63, "y": 1.327}, {"x": 5.86, "y": 1.33}, {"x": 5.86, "y": 1.433}, {"x": 5.633, "y": 1.433}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.61, "y": 1.333}, {"x": 6.837, "y": 1.327}, {"x": 6.837, "y": 1.423}, {"x": 6.61, "y": 1.437}], "confidence": 0.958}, {"text": "203.09", "page": 1, "boundingPolygon": [{"x": 7.58, "y": 1.33}, {"x": 7.923, "y": 1.327}, {"x": 7.927, "y": 1.43}, {"x": 7.58, "y": 1.433}], "confidence": 0.866}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid": {"source": "197.34", "value": 197.34, "unit": "$", "type": "currency", "lines": [{"text": "197.34", "page": 1, "boundingPolygon": [{"x": 4.547, "y": 1.33}, {"x": 4.88, "y": 1.327}, {"x": 4.88, "y": 1.43}, {"x": 4.547, "y": 1.433}], "confidence": 0.959}, {"text": "5.75", "page": 1, "boundingPolygon": [{"x": 5.63, "y": 1.327}, {"x": 5.86, "y": 1.33}, {"x": 5.86, "y": 1.433}, {"x": 5.633, "y": 1.433}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.61, "y": 1.333}, {"x": 6.837, "y": 1.327}, {"x": 6.837, "y": 1.423}, {"x": 6.61, "y": 1.437}], "confidence": 0.958}, {"text": "203.09", "page": 1, "boundingPolygon": [{"x": 7.58, "y": 1.33}, {"x": 7.923, "y": 1.327}, {"x": 7.927, "y": 1.43}, {"x": 7.58, "y": 1.433}], "confidence": 0.866}], "anchorConfidence": 0.958, "valueConfidence": 0.959}, "total_amount_paid_expense": {"source": "5.75", "value": 5.75, "unit": "$", "type": "currency", "lines": [{"text": "197.34", "page": 1, "boundingPolygon": [{"x": 4.547, "y": 1.33}, {"x": 4.88, "y": 1.327}, {"x": 4.88, "y": 1.43}, {"x": 4.547, "y": 1.433}], "confidence": 0.959}, {"text": "5.75", "page": 1, "boundingPolygon": [{"x": 5.63, "y": 1.327}, {"x": 5.86, "y": 1.33}, {"x": 5.86, "y": 1.433}, {"x": 5.633, "y": 1.433}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.61, "y": 1.333}, {"x": 6.837, "y": 1.327}, {"x": 6.837, "y": 1.423}, {"x": 6.61, "y": 1.437}], "confidence": 0.958}, {"text": "203.09", "page": 1, "boundingPolygon": [{"x": 7.58, "y": 1.33}, {"x": 7.923, "y": 1.327}, {"x": 7.927, "y": 1.43}, {"x": 7.58, "y": 1.433}], "confidence": 0.866}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "policy_effective_date": {"source": "03/01/2016", "value": "2016-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2017", "value": "2017-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}, {"claim_number": {"type": "string", "value": "229611", "lines": [{"text": "229611", "page": 1, "boundingPolygon": [{"x": 1.817, "y": 1.463}, {"x": 2.177, "y": 1.463}, {"x": 2.177, "y": 1.57}, {"x": 1.817, "y": 1.57}], "confidence": 0.958}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "insured": {"type": "string", "value": "Rugby Personnel Services, Inc.", "lines": [{"text": "Rugby Personnel Services, Inc.", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 2.43}, {"x": 5.821, "y": 2.436}, {"x": 5.82, "y": 2.553}, {"x": 4.31, "y": 2.544}], "confidence": 0.9432499999999999}], "anchorConfidence": 0.942, "valueConfidence": 0.9432499999999999}, "carrier": {"value": "Harford Insurance Comapny", "type": "string", "valueConfidence": null}, "line_of_business": {"value": "Unknown", "type": "string", "valueConfidence": null}, "date_of_loss": {"source": "09/15/2016", "value": "2016-09-15T00:00:00.000Z", "type": "date", "lines": [{"text": "09/15/2016", "page": 1, "boundingPolygon": [{"x": 3.173, "y": 1.463}, {"x": 3.737, "y": 1.463}, {"x": 3.737, "y": 1.573}, {"x": 3.173, "y": 1.577}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 4.657, "y": 1.463}, {"x": 4.873, "y": 1.46}, {"x": 4.87, "y": 1.563}, {"x": 4.66, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 5.633, "y": 1.463}, {"x": 5.85, "y": 1.457}, {"x": 5.85, "y": 1.56}, {"x": 5.637, "y": 1.567}], "confidence": 0.953}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.603, "y": 1.47}, {"x": 6.813, "y": 1.457}, {"x": 6.813, "y": 1.563}, {"x": 6.603, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 7.703, "y": 1.46}, {"x": 7.923, "y": 1.457}, {"x": 7.923, "y": 1.563}, {"x": 7.703, "y": 1.567}], "confidence": 0.955}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "loss_reported_date": {"source": "09/15/2016", "value": "2016-09-15T00:00:00.000Z", "type": "date", "lines": [{"text": "09/15/2016", "page": 1, "boundingPolygon": [{"x": 3.173, "y": 1.463}, {"x": 3.737, "y": 1.463}, {"x": 3.737, "y": 1.573}, {"x": 3.173, "y": 1.577}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 4.657, "y": 1.463}, {"x": 4.873, "y": 1.46}, {"x": 4.87, "y": 1.563}, {"x": 4.66, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 5.633, "y": 1.463}, {"x": 5.85, "y": 1.457}, {"x": 5.85, "y": 1.56}, {"x": 5.637, "y": 1.567}], "confidence": 0.953}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.603, "y": 1.47}, {"x": 6.813, "y": 1.457}, {"x": 6.813, "y": 1.563}, {"x": 6.603, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 7.703, "y": 1.46}, {"x": 7.923, "y": 1.457}, {"x": 7.923, "y": 1.563}, {"x": 7.703, "y": 1.567}], "confidence": 0.955}], "anchorConfidence": 0.956, "valueConfidence": 0.958}, "total_amount_incurred": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 1, "boundingPolygon": [{"x": 4.657, "y": 1.463}, {"x": 4.873, "y": 1.46}, {"x": 4.87, "y": 1.563}, {"x": 4.66, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 5.633, "y": 1.463}, {"x": 5.85, "y": 1.457}, {"x": 5.85, "y": 1.56}, {"x": 5.637, "y": 1.567}], "confidence": 0.953}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.603, "y": 1.47}, {"x": 6.813, "y": 1.457}, {"x": 6.813, "y": 1.563}, {"x": 6.603, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 7.703, "y": 1.46}, {"x": 7.923, "y": 1.457}, {"x": 7.923, "y": 1.563}, {"x": 7.703, "y": 1.567}], "confidence": 0.955}], "anchorConfidence": 0.958, "valueConfidence": 0.955}, "total_amount_reserved": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 1, "boundingPolygon": [{"x": 4.657, "y": 1.463}, {"x": 4.873, "y": 1.46}, {"x": 4.87, "y": 1.563}, {"x": 4.66, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 5.633, "y": 1.463}, {"x": 5.85, "y": 1.457}, {"x": 5.85, "y": 1.56}, {"x": 5.637, "y": 1.567}], "confidence": 0.953}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.603, "y": 1.47}, {"x": 6.813, "y": 1.457}, {"x": 6.813, "y": 1.563}, {"x": 6.603, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 7.703, "y": 1.46}, {"x": 7.923, "y": 1.457}, {"x": 7.923, "y": 1.563}, {"x": 7.703, "y": 1.567}], "confidence": 0.955}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 1, "boundingPolygon": [{"x": 4.657, "y": 1.463}, {"x": 4.873, "y": 1.46}, {"x": 4.87, "y": 1.563}, {"x": 4.66, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 5.633, "y": 1.463}, {"x": 5.85, "y": 1.457}, {"x": 5.85, "y": 1.56}, {"x": 5.637, "y": 1.567}], "confidence": 0.953}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.603, "y": 1.47}, {"x": 6.813, "y": 1.457}, {"x": 6.813, "y": 1.563}, {"x": 6.603, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 7.703, "y": 1.46}, {"x": 7.923, "y": 1.457}, {"x": 7.923, "y": 1.563}, {"x": 7.703, "y": 1.567}], "confidence": 0.955}], "anchorConfidence": 0.958, "valueConfidence": 0.958}, "total_amount_paid_expense": {"source": "0.00", "value": 0, "unit": "$", "type": "currency", "lines": [{"text": "0.00", "page": 1, "boundingPolygon": [{"x": 4.657, "y": 1.463}, {"x": 4.873, "y": 1.46}, {"x": 4.87, "y": 1.563}, {"x": 4.66, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 5.633, "y": 1.463}, {"x": 5.85, "y": 1.457}, {"x": 5.85, "y": 1.56}, {"x": 5.637, "y": 1.567}], "confidence": 0.953}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 6.603, "y": 1.47}, {"x": 6.813, "y": 1.457}, {"x": 6.813, "y": 1.563}, {"x": 6.603, "y": 1.567}], "confidence": 0.958}, {"text": "0.00", "page": 1, "boundingPolygon": [{"x": 7.703, "y": 1.46}, {"x": 7.923, "y": 1.457}, {"x": 7.923, "y": 1.563}, {"x": 7.703, "y": 1.567}], "confidence": 0.955}], "anchorConfidence": 0.958, "valueConfidence": 0.953}, "policy_effective_date": {"source": "03/01/2016", "value": "2016-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "policy_expiration_date": {"source": "03/01/2017", "value": "2017-03-01T00:00:00.000Z", "type": "date", "valueConfidence": null}, "claim_status": {"value": "Closed", "type": "string", "valueConfidence": null}}]}
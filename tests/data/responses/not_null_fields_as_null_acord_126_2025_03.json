{"application_information": [{"phone": null, "fax": null, "agency": {"type": "string", "value": "ABEL LUNA INSURANCE", "lines": [{"text": "ABEL LUNA INSURANCE", "page": 0, "boundingPolygon": [{"x": 0.293, "y": 1.214}, {"x": 1.573, "y": 1.211}, {"x": 1.574, "y": 1.321}, {"x": 0.293, "y": 1.321}], "confidence": 0.959}], "anchorConfidence": 0.958, "valueConfidence": 0.959}, "code": null, "sub_code": null, "agency_customer_id": null, "applicant_first_named_insured": {"type": "string", "value": "140 WARE ROAD, LLC", "lines": [{"text": "140 WARE ROAD, LLC", "page": 0, "boundingPolygon": [{"x": 4.198, "y": 1.541}, {"x": 5.344, "y": 1.538}, {"x": 5.345, "y": 1.648}, {"x": 4.198, "y": 1.647}], "confidence": 0.9532499999999999}], "anchorConfidence": 0.9433999999999999, "valueConfidence": 0.9532499999999999}, "effective_date": null, "expiration_date": null, "bill_direct": null, "bill_agency": null, "payment_plan": null, "audit": null, "for_company_use_only": null, "date": {"source": "04/22/2025", "value": "2025-04-22T00:00:00.000Z", "type": "date", "lines": [{"text": "04/22/2025", "page": 0, "boundingPolygon": [{"x": 7.139, "y": 0.89}, {"x": 7.69, "y": 0.888}, {"x": 7.69, "y": 0.995}, {"x": 7.14, "y": 0.996}], "confidence": 0.958}], "anchorConfidence": 0.903, "valueConfidence": 0.958}, "carrier": null, "naic_code": null, "policy_number": null}], "coverages": [{"commercial_general_liability": null, "claims_made": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9005, "valueConfidence": null}, "occurrence": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.958, "valueConfidence": null}, "owners_and_contractors_protective": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.91525, "valueConfidence": null}, "coverage_custom_name": null, "coverage_custom_value": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.91525, "valueConfidence": null}, "deductibles_property_damage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "deductibles_property_damage_amount": null, "deductibles_bodily_injury": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9099999999999999, "valueConfidence": null}, "deductibles_bodily_injury_amount": null, "deductibles_custom": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9099999999999999, "valueConfidence": null}, "deductibles_custom_name": null, "deductibles_custom_value": null, "deductible_per_claim": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.948, "valueConfidence": null}, "deductibles_per_occurrence": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.958, "valueConfidence": null}, "limits_general_aggregate": null, "limits_general_aggregate_limit_applies_per_policy": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.907, "valueConfidence": null}, "limits_general_aggregate_limit_applies_per_location": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "limits_general_aggregate_limit_applies_per_project": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "limits_general_aggregate_limit_applies_per_other": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.958, "valueConfidence": null}, "limits_products_and_completed_operations_aggregate": null, "limits_personal_and_advertising_injury": null, "limits_each_occurrence": {"source": "10,000,000", "value": 10000000, "type": "number", "lines": [{"text": "$ 10,000,000", "page": 0, "boundingPolygon": [{"x": 5.412, "y": 3.098}, {"x": 6.088, "y": 3.079}, {"x": 6.092, "y": 3.179}, {"x": 5.412, "y": 3.204}], "confidence": 0.8355}], "anchorConfidence": 0.955, "valueConfidence": 0.8355}, "limits_damage_to_rented_premises": null, "limits_medical_expense": null, "limits_employee_benefits": null, "limits_custom_limit_name": null, "limits_custom_limit_amount": null, "premiums_premises_operations": null, "premiums_products": null, "premiums_other": null, "premiums_total": null, "other_coverages": null, "wisconsin_um_coverage_is_available": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9325999999999999, "valueConfidence": null}, "wisconsin_medical_payments_coverage_is_available": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.955, "valueConfidence": null}}], "schedule_of_hazards": [{"location_number": null, "building_number": null, "hazard_number": null, "classification": {"type": "string", "value": "COMMERCIAL LAND DEVELOPER", "lines": [{"text": "COMMERCIAL LAND DEVELOPER", "page": 0, "boundingPolygon": [{"x": 0.314, "y": 5.505}, {"x": 2.061, "y": 5.5}, {"x": 2.062, "y": 5.607}, {"x": 0.315, "y": 5.608}], "confidence": 0.9586666666666667}], "anchorConfidence": 0.957, "valueConfidence": 0.9586666666666667}, "class_code": {"type": "string", "value": "237210", "lines": [{"text": "237210", "page": 0, "boundingPolygon": [{"x": 1.227, "y": 5.179}, {"x": 1.597, "y": 5.178}, {"x": 1.597, "y": 5.278}, {"x": 1.227, "y": 5.279}], "confidence": 0.958}], "anchorConfidence": 0.959, "valueConfidence": 0.958}, "premium_basis": null, "exposure": null, "territorial_rating": null, "rate_prem_ops": null, "rate_products": null, "premium_prem_ops": null, "premium_products": null}], "claims_made": [{"proposed_retroactive_date": null, "entry_date_uninterrupted_claims_made_coverage": null, "any_product_work_accident_location_been_excluded": null, "any_product_work_accident_location_been_excluded_explanation": null, "tail_coverage_purchased": null, "tail_coverage_purchased_explanation": null}], "employee_benefits_liability": [{"deductible_per_claim": null, "number_of_employees": null, "number_of_employees_covered": null, "retroactive_date": null}], "contractors": [{"applicant_draw_plans_for_others": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 1, "boundingPolygon": [{"x": 7.841, "y": 0.908}, {"x": 7.928, "y": 0.901}, {"x": 7.931, "y": 0.991}, {"x": 7.845, "y": 0.998}], "confidence": 0.888}], "anchorConfidence": 0.944, "valueConfidence": 0.888}, "applicant_draw_plans_for_others_explanation": null, "any_operations_include_blasting_or_explosive_material": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 1, "boundingPolygon": [{"x": 7.846, "y": 1.405}, {"x": 7.93, "y": 1.395}, {"x": 7.933, "y": 1.478}, {"x": 7.85, "y": 1.488}], "confidence": 0.888}], "anchorConfidence": 0.9575833333333333, "valueConfidence": 0.888}, "any_operations_include_blasting_or_explosive_material_explanation": null, "any_operation_include_excavation": {"value": true, "type": "boolean", "lines": [{"text": "Y", "page": 1, "boundingPolygon": [{"x": 7.838, "y": 1.899}, {"x": 7.932, "y": 1.881}, {"x": 7.945, "y": 1.965}, {"x": 7.849, "y": 1.985}], "confidence": 0.872}], "anchorConfidence": 0.9543333333333334, "valueConfidence": 0.872}, "any_operation_include_excavation_explanation": null, "subcontractors_carry_coverages_less": {"value": true, "type": "boolean", "lines": [{"text": "Y", "page": 1, "boundingPolygon": [{"x": 7.834, "y": 2.375}, {"x": 7.937, "y": 2.358}, {"x": 7.947, "y": 2.448}, {"x": 7.844, "y": 2.469}], "confidence": 0.849}], "anchorConfidence": 0.954181818181818, "valueConfidence": 0.849}, "subcontractors_carry_coverages_less_explanation": null, "subcontractors_allowed_to_work_without_insurance_certificate": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 1, "boundingPolygon": [{"x": 7.836, "y": 2.862}, {"x": 7.926, "y": 2.848}, {"x": 7.933, "y": 2.942}, {"x": 7.846, "y": 2.955}], "confidence": 0.89}], "anchorConfidence": 0.9529285714285713, "valueConfidence": 0.89}, "subcontractors_allowed_to_work_without_insurance_certificate_explanation": null, "applicant_lease_equipment_to_others": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 1, "boundingPolygon": [{"x": 7.838, "y": 3.349}, {"x": 7.931, "y": 3.335}, {"x": 7.935, "y": 3.428}, {"x": 7.845, "y": 3.442}], "confidence": 0.881}], "anchorConfidence": 0.9443636363636362, "valueConfidence": 0.881}, "applicant_lease_equipment_to_others_explanation": null, "describe_type_of_work_subcontracted": null, "money_paid_to_subcontractors": null, "percentage_of_work_subcontracted": null, "number_of_full_time_staff": null, "number_of_part_time_staff": null}], "products_completed_operations": null, "products": [{"applicant_installs_services_demonstrates_products": null, "applicant_installs_services_demonstrates_products_explanation": null, "foreign_products": null, "foreign_products_explanation": null, "research_and_development_conducted": null, "research_and_development_conducted_explanation": null, "guarantees_warranties_hold_harmless_agreements": null, "guarantees_warranties_hold_harmless_agreements_explanation": null, "products_related_to_aircraft_space_industry": null, "products_related_to_aircraft_space_industry_explanation": null, "products_recalled_discontinued_changed": null, "products_recalled_discontinued_changed_explanation": null, "products_of_others_sold": null, "products_of_others_sold_explanation": null, "products_under_label_of_others": null, "products_under_label_of_others_explanation": null, "vendors_coverage_required": null, "vendors_coverage_required_explanation": null, "any_named_insureds_sell_to_others": null, "any_named_insureds_sell_to_others_explanation": null}], "additional_interest": [{"interest_additional_insured": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9584999999999999, "valueConfidence": null}, "interest_loss_payee": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "interest_mortgagee": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "interest_lienholder": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.958, "valueConfidence": null}, "interest_employee_as_lessor": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9586666666666667, "valueConfidence": null}, "rank": null, "name_and_address": null, "reference_number": null, "certificate_required": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.958, "valueConfidence": null}, "location": null, "building": null, "vehicle": null, "boat": null, "scheduled_item_number": null, "other": null, "item_class": null, "item": null, "item_description": null}], "general_information": [{"any_medical_facilities_provided": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.839, "y": 2.456}, {"x": 7.916, "y": 2.452}, {"x": 7.92, "y": 2.539}, {"x": 7.843, "y": 2.542}], "confidence": 0.893}], "anchorConfidence": 0.9517272727272726, "valueConfidence": 0.893}, "any_medical_facilities_provided_explanation": null, "any_exposure_radioactive_nuclear_materials": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.842, "y": 2.942}, {"x": 7.922, "y": 2.929}, {"x": 7.929, "y": 3.019}, {"x": 7.849, "y": 3.032}], "confidence": 0.888}], "anchorConfidence": 0.9461666666666666, "valueConfidence": 0.888}, "any_exposure_radioactive_nuclear_materials_explanation": null, "hazardous_material": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.834, "y": 3.429}, {"x": 7.927, "y": 3.412}, {"x": 7.938, "y": 3.512}, {"x": 7.841, "y": 3.529}], "confidence": 0.883}], "anchorConfidence": 0.9469999999999998, "valueConfidence": 0.883}, "hazardous_material_explanation": null, "any_operations_sold_acquired_discontinued": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.831, "y": 4.076}, {"x": 7.924, "y": 4.062}, {"x": 7.935, "y": 4.159}, {"x": 7.841, "y": 4.172}], "confidence": 0.885}], "anchorConfidence": 0.9484166666666667, "valueConfidence": 0.885}, "any_operations_sold_acquired_discontinued_explanation": null, "equipment_loaned_or_rented": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.831, "y": 4.712}, {"x": 7.927, "y": 4.702}, {"x": 7.934, "y": 4.802}, {"x": 7.838, "y": 4.812}], "confidence": 0.883}], "anchorConfidence": 0.9485555555555555, "valueConfidence": 0.883}, "equipment_loaned_or_rented_explanation": null, "equipment_loaned_or_rented_list": null, "watercraft_docks_floats": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.837, "y": 5.356}, {"x": 7.934, "y": 5.345}, {"x": 7.938, "y": 5.445}, {"x": 7.841, "y": 5.456}], "confidence": 0.884}], "anchorConfidence": 0.946111111111111, "valueConfidence": 0.884}, "watercraft_docks_floats_explanation": null, "parking_facilities_owned_rented": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.836, "y": 5.849}, {"x": 7.93, "y": 5.832}, {"x": 7.937, "y": 5.929}, {"x": 7.843, "y": 5.946}], "confidence": 0.884}], "anchorConfidence": 0.9484, "valueConfidence": 0.884}, "parking_facilities_owned_rented_explanation": null, "fee_charged_for_parking": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.835, "y": 6.333}, {"x": 7.932, "y": 6.315}, {"x": 7.936, "y": 6.409}, {"x": 7.839, "y": 6.423}], "confidence": 0.878}], "anchorConfidence": 0.928, "valueConfidence": 0.878}, "fee_charged_for_parking_explanation": null, "recreation_facilities_provided": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.834, "y": 6.806}, {"x": 7.924, "y": 6.795}, {"x": 7.925, "y": 6.882}, {"x": 7.835, "y": 6.893}], "confidence": 0.885}], "anchorConfidence": 0.9235, "valueConfidence": 0.885}, "recreation_facilities_provided_explanation": null, "lodging_operations": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.83, "y": 7.276}, {"x": 7.937, "y": 7.265}, {"x": 7.937, "y": 7.375}, {"x": 7.834, "y": 7.386}], "confidence": 0.887}], "anchorConfidence": 0.9366923076923077, "valueConfidence": 0.887}, "lodging_operations_number_of_apartments": null, "lodging_operations_total_apartment_area": null, "lodging_operations_other_lodging_operations_description": null, "swimming_pool_on_premises": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.826, "y": 7.769}, {"x": 7.919, "y": 7.756}, {"x": 7.926, "y": 7.855}, {"x": 7.836, "y": 7.869}], "confidence": 0.888}], "anchorConfidence": 0.9400000000000001, "valueConfidence": 0.888}, "swimming_pool_on_premises_explanation": null, "swimming_pool_on_premises_approved_fence": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "swimming_pool_on_premises_limited_access": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9584999999999999, "valueConfidence": null}, "swimming_pool_on_premises_diving_board": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "swimming_pool_on_premises_slide": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "swimming_pool_on_premises_above_ground": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.959, "valueConfidence": null}, "swimming_pool_on_premises_in_ground": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.958, "valueConfidence": null}, "swimming_pool_on_premises_life_guard": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.63, "valueConfidence": null}, "social_events": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.828, "y": 8.093}, {"x": 7.921, "y": 8.076}, {"x": 7.931, "y": 8.172}, {"x": 7.838, "y": 8.193}], "confidence": 0.875}], "anchorConfidence": 0.9428000000000001, "valueConfidence": 0.875}, "social_events_explanation": null, "athletic_teams_sponsored": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.827, "y": 8.576}, {"x": 7.93, "y": 8.559}, {"x": 7.937, "y": 8.659}, {"x": 7.837, "y": 8.676}], "confidence": 0.881}], "anchorConfidence": 0.9456, "valueConfidence": 0.881}, "athletic_teams_sponsored_list": [{"type_of_sport": null, "contact_sport": null, "age_group_12_and_under": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9013333333333334, "valueConfidence": null}, "age_group_13_to_18": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9266666666666667, "valueConfidence": null}, "age_group_over_18": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9575, "valueConfidence": null}}], "structural_alterations_contemplated": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.827, "y": 9.299}, {"x": 7.92, "y": 9.289}, {"x": 7.924, "y": 9.382}, {"x": 7.831, "y": 9.393}], "confidence": 0.891}], "anchorConfidence": 0.929, "valueConfidence": 0.891}, "structural_alterations_contemplated_explanation": null, "any_demolition_exposure_contemplated": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 2, "boundingPolygon": [{"x": 7.833, "y": 9.796}, {"x": 7.926, "y": 9.782}, {"x": 7.926, "y": 9.872}, {"x": 7.836, "y": 9.886}], "confidence": 0.879}], "anchorConfidence": 0.9468, "valueConfidence": 0.879}, "any_demolition_exposure_contemplated_explanation": null, "applicant_involved_joint_ventures": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 3, "boundingPolygon": [{"x": 7.827, "y": 0.918}, {"x": 7.91, "y": 0.914}, {"x": 7.914, "y": 1.004}, {"x": 7.83, "y": 1.005}], "confidence": 0.894}], "anchorConfidence": 0.9424615384615385, "valueConfidence": 0.894}, "applicant_involved_joint_ventures_explanation": null, "lease_employees_other_employers": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 3, "boundingPolygon": [{"x": 7.821, "y": 1.405}, {"x": 7.921, "y": 1.391}, {"x": 7.928, "y": 1.488}, {"x": 7.828, "y": 1.498}], "confidence": 0.886}], "anchorConfidence": 0.9419999999999998, "valueConfidence": 0.886}, "lease_employees_other_employers_explanation": null, "lease_employees_other_employers_lease_to_list": [{"name": {"value": "", "type": "string", "lines": [{"text": "", "page": 3, "boundingPolygon": [{"x": 16.7735, "y": 7.6482}, {"x": 26.2613, "y": 7.7393}, {"x": 26.2819, "y": 8.4133}, {"x": 16.7735, "y": 8.3951}], "confidence": 1}], "valueConfidence": 1}, "wc_coverage": null}, {"name": {"value": "", "type": "string", "lines": [{"text": "", "page": 3, "boundingPolygon": [{"x": 16.7735, "y": 8.3951}, {"x": 26.2819, "y": 8.4133}, {"x": 26.2819, "y": 9.0872}, {"x": 16.7735, "y": 9.069}], "confidence": 1}], "valueConfidence": 1}, "wc_coverage": null}], "lease_employees_other_employers_lease_from_list": null, "labor_interchange_other_business_or_subsidiaries": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 3, "boundingPolygon": [{"x": 7.824, "y": 2.215}, {"x": 7.921, "y": 2.204}, {"x": 7.924, "y": 2.301}, {"x": 7.828, "y": 2.308}], "confidence": 0.89}], "anchorConfidence": 0.94225, "valueConfidence": 0.89}, "labor_interchange_other_business_or_subsidiaries_explanation": null, "day_care_facilities": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 3, "boundingPolygon": [{"x": 7.822, "y": 2.701}, {"x": 7.922, "y": 2.691}, {"x": 7.926, "y": 2.788}, {"x": 7.826, "y": 2.798}], "confidence": 0.888}], "anchorConfidence": 0.94425, "valueConfidence": 0.888}, "day_care_facilities_explanation": null, "crimes_occured_or_attempted": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 3, "boundingPolygon": [{"x": 7.827, "y": 3.185}, {"x": 7.917, "y": 3.178}, {"x": 7.921, "y": 3.268}, {"x": 7.834, "y": 3.278}], "confidence": 0.891}], "anchorConfidence": 0.9551764705882353, "valueConfidence": 0.891}, "crimes_occured_or_attempted_explanation": null, "formal_safety_policy": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 3, "boundingPolygon": [{"x": 7.825, "y": 3.675}, {"x": 7.912, "y": 3.665}, {"x": 7.919, "y": 3.755}, {"x": 7.832, "y": 3.765}], "confidence": 0.892}], "anchorConfidence": 0.9504166666666668, "valueConfidence": 0.892}, "formal_safety_policy_explanation": null, "does_business_promotion_represent_premises_safety_or_security": {"value": false, "type": "boolean", "lines": [{"text": "N", "page": 3, "boundingPolygon": [{"x": 7.823, "y": 4.155}, {"x": 7.927, "y": 4.145}, {"x": 7.927, "y": 4.245}, {"x": 7.827, "y": 4.252}], "confidence": 0.886}], "anchorConfidence": 0.9513529411764705, "valueConfidence": 0.886}, "does_business_promotion_represent_premises_safety_or_security_explanation": null}], "remarks": null, "signature": [{"producers_signature": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9515, "valueConfidence": null}, "producers_name": {"type": "string", "value": "ABEL LUNA", "lines": [{"text": "ABEL LUNA", "page": 4, "boundingPolygon": [{"x": 3.537, "y": 4.787}, {"x": 4.13, "y": 4.783}, {"x": 4.131, "y": 4.886}, {"x": 3.537, "y": 4.887}], "confidence": 0.959}], "anchorConfidence": 0.9377500000000001, "valueConfidence": 0.959}, "state_producer_license_number": null, "applicants_signature": null, "date": null, "national_producers_number": null}]}
import json

from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.file_onboarding import (
    AdditionalData,
    Evidence,
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
    SuggestedField,
)
from static_common.schemas.file_onboarding import OnboardedFileSchema
import pytest

from copilot.logic.onboarded_files_transformation import (
    calculate_entity_id,
    create_or_update_entity_information,
    merge_onboarded_data,
)
from copilot.models.files_merging import MergeDataRequest


@pytest.fixture
def onboarded_file() -> OnboardedFile:
    with open("tests/data/data_onboarding/onboarded_file_no_entity_name.json") as file:
        d = json.loads(file.read())
    return OnboardedFileSchema().load(d)


def test_create_or_update_entity_information(onboarded_file):
    of = create_or_update_entity_information(onboarded_file, 0, EntityFieldID.NAME, "Name1")

    assert of.entity_information[1].name == EntityFieldID.NAME.value
    assert len(of.entity_information[1].values) == 1
    assert of.entity_information[1].values[0].value == "Name1"
    assert of.entities[0].id == calculate_entity_id("Name1", "Address1")

    of = create_or_update_entity_information(of, 1, EntityFieldID.NAME, "Name1")
    assert len(of.entity_information[1].values) == 2
    assert of.entity_information[1].values[1].value == "Name1"
    assert of.entities[1].id == "849d3c0b-ffaf-4249-b98e-f003c2a65759"

    of = create_or_update_entity_information(of, 2, EntityFieldID.NAME, "Name2")
    assert len(of.entity_information[1].values) == 3
    assert of.entity_information[1].values[2].value == "Name2"
    assert of.entities[2].id == calculate_entity_id("Name2", "Address2")

    of = create_or_update_entity_information(of, 0, EntityFieldID.NAME, "New Name1")
    assert len(of.entity_information[1].values) == 3
    assert of.entity_information[1].values[0].value == "New Name1"
    assert of.entities[0].id == calculate_entity_id("New Name1", "Address1")


def test_merge_suggested_fields():
    f1 = OnboardedFile(
        files=["file_1"],
        additional_data=AdditionalData(
            suggested_fields=[
                SuggestedField(name="name1", fact_subtype_id="fact_subtype_id1", evidences=[Evidence(file_idx=0)]),
                SuggestedField(name="name1", fact_subtype_id="fact_subtype_id2", evidences=[Evidence(file_idx=0)]),
            ]
        ),
    )
    f2 = OnboardedFile(
        files=["file_2"],
        additional_data=AdditionalData(
            suggested_fields=[
                SuggestedField(name="name2", fact_subtype_id="fact_subtype_id1", evidences=[Evidence(file_idx=0)]),
                SuggestedField(name="name1", fact_subtype_id="fact_subtype_id3", evidences=[Evidence(file_idx=0)]),
            ]
        ),
    )
    f3 = OnboardedFile(
        files=["file_3"],
        fields=[
            ResolvedDataField(
                name="fact_subtype_id2_name", fact_subtype_id="fact_subtype_id2", value_type=FieldType.TEXT, values=[]
            ),
            ResolvedDataField(
                name="fact_subtype_id3_name",
                fact_subtype_id="fact_subtype_id3",
                value_type=FieldType.TEXT,
                values=[ResolvedDataValue(value=1)],
            ),
        ],
        additional_data=AdditionalData(
            suggested_fields=[SuggestedField(name="name3", evidences=[Evidence(file_idx=0)])]
        ),
    )
    request = [
        MergeDataRequest(file_id="file_1", data=f1, classification=ClassificationDocumentType.UNKNOWN),
        MergeDataRequest(file_id="file_2", data=f2, classification=ClassificationDocumentType.UNKNOWN),
        MergeDataRequest(file_id="file_3", data=f3, classification=ClassificationDocumentType.UNKNOWN),
    ]
    merged_data = merge_onboarded_data(request, True, True)
    suggested_fields = merged_data.additional_data.suggested_fields
    assert len(suggested_fields) == 3
    assert suggested_fields[0].fact_subtype_id == "fact_subtype_id1"
    assert len(suggested_fields[0].evidences) == 2
    assert suggested_fields[0].evidences[0].file_idx == 0
    assert suggested_fields[0].evidences[1].file_idx == 1
    assert suggested_fields[1].name == "name1"
    assert len(suggested_fields[1].evidences) == 1
    assert suggested_fields[1].evidences[0].file_idx == 0
    assert suggested_fields[1].fact_subtype_id == "fact_subtype_id2"
    assert suggested_fields[2].name == "name3"
    assert len(suggested_fields[1].evidences) == 1
    assert suggested_fields[1].evidences[0].file_idx == 0


def test_merge_do_not_merge_fact_subtype_and_column_name():
    f1 = OnboardedFile(
        files=["file_1"],
        fields=[
            ResolvedDataField(
                name="Total value",
                fact_subtype_id=FactSubtypeID.TIV,
                value_type=FieldType.NUMBER,
                values=[ResolvedDataValue(value=1000)],
            ),
        ],
    )
    f2 = OnboardedFile(
        files=["file_2"],
        fields=[
            ResolvedDataField(
                name="TIV",
                value_type=FieldType.NUMBER,
                values=[ResolvedDataValue(value=1000)],
            ),
        ],
    )
    request = [
        MergeDataRequest(file_id="file_1", data=f1, classification=ClassificationDocumentType.UNKNOWN),
        MergeDataRequest(file_id="file_2", data=f2, classification=ClassificationDocumentType.UNKNOWN),
    ]
    merged_data = merge_onboarded_data(request, True, True)
    assert len(merged_data.fields) == 2
    assert merged_data.fields[0].name == "Total value"
    assert merged_data.fields[1].name == "TIV"
    assert merged_data.fields[0].fact_subtype_id == FactSubtypeID.TIV
    assert merged_data.fields[1].fact_subtype_id is None


def test_do_not_merge_vehicles_and_drivers():
    f1 = OnboardedFile(
        files=["VEHICLES_file"],
        fields=[
            ResolvedDataField(
                name="Driver Name",
                value_type=FieldType.TEXT,
                values=[ResolvedDataValue(value="a", entity_idx=0)],
            ),
        ],
        entities=[SubmissionEntity(type=SubmissionEntityType.VEHICLE)],
    )
    f2 = OnboardedFile(
        files=["DRIVERS_file"],
        fields=[
            ResolvedDataField(
                name="Driver Name",
                value_type=FieldType.TEXT,
                values=[ResolvedDataValue(value="b", entity_idx=0)],
            ),
        ],
        entities=[SubmissionEntity(type=SubmissionEntityType.DRIVER)],
    )
    request = [
        MergeDataRequest(file_id="file_1", data=f1, classification=ClassificationDocumentType.UNKNOWN),
        MergeDataRequest(file_id="file_2", data=f2, classification=ClassificationDocumentType.UNKNOWN),
    ]
    merged_data = merge_onboarded_data(request, True, True)
    assert len(merged_data.fields) == 2
    assert merged_data.fields[0].name == merged_data.fields[1].name == "Driver Name"
    assert [x.value for x in merged_data.fields[0].values] == ["a"]
    assert [x.value for x in merged_data.fields[1].values] == ["b"]


def test_merge_field_with_subtype_and_field_without_subtype():
    f1 = OnboardedFile(
        files=["VEHICLES_file"],
        fields=[
            ResolvedDataField(
                name="Driver Name",
                value_type=FieldType.TEXT,
                fact_subtype_id=FactSubtypeID.DRIVER_NAME,
                values=[ResolvedDataValue(value="a", entity_idx=0)],
            ),
        ],
        entities=[SubmissionEntity(type=SubmissionEntityType.VEHICLE)],
    )
    f2 = OnboardedFile(
        files=["DRIVERS_file"],
        fields=[
            ResolvedDataField(
                name="Driver Name",
                value_type=FieldType.TEXT,
                values=[ResolvedDataValue(value="b", entity_idx=0)],
            ),
        ],
        entities=[SubmissionEntity(type=SubmissionEntityType.DRIVER)],
    )
    request = [
        MergeDataRequest(file_id="file_1", data=f1, classification=ClassificationDocumentType.UNKNOWN),
        MergeDataRequest(file_id="file_2", data=f2, classification=ClassificationDocumentType.UNKNOWN),
    ]
    merged_data = merge_onboarded_data(request, True, True)
    assert len(merged_data.fields) == 2
    assert merged_data.fields[0].name == merged_data.fields[1].name == "Driver Name"
    assert [x.value for x in merged_data.fields[0].values] == ["a"]
    assert [x.value for x in merged_data.fields[1].values] == ["b"]


def test_merge_handles_lists():
    f1 = OnboardedFile(
        fields=[
            ResolvedDataField(
                name="Some",
                value_type=FieldType.TEXT_ARRAY,
                values=[ResolvedDataValue(value=[["a"]], entity_idx=0)],
            ),
        ],
        entities=[SubmissionEntity(type=SubmissionEntityType.BUSINESS, id="1")],
    )
    f2 = OnboardedFile(
        fields=[
            ResolvedDataField(
                name="Some",
                value_type=FieldType.TEXT_ARRAY,
                values=[ResolvedDataValue(value="b", entity_idx=0), ResolvedDataValue(value=[["a"]], entity_idx=0)],
            ),
        ],
        entities=[SubmissionEntity(type=SubmissionEntityType.BUSINESS, id="1")],
    )
    request = [
        MergeDataRequest(file_id="file_1", data=f1, classification=ClassificationDocumentType.UNKNOWN),
        MergeDataRequest(file_id="file_2", data=f2, classification=ClassificationDocumentType.UNKNOWN),
    ]
    merged_data = merge_onboarded_data(request, True, True)
    assert len(merged_data.fields) == 1
    assert len(merged_data.fields[0].values) == 2
    values = [x.value for x in merged_data.fields[0].values]
    assert [["a"]] in values
    assert "b" in values

from uuid import uuid4

import pytest

from copilot.models.reports import SubmissionDeductible
from copilot.models.types import CoverageType, PolicyLevelDeductibleType
from tests.unit.duplication import check_copy

# these tests will test the report duplication functionality.
# to add some future proofing. First we are going to inspect the class and based on that we'll retrieve and test values
# only simple values will be tested for equality. Nested types and lists will be checked for None or empty.
# If a value is None or len(List) == 0, it means that is a new field which is not covered by the copy mechanism


# fields which are skipped because they are:
# - Are relations
# - Virtual columns
# - Not copied
# - etc..
SKIPPED_FIELDS = {"id", "created_at", "updated_at", "submission_id", "coverage", "submission"}


@pytest.fixture
def original() -> SubmissionDeductible:
    original = SubmissionDeductible()
    original.id = uuid4()
    original.coverage_id = uuid4()
    original.policy_limit = 5.1
    original.policy_level = 1.0
    original.policy_level_type = PolicyLevelDeductibleType.NUMERIC
    original.minimum = 2.3
    original.comment = "Comment"
    original.coverage_type = CoverageType.EXCESS
    return original


def test_copy_report(original):
    copy = original.copy()
    check_copy(SubmissionDeductible, original, copy, SKIPPED_FIELDS, {})

import builtins
import os
import threading
import time

import psutil
import pytest

test_metrics = {}
collection_start_time = None
collection_end_time = None
import_times = {}
fixture_setup_times = {}
fixture_teardown_times = {}


def monitor_resource_usage(pid, usage_list, stop_event):
    process = psutil.Process(pid)
    while not stop_event.is_set():
        cpu = process.cpu_percent(interval=None)
        mem = process.memory_info().rss
        usage_list.append((cpu, mem))
        time.sleep(0.1)


@pytest.hookimpl
def pytest_sessionstart(session):
    global collection_start_time
    collection_start_time = time.time()
    enable_import_hook()


@pytest.hookimpl
def pytest_sessionfinish(session, exitstatus):
    global collection_end_time
    collection_end_time = time.time()
    disable_import_hook()
    output_results()


def output_results():
    with open("test_metrics.txt", "w") as f:
        f.write("Test Execution Metrics:\n")
        sorted_tests = sorted(test_metrics.items(), key=lambda x: x[1]["execution_time"], reverse=True)
        for test, metrics in sorted_tests:
            f.write(f"{test}:\n")
            f.write(f"  Execution Time: {metrics['execution_time']:.4f}s\n")
            f.write(f"  Setup Time: {metrics.get('setup_time', 0):.4f}s\n")
            f.write(f"  Teardown Time: {metrics.get('teardown_time', 0):.4f}s\n")
            f.write(f"  Avg CPU Usage: {metrics['avg_cpu']:.2f}%\n")
            f.write(f"  Peak CPU Usage: {metrics['peak_cpu']:.2f}%\n")
            f.write(f"  Avg Memory Usage: {metrics['avg_mem']/1024/1024:.2f} MB\n")
            f.write(f"  Peak Memory Usage: {metrics['peak_mem']/1024/1024:.2f} MB\n\n")

        f.write("——————————\n\n")

        collection_time = collection_end_time - collection_start_time
        f.write(f"Test Collection Time: {collection_time:.4f}s\n\n")

        f.write("——————————\n\n")

        f.write("Module Import Times:\n")
        sorted_imports = sorted(import_times.items(), key=lambda x: x[1], reverse=True)
        for module, t in sorted_imports:
            f.write(f"{module}: {t:.6f}s\n\n")

        f.write("——————————\n\n")

        f.write("Fixture Setup Times:\n")
        for fixture, t in fixture_setup_times.items():
            f.write(f"{fixture}: {t:.6f}s\n")
        f.write("\nFixture Teardown Times:\n")
        for fixture, t in fixture_teardown_times.items():
            f.write(f"{fixture}: {t:.6f}s\n")


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_protocol(item, nextitem):
    pid = os.getpid()
    usage_list = []
    stop_event = threading.Event()
    monitor_thread = threading.Thread(target=monitor_resource_usage, args=(pid, usage_list, stop_event))
    monitor_thread.start()

    start_time = time.time()
    yield
    end_time = time.time()
    execution_time = end_time - start_time

    stop_event.set()
    monitor_thread.join()

    if usage_list:
        cpu_usage = [usage[0] for usage in usage_list]
        mem_usage = [usage[1] for usage in usage_list]
        avg_cpu = sum(cpu_usage) / len(cpu_usage)
        peak_cpu = max(cpu_usage)
        avg_mem = sum(mem_usage) / len(mem_usage)
        peak_mem = max(mem_usage)
    else:
        avg_cpu = peak_cpu = avg_mem = peak_mem = 0

    test_metrics[item.nodeid] = {
        "execution_time": execution_time,
        "avg_cpu": avg_cpu,
        "peak_cpu": peak_cpu,
        "avg_mem": avg_mem,
        "peak_mem": peak_mem,
        "setup_time": item.setup_time,
        "teardown_time": item.teardown_time,
    }


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_setup(item):
    start_time = time.time()
    yield
    end_time = time.time()
    item.setup_time = end_time - start_time


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_teardown(item):
    start_time = time.time()
    yield
    end_time = time.time()
    item.teardown_time = end_time - start_time


@pytest.hookimpl(hookwrapper=True)
def pytest_fixture_setup(fixturedef, request):
    start_time = time.time()
    yield
    end_time = time.time()
    duration = end_time - start_time
    fixture_setup_times[fixturedef.argname] = fixture_setup_times.get(fixturedef.argname, 0) + duration


@pytest.hookimpl(hookwrapper=True)
def pytest_fixture_post_finalizer(fixturedef):
    start_time = time.time()
    yield
    end_time = time.time()
    duration = end_time - start_time
    fixture_teardown_times[fixturedef.argname] = fixture_teardown_times.get(fixturedef.argname, 0) + duration


original_import = builtins.__import__


def custom_import(name, globals=None, locals=None, fromlist=(), level=0):
    start_time = time.time()
    module = original_import(name, globals, locals, fromlist, level)
    end_time = time.time()
    duration = end_time - start_time
    full_name = module.__name__
    import_times[full_name] = import_times.get(full_name, 0) + duration
    return module


def enable_import_hook():
    builtins.__import__ = custom_import


def disable_import_hook():
    builtins.__import__ = original_import
